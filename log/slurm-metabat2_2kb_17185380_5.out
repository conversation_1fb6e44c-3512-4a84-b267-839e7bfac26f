Decompressing scaffold file for 1507995...
Generating depth file for 1507995...
Running MetaBAT2 for 1507995 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 123. with random seed=1745943204
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [32.7Gb / 503.5Gb]
[00:00:00] Parsing assembly file [32.7Gb / 503.5Gb]
[00:00:00] ... processed 12 seqs, 12 long (>=2000), 0 short (>=1000) 1.0% (498805 of 49057406), ETA 0:00:03     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 27 seqs, 27 long (>=2000), 0 short (>=1000) 2.0% (985275 of 49057406), ETA 0:00:02     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 45 seqs, 45 long (>=2000), 0 short (>=1000) 3.0% (1491624 of 49057406), ETA 0:00:01     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 65 seqs, 65 long (>=2000), 0 short (>=1000) 4.0% (1980149 of 49057406), ETA 0:00:01     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 86 seqs, 86 long (>=2000), 0 short (>=1000) 5.0% (2453012 of 49057406), ETA 0:00:01     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 110 seqs, 110 long (>=2000), 0 short (>=1000) 6.0% (2959498 of 49057406), ETA 0:00:01     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 135 seqs, 135 long (>=2000), 0 short (>=1000) 7.0% (3448215 of 49057406), ETA 0:00:01     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 161 seqs, 161 long (>=2000), 0 short (>=1000) 8.0% (3929289 of 49057406), ETA 0:00:01     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 189 seqs, 189 long (>=2000), 0 short (>=1000) 9.0% (4422855 of 49057406), ETA 0:00:01     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 218 seqs, 218 long (>=2000), 0 short (>=1000) 10.0% (4907164 of 49057406), ETA 0:00:01     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 249 seqs, 249 long (>=2000), 0 short (>=1000) 11.0% (5401815 of 49057406), ETA 0:00:01     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 281 seqs, 281 long (>=2000), 0 short (>=1000) 12.0% (5887168 of 49057406), ETA 0:00:01     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 315 seqs, 315 long (>=2000), 0 short (>=1000) 13.0% (6380362 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 351 seqs, 351 long (>=2000), 0 short (>=1000) 14.0% (6879857 of 49057406), ETA 0:00:01     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 387 seqs, 387 long (>=2000), 0 short (>=1000) 15.0% (7360139 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 426 seqs, 426 long (>=2000), 0 short (>=1000) 16.0% (7861504 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 465 seqs, 465 long (>=2000), 0 short (>=1000) 17.0% (8346458 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 506 seqs, 506 long (>=2000), 0 short (>=1000) 18.0% (8832572 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 549 seqs, 549 long (>=2000), 0 short (>=1000) 19.0% (9323030 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 594 seqs, 594 long (>=2000), 0 short (>=1000) 20.0% (9817990 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 640 seqs, 640 long (>=2000), 0 short (>=1000) 21.0% (10308924 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 687 seqs, 687 long (>=2000), 0 short (>=1000) 22.0% (10792708 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 737 seqs, 737 long (>=2000), 0 short (>=1000) 23.0% (11287313 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 789 seqs, 789 long (>=2000), 0 short (>=1000) 24.0% (11781322 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 842 seqs, 842 long (>=2000), 0 short (>=1000) 25.0% (12265442 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 898 seqs, 898 long (>=2000), 0 short (>=1000) 26.0% (12758742 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 956 seqs, 956 long (>=2000), 0 short (>=1000) 27.0% (13251246 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1016 seqs, 1016 long (>=2000), 0 short (>=1000) 28.0% (13741894 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1078 seqs, 1078 long (>=2000), 0 short (>=1000) 29.0% (14230210 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1142 seqs, 1142 long (>=2000), 0 short (>=1000) 30.0% (14718464 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1209 seqs, 1209 long (>=2000), 0 short (>=1000) 31.0% (15213738 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1277 seqs, 1277 long (>=2000), 0 short (>=1000) 32.0% (15699559 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1348 seqs, 1348 long (>=2000), 0 short (>=1000) 33.0% (16191326 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1421 seqs, 1421 long (>=2000), 0 short (>=1000) 34.0% (16681665 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1496 seqs, 1496 long (>=2000), 0 short (>=1000) 35.0% (17170973 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1574 seqs, 1574 long (>=2000), 0 short (>=1000) 36.0% (17664068 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1654 seqs, 1654 long (>=2000), 0 short (>=1000) 37.0% (18152813 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1737 seqs, 1737 long (>=2000), 0 short (>=1000) 38.0% (18642307 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1823 seqs, 1823 long (>=2000), 0 short (>=1000) 39.0% (19133479 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 1912 seqs, 1912 long (>=2000), 0 short (>=1000) 40.0% (19626790 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 2003 seqs, 2003 long (>=2000), 0 short (>=1000) 41.0% (20114173 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 2099 seqs, 2099 long (>=2000), 0 short (>=1000) 42.0% (20608093 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 2197 seqs, 2197 long (>=2000), 0 short (>=1000) 43.0% (21095420 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 2300 seqs, 2300 long (>=2000), 0 short (>=1000) 44.0% (21589130 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 2406 seqs, 2406 long (>=2000), 0 short (>=1000) 45.0% (22078193 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 2516 seqs, 2516 long (>=2000), 0 short (>=1000) 46.0% (22566471 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 2631 seqs, 2631 long (>=2000), 0 short (>=1000) 47.0% (23057812 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 2751 seqs, 2751 long (>=2000), 0 short (>=1000) 48.0% (23549540 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 2875 seqs, 2875 long (>=2000), 0 short (>=1000) 49.0% (24038323 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 3005 seqs, 3005 long (>=2000), 0 short (>=1000) 50.0% (24528816 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 3142 seqs, 3142 long (>=2000), 0 short (>=1000) 51.0% (25021903 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 3284 seqs, 3284 long (>=2000), 0 short (>=1000) 52.0% (25511746 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 3433 seqs, 3433 long (>=2000), 0 short (>=1000) 53.0% (26003191 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 3588 seqs, 3588 long (>=2000), 0 short (>=1000) 54.0% (26493261 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 3750 seqs, 3750 long (>=2000), 0 short (>=1000) 55.0% (26983126 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 3920 seqs, 3920 long (>=2000), 0 short (>=1000) 56.0% (27474661 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 4098 seqs, 4098 long (>=2000), 0 short (>=1000) 57.0% (27963216 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 4288 seqs, 4288 long (>=2000), 0 short (>=1000) 58.0% (28455080 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 4490 seqs, 4490 long (>=2000), 0 short (>=1000) 59.0% (28944949 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 4706 seqs, 4706 long (>=2000), 0 short (>=1000) 60.0% (29435380 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 4937 seqs, 4937 long (>=2000), 0 short (>=1000) 61.0% (29925603 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 5186 seqs, 5013 long (>=2000), 173 short (>=1000) 62.0% (30416870 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 5454 seqs, 5013 long (>=2000), 441 short (>=1000) 63.0% (30906842 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 5744 seqs, 5013 long (>=2000), 731 short (>=1000) 64.0% (31397908 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 6057 seqs, 5013 long (>=2000), 1044 short (>=1000) 65.0% (31887961 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 6394 seqs, 5013 long (>=2000), 1381 short (>=1000) 66.0% (32378618 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 6758 seqs, 5013 long (>=2000), 1745 short (>=1000) 67.0% (32869595 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 7149 seqs, 5013 long (>=2000), 2136 short (>=1000) 68.0% (33359119 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 7569 seqs, 5013 long (>=2000), 2556 short (>=1000) 69.0% (33850060 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 8020 seqs, 5013 long (>=2000), 3007 short (>=1000) 70.0% (34341100 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 8501 seqs, 5013 long (>=2000), 3397 short (>=1000) 71.0% (34831609 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 9012 seqs, 5013 long (>=2000), 3397 short (>=1000) 72.0% (35321997 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 9556 seqs, 5013 long (>=2000), 3397 short (>=1000) 73.0% (35812344 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 10137 seqs, 5013 long (>=2000), 3397 short (>=1000) 74.0% (36302995 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 10759 seqs, 5013 long (>=2000), 3397 short (>=1000) 75.0% (36793537 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 11427 seqs, 5013 long (>=2000), 3397 short (>=1000) 76.0% (37284389 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 12146 seqs, 5013 long (>=2000), 3397 short (>=1000) 77.0% (37774856 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 12922 seqs, 5013 long (>=2000), 3397 short (>=1000) 78.0% (38265199 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 13762 seqs, 5013 long (>=2000), 3397 short (>=1000) 79.0% (38755983 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 14670 seqs, 5013 long (>=2000), 3397 short (>=1000) 80.0% (39246001 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 15657 seqs, 5013 long (>=2000), 3397 short (>=1000) 81.0% (39736909 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 16732 seqs, 5013 long (>=2000), 3397 short (>=1000) 82.0% (40227577 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 17903 seqs, 5013 long (>=2000), 3397 short (>=1000) 83.0% (40718120 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 19173 seqs, 5013 long (>=2000), 3397 short (>=1000) 84.0% (41208306 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 20555 seqs, 5013 long (>=2000), 3397 short (>=1000) 85.0% (41698992 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 22061 seqs, 5013 long (>=2000), 3397 short (>=1000) 86.0% (42189626 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 23715 seqs, 5013 long (>=2000), 3397 short (>=1000) 87.0% (42680077 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 25522 seqs, 5013 long (>=2000), 3397 short (>=1000) 88.0% (43170672 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 27487 seqs, 5013 long (>=2000), 3397 short (>=1000) 89.0% (43661387 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 29620 seqs, 5013 long (>=2000), 3397 short (>=1000) 90.0% (44151864 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 31986 seqs, 5013 long (>=2000), 3397 short (>=1000) 91.0% (44642511 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 34683 seqs, 5013 long (>=2000), 3397 short (>=1000) 92.0% (45132918 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 37767 seqs, 5013 long (>=2000), 3397 short (>=1000) 93.0% (45623598 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] ... processed 41500 seqs, 5013 long (>=2000), 3397 short (>=1000) 94.0% (46114094 of 49057406), ETA 0:00:00     [32.7Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 5013, and small contigs >= 1000 bp are 3397                                                                  
[00:00:00] Allocating 5013 contigs by 1 samples abundances [32.7Gb / 503.5Gb]
[00:00:00] Allocating 5013 contigs by 1 samples variances [32.7Gb / 503.5Gb]
[00:00:00] Allocating 3397 small contigs by 1 samples abundances [32.7Gb / 503.5Gb]
[00:00:00] Reading 0.002741Gb abundance file [32.7Gb / 503.5Gb]
[00:00:00] ... processed 448 lines 448 contigs and 0 short contigs 1.0% (29451 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 902 lines 902 contigs and 0 short contigs 2.0% (58858 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 1359 lines 1359 contigs and 0 short contigs 3.0% (88326 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 1813 lines 1813 contigs and 0 short contigs 4.0% (117727 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 2268 lines 2268 contigs and 0 short contigs 5.0% (147162 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 2724 lines 2724 contigs and 0 short contigs 6.0% (176623 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 3179 lines 3179 contigs and 0 short contigs 7.0% (206065 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 3634 lines 3634 contigs and 0 short contigs 8.0% (235475 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 4089 lines 4089 contigs and 0 short contigs 9.0% (264870 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 4544 lines 4544 contigs and 0 short contigs 10.0% (294290 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 5001 lines 5001 contigs and 0 short contigs 11.0% (323764 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 5457 lines 5013 contigs and 444 short contigs 12.0% (353212 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 5912 lines 5013 contigs and 899 short contigs 13.0% (382583 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 6368 lines 5013 contigs and 1355 short contigs 14.0% (412013 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 6825 lines 5013 contigs and 1812 short contigs 15.0% (441478 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 7281 lines 5013 contigs and 2268 short contigs 16.0% (470889 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 7737 lines 5013 contigs and 2724 short contigs 17.0% (500294 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8193 lines 5013 contigs and 3180 short contigs 18.0% (529727 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 19.0% (559180 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 20.0% (588606 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 21.0% (618024 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 22.0% (647461 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 23.0% (676913 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 24.0% (706318 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 25.0% (735730 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 26.0% (765165 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 27.0% (794593 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 28.0% (824024 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 29.0% (853454 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 30.0% (882885 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 31.0% (912361 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 32.0% (941783 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 33.0% (971167 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 34.0% (1000632 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 35.0% (1030042 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 36.0% (1059503 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 37.0% (1088911 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 38.0% (1118360 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 39.0% (1147758 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 40.0% (1177196 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 41.0% (1206650 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 42.0% (1236053 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 43.0% (1265447 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 44.0% (1294927 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 45.0% (1324307 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 46.0% (1353777 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 47.0% (1383179 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 48.0% (1412643 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 49.0% (1442033 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 50.0% (1471466 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 51.0% (1500895 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 52.0% (1530312 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 53.0% (1559792 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 54.0% (1589192 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 55.0% (1618643 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 56.0% (1648046 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 57.0% (1677511 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 58.0% (1706939 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 59.0% (1736332 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 60.0% (1765765 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 61.0% (1795172 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 62.0% (1824649 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 63.0% (1854037 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 64.0% (1883504 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 65.0% (1912937 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 66.0% (1942351 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 67.0% (1971784 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 68.0% (2001192 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 69.0% (2030650 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 70.0% (2060033 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 71.0% (2089464 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 72.0% (2118894 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 73.0% (2148374 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 74.0% (2177788 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 75.0% (2207181 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 76.0% (2236620 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 77.0% (2266066 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 78.0% (2295480 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 79.0% (2324915 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 80.0% (2354379 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 81.0% (2383809 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 82.0% (2413236 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 83.0% (2442657 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 84.0% (2472084 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 85.0% (2501482 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 86.0% (2530934 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 87.0% (2560331 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 88.0% (2589805 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 89.0% (2619192 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 90.0% (2648647 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 91.0% (2678080 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 92.0% (2707491 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 93.0% (2736949 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 94.0% (2766375 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 95.0% (2795801 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 96.0% (2825237 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 97.0% (2854651 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 98.0% (2884060 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 99.0% (2913482 of 2942852), ETA 0:00:00     [32.7Gb / 503.5Gb]                 
[00:00:00] Finished reading 47143 contigs and 1 coverages from 03bins/metabat2_2kb/1507995/temp/1507995.depth.txt [32.7Gb / 503.5Gb]. Ignored 38733 too small contigs.                                     
[00:00:00] Number of target contigs: 5013 of large (>= 2000) and 3397 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 5013
[00:00:00] Allocated memory for TNF [32.7Gb / 503.5Gb]
[00:00:00] Calculating TNF 48.4% (2426 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 48.9% (2449 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 49.9% (2503 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 50.9% (2550 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 51.9% (2601 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 53.0% (2655 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 54.0% (2707 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 54.9% (2754 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 56.0% (2807 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 57.0% (2858 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 58.0% (2908 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 59.0% (2958 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 60.1% (3015 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 61.1% (3062 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 62.1% (3113 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 63.1% (3163 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 64.1% (3213 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 65.2% (3267 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 66.2% (3317 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 67.2% (3367 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 68.2% (3419 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 69.2% (3470 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 70.2% (3519 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 71.3% (3573 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 72.2% (3621 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 73.3% (3673 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 74.3% (3726 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 75.4% (3778 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 76.4% (3829 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 77.3% (3876 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 78.3% (3927 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 79.4% (3978 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 80.4% (4031 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 81.4% (4082 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 82.4% (4131 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 83.4% (4183 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 84.5% (4236 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 85.5% (4285 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 86.6% (4339 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 87.5% (4386 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 88.5% (4439 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 89.5% (4489 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 90.6% (4541 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 91.6% (4590 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 92.6% (4642 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 93.7% (4696 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 94.6% (4744 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 95.6% (4794 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 96.7% (4848 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 97.7% (4897 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 98.7% (4948 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 99.7% (5000 of 5013), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [32.7Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 3.6% (179 of 5013), ETA 0:00:02                   
[00:00:00] ... processing TNF matrix 4.1% (204 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 7.3% (365 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 8.2% (409 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 10.2% (513 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 11.2% (563 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 14.1% (705 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 14.3% (716 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 18.0% (900 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 18.4% (923 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 21.9% (1098 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 22.4% (1123 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 25.6% (1281 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 26.5% (1327 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 29.7% (1490 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 30.6% (1532 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 33.7% (1688 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 34.6% (1737 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 38.8% (1944 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.7% (1990 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.7% (2140 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.8% (2146 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.2% (2315 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.9% (2351 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 49.8% (2498 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 50.0% (2504 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 53.4% (2675 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 54.0% (2705 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 57.4% (2875 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 58.1% (2911 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 61.7% (3094 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 62.1% (3112 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 64.0% (3210 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 64.2% (3216 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 67.5% (3384 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 68.2% (3421 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 70.6% (3541 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 71.3% (3573 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 75.4% (3780 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 79.7% (3996 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 80.4% (4029 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 83.4% (4180 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 83.5% (4187 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 87.9% (4407 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 88.6% (4442 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 90.6% (4540 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 91.6% (4590 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 92.6% (4644 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 93.6% (4692 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 94.6% (4743 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 95.7% (4796 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 96.6% (4845 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 97.7% (4897 of 5013), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 98.7% (4949 of 5013), ETA 0:00:00                   
[00:00:01] Finding cutoff p=999 [32.9Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=999 72.4% (3630 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 73.3% (3673 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 74.3% (3724 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 75.3% (3775 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 76.3% (3826 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 77.4% (3878 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 78.4% (3929 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 79.4% (3979 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 81.4% (4081 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 82.4% (4132 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 83.4% (4183 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 84.5% (4234 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 85.5% (4284 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 86.5% (4337 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 87.5% (4387 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 88.5% (4437 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 89.6% (4490 of 5013), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.9; 0 / 5013 (P = 0.00%) round 1]               
[00:00:01] Finding cutoff p=998 [32.9Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=998 92.5% (4639 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 92.7% (4648 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 93.6% (4694 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 94.6% (4743 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 95.6% (4794 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.8; 0 / 5013 (P = 0.00%) round 2]               
[00:00:01] Finding cutoff p=996 [32.9Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=996 78.5% (3937 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 80.4% (4031 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 81.4% (4081 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 82.4% (4131 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 83.4% (4182 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 84.4% (4233 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 85.5% (4284 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 86.5% (4335 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 87.5% (4386 of 5013), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.6; 15 / 5013 (P = 0.30%) round 3]               
[00:00:01] Finding cutoff p=994 [33.0Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=994 47.7% (2390 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 47.8% (2398 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 48.8% (2448 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 49.9% (2499 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 51.1% (2562 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 51.9% (2601 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 53.1% (2662 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 53.9% (2704 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 55.3% (2770 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 56.0% (2807 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 57.5% (2883 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 58.2% (2916 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 59.0% (2958 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 60.0% (3009 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 61.0% (3060 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 62.1% (3112 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 63.1% (3163 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 64.1% (3213 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 65.1% (3265 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 66.1% (3316 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 67.1% (3366 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 68.2% (3417 of 5013), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.4; 92 / 5013 (P = 1.84%) round 4]               
[00:00:01] Finding cutoff p=992 [33.0Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=992 1.2% (60 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 2.2% (112 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 3.2% (160 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 4.2% (212 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 5.1% (255 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 6.4% (321 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 7.4% (369 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 8.3% (417 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 9.3% (466 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 10.2% (513 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 11.5% (575 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 12.2% (613 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 13.5% (675 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 14.5% (728 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 15.5% (778 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 16.4% (820 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 17.4% (873 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 18.4% (921 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 19.4% (975 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 20.4% (1021 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 21.4% (1075 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 22.4% (1122 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 23.5% (1176 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 24.5% (1229 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 25.5% (1277 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 26.5% (1326 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 27.5% (1381 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 28.5% (1430 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 29.6% (1482 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 30.6% (1533 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 31.6% (1583 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 32.6% (1632 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 33.6% (1683 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 34.6% (1734 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 35.6% (1785 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 36.6% (1836 of 5013), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.2; 192 / 5013 (P = 3.83%) round 5]               
[00:00:01] Finding cutoff p=989 [33.0Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=989 49.7% (2492 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 49.9% (2500 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 50.9% (2551 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 51.9% (2603 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 52.9% (2652 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 54.0% (2705 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 55.0% (2755 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 56.0% (2806 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 57.0% (2856 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 58.0% (2908 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 59.0% (2958 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 60.0% (3009 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 61.1% (3063 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 62.1% (3111 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 63.1% (3162 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 64.1% (3213 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 65.1% (3265 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 66.1% (3316 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 67.1% (3366 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=989 68.2% (3417 of 5013), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 98.9; 362 / 5013 (P = 7.22%) round 6]               
[00:00:01] Finding cutoff p=985 [33.0Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=985 46.4% (2325 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 46.8% (2346 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 47.8% (2398 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 48.9% (2450 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 49.9% (2500 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 50.9% (2550 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 51.9% (2602 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 53.0% (2655 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 54.0% (2707 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 54.9% (2754 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 56.0% (2806 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 57.0% (2858 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 58.0% (2909 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 59.1% (2962 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 60.0% (3009 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 61.1% (3065 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 62.1% (3115 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 63.1% (3162 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 64.1% (3215 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 65.2% (3268 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 66.1% (3316 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 67.1% (3366 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 68.2% (3421 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 69.2% (3471 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 70.2% (3520 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 71.2% (3571 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 72.3% (3623 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 73.3% (3673 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 74.3% (3723 of 5013), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 98.5; 618 / 5013 (P = 12.33%) round 7]               
[00:00:01] Finding cutoff p=982 [33.0Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=982 1.2% (62 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 2.1% (105 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 3.1% (156 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 4.2% (210 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 5.2% (263 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 6.4% (321 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 7.2% (363 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 8.3% (415 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 9.3% (465 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 10.3% (516 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 11.3% (567 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 12.3% (616 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 13.3% (669 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 14.2% (714 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 15.4% (772 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 16.5% (829 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 17.3% (869 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 18.4% (920 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 19.3% (969 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 20.4% (1022 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 21.5% (1076 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 22.4% (1125 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 23.5% (1179 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 24.5% (1227 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 25.6% (1281 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 26.5% (1330 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 27.5% (1381 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 28.5% (1429 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 29.5% (1480 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 30.5% (1531 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 31.6% (1583 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 32.6% (1635 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 33.7% (1687 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 34.7% (1738 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 35.6% (1787 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 36.7% (1840 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 37.7% (1892 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 38.7% (1938 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 39.7% (1992 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 40.8% (2043 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 41.7% (2091 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 42.7% (2142 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=982 43.7% (2193 of 5013), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 98.2; 821 / 5013 (P = 16.38%) round 8]               
[00:00:01] Finding cutoff p=978 [33.0Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=978 62.5% (3134 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 63.1% (3162 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 64.1% (3213 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 65.2% (3268 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 66.2% (3317 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 67.2% (3371 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 68.2% (3418 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 69.2% (3471 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 70.2% (3521 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 71.2% (3570 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 72.3% (3626 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 73.2% (3672 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 74.3% (3727 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 75.4% (3778 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 76.3% (3825 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 77.3% (3876 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 78.3% (3927 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 79.4% (3979 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=978 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 97.8; 1070 / 5013 (P = 21.34%) round 9]               
[00:00:01] Finding cutoff p=975 [33.0Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=975 1.4% (71 of 5013), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=975 2.1% (107 of 5013), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=975 3.2% (161 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 4.4% (223 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 5.2% (261 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 6.1% (308 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 7.7% (385 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 8.4% (423 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 9.2% (460 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 10.2% (510 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 11.3% (566 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 12.2% (613 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 13.3% (669 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 14.3% (718 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 15.3% (766 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 16.3% (816 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 17.4% (873 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 18.4% (924 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 19.3% (969 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 20.4% (1023 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 21.4% (1071 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 22.4% (1125 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 23.4% (1175 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 24.5% (1227 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 25.4% (1275 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 26.5% (1327 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 27.5% (1378 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 28.5% (1428 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 29.5% (1479 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=975 30.5% (1530 of 5013), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 97.5; 1256 / 5013 (P = 25.05%) round 10]               
[00:00:01] Finding cutoff p=972 [33.0Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=972 56.3% (2820 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 57.0% (2856 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 58.1% (2912 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 59.0% (2958 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 60.2% (3016 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 61.2% (3070 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 62.2% (3116 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 63.1% (3164 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 64.3% (3221 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 65.1% (3264 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 66.1% (3315 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 67.2% (3367 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 68.2% (3417 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 69.2% (3469 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 70.2% (3519 of 5013), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 71.3% (3572 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 97.2; 1441 / 5013 (P = 28.75%) round 11]               
[00:00:02] Finding cutoff p=968 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=968 51.1% (2560 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 51.9% (2602 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 52.9% (2653 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 54.0% (2705 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 55.0% (2756 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 56.0% (2806 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 57.0% (2856 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 58.1% (2913 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 59.0% (2960 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 60.1% (3011 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 61.1% (3064 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 62.1% (3113 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 63.2% (3168 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 64.2% (3216 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 65.2% (3266 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 66.2% (3317 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 67.2% (3367 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 68.2% (3417 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 69.2% (3469 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 70.2% (3521 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 71.3% (3574 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 72.3% (3625 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 73.3% (3676 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 74.3% (3724 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 75.3% (3775 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 76.3% (3826 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 77.3% (3876 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=968 78.4% (3928 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 96.8; 1677 / 5013 (P = 33.45%) round 12]               
[00:00:02] Finding cutoff p=965 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=965 46.1% (2310 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 46.9% (2350 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 47.9% (2400 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 48.9% (2453 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 49.9% (2502 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 50.9% (2554 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 52.0% (2605 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 53.0% (2658 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 54.0% (2706 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 55.0% (2758 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 56.0% (2807 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 57.1% (2861 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 58.0% (2910 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 59.0% (2958 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 60.0% (3010 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 61.0% (3060 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 62.1% (3113 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 63.1% (3164 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 64.1% (3215 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 65.1% (3265 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 66.1% (3315 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 67.2% (3369 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 68.2% (3418 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 69.2% (3468 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 70.2% (3521 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 71.2% (3571 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 72.2% (3621 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 73.2% (3672 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=965 74.3% (3724 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 96.5; 1840 / 5013 (P = 36.70%) round 13]               
[00:00:02] Finding cutoff p=962 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=962 85.5% (4287 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 86.5% (4337 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 87.6% (4391 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 88.5% (4438 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 89.6% (4494 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 90.6% (4544 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 91.6% (4591 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 92.6% (4641 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 94.6% (4743 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=962 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 96.2; 1987 / 5013 (P = 39.64%) round 14]               
[00:00:02] Finding cutoff p=958 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=958 1.1% (57 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 2.2% (108 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 3.2% (159 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 4.1% (207 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 5.2% (263 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 6.3% (314 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 7.3% (368 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 8.2% (411 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 9.3% (464 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 10.3% (517 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 11.3% (565 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 12.4% (620 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 13.4% (670 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 14.3% (719 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 15.3% (768 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 16.3% (819 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 17.4% (872 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 18.4% (922 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 19.4% (971 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 20.5% (1026 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 21.5% (1076 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 22.5% (1128 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 23.5% (1177 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 24.4% (1224 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 25.4% (1275 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 26.5% (1327 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 27.6% (1383 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 28.5% (1429 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 29.6% (1486 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 30.6% (1535 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 31.6% (1586 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 32.6% (1634 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 33.6% (1686 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 34.6% (1735 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 35.6% (1786 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=958 36.6% (1837 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 95.8; 2193 / 5013 (P = 43.75%) round 15]               
[00:00:02] Finding cutoff p=955 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=955 76.9% (3856 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=955 77.4% (3882 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=955 78.4% (3929 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=955 79.5% (3984 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=955 80.4% (4030 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=955 81.4% (4083 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=955 82.4% (4131 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=955 83.4% (4182 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=955 84.4% (4233 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=955 85.5% (4284 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=955 86.5% (4335 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=955 87.5% (4386 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 95.5; 2326 / 5013 (P = 46.40%) round 16]               
[00:00:02] Finding cutoff p=950 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=950 45.1% (2260 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 45.8% (2295 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 46.9% (2349 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 47.9% (2400 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 48.9% (2450 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 49.9% (2499 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 50.9% (2553 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 51.9% (2602 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 52.9% (2652 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 53.9% (2704 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 54.9% (2754 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 56.0% (2808 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 57.1% (2861 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 58.0% (2909 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 59.0% (2960 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 60.0% (3010 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 61.1% (3064 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 62.1% (3114 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 63.1% (3163 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 64.1% (3214 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 65.1% (3265 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 66.1% (3315 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 67.2% (3370 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 68.2% (3417 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 69.3% (3472 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 70.2% (3520 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 71.3% (3573 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=950 72.2% (3621 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 95.0; 2556 / 5013 (P = 50.99%) round 17]               
[00:00:02] Finding cutoff p=947 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=947 46.6% (2338 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 46.8% (2347 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 47.8% (2397 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 48.9% (2450 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 49.9% (2500 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 50.9% (2553 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 51.9% (2603 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 52.9% (2653 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 54.0% (2706 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 55.0% (2756 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 56.0% (2806 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 57.0% (2856 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 58.0% (2909 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 59.1% (2961 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 60.1% (3012 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 61.0% (3060 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 62.1% (3113 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 63.2% (3170 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 64.1% (3215 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 65.3% (3273 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 66.3% (3325 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 67.3% (3372 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 68.2% (3421 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 69.4% (3477 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 70.4% (3529 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 71.4% (3581 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 72.2% (3621 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 73.3% (3673 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 74.3% (3726 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 75.3% (3777 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 76.3% (3827 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 77.4% (3879 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 78.5% (3933 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=947 79.4% (3982 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 94.7; 2689 / 5013 (P = 53.64%) round 18]               
[00:00:02] Finding cutoff p=944 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=944 51.0% (2557 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 52.1% (2612 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 52.9% (2654 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 54.2% (2715 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 55.2% (2768 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 56.0% (2806 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 57.6% (2886 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 58.5% (2934 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 59.0% (2959 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 60.0% (3010 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 61.1% (3063 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 62.1% (3113 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 63.1% (3164 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 64.1% (3215 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 65.2% (3267 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 66.1% (3316 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 67.1% (3366 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 68.2% (3418 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 69.2% (3471 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 70.2% (3519 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 71.3% (3573 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 72.3% (3622 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 73.3% (3673 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 74.3% (3725 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 75.3% (3775 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 76.3% (3825 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 77.4% (3878 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 78.4% (3929 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=944 80.4% (4030 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 94.4; 2805 / 5013 (P = 55.95%) round 19]               
[00:00:02] Finding cutoff p=939 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=939 62.8% (3148 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 63.2% (3166 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 64.2% (3216 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 65.2% (3268 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 66.2% (3320 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 67.3% (3372 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 68.2% (3420 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 69.3% (3472 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 70.2% (3519 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 71.2% (3571 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 72.3% (3625 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 73.2% (3672 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 74.3% (3724 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 75.3% (3775 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 76.3% (3827 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 77.4% (3878 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 78.4% (3930 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 79.4% (3980 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 81.4% (4081 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 82.4% (4133 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 83.4% (4182 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 84.5% (4234 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 85.5% (4285 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 86.5% (4335 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=939 87.5% (4386 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 93.9; 2928 / 5013 (P = 58.41%) round 20]               
[00:00:02] Finding cutoff p=935 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=935 63.7% (3195 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 64.1% (3214 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 65.1% (3264 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 66.1% (3315 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 67.2% (3367 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 68.2% (3417 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 69.3% (3472 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 70.2% (3520 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 71.2% (3570 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 72.3% (3623 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 73.3% (3674 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 74.3% (3724 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 75.3% (3776 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 76.3% (3825 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 77.4% (3879 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 78.4% (3931 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 79.4% (3979 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 80.5% (4033 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 81.4% (4082 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 82.4% (4133 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 83.4% (4182 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 84.5% (4237 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 85.5% (4284 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 86.5% (4336 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=935 87.5% (4386 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 93.5; 3070 / 5013 (P = 61.24%) round 21]               
[00:00:02] Finding cutoff p=930 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=930 62.5% (3134 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 63.1% (3162 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 64.1% (3215 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 65.2% (3269 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 66.1% (3316 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 67.2% (3368 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 68.3% (3422 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 69.4% (3481 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 70.2% (3521 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 71.2% (3571 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 72.2% (3621 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 73.3% (3675 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 74.4% (3728 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 75.3% (3777 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 76.4% (3829 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 77.3% (3876 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 78.3% (3927 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 79.4% (3979 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 80.4% (4030 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 81.4% (4081 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 82.4% (4131 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 83.4% (4183 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 84.5% (4234 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 85.5% (4285 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 86.5% (4335 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 87.5% (4386 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 93.0; 3208 / 5013 (P = 63.99%) round 22]               
[00:00:02] Finding cutoff p=927 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=927 1.3% (64 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 2.3% (115 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 3.1% (157 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 4.1% (204 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 5.5% (278 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 6.2% (311 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 7.4% (373 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 8.2% (410 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 9.2% (461 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 10.5% (527 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 11.3% (567 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 12.3% (617 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 13.3% (667 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 14.3% (717 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 15.3% (765 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 16.3% (818 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 17.3% (867 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 18.4% (922 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 19.4% (971 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 20.4% (1022 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 21.5% (1076 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 22.4% (1122 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 23.6% (1181 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 24.5% (1226 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 25.5% (1276 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 26.6% (1335 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 27.5% (1379 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 28.6% (1432 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 29.6% (1483 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 30.5% (1530 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 31.6% (1583 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 32.6% (1636 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 33.7% (1687 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 34.6% (1735 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 35.6% (1785 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 36.7% (1841 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 37.7% (1891 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 38.7% (1940 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 39.7% (1990 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 40.7% (2042 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 41.8% (2093 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 42.7% (2142 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=927 43.7% (2193 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 92.7; 3306 / 5013 (P = 65.95%) round 23]               
[00:00:02] Finding cutoff p=924 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=924 56.8% (2846 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 57.0% (2857 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 58.0% (2907 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 59.0% (2959 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 60.1% (3012 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 61.0% (3060 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 62.2% (3118 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 63.2% (3170 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 64.2% (3219 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 65.2% (3267 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 66.1% (3316 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 67.2% (3368 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 68.2% (3420 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 69.3% (3476 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 70.4% (3528 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 71.2% (3570 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 72.3% (3623 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 73.3% (3673 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 74.3% (3723 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 75.3% (3777 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 76.3% (3826 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 77.3% (3876 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 78.3% (3927 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=924 80.4% (4030 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 92.4; 3392 / 5013 (P = 67.66%) round 24]               
[00:00:02] Finding cutoff p=921 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=921 87.5% (4387 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=921 88.5% (4437 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=921 89.5% (4488 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=921 90.5% (4539 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=921 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=921 92.6% (4641 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=921 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=921 94.6% (4743 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 92.1; 3458 / 5013 (P = 68.98%) round 25]               
[00:00:02] Finding cutoff p=917 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=917 75.0% (3761 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 75.3% (3774 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 76.3% (3825 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 77.4% (3878 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 78.4% (3930 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 81.4% (4081 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 82.9% (4157 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 83.6% (4191 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 85.0% (4260 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 86.1% (4315 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 86.9% (4354 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 87.9% (4405 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 89.2% (4471 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 89.5% (4488 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 90.6% (4541 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 92.6% (4641 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=917 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 91.7; 3557 / 5013 (P = 70.96%) round 26]               
[00:00:02] Finding cutoff p=912 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=912 66.9% (3356 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 67.2% (3371 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 68.2% (3418 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 69.2% (3468 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 70.3% (3522 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 71.2% (3570 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 72.3% (3622 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 73.2% (3672 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 74.3% (3724 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 75.3% (3775 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 76.3% (3825 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 77.3% (3876 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 78.4% (3928 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=912 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 91.2; 3664 / 5013 (P = 73.09%) round 27]               
[00:00:02] Finding cutoff p=907 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=907 43.7% (2193 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 44.8% (2244 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 45.8% (2296 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 46.8% (2346 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 47.8% (2397 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 48.9% (2450 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 49.9% (2499 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 50.9% (2551 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 51.9% (2601 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 52.9% (2652 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 53.9% (2703 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 55.0% (2755 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 56.0% (2806 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 57.0% (2859 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 58.0% (2909 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 59.1% (2961 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 60.0% (3009 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 61.2% (3069 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 62.1% (3113 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 63.1% (3163 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 90.7; 3754 / 5013 (P = 74.89%) round 28]               
[00:00:02] Finding cutoff p=903 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=903 64.7% (3244 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 65.2% (3266 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 66.1% (3316 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 67.2% (3368 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 68.2% (3417 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 69.2% (3468 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 70.2% (3519 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 71.3% (3572 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 72.3% (3622 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 73.3% (3675 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 74.3% (3724 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 75.3% (3777 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 76.3% (3826 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 77.3% (3876 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 78.4% (3928 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=903 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 90.3; 3833 / 5013 (P = 76.46%) round 29]               
[00:00:02] Finding cutoff p=899 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=899 50.0% (2509 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 51.6% (2586 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 52.5% (2631 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 53.8% (2697 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 55.8% (2797 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 56.5% (2834 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 57.0% (2856 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 58.0% (2907 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 59.1% (2964 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 60.1% (3011 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 61.1% (3065 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 62.3% (3122 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 63.3% (3173 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 64.2% (3218 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 65.2% (3268 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 66.1% (3315 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 67.3% (3372 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 68.3% (3422 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 69.3% (3472 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 70.3% (3522 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 71.2% (3570 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 72.3% (3622 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 73.3% (3674 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 74.3% (3725 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 75.3% (3774 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 76.3% (3826 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 77.3% (3877 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 78.4% (3928 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 79.4% (3981 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 89.9; 3892 / 5013 (P = 77.64%) round 30]               
[00:00:02] Finding cutoff p=889 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=889 83.9% (4207 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 84.5% (4235 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 85.5% (4284 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 86.5% (4338 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 87.6% (4390 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 88.5% (4439 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 89.6% (4490 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 90.5% (4539 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 91.6% (4591 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 92.6% (4644 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 93.7% (4695 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 94.6% (4744 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 96.7% (4846 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 88.9; 3944 / 5013 (P = 78.68%) round 31]               
[00:00:02] Finding cutoff p=880 [32.9Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=880 1.2% (58 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 2.2% (110 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 3.2% (162 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 4.3% (216 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 5.1% (258 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 6.5% (324 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 7.4% (372 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 8.2% (409 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 9.3% (464 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 10.8% (541 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 12.0% (600 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 12.7% (637 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 13.3% (666 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 14.2% (714 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 15.5% (778 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 16.6% (830 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 17.4% (870 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 18.3% (919 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 19.3% (970 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 20.3% (1020 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 21.4% (1074 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 22.4% (1123 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 23.4% (1174 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 24.5% (1228 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 25.5% (1276 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 26.5% (1330 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 27.5% (1377 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 28.5% (1431 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 29.5% (1480 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 30.6% (1532 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 31.6% (1583 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 32.6% (1634 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 33.6% (1683 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 34.6% (1734 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 35.6% (1787 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 36.7% (1838 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 37.6% (1887 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 38.7% (1941 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 39.7% (1990 of 5013), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=880 40.7% (2042 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 88.0; 4029 / 5013 (P = 80.37%) round 32]               
[00:00:03] Finding cutoff p=870 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=870 50.0% (2508 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 50.9% (2551 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 51.9% (2602 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 52.9% (2652 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 53.9% (2704 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 55.1% (2761 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 56.0% (2807 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 57.0% (2859 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 58.2% (2916 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 59.2% (2969 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 60.1% (3012 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 61.2% (3070 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 62.1% (3115 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 63.1% (3164 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 64.2% (3217 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 65.1% (3264 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 66.1% (3315 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 67.2% (3368 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 68.2% (3418 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 69.3% (3474 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 70.2% (3519 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 71.3% (3572 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 72.2% (3621 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 73.3% (3673 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 74.3% (3724 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 75.3% (3776 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 76.3% (3825 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=870 77.3% (3877 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 87.0; 4118 / 5013 (P = 82.15%) round 33]               
[00:00:03] Finding cutoff p=861 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=861 1.0% (52 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 2.3% (116 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 3.3% (165 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 4.2% (209 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 5.4% (269 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 6.4% (322 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 7.6% (381 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 8.4% (420 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 9.3% (467 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 10.4% (519 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 11.3% (566 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 12.2% (612 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 13.8% (693 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 14.5% (725 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 15.8% (793 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 16.5% (828 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 17.4% (873 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 18.4% (923 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 19.6% (982 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 20.5% (1027 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 21.5% (1080 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 22.5% (1130 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 23.5% (1179 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 24.5% (1230 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 25.6% (1281 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 26.5% (1327 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 27.5% (1379 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 28.6% (1434 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 29.5% (1480 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 30.5% (1530 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 31.6% (1583 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 32.6% (1636 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 33.7% (1687 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 34.8% (1743 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 35.6% (1787 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 36.8% (1846 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 37.7% (1889 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 38.7% (1940 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 39.8% (1995 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 40.7% (2040 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 41.7% (2091 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=861 42.7% (2142 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 86.1; 4227 / 5013 (P = 84.32%) round 34]               
[00:00:03] Finding cutoff p=852 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=852 81.3% (4074 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 81.4% (4080 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 82.4% (4131 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 83.4% (4182 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 84.5% (4235 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 85.5% (4285 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 86.5% (4337 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 87.5% (4387 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 88.7% (4445 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 89.6% (4490 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 90.6% (4541 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 91.6% (4591 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 92.7% (4646 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 93.6% (4694 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 94.6% (4743 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=852 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 85.2; 4312 / 5013 (P = 86.02%) round 35]               
[00:00:03] Finding cutoff p=843 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=843 12.4% (622 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 13.5% (678 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 14.4% (720 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 15.6% (784 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 16.5% (825 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 17.6% (880 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 18.5% (928 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 19.3% (969 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 20.3% (1020 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 21.4% (1074 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 22.4% (1122 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 23.5% (1176 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 24.5% (1229 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 25.6% (1283 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 26.5% (1329 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 27.6% (1384 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 28.6% (1432 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 29.8% (1493 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 30.7% (1537 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 31.6% (1583 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 32.6% (1636 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 33.7% (1690 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 34.6% (1737 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 35.6% (1785 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 36.7% (1840 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 37.7% (1889 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 38.7% (1940 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 39.7% (1990 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 40.7% (2041 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 41.7% (2092 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 42.7% (2142 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=843 43.7% (2193 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 84.3; 4387 / 5013 (P = 87.51%) round 36]               
[00:00:03] Finding cutoff p=833 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=833 43.8% (2195 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=833 44.8% (2244 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=833 45.8% (2295 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=833 46.8% (2346 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=833 47.8% (2397 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=833 48.8% (2448 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=833 49.9% (2499 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 83.3; 4462 / 5013 (P = 89.01%) round 37]               
[00:00:03] Finding cutoff p=823 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=823 37.5% (1880 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 37.6% (1887 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 38.7% (1938 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 39.7% (1989 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 40.7% (2040 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 41.8% (2093 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 42.7% (2143 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 44.1% (2212 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 44.9% (2249 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 46.3% (2320 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 47.4% (2378 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 47.8% (2397 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 48.8% (2448 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=823 49.9% (2499 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 82.3; 4542 / 5013 (P = 90.60%) round 38]               
[00:00:03] Finding cutoff p=813 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=813 74.8% (3749 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=813 75.3% (3774 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=813 76.3% (3825 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=813 77.3% (3876 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=813 78.3% (3927 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=813 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=813 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 81.3; 4599 / 5013 (P = 91.74%) round 39]               
[00:00:03] Finding cutoff p=802 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=802 50.0% (2509 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 51.0% (2559 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 52.0% (2607 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 52.9% (2653 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 54.0% (2708 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 55.0% (2757 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 56.0% (2807 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 57.0% (2856 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 58.0% (2909 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 59.1% (2961 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 60.0% (3010 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 61.1% (3063 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 62.1% (3112 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 63.1% (3162 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 64.1% (3215 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 65.1% (3264 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 66.2% (3321 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 67.2% (3370 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 68.2% (3418 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 69.3% (3475 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 70.3% (3523 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 71.3% (3573 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 72.3% (3624 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 73.2% (3672 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=802 74.3% (3723 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 80.2; 4644 / 5013 (P = 92.64%) round 40]               
[00:00:03] Finding cutoff p=791 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=791 52.8% (2648 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 53.0% (2655 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 53.9% (2704 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 55.0% (2758 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 56.0% (2806 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 57.1% (2860 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 58.1% (2912 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 59.1% (2962 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 60.1% (3011 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 61.2% (3068 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 62.2% (3116 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 63.1% (3163 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 64.1% (3215 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 65.1% (3264 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 66.1% (3316 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 67.2% (3368 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 68.2% (3419 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 69.4% (3478 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 70.3% (3522 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 71.4% (3577 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 72.3% (3623 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 73.4% (3681 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 74.3% (3725 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 75.3% (3774 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 76.3% (3826 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 77.4% (3878 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 78.4% (3930 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=791 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 79.1; 4687 / 5013 (P = 93.50%) round 41]               
[00:00:03] Finding cutoff p=780 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=780 54.3% (2723 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 55.1% (2760 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 56.0% (2809 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 57.2% (2865 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 58.0% (2908 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 59.2% (2966 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 60.2% (3016 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 61.0% (3060 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 62.1% (3114 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 63.1% (3164 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 64.1% (3215 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 65.1% (3265 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 66.1% (3316 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 67.3% (3372 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 68.3% (3424 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 69.2% (3469 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 70.2% (3521 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 71.2% (3570 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 72.3% (3622 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 73.3% (3673 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 74.3% (3724 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 75.3% (3774 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 76.3% (3826 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 77.3% (3876 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 78.3% (3927 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=780 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 78.0; 4728 / 5013 (P = 94.31%) round 42]               
[00:00:03] Finding cutoff p=770 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=770 52.9% (2650 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 53.0% (2656 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 54.0% (2706 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 55.0% (2758 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 56.0% (2806 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 57.1% (2863 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 58.2% (2918 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 59.1% (2962 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 60.2% (3020 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 61.1% (3061 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 62.2% (3116 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 63.4% (3176 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 64.2% (3218 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 65.2% (3268 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 66.2% (3319 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 67.1% (3366 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 68.2% (3419 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 69.2% (3468 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 70.2% (3521 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 71.3% (3575 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 72.3% (3622 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 73.3% (3674 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 74.4% (3728 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 75.3% (3774 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 76.3% (3825 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 77.4% (3879 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 78.4% (3928 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=770 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 77.0; 4753 / 5013 (P = 94.81%) round 43]               
[00:00:03] Finding cutoff p=761 [32.9Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=761 55.8% (2799 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 56.0% (2809 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 57.1% (2860 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 58.0% (2910 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 59.0% (2958 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 60.1% (3011 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 61.1% (3063 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 62.1% (3114 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 63.1% (3164 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 64.1% (3215 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 65.3% (3275 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 66.2% (3318 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 67.2% (3368 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 68.2% (3418 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 69.3% (3473 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 70.2% (3521 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 71.3% (3572 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 72.2% (3621 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 73.3% (3673 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 74.4% (3731 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 75.3% (3776 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 76.4% (3829 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 77.4% (3878 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 78.4% (3928 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 79.4% (3980 of 5013), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=761 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:03] Finished Preparing TNF Graph Building [pTNF = 77.00] [32.7Gb / 503.5Gb]                                            
[00:00:03] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=5013 maxEdges=200
[00:00:03] Building TNF Graph 6.2% (309 of 5013), ETA 0:00:02     [32.7Gb / 503.5Gb]                           
[00:00:03] Building TNF Graph 34.9% (1751 of 5013), ETA 0:00:01     [32.7Gb / 503.5Gb]                           
[00:00:04] Building TNF Graph 65.7% (3296 of 5013), ETA 0:00:00     [32.7Gb / 503.5Gb]                           
[00:00:04] Building TNF Graph 100.7% (5047 of 5013), ETA 0:00:00     [32.7Gb / 503.5Gb]                           
[00:00:04] Finished Building TNF Graph (206244 edges) [32.7Gb / 503.5Gb]                                          
[00:00:04] Cleaned up after Building TNF Graph (206244 edges) [32.7Gb / 503.5Gb]                                          
[00:00:04] Cleaned up TNF matrix of large contigs [32.7Gb / 503.5Gb]                                             
[00:00:04] Applying coverage correlations to TNF graph with 206244 edges
[00:00:04] Allocated memory for graph edges [32.7Gb / 503.5Gb]
[00:00:04] ... calculating abundance dist 52.5% (108261 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 53.0% (109340 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 54.0% (111402 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 55.0% (113465 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 56.0% (115529 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 57.0% (117593 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 58.0% (119656 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 59.0% (121718 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 60.0% (123782 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 61.0% (125844 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 62.0% (127907 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 63.0% (129969 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 64.0% (132032 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 65.0% (134095 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 66.0% (136158 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 67.0% (138222 of 206244), ETA 0:00:00                              
[00:00:04] ... calculating abundance dist 68.0% (140285 of 206244), ETA 0:00:00                              
[00:00:04] Calculating geometric means [32.8Gb / 503.5Gb]
[00:00:04] Traversing graph with 5013 nodes and 206244 edges [32.8Gb / 503.5Gb]
[00:00:04] Building SCR Graph and Binning (477 vertices and 985 edges) [P = 9.50%; 32.8Gb / 503.5Gb]                           
[00:00:04] ... traversing graph 1.0% (2063 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 2.0% (4126 of 206244), ETA 0:00:00                               
[00:00:04] Building SCR Graph and Binning (953 vertices and 3537 edges) [P = 19.00%; 32.8Gb / 503.5Gb]                           
[00:00:04] ... traversing graph 3.0% (6189 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 4.0% (8252 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 5.0% (10315 of 206244), ETA 0:00:00                               
[00:00:04] Building SCR Graph and Binning (1429 vertices and 5311 edges) [P = 28.50%; 32.8Gb / 503.5Gb]                           
[00:00:04] ... traversing graph 6.0% (12378 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 7.0% (14441 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 8.0% (16504 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 9.0% (18567 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 10.0% (20630 of 206244), ETA 0:00:00                               
[00:00:04] Building SCR Graph and Binning (1905 vertices and 6246 edges) [P = 38.00%; 32.8Gb / 503.5Gb]                           
[00:00:04] ... traversing graph 11.0% (22693 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 12.0% (24756 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 13.0% (26819 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 14.0% (28882 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 15.0% (30945 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 16.0% (33008 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 17.0% (35071 of 206244), ETA 0:00:00                               
[00:00:04] Building SCR Graph and Binning (2382 vertices and 7417 edges) [P = 47.50%; 32.8Gb / 503.5Gb]                           
[00:00:04] ... traversing graph 18.0% (37134 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 19.0% (39197 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 20.0% (41260 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 21.0% (43323 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 22.0% (45386 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 23.0% (47449 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 24.0% (49512 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 25.0% (51575 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 26.0% (53638 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 27.0% (55701 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 28.0% (57764 of 206244), ETA 0:00:00                               
[00:00:04] Building SCR Graph and Binning (2858 vertices and 8660 edges) [P = 57.00%; 32.8Gb / 503.5Gb]                           
[00:00:04] ... traversing graph 29.0% (59827 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 30.0% (61890 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 31.0% (63953 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 32.0% (66016 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 33.0% (68079 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 34.0% (70142 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 35.0% (72205 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 36.0% (74268 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 37.0% (76331 of 206244), ETA 0:00:00                               
[00:00:04] Building SCR Graph and Binning (3335 vertices and 9639 edges) [P = 66.50%; 32.8Gb / 503.5Gb]                           
[00:00:04] ... traversing graph 38.0% (78394 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 39.0% (80457 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 40.0% (82520 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 41.0% (84583 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 42.0% (86646 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 43.0% (88709 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 44.0% (90772 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 45.0% (92835 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 46.0% (94898 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 47.0% (96961 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 48.0% (99024 of 206244), ETA 0:00:00                               
[00:00:04] Building SCR Graph and Binning (3810 vertices and 10973 edges) [P = 76.00%; 32.8Gb / 503.5Gb]                           
[00:00:04] ... traversing graph 49.0% (101087 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 50.0% (103150 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 51.0% (105213 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 52.0% (107276 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 53.0% (109339 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 54.0% (111402 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 55.0% (113465 of 206244), ETA 0:00:00                               
[00:00:04] ... traversing graph 56.0% (115528 of 206244), ETA 0:00:00                               
[00:00:04] Building SCR Graph and Binning (4036 vertices and 12063 edges) [P = 85.50%; 32.8Gb / 503.5Gb]                           
[00:00:04] Finished Traversing graph [32.7Gb / 503.5Gb]                                       
[00:00:04] Dissolved 0 small clusters leaving 0 leftover contigs to be re-merged into larger clusters
[00:00:04] Rescuing singleton large contigs                                   
[00:00:04] There are 1129 bins already
[00:00:04] Outputting bins
[00:00:04] Writing cluster stats to: 03bins/metabat2_2kb/1507995/1507995.bin.BinInfo.txt
[00:00:14] 100.00% (30072318 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
1129 bins (30072318 bases in total) formed.
[00:00:14] Finished
MetaBAT2 generated 1129 bins for 1507995
MetaBAT2 binning completed for 1507995
