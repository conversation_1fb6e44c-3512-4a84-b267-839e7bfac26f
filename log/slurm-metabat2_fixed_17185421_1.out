Decompressing scaffold file for 1507990...
Generating depth file for 1507990...
Running MetaBAT2 for 1507990 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 200000. with random seed=123
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [20.0Gb / 503.5Gb]
[00:00:00] Parsing assembly file [20.0Gb / 503.5Gb]
[00:00:00] ... processed 2 seqs, 2 long (>=2000), 0 short (>=1000) 1.2% (115080 of 9913441), ETA 0:00:02     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 5 seqs, 5 long (>=2000), 0 short (>=1000) 2.1% (209049 of 9913441), ETA 0:00:01     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 9 seqs, 9 long (>=2000), 0 short (>=1000) 3.2% (319185 of 9913441), ETA 0:00:01     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 12 seqs, 12 long (>=2000), 0 short (>=1000) 4.0% (397293 of 9913441), ETA 0:00:01     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 17 seqs, 17 long (>=2000), 0 short (>=1000) 5.2% (517609 of 9913441), ETA 0:00:01     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 21 seqs, 21 long (>=2000), 0 short (>=1000) 6.1% (604212 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 26 seqs, 26 long (>=2000), 0 short (>=1000) 7.1% (705461 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 31 seqs, 31 long (>=2000), 0 short (>=1000) 8.1% (799094 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 37 seqs, 37 long (>=2000), 0 short (>=1000) 9.2% (908131 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 42 seqs, 42 long (>=2000), 0 short (>=1000) 10.0% (995558 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 48 seqs, 48 long (>=2000), 0 short (>=1000) 11.1% (1096223 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 54 seqs, 54 long (>=2000), 0 short (>=1000) 12.0% (1191950 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 61 seqs, 61 long (>=2000), 0 short (>=1000) 13.1% (1297675 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 68 seqs, 68 long (>=2000), 0 short (>=1000) 14.1% (1397876 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 75 seqs, 75 long (>=2000), 0 short (>=1000) 15.1% (1492923 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 83 seqs, 83 long (>=2000), 0 short (>=1000) 16.1% (1596130 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 91 seqs, 91 long (>=2000), 0 short (>=1000) 17.1% (1695792 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 99 seqs, 99 long (>=2000), 0 short (>=1000) 18.1% (1792586 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 107 seqs, 107 long (>=2000), 0 short (>=1000) 19.0% (1887263 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 116 seqs, 116 long (>=2000), 0 short (>=1000) 20.1% (1990590 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 125 seqs, 125 long (>=2000), 0 short (>=1000) 21.1% (2089952 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 134 seqs, 134 long (>=2000), 0 short (>=1000) 22.0% (2184062 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 144 seqs, 144 long (>=2000), 0 short (>=1000) 23.0% (2281894 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 155 seqs, 155 long (>=2000), 0 short (>=1000) 24.0% (2383085 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 166 seqs, 166 long (>=2000), 0 short (>=1000) 25.0% (2479264 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 178 seqs, 178 long (>=2000), 0 short (>=1000) 26.0% (2579796 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 190 seqs, 190 long (>=2000), 0 short (>=1000) 27.0% (2677160 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 203 seqs, 203 long (>=2000), 0 short (>=1000) 28.0% (2778271 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 217 seqs, 217 long (>=2000), 0 short (>=1000) 29.1% (2881516 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 231 seqs, 231 long (>=2000), 0 short (>=1000) 30.1% (2980762 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 245 seqs, 245 long (>=2000), 0 short (>=1000) 31.0% (3076136 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 260 seqs, 260 long (>=2000), 0 short (>=1000) 32.0% (3175287 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 276 seqs, 276 long (>=2000), 0 short (>=1000) 33.1% (3276620 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 292 seqs, 292 long (>=2000), 0 short (>=1000) 34.0% (3374644 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 309 seqs, 309 long (>=2000), 0 short (>=1000) 35.1% (3474695 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 326 seqs, 326 long (>=2000), 0 short (>=1000) 36.0% (3571456 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 344 seqs, 344 long (>=2000), 0 short (>=1000) 37.0% (3670186 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 363 seqs, 363 long (>=2000), 0 short (>=1000) 38.0% (3770214 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 382 seqs, 382 long (>=2000), 0 short (>=1000) 39.0% (3866629 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 403 seqs, 403 long (>=2000), 0 short (>=1000) 40.0% (3968971 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 424 seqs, 424 long (>=2000), 0 short (>=1000) 41.0% (4064876 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 447 seqs, 447 long (>=2000), 0 short (>=1000) 42.0% (4164217 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 471 seqs, 471 long (>=2000), 0 short (>=1000) 43.0% (4262917 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 497 seqs, 497 long (>=2000), 0 short (>=1000) 44.0% (4364215 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 524 seqs, 524 long (>=2000), 0 short (>=1000) 45.0% (4464611 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 552 seqs, 552 long (>=2000), 0 short (>=1000) 46.0% (4563445 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 581 seqs, 581 long (>=2000), 0 short (>=1000) 47.0% (4660307 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 612 seqs, 612 long (>=2000), 0 short (>=1000) 48.0% (4759437 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 644 seqs, 644 long (>=2000), 0 short (>=1000) 49.0% (4858041 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 678 seqs, 678 long (>=2000), 0 short (>=1000) 50.0% (4957105 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 715 seqs, 715 long (>=2000), 0 short (>=1000) 51.0% (5058197 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 753 seqs, 753 long (>=2000), 0 short (>=1000) 52.0% (5155105 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 795 seqs, 795 long (>=2000), 0 short (>=1000) 53.0% (5255393 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 839 seqs, 839 long (>=2000), 0 short (>=1000) 54.0% (5355356 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 884 seqs, 884 long (>=2000), 0 short (>=1000) 55.0% (5453006 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 933 seqs, 923 long (>=2000), 10 short (>=1000) 56.0% (5552761 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 985 seqs, 923 long (>=2000), 62 short (>=1000) 57.0% (5651702 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1041 seqs, 923 long (>=2000), 118 short (>=1000) 58.0% (5751212 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1100 seqs, 923 long (>=2000), 177 short (>=1000) 59.0% (5850368 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1163 seqs, 923 long (>=2000), 240 short (>=1000) 60.0% (5949498 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1230 seqs, 923 long (>=2000), 307 short (>=1000) 61.0% (6048102 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1302 seqs, 923 long (>=2000), 379 short (>=1000) 62.0% (6146822 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1379 seqs, 923 long (>=2000), 456 short (>=1000) 63.0% (6246522 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1460 seqs, 923 long (>=2000), 537 short (>=1000) 64.0% (6345218 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1546 seqs, 923 long (>=2000), 623 short (>=1000) 65.0% (6443858 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1638 seqs, 923 long (>=2000), 715 short (>=1000) 66.0% (6543739 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1735 seqs, 923 long (>=2000), 799 short (>=1000) 67.0% (6642563 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1839 seqs, 923 long (>=2000), 799 short (>=1000) 68.0% (6741359 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 1950 seqs, 923 long (>=2000), 799 short (>=1000) 69.0% (6840591 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 2068 seqs, 923 long (>=2000), 799 short (>=1000) 70.0% (6939952 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 2193 seqs, 923 long (>=2000), 799 short (>=1000) 71.0% (7039087 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 2326 seqs, 923 long (>=2000), 799 short (>=1000) 72.0% (7138011 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 2468 seqs, 923 long (>=2000), 799 short (>=1000) 73.0% (7236979 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 2618 seqs, 923 long (>=2000), 799 short (>=1000) 74.0% (7336190 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 2776 seqs, 923 long (>=2000), 799 short (>=1000) 75.0% (7435717 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 2942 seqs, 923 long (>=2000), 799 short (>=1000) 76.0% (7534388 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 3121 seqs, 923 long (>=2000), 799 short (>=1000) 77.0% (7633817 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 3310 seqs, 923 long (>=2000), 799 short (>=1000) 78.0% (7732844 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 3510 seqs, 923 long (>=2000), 799 short (>=1000) 79.0% (7831670 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 3724 seqs, 923 long (>=2000), 799 short (>=1000) 80.0% (7930849 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 3952 seqs, 923 long (>=2000), 799 short (>=1000) 81.0% (8029940 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 4196 seqs, 923 long (>=2000), 799 short (>=1000) 82.0% (8129247 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 4456 seqs, 923 long (>=2000), 799 short (>=1000) 83.0% (8228320 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 4733 seqs, 923 long (>=2000), 799 short (>=1000) 84.0% (8327496 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 5026 seqs, 923 long (>=2000), 799 short (>=1000) 85.0% (8426507 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 5336 seqs, 923 long (>=2000), 799 short (>=1000) 86.0% (8525909 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 5663 seqs, 923 long (>=2000), 799 short (>=1000) 87.0% (8624790 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 6010 seqs, 923 long (>=2000), 799 short (>=1000) 88.0% (8723910 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 6372 seqs, 923 long (>=2000), 799 short (>=1000) 89.0% (8823160 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 6746 seqs, 923 long (>=2000), 799 short (>=1000) 90.0% (8922334 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 7134 seqs, 923 long (>=2000), 799 short (>=1000) 91.0% (9021452 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 7538 seqs, 923 long (>=2000), 799 short (>=1000) 92.0% (9120498 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 7962 seqs, 923 long (>=2000), 799 short (>=1000) 93.0% (9219591 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 8411 seqs, 923 long (>=2000), 799 short (>=1000) 94.0% (9318754 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] ... processed 8934 seqs, 923 long (>=2000), 799 short (>=1000) 95.0% (9417836 of 9913441), ETA 0:00:00     [20.0Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 923, and small contigs >= 1000 bp are 799                                                                  
[00:00:00] Allocating 923 contigs by 1 samples abundances [20.0Gb / 503.5Gb]
[00:00:00] Allocating 923 contigs by 1 samples variances [20.0Gb / 503.5Gb]
[00:00:00] Allocating 799 small contigs by 1 samples abundances [20.0Gb / 503.5Gb]
[00:00:00] Reading 0.000532Gb abundance file [20.0Gb / 503.5Gb]
[00:00:00] ... processed 86 lines 86 contigs and 0 short contigs 1.0% (5752 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 174 lines 174 contigs and 0 short contigs 2.0% (11466 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 263 lines 263 contigs and 0 short contigs 3.0% (17158 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 353 lines 353 contigs and 0 short contigs 4.0% (22888 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 443 lines 443 contigs and 0 short contigs 5.0% (28601 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 533 lines 533 contigs and 0 short contigs 6.0% (34308 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 623 lines 623 contigs and 0 short contigs 7.0% (40016 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 713 lines 713 contigs and 0 short contigs 8.0% (45707 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 803 lines 803 contigs and 0 short contigs 9.0% (51385 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 894 lines 894 contigs and 0 short contigs 10.0% (57108 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 984 lines 923 contigs and 61 short contigs 11.0% (62802 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1074 lines 923 contigs and 151 short contigs 12.0% (68546 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1163 lines 923 contigs and 240 short contigs 13.0% (74240 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1252 lines 923 contigs and 329 short contigs 14.0% (79947 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1341 lines 923 contigs and 418 short contigs 15.0% (85637 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1431 lines 923 contigs and 508 short contigs 16.0% (91386 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1520 lines 923 contigs and 597 short contigs 17.0% (97085 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1609 lines 923 contigs and 686 short contigs 18.0% (102781 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1698 lines 923 contigs and 775 short contigs 19.0% (108477 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 20.0% (114219 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 21.0% (119925 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 22.0% (125613 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 23.0% (131313 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 24.0% (137057 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 25.0% (142736 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 26.0% (148490 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 27.0% (154181 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 28.0% (159870 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 29.0% (165622 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 30.0% (171305 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 31.0% (176986 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 32.0% (182748 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 33.0% (188454 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 34.0% (194138 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 35.0% (199875 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 36.0% (205571 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 37.0% (211258 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 38.0% (216980 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 39.0% (222689 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 40.0% (228384 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 41.0% (234088 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 42.0% (239778 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 43.0% (245529 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 44.0% (251225 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 45.0% (256911 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 46.0% (262619 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 47.0% (268333 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 48.0% (274042 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 49.0% (279742 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 50.0% (285506 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 51.0% (291219 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 52.0% (296893 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 53.0% (302635 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 54.0% (308305 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 55.0% (314026 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 56.0% (319729 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 57.0% (325418 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 58.0% (331133 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 59.0% (336861 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 60.0% (342575 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 61.0% (348280 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 62.0% (353988 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 63.0% (359684 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 64.0% (365396 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 65.0% (371125 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 66.0% (376851 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 67.0% (382547 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 68.0% (388254 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 69.0% (393940 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 70.0% (399671 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 71.0% (405347 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 72.0% (411063 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 73.0% (416810 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 74.0% (422526 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 75.0% (428230 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 76.0% (433920 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 77.0% (439594 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 78.0% (445348 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 79.0% (451043 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 80.0% (456777 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 81.0% (462455 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 82.0% (468162 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 83.0% (473902 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 84.0% (479575 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 85.0% (485300 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 86.0% (491027 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 87.0% (496695 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 88.0% (502415 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 89.0% (508156 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 90.0% (513819 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 91.0% (519521 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 92.0% (525269 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 93.0% (530990 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 94.0% (536685 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 95.0% (542383 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 96.0% (548081 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 97.0% (553788 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 98.0% (559536 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 99.0% (565191 of 570893), ETA 0:00:00     [20.0Gb / 503.5Gb]                 
[00:00:00] Finished reading 9192 contigs and 1 coverages from 03bins/metabat2_fixed/1507990/temp/1507990.depth.txt [20.0Gb / 503.5Gb]. Ignored 7470 too small contigs.                                     
[00:00:00] Number of target contigs: 923 of large (>= 2000) and 799 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 923
[00:00:00] Allocated memory for TNF [20.0Gb / 503.5Gb]
[00:00:00] Calculating TNF 1.4% (13 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 3.5% (32 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 5.4% (50 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 7.2% (66 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 8.5% (78 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 10.5% (97 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 12.1% (112 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 13.9% (128 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 16.1% (149 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 18.1% (167 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 19.8% (183 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 21.2% (196 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 23.2% (214 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 25.2% (233 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 27.4% (253 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 29.4% (271 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 31.6% (292 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 33.6% (310 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 35.4% (327 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 37.9% (350 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 39.9% (368 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 41.2% (380 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 42.9% (396 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 44.7% (413 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 47.1% (435 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 48.9% (451 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 50.7% (468 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 53.0% (489 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 54.8% (506 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 56.3% (520 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 57.4% (530 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 59.7% (551 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 61.6% (569 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 63.8% (589 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 65.5% (605 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 67.7% (625 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 69.8% (644 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 71.8% (663 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 74.0% (683 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 75.5% (697 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 77.5% (715 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 79.4% (733 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 81.7% (754 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 83.4% (770 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 85.6% (790 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 87.5% (808 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 89.6% (827 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 91.5% (845 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 93.8% (866 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 96.2% (888 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 97.8% (903 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 99.6% (919 of 923), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [20.0Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.8% (17 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 3.9% (36 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 6.2% (57 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 8.7% (80 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 10.4% (96 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 12.1% (112 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 13.9% (128 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 15.6% (144 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 17.3% (160 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 19.1% (176 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 20.8% (192 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 22.5% (208 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.3% (224 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.0% (240 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.7% (256 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.5% (272 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.2% (288 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.9% (304 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.7% (320 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.4% (336 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 38.1% (352 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.9% (368 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.6% (384 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.3% (400 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 45.1% (416 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.8% (432 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.5% (448 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.3% (464 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.0% (480 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.7% (496 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.5% (512 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 57.3% (529 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.2% (546 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.0% (563 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 62.7% (579 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 64.6% (596 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.5% (614 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.6% (633 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 70.4% (650 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 72.6% (670 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.3% (686 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 76.1% (702 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 77.9% (719 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 79.6% (735 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 81.4% (751 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.1% (767 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 84.8% (783 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 86.6% (799 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 88.3% (815 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 90.0% (831 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 91.8% (847 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 93.5% (863 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 95.2% (879 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 97.0% (895 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 98.7% (911 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 99.9% (922 of 923), ETA 0:00:00                   
[00:00:00] Finding cutoff p=999 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=999 7.7% (71 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 21.6% (199 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 32.9% (304 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 44.3% (409 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 53.2% (491 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 60.2% (556 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 65.9% (608 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 69.8% (644 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 72.7% (671 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 75.6% (698 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 77.8% (718 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.8% (737 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 81.6% (753 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 83.4% (770 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 84.7% (782 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 85.9% (793 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 87.2% (805 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.0% (812 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.8% (820 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 89.9% (830 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 91.1% (841 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 92.1% (850 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 93.2% (860 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 94.5% (872 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 96.5% (891 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 97.7% (902 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 98.7% (911 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.9; 0 / 923 (P = 0.00%) round 1]               
[00:00:00] Finding cutoff p=997 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=997 3.7% (34 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 16.6% (153 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 30.0% (277 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 45.1% (416 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 67.0% (618 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 78.7% (726 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 86.0% (794 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 89.2% (823 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 91.4% (844 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 93.4% (862 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 94.7% (874 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.7; 0 / 923 (P = 0.00%) round 2]               
[00:00:00] Finding cutoff p=994 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=994 3.7% (34 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 15.6% (144 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 28.9% (267 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 40.8% (377 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 54.4% (502 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 65.1% (601 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 77.2% (713 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 86.0% (794 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 89.6% (827 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 92.1% (850 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 93.7% (865 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.4; 4 / 923 (P = 0.43%) round 3]               
[00:00:00] Finding cutoff p=992 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=992 4.6% (42 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 18.2% (168 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 32.6% (301 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 48.5% (448 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 60.5% (558 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 72.2% (666 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 82.6% (762 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 86.9% (802 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 90.0% (831 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 94.0% (868 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.2; 14 / 923 (P = 1.52%) round 4]               
[00:00:00] Finding cutoff p=991 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=991 2.5% (23 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 16.5% (152 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 31.4% (290 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 47.6% (439 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 60.9% (562 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 73.5% (678 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 84.1% (776 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 88.5% (817 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 91.1% (841 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 93.0% (858 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 94.4% (871 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.1; 16 / 923 (P = 1.73%) round 5]               
[00:00:00] Finding cutoff p=989 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=989 3.8% (35 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 17.2% (159 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 28.8% (266 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 42.9% (396 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 55.8% (515 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 67.8% (626 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 80.5% (743 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 86.5% (798 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 89.6% (827 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 92.4% (853 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 93.9% (867 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 94.9% (876 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.9; 34 / 923 (P = 3.68%) round 6]               
[00:00:00] Finding cutoff p=984 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=984 3.1% (29 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 15.0% (138 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 28.9% (267 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 44.3% (409 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 56.6% (522 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 70.0% (646 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 80.0% (738 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 86.1% (795 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 89.7% (828 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 92.1% (850 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 93.9% (867 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 94.9% (876 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.4; 90 / 923 (P = 9.75%) round 7]               
[00:00:00] Finding cutoff p=980 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=980 4.0% (37 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 18.0% (166 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 30.8% (284 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 43.4% (401 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 57.0% (526 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 68.4% (631 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 79.5% (734 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 84.9% (784 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 88.1% (813 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 90.9% (839 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 93.4% (862 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 94.6% (873 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.0; 121 / 923 (P = 13.11%) round 8]               
[00:00:00] Finding cutoff p=975 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=975 3.9% (36 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 17.0% (157 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 29.4% (271 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 42.5% (392 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 55.6% (513 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 67.3% (621 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 78.0% (720 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 84.7% (782 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 88.4% (816 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 91.2% (842 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 93.4% (862 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.5; 167 / 923 (P = 18.09%) round 9]               
[00:00:00] Finding cutoff p=972 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=972 3.8% (35 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 16.5% (152 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 28.3% (261 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 42.5% (392 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 54.4% (502 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 65.9% (608 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 77.6% (716 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 84.4% (779 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 87.9% (811 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 90.7% (837 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 93.2% (860 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 94.7% (874 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.2; 191 / 923 (P = 20.69%) round 10]               
[00:00:00] Finding cutoff p=967 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=967 2.6% (24 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 15.2% (140 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 26.5% (245 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 46.9% (433 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 61.0% (563 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 73.2% (676 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 81.8% (755 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 85.5% (789 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 89.1% (822 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 91.7% (846 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 93.6% (864 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 94.9% (876 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.7; 237 / 923 (P = 25.68%) round 11]               
[00:00:00] Finding cutoff p=962 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=962 3.7% (34 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 15.3% (141 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 25.7% (237 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 37.1% (342 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 48.2% (445 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 60.9% (562 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 70.2% (648 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 78.7% (726 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 84.9% (784 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 88.6% (818 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 91.4% (844 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 93.8% (866 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 95.0% (877 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.2; 275 / 923 (P = 29.79%) round 12]               
[00:00:00] Finding cutoff p=958 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=958 2.7% (25 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 13.7% (126 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 25.0% (231 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 34.7% (320 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 46.6% (430 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 71.1% (656 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 79.5% (734 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 84.3% (778 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 88.2% (814 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 91.0% (840 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 93.4% (862 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.8; 296 / 923 (P = 32.07%) round 13]               
[00:00:00] Finding cutoff p=954 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=954 2.4% (22 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 13.4% (124 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 24.5% (226 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 35.1% (324 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 48.4% (447 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 59.5% (549 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 69.2% (639 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 78.2% (722 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 84.2% (777 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 88.0% (812 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 91.0% (840 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 93.5% (863 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 95.0% (877 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.4; 323 / 923 (P = 34.99%) round 14]               
[00:00:00] Finding cutoff p=949 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=949 2.5% (23 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 12.0% (111 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 21.5% (198 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 32.1% (296 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 42.9% (396 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 51.4% (474 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 60.7% (560 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 69.7% (643 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 83.5% (771 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 87.4% (807 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 90.4% (834 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 93.2% (860 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.9; 358 / 923 (P = 38.79%) round 15]               
[00:00:00] Finding cutoff p=946 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=946 3.3% (30 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 13.2% (122 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 24.1% (222 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 31.0% (286 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 41.7% (385 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 51.7% (477 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 60.9% (562 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 68.4% (631 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 76.7% (708 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 83.0% (766 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 86.9% (802 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 90.2% (833 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 92.8% (857 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 94.5% (872 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 95.4% (881 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.6; 369 / 923 (P = 39.98%) round 16]               
[00:00:00] Finding cutoff p=943 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=943 3.0% (28 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 13.7% (126 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 22.3% (206 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 32.1% (296 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 42.7% (394 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 52.5% (485 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 61.3% (566 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 69.6% (642 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 77.5% (715 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 87.4% (807 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 90.7% (837 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 93.6% (864 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 95.1% (878 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.3; 390 / 923 (P = 42.25%) round 17]               
[00:00:00] Finding cutoff p=940 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=940 3.7% (34 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 12.5% (115 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 22.6% (209 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 29.3% (270 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 38.1% (352 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 49.1% (453 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 56.7% (523 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 64.5% (595 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 72.3% (667 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 79.4% (733 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 84.5% (780 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 88.1% (813 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 91.1% (841 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 93.8% (866 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 95.2% (879 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 95.6% (882 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.0; 411 / 923 (P = 44.53%) round 18]               
[00:00:00] Finding cutoff p=937 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=937 3.5% (32 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 12.8% (118 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 20.8% (192 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 29.3% (270 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 37.2% (343 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 45.7% (422 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 53.2% (491 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 62.3% (575 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 76.6% (707 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 82.0% (757 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 86.0% (794 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 89.7% (828 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 92.5% (854 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 94.3% (870 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 95.4% (881 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.7; 420 / 923 (P = 45.50%) round 19]               
[00:00:00] Finding cutoff p=932 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=932 3.1% (29 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 12.5% (115 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 19.8% (183 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 29.0% (268 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 37.8% (349 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 46.0% (425 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 54.1% (499 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 61.1% (564 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 69.1% (638 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 76.4% (705 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 82.6% (762 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 87.4% (807 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 90.0% (831 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 92.7% (856 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 95.0% (877 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 95.6% (882 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.2; 452 / 923 (P = 48.97%) round 20]               
[00:00:00] Finding cutoff p=928 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=928 3.4% (31 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 11.8% (109 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 21.0% (194 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 28.4% (262 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 38.9% (359 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 48.5% (448 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 56.0% (517 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 70.2% (648 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 78.5% (725 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 82.8% (764 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 87.6% (809 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 90.9% (839 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 93.2% (860 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 94.5% (872 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 95.4% (881 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.8; 482 / 923 (P = 52.22%) round 21]               
[00:00:00] Finding cutoff p=924 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=924 2.4% (22 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 11.7% (108 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 19.1% (176 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 28.1% (259 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 35.3% (326 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 45.1% (416 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 51.7% (477 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 57.4% (530 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 65.2% (602 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 71.3% (658 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 77.9% (719 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 84.0% (775 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 87.9% (811 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 90.5% (835 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 93.4% (862 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 94.9% (876 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 95.7% (883 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.4; 501 / 923 (P = 54.28%) round 22]               
[00:00:00] Finding cutoff p=919 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=919 2.2% (20 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 10.4% (96 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 19.4% (179 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 27.6% (255 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 34.1% (315 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 51.1% (472 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 58.5% (540 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 62.5% (577 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 70.9% (654 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 76.1% (702 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 80.5% (743 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 86.0% (794 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 89.1% (822 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 91.9% (848 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 94.1% (869 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.9; 523 / 923 (P = 56.66%) round 23]               
[00:00:00] Finding cutoff p=914 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=914 2.4% (22 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 9.4% (87 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 17.4% (161 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 25.7% (237 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 32.4% (299 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 40.8% (377 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 48.0% (443 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 54.3% (501 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 61.6% (569 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 68.1% (629 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 73.6% (679 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 79.2% (731 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 84.9% (784 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 88.5% (817 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 91.4% (844 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 94.1% (869 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 95.6% (882 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.4; 538 / 923 (P = 58.29%) round 24]               
[00:00:00] Finding cutoff p=910 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=910 2.2% (20 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 15.4% (142 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 24.1% (222 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 30.8% (284 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 37.1% (342 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 45.2% (417 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 51.2% (473 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 57.5% (531 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 65.9% (608 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 70.2% (648 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 74.9% (691 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 80.3% (741 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 84.1% (776 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 87.9% (811 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 92.3% (852 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 94.3% (870 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 95.4% (881 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.0; 551 / 923 (P = 59.70%) round 25]               
[00:00:00] Finding cutoff p=905 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=905 2.9% (27 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 9.5% (88 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 16.8% (155 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 23.5% (217 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 31.4% (290 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 38.4% (354 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 46.4% (428 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 53.4% (493 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 62.9% (581 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 69.4% (641 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 73.8% (681 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 79.7% (736 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 83.9% (774 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 87.2% (805 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 91.7% (846 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 94.7% (874 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 95.7% (883 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.5; 565 / 923 (P = 61.21%) round 26]               
[00:00:00] Finding cutoff p=901 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=901 2.3% (21 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 9.1% (84 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 12.7% (117 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 20.6% (190 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 28.2% (260 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 34.3% (317 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 41.6% (384 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 47.9% (442 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 54.3% (501 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 59.8% (552 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 65.5% (605 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 71.9% (664 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 77.4% (714 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 81.6% (753 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 87.5% (808 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 90.4% (834 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 93.0% (858 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 95.1% (878 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 95.9% (885 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.1; 577 / 923 (P = 62.51%) round 27]               
[00:00:00] Finding cutoff p=898 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=898 2.5% (23 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 9.3% (86 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 14.3% (132 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 23.5% (217 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 32.0% (295 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 38.5% (355 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 46.3% (427 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 52.0% (480 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 59.6% (550 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 63.9% (590 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 69.1% (638 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 76.1% (702 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 81.5% (752 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 85.9% (793 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 91.5% (845 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 93.9% (867 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 95.2% (879 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 95.8% (884 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.8; 584 / 923 (P = 63.27%) round 28]               
[00:00:00] Finding cutoff p=887 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=887 2.5% (23 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 8.8% (81 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 13.8% (127 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 21.9% (202 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 28.1% (259 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 33.3% (307 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 40.2% (371 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 46.6% (430 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 55.7% (514 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 60.5% (558 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 64.6% (596 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 70.5% (651 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 76.1% (702 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 80.0% (738 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 84.9% (784 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 88.8% (820 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 92.1% (850 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 94.6% (873 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 95.4% (881 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 88.7; 595 / 923 (P = 64.46%) round 29]               
[00:00:00] Finding cutoff p=876 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=876 1.8% (17 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 8.8% (81 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 14.2% (131 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 21.2% (196 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 27.2% (251 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 34.0% (314 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 46.5% (429 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 52.2% (482 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 56.3% (520 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 63.9% (590 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 69.6% (642 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 75.1% (693 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 79.2% (731 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 85.4% (788 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 89.2% (823 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 91.7% (846 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 93.4% (862 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 95.4% (881 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=876 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 87.6; 614 / 923 (P = 66.52%) round 30]               
[00:00:00] Finding cutoff p=865 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=865 2.0% (18 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 8.7% (80 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 15.3% (141 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 22.3% (206 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 28.8% (266 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 33.3% (307 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 41.3% (381 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 48.3% (446 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 54.2% (500 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 62.1% (573 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 66.3% (612 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 72.0% (665 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 76.5% (706 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 80.9% (747 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 85.4% (788 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 89.3% (824 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 92.1% (850 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 94.1% (869 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 95.7% (883 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 86.5; 628 / 923 (P = 68.04%) round 31]               
[00:00:00] Finding cutoff p=856 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=856 2.3% (21 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 9.0% (83 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 14.0% (129 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 21.2% (196 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 27.6% (255 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 33.0% (305 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 42.3% (390 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 48.9% (451 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 53.8% (497 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 60.7% (560 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 65.3% (603 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 71.1% (656 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 74.5% (688 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 78.9% (728 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 83.2% (768 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 87.6% (809 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 91.5% (845 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 93.9% (867 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.6; 643 / 923 (P = 69.66%) round 32]               
[00:00:00] Finding cutoff p=846 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=846 2.3% (21 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 7.8% (72 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 12.4% (114 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 18.4% (170 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 24.3% (224 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 33.0% (305 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 39.2% (362 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 45.0% (415 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 51.6% (476 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 57.6% (532 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 62.6% (578 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 68.7% (634 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 73.9% (682 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 77.8% (718 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 84.8% (783 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 87.6% (809 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 93.2% (860 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 95.0% (877 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 95.8% (884 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.6; 651 / 923 (P = 70.53%) round 33]               
[00:00:00] Finding cutoff p=837 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=837 2.7% (25 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 9.3% (86 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 14.2% (131 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 19.2% (177 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 23.9% (221 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 29.7% (274 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 35.9% (331 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 41.2% (380 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 49.7% (459 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 54.3% (501 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 59.5% (549 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 65.9% (608 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 70.1% (647 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 74.2% (685 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 78.5% (725 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 82.2% (759 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 85.9% (793 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 89.4% (825 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 92.8% (857 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 95.0% (877 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 96.1% (887 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=837 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 83.7; 680 / 923 (P = 73.67%) round 34]               
[00:00:00] Finding cutoff p=826 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=826 2.6% (24 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 7.7% (71 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 13.2% (122 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 17.8% (164 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 23.5% (217 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 35.4% (327 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 45.1% (416 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 49.6% (458 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 54.5% (503 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 61.6% (569 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 65.3% (603 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 70.0% (646 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 73.6% (679 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 77.0% (711 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 82.0% (757 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 85.3% (787 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 88.6% (818 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 92.0% (849 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 95.8% (884 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=826 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.6; 708 / 923 (P = 76.71%) round 35]               
[00:00:00] Finding cutoff p=816 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=816 2.2% (20 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 5.4% (50 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 10.6% (98 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 15.2% (140 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 19.6% (181 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 27.1% (250 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 32.8% (303 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 39.9% (368 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 45.5% (420 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 51.9% (479 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 55.5% (512 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 61.6% (569 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 66.0% (609 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 69.6% (642 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 74.8% (690 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 77.9% (719 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 82.1% (758 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 86.7% (800 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 89.4% (825 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 92.0% (849 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 94.3% (870 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 96.2% (888 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 81.6; 726 / 923 (P = 78.66%) round 36]               
[00:00:00] Finding cutoff p=806 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=806 2.2% (20 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 5.9% (54 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 10.8% (100 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 15.9% (147 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 20.6% (190 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 26.0% (240 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 32.7% (302 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 37.7% (348 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 45.0% (415 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 49.8% (460 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 54.9% (507 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 61.3% (566 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 65.9% (608 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 68.8% (635 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 73.2% (676 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 76.5% (706 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 80.3% (741 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 83.5% (771 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 86.8% (801 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 90.0% (831 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 93.2% (860 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 95.8% (884 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.6; 742 / 923 (P = 80.39%) round 37]               
[00:00:00] Finding cutoff p=796 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=796 2.4% (22 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 6.5% (60 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 12.4% (114 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 16.8% (155 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 22.8% (210 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 28.7% (265 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 35.1% (324 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 42.1% (389 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 48.6% (449 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 54.0% (498 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 58.1% (536 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 62.9% (581 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 65.4% (604 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 67.9% (627 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 73.8% (681 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 76.7% (708 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 80.0% (738 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 84.1% (776 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 87.0% (803 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 89.5% (826 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 91.8% (847 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 94.6% (873 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 96.0% (886 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 79.6; 758 / 923 (P = 82.12%) round 38]               
[00:00:00] Finding cutoff p=785 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=785 2.4% (22 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 5.2% (48 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 9.6% (89 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 13.7% (126 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 18.3% (169 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 23.1% (213 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 29.8% (275 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 33.7% (311 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 39.8% (367 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 45.0% (415 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 48.8% (450 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 54.0% (498 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 57.5% (531 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 60.9% (562 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 64.8% (598 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 68.7% (634 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 72.2% (666 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 75.9% (701 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 78.4% (724 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 82.1% (758 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 89.4% (825 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 92.5% (854 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 94.6% (873 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 96.3% (889 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 96.5% (891 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.5; 767 / 923 (P = 83.10%) round 39]               
[00:00:00] Finding cutoff p=775 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=775 2.3% (21 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 6.8% (63 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 10.8% (100 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 13.2% (122 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 16.4% (151 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 20.9% (193 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 27.8% (257 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 32.0% (295 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 38.6% (356 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 42.8% (395 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 46.5% (429 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 51.6% (476 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 54.9% (507 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 58.8% (543 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 64.0% (591 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 67.5% (623 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 71.4% (659 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 74.5% (688 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 78.3% (723 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 80.3% (741 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 83.6% (772 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 89.2% (823 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 91.9% (848 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 93.9% (867 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 77.5; 783 / 923 (P = 84.83%) round 40]               
[00:00:00] Finding cutoff p=765 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=765 2.2% (20 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 9.5% (88 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 13.0% (120 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 17.9% (165 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 20.7% (191 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 26.3% (243 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 32.4% (299 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 35.0% (323 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 41.7% (385 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 47.1% (435 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 49.4% (456 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 54.8% (506 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 58.3% (538 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 62.1% (573 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 66.1% (610 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 67.9% (627 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 72.8% (672 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 75.7% (699 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 77.8% (718 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 81.5% (752 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 84.5% (780 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 87.6% (809 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 89.8% (829 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 92.6% (855 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 94.9% (876 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 96.6% (892 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 76.5; 797 / 923 (P = 86.35%) round 41]               
[00:00:00] Finding cutoff p=754 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=754 2.1% (19 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 5.9% (54 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 9.9% (91 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 12.7% (117 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 16.1% (149 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 20.4% (188 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 26.1% (241 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 30.4% (281 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 35.8% (330 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 41.6% (384 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 45.0% (415 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 49.2% (454 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 54.4% (502 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 57.1% (527 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 61.4% (567 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 65.7% (606 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 69.9% (645 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 72.9% (673 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 75.5% (697 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 77.6% (716 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 80.4% (742 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 84.6% (781 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 87.2% (805 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 89.4% (825 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 93.3% (861 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 96.0% (886 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 96.7% (893 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 75.4; 814 / 923 (P = 88.19%) round 42]               
[00:00:00] Finding cutoff p=745 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=745 2.6% (24 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 6.4% (59 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 10.3% (95 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 12.7% (117 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 15.5% (143 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 19.6% (181 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 25.9% (239 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 30.4% (281 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 35.1% (324 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 40.2% (371 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 42.9% (396 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 48.5% (448 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 51.7% (477 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 54.0% (498 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 59.6% (550 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 62.1% (573 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 67.0% (618 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 70.4% (650 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 72.6% (670 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 75.3% (695 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 77.5% (715 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 80.9% (747 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 86.5% (798 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 88.4% (816 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 91.7% (846 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 94.1% (869 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 96.0% (886 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 96.9% (894 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 74.5; 822 / 923 (P = 89.06%) round 43]               
[00:00:00] Finding cutoff p=735 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=735 2.3% (21 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 4.7% (43 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 9.6% (89 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 12.6% (116 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 15.4% (142 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 19.7% (182 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 23.5% (217 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 28.7% (265 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 33.4% (308 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 39.2% (362 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 41.6% (384 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 45.6% (421 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 47.9% (442 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 51.1% (472 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 55.6% (513 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 58.7% (542 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 62.1% (573 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 66.3% (612 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 68.9% (636 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 73.6% (679 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 76.3% (704 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 80.8% (746 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 83.2% (768 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 86.0% (794 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 88.8% (820 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 91.8% (847 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 94.1% (869 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 95.4% (881 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 97.0% (895 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 73.5; 827 / 923 (P = 89.60%) round 44]               
[00:00:00] Finding cutoff p=724 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=724 2.2% (20 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 4.7% (43 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 7.9% (73 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 11.1% (102 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 13.3% (123 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 16.8% (155 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 23.3% (215 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 26.9% (248 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 34.5% (318 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 38.6% (356 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 40.2% (371 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 44.7% (413 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 46.6% (430 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 50.4% (465 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 53.5% (494 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 56.7% (523 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 59.5% (549 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 63.8% (589 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 68.0% (628 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 70.4% (650 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 74.2% (685 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 78.0% (720 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 80.2% (740 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 83.1% (767 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 87.5% (808 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 90.8% (838 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 93.2% (860 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 94.9% (876 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 95.9% (885 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 97.1% (896 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=724 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 72.4; 832 / 923 (P = 90.14%) round 45]               
[00:00:00] Finding cutoff p=715 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=715 2.1% (19 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 4.2% (39 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 8.2% (76 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 12.1% (112 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 14.3% (132 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 17.9% (165 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 23.1% (213 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 26.1% (241 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 31.4% (290 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 36.9% (341 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 39.5% (365 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 42.8% (395 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 45.7% (422 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 48.2% (445 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 52.8% (487 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 54.9% (507 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 58.4% (539 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 64.2% (593 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 66.1% (610 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 69.3% (640 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 71.8% (663 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 76.8% (709 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 78.2% (722 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 80.5% (743 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 84.1% (776 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 87.6% (809 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 89.5% (826 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 91.2% (842 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 93.6% (864 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 95.4% (881 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 97.1% (896 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=715 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 71.5; 839 / 923 (P = 90.90%) round 46]               
[00:00:00] Finding cutoff p=704 [20.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=704 2.4% (22 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 4.4% (41 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 9.5% (88 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 12.2% (113 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 14.3% (132 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 18.3% (169 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 23.6% (218 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 26.3% (243 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 32.2% (297 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 39.7% (366 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 43.6% (402 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 47.6% (439 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 49.1% (453 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 51.1% (472 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 55.4% (511 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 58.4% (539 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 63.4% (585 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 67.6% (624 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 70.1% (647 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 72.3% (667 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 75.3% (695 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 79.5% (734 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 81.3% (750 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 83.5% (771 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 86.8% (801 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 89.8% (829 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 92.3% (852 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 93.9% (867 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 95.2% (879 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 96.1% (887 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 97.2% (897 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=704 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 70.4; 844 / 923 (P = 91.44%) round 47]               
[00:00:00] Finished Preparing TNF Graph Building [pTNF = 69.30] [20.0Gb / 503.5Gb]                                            
[00:00:00] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=923 maxEdges=200
[00:00:00] Building TNF Graph 55.8% (515 of 923), ETA 0:00:00     [20.0Gb / 503.5Gb]                           
[00:00:00] Finished Building TNF Graph (35035 edges) [20.0Gb / 503.5Gb]                                          
[00:00:00] Cleaned up after Building TNF Graph (35035 edges) [20.0Gb / 503.5Gb]                                          
[00:00:00] Cleaned up TNF matrix of large contigs [20.0Gb / 503.5Gb]                                             
[00:00:00] Applying coverage correlations to TNF graph with 35035 edges
[00:00:00] Allocated memory for graph edges [20.0Gb / 503.5Gb]
[00:00:00] ... calculating abundance dist 1.0% (363 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 2.0% (712 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 3.0% (1061 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 4.0% (1411 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 5.0% (1763 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 6.0% (2111 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 7.0% (2469 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 8.0% (2817 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 9.1% (3172 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 10.0% (3513 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 11.0% (3869 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 12.1% (4222 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 13.1% (4577 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 14.0% (4919 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 15.1% (5278 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 16.1% (5627 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 17.0% (5967 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 18.1% (6326 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 19.0% (6669 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 20.1% (7028 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 21.1% (7386 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 22.1% (7728 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 23.1% (8086 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 24.1% (8432 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 25.0% (8775 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 26.1% (9137 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 27.1% (9481 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 28.1% (9841 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 29.1% (10187 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 30.1% (10532 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 31.1% (10892 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 32.1% (11236 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 33.1% (11596 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 34.1% (11942 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 35.1% (12287 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 36.1% (12648 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 37.1% (12992 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 38.1% (13339 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 39.1% (13699 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 40.1% (14044 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 41.1% (14404 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 42.1% (14750 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 43.1% (15096 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 44.1% (15458 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 45.1% (15802 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 46.1% (16157 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 47.1% (16503 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 48.1% (16862 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 49.1% (17207 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 50.1% (17554 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 51.1% (17901 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 52.1% (18260 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 53.1% (18605 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 54.1% (18963 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 55.1% (19310 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 56.1% (19669 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 57.1% (20018 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 58.1% (20361 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 59.2% (20724 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 60.1% (21071 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 61.1% (21414 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 62.1% (21773 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 63.1% (22116 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 64.2% (22478 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 65.1% (22824 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 66.1% (23168 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 67.2% (23530 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 68.1% (23876 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 69.1% (24220 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 70.2% (24578 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 71.1% (24922 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 72.2% (25283 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 73.1% (25624 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 74.2% (25983 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 75.1% (26326 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 76.2% (26687 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 77.2% (27030 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 78.2% (27391 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 79.2% (27738 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 80.2% (28084 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 81.2% (28446 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 82.2% (28790 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 83.2% (29135 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 84.2% (29494 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 85.2% (29838 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 86.2% (30199 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 87.2% (30543 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 88.2% (30888 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 89.2% (31248 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 90.2% (31595 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 91.2% (31956 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 92.2% (32299 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 93.2% (32644 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 94.2% (33007 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 95.2% (33350 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 96.2% (33710 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 97.2% (34050 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 98.2% (34402 of 35035), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 99.2% (34749 of 35035), ETA 0:00:00                              
[00:00:00] Calculating geometric means [20.0Gb / 503.5Gb]
[00:00:00] Traversing graph with 923 nodes and 35035 edges [20.0Gb / 503.5Gb]
[00:00:00] Building SCR Graph and Binning (89 vertices and 73 edges) [P = 9.50%; 20.0Gb / 503.5Gb]                           
[00:00:00] Building SCR Graph and Binning (176 vertices and 212 edges) [P = 19.00%; 20.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 1.0% (351 of 35035), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (265 vertices and 518 edges) [P = 28.50%; 20.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 2.0% (702 of 35035), ETA 0:00:00                               
[00:00:00] ... traversing graph 3.0% (1053 of 35035), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (351 vertices and 943 edges) [P = 38.00%; 20.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 4.0% (1404 of 35035), ETA 0:00:00                               
[00:00:00] ... traversing graph 5.0% (1755 of 35035), ETA 0:00:00                               
[00:00:00] ... traversing graph 6.0% (2106 of 35035), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (439 vertices and 1845 edges) [P = 47.50%; 20.0Gb / 503.5Gb]                           
[00:00:00] Building SCR Graph and Binning (458 vertices and 2018 edges) [P = 57.00%; 20.0Gb / 503.5Gb]                           
[00:00:00] Finished Traversing graph [20.0Gb / 503.5Gb]                                       
[00:00:00] Dissolved 793 small clusters leaving 523 leftover contigs to be re-merged into larger clusters
[00:00:00] Rescuing singleton large contigs                                   
[00:00:00] There are 5 bins already
[00:00:00] Outputting bins
[00:00:00] Writing cluster stats to: 03bins/metabat2_fixed/1507990/1507990.bin.BinInfo.txt
[00:00:00] 25.01% (1383010 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
5 bins (1383010 bases in total) formed.
[00:00:00] Finished
MetaBAT2 generated 5 bins for 1507990
MetaBAT2 binning completed for 1507990
