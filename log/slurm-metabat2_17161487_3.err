INFO:    Converting OCI blobs to SIF format
INFO:    Starting build...
Copying blob sha256:79578f19b677c48727475ff0c2a4daaf7a503c13bbdbbffa9d0918214b460a22
Copying blob sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1
Copying blob sha256:6414378b647780fee8fd903ddb9541d134a1947ce092d08bdeb23a54cb3684ac
Copying blob sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1
Copying blob sha256:3069b7806bca5bd9d6b58e9582f26a6301b0fda4d087dbf33b74055179b4988f
time="2025-04-28T16:24:32-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
Copying config sha256:4094e04a5277293d0f9242f032d90dcf74996cb55c1c9ceeeebbb22d9bc691f8
Writing manifest to image destination
2025/04/28 16:24:33  info unpack layer: sha256:6414378b647780fee8fd903ddb9541d134a1947ce092d08bdeb23a54cb3684ac
2025/04/28 16:24:34  info unpack layer: sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1
2025/04/28 16:24:34  info unpack layer: sha256:3069b7806bca5bd9d6b58e9582f26a6301b0fda4d087dbf33b74055179b4988f
2025/04/28 16:24:34  info unpack layer: sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1
2025/04/28 16:24:34  info unpack layer: sha256:79578f19b677c48727475ff0c2a4daaf7a503c13bbdbbffa9d0918214b460a22
INFO:    Creating SIF file...
Output depth matrix to 03bins/metabat2/1507993/temp/1507993.depth.txt
jgi_summarize_bam_contig_depths 2.17.66-ga512006-dirty 20250124_080221
Running with 16 threads to save memory you can reduce the number of threads with the OMP_NUM_THREADS variable
Output matrix to 03bins/metabat2/1507993/temp/1507993.depth.txt
Opening all bam files and validating headers
Processing bam files with largest_contig=0
Thread 0 opening and reading the header for file: 02mapping/1507993/1507993.sorted.bam
Thread 0 opened the file: 02mapping/1507993/1507993.sorted.bam
Thread 0 processing bam 0: 1507993.sorted.bam
Thread 0 finished reading bam 0: 1507993.sorted.bam
Thread 0 finished: 1507993.sorted.bam with 8305867 reads and 7779848 readsWellMapped (93.6669%)
Creating depth matrix file: 03bins/metabat2/1507993/temp/1507993.depth.txt
Closing last bam file
Finished
