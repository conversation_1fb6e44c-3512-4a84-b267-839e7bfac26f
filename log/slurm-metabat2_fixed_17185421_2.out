Decompressing scaffold file for 1507992...
Generating depth file for 1507992...
Running MetaBAT2 for 1507992 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 200000. with random seed=123
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [8.4Gb / 503.5Gb]
[00:00:00] Parsing assembly file [8.4Gb / 503.5Gb]
[00:00:00] ... processed 5 seqs, 5 long (>=2000), 0 short (>=1000) 1.0% (246975 of 23546120), ETA 0:00:01     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 14 seqs, 14 long (>=2000), 0 short (>=1000) 2.1% (494376 of 23546120), ETA 0:00:01     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 23 seqs, 23 long (>=2000), 0 short (>=1000) 3.0% (717882 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 33 seqs, 33 long (>=2000), 0 short (>=1000) 4.0% (947922 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 45 seqs, 45 long (>=2000), 0 short (>=1000) 5.1% (1196326 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 57 seqs, 57 long (>=2000), 0 short (>=1000) 6.0% (1424111 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 70 seqs, 70 long (>=2000), 0 short (>=1000) 7.0% (1651873 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 84 seqs, 84 long (>=2000), 0 short (>=1000) 8.0% (1885459 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 99 seqs, 99 long (>=2000), 0 short (>=1000) 9.0% (2122050 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 115 seqs, 115 long (>=2000), 0 short (>=1000) 10.0% (2362335 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 132 seqs, 132 long (>=2000), 0 short (>=1000) 11.0% (2600647 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 149 seqs, 149 long (>=2000), 0 short (>=1000) 12.0% (2831238 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 167 seqs, 167 long (>=2000), 0 short (>=1000) 13.0% (3066080 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 186 seqs, 186 long (>=2000), 0 short (>=1000) 14.0% (3302670 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 206 seqs, 206 long (>=2000), 0 short (>=1000) 15.0% (3539080 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 227 seqs, 227 long (>=2000), 0 short (>=1000) 16.0% (3776100 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 249 seqs, 249 long (>=2000), 0 short (>=1000) 17.0% (4009331 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 272 seqs, 272 long (>=2000), 0 short (>=1000) 18.0% (4241639 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 297 seqs, 297 long (>=2000), 0 short (>=1000) 19.0% (4482232 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 322 seqs, 322 long (>=2000), 0 short (>=1000) 20.0% (4712668 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 349 seqs, 349 long (>=2000), 0 short (>=1000) 21.0% (4952389 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 376 seqs, 376 long (>=2000), 0 short (>=1000) 22.0% (5183247 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 405 seqs, 405 long (>=2000), 0 short (>=1000) 23.0% (5421177 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 435 seqs, 435 long (>=2000), 0 short (>=1000) 24.0% (5657810 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 465 seqs, 465 long (>=2000), 0 short (>=1000) 25.0% (5886563 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 497 seqs, 497 long (>=2000), 0 short (>=1000) 26.0% (6122518 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 531 seqs, 531 long (>=2000), 0 short (>=1000) 27.0% (6364315 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 565 seqs, 565 long (>=2000), 0 short (>=1000) 28.0% (6596675 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 601 seqs, 601 long (>=2000), 0 short (>=1000) 29.0% (6830372 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 639 seqs, 639 long (>=2000), 0 short (>=1000) 30.0% (7067197 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 678 seqs, 678 long (>=2000), 0 short (>=1000) 31.0% (7300721 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 720 seqs, 720 long (>=2000), 0 short (>=1000) 32.0% (7539512 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 763 seqs, 763 long (>=2000), 0 short (>=1000) 33.0% (7773589 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 808 seqs, 808 long (>=2000), 0 short (>=1000) 34.0% (8009378 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 854 seqs, 854 long (>=2000), 0 short (>=1000) 35.0% (8241291 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 903 seqs, 903 long (>=2000), 0 short (>=1000) 36.0% (8476851 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 955 seqs, 955 long (>=2000), 0 short (>=1000) 37.0% (8716504 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1008 seqs, 1008 long (>=2000), 0 short (>=1000) 38.0% (8949239 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1064 seqs, 1064 long (>=2000), 0 short (>=1000) 39.0% (9184395 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1123 seqs, 1123 long (>=2000), 0 short (>=1000) 40.0% (9421149 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1184 seqs, 1184 long (>=2000), 0 short (>=1000) 41.0% (9655448 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1248 seqs, 1248 long (>=2000), 0 short (>=1000) 42.0% (9890628 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1315 seqs, 1315 long (>=2000), 0 short (>=1000) 43.0% (10126799 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1385 seqs, 1385 long (>=2000), 0 short (>=1000) 44.0% (10363402 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1457 seqs, 1457 long (>=2000), 0 short (>=1000) 45.0% (10596184 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1533 seqs, 1533 long (>=2000), 0 short (>=1000) 46.0% (10831562 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1613 seqs, 1613 long (>=2000), 0 short (>=1000) 47.0% (11067411 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1697 seqs, 1697 long (>=2000), 0 short (>=1000) 48.0% (11303002 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1785 seqs, 1785 long (>=2000), 0 short (>=1000) 49.0% (11538606 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1877 seqs, 1877 long (>=2000), 0 short (>=1000) 50.0% (11773712 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1974 seqs, 1974 long (>=2000), 0 short (>=1000) 51.0% (12009422 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2077 seqs, 2077 long (>=2000), 0 short (>=1000) 52.0% (12245547 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2186 seqs, 2186 long (>=2000), 0 short (>=1000) 53.0% (12480492 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2302 seqs, 2282 long (>=2000), 20 short (>=1000) 54.0% (12716818 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2425 seqs, 2282 long (>=2000), 143 short (>=1000) 55.0% (12952036 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2556 seqs, 2282 long (>=2000), 274 short (>=1000) 56.0% (13187202 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2695 seqs, 2282 long (>=2000), 413 short (>=1000) 57.0% (13422110 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2843 seqs, 2282 long (>=2000), 561 short (>=1000) 58.0% (13657375 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3000 seqs, 2282 long (>=2000), 718 short (>=1000) 59.0% (13892766 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3167 seqs, 2282 long (>=2000), 885 short (>=1000) 60.0% (14129072 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3343 seqs, 2282 long (>=2000), 1061 short (>=1000) 61.0% (14364068 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3529 seqs, 2282 long (>=2000), 1247 short (>=1000) 62.0% (14599504 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3726 seqs, 2282 long (>=2000), 1444 short (>=1000) 63.0% (14834481 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3935 seqs, 2282 long (>=2000), 1653 short (>=1000) 64.0% (15070029 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 4157 seqs, 2282 long (>=2000), 1875 short (>=1000) 65.0% (15305588 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 4393 seqs, 2282 long (>=2000), 1970 short (>=1000) 66.0% (15540853 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 4643 seqs, 2282 long (>=2000), 1970 short (>=1000) 67.0% (15776768 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 4905 seqs, 2282 long (>=2000), 1970 short (>=1000) 68.0% (16012288 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 5182 seqs, 2282 long (>=2000), 1970 short (>=1000) 69.0% (16247654 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 5476 seqs, 2282 long (>=2000), 1970 short (>=1000) 70.0% (16483053 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 5787 seqs, 2282 long (>=2000), 1970 short (>=1000) 71.0% (16718251 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 6115 seqs, 2282 long (>=2000), 1970 short (>=1000) 72.0% (16953576 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 6462 seqs, 2282 long (>=2000), 1970 short (>=1000) 73.0% (17189382 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 6829 seqs, 2282 long (>=2000), 1970 short (>=1000) 74.0% (17424614 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 7218 seqs, 2282 long (>=2000), 1970 short (>=1000) 75.0% (17660170 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 7629 seqs, 2282 long (>=2000), 1970 short (>=1000) 76.0% (17895198 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 8067 seqs, 2282 long (>=2000), 1970 short (>=1000) 77.0% (18131047 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 8529 seqs, 2282 long (>=2000), 1970 short (>=1000) 78.0% (18366392 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 9018 seqs, 2282 long (>=2000), 1970 short (>=1000) 79.0% (18601931 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 9535 seqs, 2282 long (>=2000), 1970 short (>=1000) 80.0% (18837356 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 10083 seqs, 2282 long (>=2000), 1970 short (>=1000) 81.0% (19072659 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 10666 seqs, 2282 long (>=2000), 1970 short (>=1000) 82.0% (19308016 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 11285 seqs, 2282 long (>=2000), 1970 short (>=1000) 83.0% (19543470 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 11941 seqs, 2282 long (>=2000), 1970 short (>=1000) 84.0% (19779151 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 12635 seqs, 2282 long (>=2000), 1970 short (>=1000) 85.0% (20014460 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 13368 seqs, 2282 long (>=2000), 1970 short (>=1000) 86.0% (20249804 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 14144 seqs, 2282 long (>=2000), 1970 short (>=1000) 87.0% (20485395 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 14964 seqs, 2282 long (>=2000), 1970 short (>=1000) 88.0% (20720836 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 15824 seqs, 2282 long (>=2000), 1970 short (>=1000) 89.0% (20956194 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 16719 seqs, 2282 long (>=2000), 1970 short (>=1000) 90.0% (21191646 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 17651 seqs, 2282 long (>=2000), 1970 short (>=1000) 91.0% (21427153 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 18626 seqs, 2282 long (>=2000), 1970 short (>=1000) 92.0% (21662517 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 19653 seqs, 2282 long (>=2000), 1970 short (>=1000) 93.0% (21898042 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 20739 seqs, 2282 long (>=2000), 1970 short (>=1000) 94.0% (22133552 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 22374 seqs, 2282 long (>=2000), 1970 short (>=1000) 95.0% (22368898 of 23546120), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 2282, and small contigs >= 1000 bp are 1970                                                                  
[00:00:00] Allocating 2282 contigs by 1 samples abundances [8.4Gb / 503.5Gb]
[00:00:00] Allocating 2282 contigs by 1 samples variances [8.4Gb / 503.5Gb]
[00:00:00] Allocating 1970 small contigs by 1 samples abundances [8.4Gb / 503.5Gb]
[00:00:00] Reading 0.001310Gb abundance file [8.4Gb / 503.5Gb]
[00:00:00] ... processed 213 lines 213 contigs and 0 short contigs 1.0% (14076 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 433 lines 433 contigs and 0 short contigs 2.0% (28204 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 654 lines 654 contigs and 0 short contigs 3.0% (42268 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 875 lines 875 contigs and 0 short contigs 4.0% (56303 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 1095 lines 1095 contigs and 0 short contigs 5.0% (70361 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 1314 lines 1314 contigs and 0 short contigs 6.0% (84453 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 1533 lines 1533 contigs and 0 short contigs 7.0% (98546 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 1753 lines 1753 contigs and 0 short contigs 8.0% (112631 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 1972 lines 1972 contigs and 0 short contigs 9.0% (126649 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 2193 lines 2193 contigs and 0 short contigs 10.0% (140781 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 2413 lines 2282 contigs and 131 short contigs 11.0% (154835 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 2633 lines 2282 contigs and 351 short contigs 12.0% (168909 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 2853 lines 2282 contigs and 571 short contigs 13.0% (182958 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 3073 lines 2282 contigs and 791 short contigs 14.0% (197028 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 3294 lines 2282 contigs and 1012 short contigs 15.0% (211138 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 3514 lines 2282 contigs and 1232 short contigs 16.0% (225202 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 3734 lines 2282 contigs and 1452 short contigs 17.0% (239249 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 3955 lines 2282 contigs and 1673 short contigs 18.0% (253338 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4175 lines 2282 contigs and 1893 short contigs 19.0% (267393 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 20.0% (281488 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 21.0% (295536 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 22.0% (309635 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 23.0% (323704 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 24.0% (337747 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 25.0% (351850 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 26.0% (365884 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 27.0% (379958 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 28.0% (394029 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 29.0% (408102 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 30.0% (422198 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 31.0% (436269 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 32.0% (450358 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 33.0% (464381 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 34.0% (478494 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 35.0% (492527 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 36.0% (506622 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 37.0% (520713 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 38.0% (534736 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 39.0% (548848 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 40.0% (562939 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 41.0% (576982 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 42.0% (591052 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 43.0% (605145 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 44.0% (619194 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 45.0% (633262 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 46.0% (647341 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 47.0% (661440 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 48.0% (675475 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 49.0% (689577 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 50.0% (703642 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 51.0% (717713 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 52.0% (731796 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 53.0% (745851 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 54.0% (759918 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 55.0% (773976 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 56.0% (788083 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 57.0% (802155 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 58.0% (816207 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 59.0% (830281 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 60.0% (844366 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 61.0% (858446 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 62.0% (872505 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 63.0% (886543 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 64.0% (900650 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 65.0% (914691 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 66.0% (928754 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 67.0% (942858 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 68.0% (956909 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 69.0% (970973 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 70.0% (985101 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 71.0% (999131 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 72.0% (1013232 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 73.0% (1027310 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 74.0% (1041346 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 75.0% (1055448 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 76.0% (1069512 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 77.0% (1083601 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 78.0% (1097620 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 79.0% (1111701 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 80.0% (1125761 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 81.0% (1139841 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 82.0% (1153933 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 83.0% (1167979 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 84.0% (1182100 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 85.0% (1196126 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 86.0% (1210223 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 87.0% (1224283 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 88.0% (1238374 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 89.0% (1252468 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 90.0% (1266541 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 91.0% (1280564 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 92.0% (1294670 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 93.0% (1308730 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 94.0% (1322804 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 95.0% (1336856 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 96.0% (1350941 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 97.0% (1365029 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 98.0% (1379080 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2282 contigs and 1970 short contigs 99.0% (1393179 of 1407113), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] Finished reading 22514 contigs and 1 coverages from 03bins/metabat2_fixed/1507992/temp/1507992.depth.txt [8.4Gb / 503.5Gb]. Ignored 18262 too small contigs.                                     
[00:00:00] Number of target contigs: 2282 of large (>= 2000) and 1970 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 2282
[00:00:00] Allocated memory for TNF [8.4Gb / 503.5Gb]
[00:00:00] Calculating TNF 1.7% (39 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 2.2% (51 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 3.7% (84 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 4.3% (98 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 5.3% (120 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 6.1% (140 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 7.6% (173 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 8.3% (189 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 9.6% (219 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 10.8% (246 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 11.4% (260 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 12.2% (279 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 13.7% (312 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 14.4% (329 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 15.1% (345 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 16.8% (383 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 17.5% (399 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 18.3% (417 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 19.4% (443 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 20.2% (460 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 21.8% (497 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 22.2% (506 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 23.8% (542 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 24.4% (557 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 25.9% (592 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 26.4% (603 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 27.6% (630 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 28.4% (647 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 29.7% (678 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 30.5% (697 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 31.3% (714 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 32.8% (748 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 33.6% (766 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 34.4% (784 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 35.9% (819 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 36.7% (837 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 37.4% (853 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 38.3% (875 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 39.7% (906 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 40.5% (925 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 41.3% (943 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 42.9% (980 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 43.6% (995 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 44.4% (1013 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 45.8% (1046 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 46.6% (1063 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 48.0% (1096 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 48.6% (1109 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 50.0% (1142 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 50.9% (1162 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 51.9% (1185 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 52.5% (1199 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 53.4% (1219 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 54.7% (1248 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 56.1% (1280 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 56.7% (1294 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 58.0% (1324 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 58.8% (1341 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 59.9% (1367 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 60.6% (1384 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 61.7% (1409 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 63.0% (1438 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 63.8% (1456 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 64.6% (1474 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 66.1% (1508 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 66.8% (1524 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 67.6% (1542 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 68.7% (1568 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 70.1% (1600 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 70.9% (1619 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 71.7% (1637 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 72.7% (1660 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 74.1% (1692 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 75.1% (1713 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 75.8% (1730 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 77.3% (1763 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 78.0% (1780 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 78.8% (1799 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 80.2% (1831 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 80.9% (1845 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 81.8% (1866 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 83.2% (1899 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 84.0% (1916 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 84.8% (1935 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 86.3% (1970 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 87.1% (1988 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 87.9% (2006 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 89.3% (2037 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 90.0% (2054 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 90.8% (2071 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 92.4% (2108 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 93.0% (2123 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 94.0% (2146 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 94.8% (2163 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 95.7% (2185 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 97.2% (2218 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 98.1% (2239 of 2282), ETA 0:00:00    
[00:00:00] Calculating TNF 98.8% (2255 of 2282), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [8.4Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.5% (35 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 2.7% (61 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 3.4% (77 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 4.0% (92 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 5.4% (123 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 6.1% (139 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 7.4% (170 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 8.2% (186 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 9.5% (217 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 10.2% (233 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 11.5% (263 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 12.2% (278 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 13.5% (308 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 14.2% (324 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 15.6% (356 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 16.3% (372 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 17.6% (402 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 18.3% (418 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 19.7% (449 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 20.3% (464 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 21.7% (495 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 22.4% (511 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 23.8% (543 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.5% (558 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 25.7% (587 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.3% (601 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.7% (632 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 28.4% (648 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.8% (680 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 30.5% (696 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.9% (727 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.6% (743 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 33.3% (759 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.6% (789 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 35.3% (805 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.7% (837 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.3% (852 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 38.7% (884 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.4% (900 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 40.9% (933 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.6% (949 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.5% (969 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.9% (1002 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 44.5% (1015 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 45.9% (1047 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.6% (1063 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.0% (1095 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.6% (1110 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.0% (1140 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.7% (1156 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.0% (1186 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.7% (1202 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.4% (1219 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 54.8% (1251 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.5% (1266 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.9% (1298 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 57.6% (1314 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.0% (1346 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.7% (1362 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.0% (1392 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.7% (1408 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.1% (1440 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.8% (1456 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 64.5% (1473 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 65.8% (1502 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.5% (1518 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 67.9% (1549 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.6% (1565 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 69.9% (1595 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 70.6% (1610 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 71.9% (1641 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 72.6% (1657 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.0% (1688 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.7% (1704 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 76.1% (1736 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 76.8% (1752 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 78.2% (1784 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 78.9% (1800 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 80.2% (1830 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 80.9% (1846 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 82.3% (1877 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.0% (1893 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.7% (1909 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 85.1% (1941 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 85.8% (1957 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 87.2% (1989 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 87.9% (2005 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 89.3% (2038 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 90.1% (2056 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 90.7% (2070 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 92.2% (2103 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 92.8% (2117 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 94.2% (2150 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 95.0% (2167 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 96.4% (2200 of 2282), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 97.0% (2213 of 2282), ETA 0:00:00                   
[00:00:00] Finding cutoff p=999 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=999 2.3% (52 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 5.3% (121 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 9.5% (217 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 12.2% (278 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 15.1% (344 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 16.7% (381 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 18.2% (416 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 19.9% (454 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 21.2% (483 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 22.6% (515 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 23.6% (539 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 25.4% (579 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 26.7% (609 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 28.4% (647 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 29.9% (682 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 31.5% (718 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 32.9% (750 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 34.7% (792 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 37.1% (847 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 39.2% (895 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 40.9% (933 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 42.6% (972 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 44.7% (1019 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 46.5% (1062 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 48.2% (1101 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 49.5% (1130 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 51.2% (1168 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 52.5% (1199 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 54.0% (1233 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 55.5% (1266 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.6% (1292 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 57.8% (1319 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 58.9% (1344 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 60.4% (1379 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 61.6% (1406 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 63.0% (1438 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 64.1% (1463 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 65.6% (1497 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 66.7% (1523 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 67.8% (1548 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 69.1% (1577 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 70.4% (1606 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 71.6% (1634 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 72.6% (1657 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 73.6% (1680 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 76.3% (1741 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 77.1% (1760 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 78.1% (1782 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.1% (1805 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.9% (1824 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 80.8% (1844 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 81.9% (1868 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 82.9% (1892 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 83.8% (1912 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 84.8% (1936 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 85.9% (1961 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 86.9% (1983 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 87.8% (2004 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.7% (2025 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 89.7% (2047 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 90.8% (2072 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 91.8% (2096 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 92.8% (2118 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 94.9% (2165 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 95.8% (2187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 96.9% (2211 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.9; 0 / 2282 (P = 0.00%) round 1]               
[00:00:00] Finding cutoff p=997 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=997 1.4% (31 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 4.8% (109 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 7.9% (181 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 11.5% (262 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 15.1% (344 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 18.8% (429 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 22.5% (513 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 26.4% (602 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 30.3% (692 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 33.6% (766 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 36.5% (833 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 39.9% (910 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 42.6% (973 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 45.3% (1034 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 49.6% (1131 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 52.1% (1190 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 54.6% (1247 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 57.4% (1310 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 59.8% (1365 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 61.8% (1410 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 63.9% (1458 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 65.9% (1503 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 67.8% (1547 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 69.3% (1582 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 70.8% (1616 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 72.2% (1648 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 73.6% (1680 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 74.8% (1708 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 76.0% (1735 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 77.3% (1765 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 78.6% (1793 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 79.9% (1824 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 81.1% (1850 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 81.9% (1869 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 82.9% (1891 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 83.8% (1913 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 84.8% (1934 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 85.7% (1956 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 86.7% (1978 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 87.9% (2006 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 88.9% (2028 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 89.7% (2048 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 90.7% (2070 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 91.7% (2093 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 92.7% (2116 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 93.8% (2140 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 96.8% (2209 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.7; 0 / 2282 (P = 0.00%) round 2]               
[00:00:00] Finding cutoff p=995 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=995 1.5% (34 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 5.4% (123 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 11.8% (269 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 15.4% (351 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 19.2% (439 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 22.8% (521 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 26.7% (609 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 31.2% (713 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 34.6% (790 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 37.6% (857 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 40.8% (932 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 43.4% (991 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 45.7% (1042 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 48.0% (1096 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 50.4% (1150 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 53.0% (1209 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 55.3% (1262 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 58.1% (1325 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 60.8% (1387 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 62.8% (1432 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 64.6% (1474 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 66.5% (1518 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 68.3% (1558 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 69.9% (1594 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 71.5% (1631 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 72.9% (1664 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 74.7% (1704 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 76.5% (1745 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 78.3% (1786 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 79.9% (1823 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 81.4% (1858 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 82.8% (1889 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 83.8% (1913 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 84.7% (1932 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 85.8% (1959 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 86.7% (1978 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 87.8% (2003 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 88.7% (2025 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 89.9% (2051 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 90.8% (2071 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 91.8% (2095 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 92.7% (2116 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 93.8% (2140 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 94.7% (2162 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 96.8% (2209 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.5; 9 / 2282 (P = 0.39%) round 3]               
[00:00:00] Finding cutoff p=993 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=993 1.4% (33 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 5.8% (132 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 10.1% (230 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 14.1% (322 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 18.2% (416 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 22.7% (518 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 27.3% (624 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 32.1% (733 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 37.2% (850 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 40.8% (932 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 44.6% (1018 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 48.1% (1097 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 51.6% (1177 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 54.3% (1239 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 57.0% (1301 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 59.5% (1357 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 61.6% (1405 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 63.9% (1458 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 66.0% (1507 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 68.1% (1554 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 70.0% (1598 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 72.0% (1643 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 73.9% (1686 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 75.6% (1725 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 77.1% (1760 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 78.6% (1794 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 80.1% (1827 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 81.4% (1858 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 82.6% (1886 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 83.8% (1913 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 84.8% (1936 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 85.8% (1957 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 86.9% (1984 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 87.9% (2006 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 88.8% (2027 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 90.0% (2053 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 90.8% (2071 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 91.7% (2093 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 92.9% (2120 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 93.9% (2142 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 94.7% (2162 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.3; 14 / 2282 (P = 0.61%) round 4]               
[00:00:00] Finding cutoff p=991 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=991 1.4% (32 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 5.7% (131 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 9.8% (224 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 14.0% (320 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 17.9% (409 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 22.0% (502 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 26.1% (596 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 30.8% (702 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 35.6% (813 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 39.8% (909 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 43.8% (999 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 47.5% (1083 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 51.1% (1165 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 54.4% (1242 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 57.1% (1304 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 59.9% (1367 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 62.2% (1419 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 64.5% (1471 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 66.6% (1520 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 68.5% (1564 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 70.7% (1614 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 72.7% (1659 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 74.7% (1704 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 76.3% (1742 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 77.9% (1778 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 79.3% (1810 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 81.7% (1865 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 83.0% (1894 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 84.2% (1921 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 85.4% (1948 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 86.4% (1972 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 87.4% (1994 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 88.4% (2018 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 89.3% (2037 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 90.1% (2056 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 90.8% (2073 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 91.9% (2098 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 92.8% (2117 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 93.9% (2143 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.1; 44 / 2282 (P = 1.93%) round 5]               
[00:00:00] Finding cutoff p=989 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=989 1.4% (33 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 5.4% (123 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 9.5% (217 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 13.6% (311 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 17.9% (409 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 22.1% (504 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 26.1% (596 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 30.4% (694 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 34.8% (793 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 39.4% (899 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 42.9% (978 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 46.5% (1060 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 50.4% (1149 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 53.8% (1228 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 56.8% (1296 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 59.3% (1354 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 61.5% (1404 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 63.8% (1456 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 66.0% (1505 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 67.9% (1550 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 71.6% (1635 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 73.5% (1677 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 75.2% (1717 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 76.9% (1754 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 78.4% (1789 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 79.8% (1821 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 81.2% (1853 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 82.5% (1883 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 83.7% (1911 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 84.9% (1938 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 86.2% (1966 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 87.1% (1987 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 88.0% (2008 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 88.9% (2029 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 89.7% (2047 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 90.7% (2070 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 91.9% (2098 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 92.9% (2119 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.9; 80 / 2282 (P = 3.51%) round 6]               
[00:00:00] Finding cutoff p=984 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=984 1.6% (37 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 5.9% (134 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 10.1% (230 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 14.0% (319 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 18.2% (415 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 22.3% (510 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 26.9% (613 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 31.8% (725 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 36.8% (839 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 40.7% (928 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 44.3% (1010 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 47.7% (1089 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 51.3% (1171 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 54.5% (1244 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 60.7% (1386 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 63.1% (1439 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 65.2% (1488 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 67.5% (1541 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 69.8% (1592 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 71.6% (1635 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 73.6% (1679 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 75.2% (1717 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 76.8% (1752 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 78.4% (1788 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 79.8% (1821 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 81.1% (1850 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 82.4% (1880 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 83.6% (1907 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 84.8% (1934 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 86.0% (1962 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 86.9% (1984 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 87.9% (2005 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 88.9% (2029 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 90.1% (2055 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 90.8% (2072 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 91.8% (2095 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 92.7% (2116 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 93.9% (2142 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 95.8% (2187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.4; 186 / 2282 (P = 8.15%) round 7]               
[00:00:00] Finding cutoff p=979 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=979 1.5% (35 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 5.9% (135 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 9.7% (222 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 13.3% (303 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 17.0% (389 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 20.8% (474 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 24.5% (558 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 28.6% (653 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 32.5% (742 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 36.1% (823 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 42.3% (966 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 46.1% (1051 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 49.6% (1133 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 52.7% (1202 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 55.7% (1271 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 58.1% (1326 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 60.3% (1376 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 62.6% (1428 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 64.9% (1480 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 66.7% (1523 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 69.2% (1580 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 71.4% (1629 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 73.9% (1686 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 75.6% (1725 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 77.3% (1765 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 79.3% (1809 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 80.9% (1845 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 82.1% (1874 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 83.3% (1900 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 84.5% (1929 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 85.7% (1956 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 86.9% (1984 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 88.1% (2011 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 89.2% (2035 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 89.9% (2052 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 90.8% (2072 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 91.9% (2097 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 92.7% (2116 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 93.9% (2142 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.9; 308 / 2282 (P = 13.50%) round 8]               
[00:00:00] Finding cutoff p=975 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=975 1.1% (26 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 4.5% (102 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 7.6% (174 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 11.0% (250 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 17.9% (408 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 21.7% (496 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 25.5% (582 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 29.7% (678 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 34.0% (776 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 37.9% (865 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 40.6% (927 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 43.4% (991 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 46.6% (1063 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 49.3% (1125 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 52.0% (1187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 54.7% (1249 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 57.3% (1308 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 60.3% (1375 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 63.3% (1444 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 66.0% (1506 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 68.7% (1567 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 70.4% (1606 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 72.2% (1647 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 73.8% (1683 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 75.6% (1726 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 77.3% (1765 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 79.1% (1805 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 80.7% (1842 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 82.1% (1874 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 83.5% (1906 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 84.9% (1938 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 86.2% (1966 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 87.3% (1992 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 88.5% (2019 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 89.4% (2041 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 90.3% (2060 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 91.1% (2079 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 91.8% (2095 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 92.7% (2116 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 93.8% (2140 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.5; 411 / 2282 (P = 18.01%) round 9]               
[00:00:00] Finding cutoff p=970 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=970 1.2% (28 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 4.9% (112 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 8.0% (182 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 11.5% (262 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 15.3% (349 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 18.6% (424 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 22.5% (513 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 25.9% (592 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 29.5% (674 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 32.8% (748 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 36.9% (843 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 40.7% (928 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 44.4% (1013 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 47.4% (1081 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 49.9% (1139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 52.5% (1197 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 55.0% (1254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 57.4% (1310 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 59.8% (1364 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 62.0% (1414 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 64.0% (1461 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 66.0% (1506 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 68.0% (1551 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 69.7% (1590 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 72.0% (1642 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 75.0% (1711 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 78.5% (1791 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 80.7% (1841 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 82.0% (1871 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 83.1% (1897 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 84.4% (1925 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 85.5% (1950 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 86.4% (1972 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 87.3% (1993 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 88.3% (2014 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 89.0% (2032 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 90.0% (2053 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 90.8% (2071 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 92.1% (2102 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 92.8% (2118 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.0; 530 / 2282 (P = 23.23%) round 10]               
[00:00:00] Finding cutoff p=966 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=966 1.4% (33 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 5.6% (128 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 9.6% (218 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 13.7% (312 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 18.0% (411 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 22.1% (505 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 26.3% (600 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 30.1% (688 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 35.2% (803 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 38.7% (884 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 42.6% (973 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 46.1% (1053 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 49.6% (1132 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 52.7% (1203 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 55.5% (1266 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 58.0% (1324 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 60.7% (1386 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 63.4% (1446 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 65.6% (1498 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 68.0% (1551 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 69.8% (1593 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 71.7% (1636 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 73.6% (1679 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 75.3% (1719 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 77.3% (1763 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 78.7% (1797 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 80.1% (1828 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 81.6% (1863 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 82.9% (1892 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 84.0% (1917 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 86.1% (1964 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 87.1% (1988 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 88.0% (2009 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 89.0% (2031 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 89.7% (2048 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 91.0% (2076 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 92.0% (2099 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 92.8% (2118 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 94.7% (2162 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 95.8% (2187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.6; 621 / 2282 (P = 27.21%) round 11]               
[00:00:00] Finding cutoff p=963 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=963 1.4% (31 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 5.6% (127 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 9.3% (213 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 13.0% (297 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 17.4% (397 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 21.2% (484 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 25.5% (581 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 29.9% (682 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 34.0% (776 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 38.2% (872 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 42.4% (967 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 45.7% (1042 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 48.9% (1116 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 52.1% (1189 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 54.8% (1251 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 57.5% (1312 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 60.1% (1371 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 62.8% (1433 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 65.0% (1483 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 67.1% (1531 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 69.1% (1577 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 71.0% (1620 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 72.8% (1662 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 74.6% (1703 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 77.8% (1775 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 79.2% (1808 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 80.6% (1839 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 81.9% (1869 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 83.1% (1897 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 84.3% (1923 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 85.2% (1945 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 86.2% (1967 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 87.1% (1987 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 88.0% (2008 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 88.9% (2029 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 90.1% (2057 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 90.9% (2074 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 92.0% (2100 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 92.7% (2116 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 95.8% (2187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.3; 690 / 2282 (P = 30.24%) round 12]               
[00:00:00] Finding cutoff p=960 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=960 1.4% (31 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 5.2% (119 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 8.3% (189 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 11.1% (253 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 14.7% (336 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 18.1% (413 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 20.7% (472 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 23.8% (543 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 26.9% (614 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 30.6% (699 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 34.0% (777 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 37.8% (862 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 41.3% (943 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 44.4% (1014 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 47.6% (1086 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 50.0% (1141 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 53.2% (1215 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 61.6% (1405 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 63.9% (1459 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 66.2% (1511 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 68.4% (1562 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 70.6% (1611 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 72.8% (1661 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 74.7% (1704 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 77.0% (1756 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 78.7% (1795 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 80.5% (1838 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 82.1% (1874 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 83.4% (1904 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 84.7% (1932 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 85.8% (1959 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 86.7% (1978 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 88.1% (2010 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 89.0% (2032 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 89.8% (2049 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 91.0% (2076 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 91.8% (2096 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 92.9% (2121 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 93.8% (2141 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.0; 753 / 2282 (P = 33.00%) round 13]               
[00:00:00] Finding cutoff p=956 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=956 1.4% (32 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 5.6% (127 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 8.7% (198 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 12.3% (281 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 16.8% (383 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 20.6% (471 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 24.5% (559 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 28.4% (649 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 32.0% (731 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 35.5% (810 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 38.9% (887 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 45.4% (1036 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 49.3% (1124 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 52.7% (1202 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 55.8% (1273 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 58.9% (1343 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 62.3% (1422 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 64.8% (1479 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 66.9% (1527 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 68.9% (1572 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 70.9% (1618 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 72.5% (1655 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 74.1% (1691 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 75.6% (1726 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 77.3% (1763 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 78.7% (1796 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 80.2% (1831 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 81.7% (1864 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 83.0% (1895 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 84.5% (1929 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 85.6% (1954 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 86.6% (1977 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 87.5% (1997 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 88.4% (2018 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 89.3% (2037 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 90.0% (2053 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 90.9% (2074 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 92.0% (2099 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 92.8% (2117 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 93.8% (2141 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 94.9% (2165 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 95.8% (2187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.6; 817 / 2282 (P = 35.80%) round 14]               
[00:00:00] Finding cutoff p=952 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=952 1.4% (31 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 5.2% (119 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 8.6% (197 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 12.2% (279 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 19.0% (434 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 22.9% (522 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 26.8% (611 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 30.2% (690 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 34.1% (779 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 38.7% (883 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 42.1% (961 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 45.8% (1045 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 48.9% (1115 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 51.9% (1184 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 54.7% (1249 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 57.1% (1302 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 60.0% (1369 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 62.5% (1427 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 64.9% (1481 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 67.0% (1529 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 69.1% (1578 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 71.2% (1624 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 72.9% (1663 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 74.7% (1705 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 76.6% (1747 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 78.2% (1784 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 79.7% (1819 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 81.1% (1851 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 82.5% (1882 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 83.8% (1912 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 85.0% (1940 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 86.3% (1970 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 87.2% (1989 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 88.1% (2010 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 88.9% (2029 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 89.7% (2047 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 90.7% (2070 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 91.9% (2097 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 92.7% (2116 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 94.8% (2163 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 95.8% (2187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=952 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.2; 886 / 2282 (P = 38.83%) round 15]               
[00:00:00] Finding cutoff p=949 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=949 1.3% (30 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 5.2% (119 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 8.2% (186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 11.7% (267 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 16.3% (373 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 20.2% (460 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 24.6% (561 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 28.4% (649 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 32.3% (738 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 37.0% (844 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 40.4% (922 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 44.0% (1004 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 46.9% (1070 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 50.0% (1140 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 52.8% (1204 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 55.2% (1259 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 57.4% (1309 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 60.0% (1369 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 62.3% (1422 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 64.6% (1475 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 67.0% (1530 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 69.1% (1577 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 71.0% (1620 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 72.6% (1656 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 74.6% (1703 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 76.1% (1737 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 77.6% (1770 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 79.3% (1809 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 80.6% (1840 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 82.0% (1872 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 83.3% (1900 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 84.4% (1925 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 85.5% (1951 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 86.5% (1975 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 87.7% (2002 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 88.7% (2024 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 89.8% (2049 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 90.8% (2073 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 92.0% (2100 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 92.8% (2118 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 93.8% (2141 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 94.9% (2165 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 95.8% (2187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=949 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.9; 929 / 2282 (P = 40.71%) round 16]               
[00:00:00] Finding cutoff p=944 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=944 1.3% (29 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 5.2% (118 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 8.3% (189 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 12.2% (278 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 17.4% (397 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 21.2% (484 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 24.8% (567 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 28.7% (654 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 32.4% (739 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 36.6% (835 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 39.9% (911 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 43.2% (986 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 46.8% (1067 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 49.5% (1129 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 52.1% (1190 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 55.0% (1255 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 57.4% (1310 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 60.1% (1371 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 62.5% (1426 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 64.7% (1476 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 66.9% (1527 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 69.0% (1574 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 70.7% (1613 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 72.5% (1654 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 74.8% (1706 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 76.1% (1737 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 77.6% (1770 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 79.0% (1803 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 80.5% (1836 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 81.6% (1862 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 83.3% (1902 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 84.4% (1927 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 85.5% (1951 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 86.5% (1975 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 87.7% (2001 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 89.3% (2038 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 90.1% (2057 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 91.3% (2084 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 92.1% (2102 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 92.9% (2119 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 94.7% (2162 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.4; 1016 / 2282 (P = 44.52%) round 17]               
[00:00:00] Finding cutoff p=940 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=940 1.4% (32 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 5.5% (126 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 8.4% (191 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 12.0% (274 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 16.8% (383 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 20.4% (466 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 24.0% (547 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 27.3% (622 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 30.8% (702 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 34.1% (779 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 37.9% (864 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 41.2% (941 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 45.0% (1026 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 47.9% (1093 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 50.6% (1154 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 53.1% (1211 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 56.4% (1287 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 58.8% (1341 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 61.1% (1395 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 63.9% (1458 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 66.6% (1520 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 69.2% (1579 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 71.8% (1639 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 73.4% (1676 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 75.5% (1722 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 76.9% (1754 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 78.3% (1787 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 79.6% (1816 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 80.7% (1841 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 81.9% (1869 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 83.0% (1894 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 84.1% (1920 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 85.1% (1943 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 86.0% (1962 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 86.9% (1984 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 88.3% (2015 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 89.0% (2031 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 89.7% (2047 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 90.8% (2071 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 91.8% (2096 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 92.9% (2119 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 94.0% (2144 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.0; 1080 / 2282 (P = 47.33%) round 18]               
[00:00:00] Finding cutoff p=937 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=937 1.4% (31 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 4.9% (111 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 7.5% (172 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 11.6% (264 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 15.8% (360 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 19.0% (434 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 22.4% (512 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 25.9% (590 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 29.2% (667 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 32.6% (743 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 36.6% (835 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 39.8% (908 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 44.1% (1007 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 50.1% (1143 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 52.8% (1205 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 55.7% (1271 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 58.1% (1326 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 60.2% (1374 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 62.8% (1432 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 65.2% (1487 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 67.5% (1540 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 69.7% (1591 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 71.8% (1638 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 73.1% (1669 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 75.4% (1720 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 76.7% (1750 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 78.3% (1786 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 79.9% (1823 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 81.3% (1856 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 82.7% (1887 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 83.9% (1914 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 85.1% (1942 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 86.2% (1968 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 87.1% (1988 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 87.9% (2007 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 88.7% (2025 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 89.8% (2050 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 90.9% (2075 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 91.9% (2098 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 92.7% (2116 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 94.0% (2144 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 94.7% (2162 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 95.9% (2188 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.7; 1118 / 2282 (P = 48.99%) round 19]               
[00:00:00] Finding cutoff p=933 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=933 1.3% (29 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 4.8% (109 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 7.4% (169 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 11.0% (250 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 15.1% (345 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 20.6% (470 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 24.1% (551 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 27.1% (619 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 30.4% (693 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 33.5% (764 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 37.0% (844 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 40.7% (929 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 44.6% (1017 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 47.2% (1078 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 50.4% (1149 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 52.9% (1208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 56.0% (1277 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 58.0% (1324 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 60.3% (1377 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 62.3% (1422 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 65.0% (1483 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 67.5% (1541 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 69.4% (1584 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 71.3% (1626 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 73.4% (1676 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 75.2% (1715 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 76.6% (1748 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 78.1% (1783 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 79.8% (1820 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 81.2% (1854 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 82.4% (1880 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 83.5% (1905 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 84.8% (1936 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 85.8% (1959 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 86.7% (1979 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 88.5% (2019 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 89.3% (2038 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 90.2% (2058 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 91.0% (2076 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 91.8% (2096 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 92.9% (2120 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 94.7% (2162 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 95.9% (2188 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.3; 1180 / 2282 (P = 51.71%) round 20]               
[00:00:00] Finding cutoff p=930 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=930 1.3% (29 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 5.0% (114 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 7.4% (168 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 10.9% (249 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 15.1% (344 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 18.4% (419 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 21.2% (484 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 24.2% (552 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 27.4% (626 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 29.8% (680 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 33.3% (761 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 36.5% (834 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 40.2% (918 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 43.4% (990 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 46.4% (1058 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 49.1% (1121 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 51.9% (1185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 54.9% (1252 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 57.5% (1312 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 59.4% (1355 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 62.0% (1414 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 64.9% (1480 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 66.8% (1524 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 69.0% (1574 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 72.2% (1648 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 73.8% (1685 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 75.9% (1732 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 77.8% (1775 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 79.4% (1813 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 81.1% (1850 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 82.3% (1879 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 83.4% (1903 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 84.6% (1931 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 85.8% (1957 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 86.8% (1981 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 88.3% (2014 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 89.2% (2035 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 90.6% (2068 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 91.3% (2083 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 92.0% (2100 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 92.8% (2118 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 93.8% (2141 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 94.8% (2163 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 95.8% (2187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.0; 1221 / 2282 (P = 53.51%) round 21]               
[00:00:00] Finding cutoff p=927 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=927 1.3% (29 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 4.8% (109 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 7.4% (168 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 10.4% (238 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 14.3% (327 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 17.6% (402 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 20.4% (465 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 23.8% (544 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 27.3% (623 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 30.2% (690 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 32.6% (743 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 35.8% (817 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 39.5% (901 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 42.5% (969 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 45.1% (1029 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 47.7% (1088 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 50.4% (1149 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 53.4% (1219 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 55.4% (1265 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 57.5% (1312 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 59.8% (1365 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 62.8% (1434 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 64.6% (1474 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 66.5% (1517 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 69.7% (1590 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 71.6% (1633 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 73.4% (1674 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 75.1% (1714 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 77.5% (1768 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 79.3% (1809 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 80.7% (1842 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 81.9% (1870 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 83.0% (1894 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 84.1% (1919 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 85.1% (1942 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 86.3% (1969 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 87.6% (1999 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 88.4% (2017 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 89.6% (2044 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 90.3% (2061 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 91.1% (2080 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 91.9% (2098 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 92.9% (2120 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 93.9% (2143 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.7; 1253 / 2282 (P = 54.91%) round 22]               
[00:00:00] Finding cutoff p=922 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=922 1.2% (28 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 4.7% (107 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 7.1% (163 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 10.9% (249 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 15.4% (351 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 18.2% (415 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 21.2% (484 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 23.8% (544 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 26.8% (611 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 30.0% (684 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 32.6% (743 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 35.0% (798 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 39.4% (899 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 41.7% (951 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 44.5% (1015 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 47.5% (1083 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 51.0% (1164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 55.2% (1259 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 57.2% (1305 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 59.2% (1352 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 62.1% (1417 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 65.0% (1483 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 66.7% (1521 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 68.4% (1561 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 70.9% (1618 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 72.8% (1662 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 75.0% (1711 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 77.0% (1757 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 78.3% (1786 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 80.0% (1826 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 81.1% (1850 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 82.2% (1876 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 83.6% (1908 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 84.8% (1935 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 86.1% (1965 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 86.9% (1983 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 88.2% (2012 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 89.0% (2030 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 90.0% (2054 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 90.8% (2072 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 91.7% (2093 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 92.8% (2117 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 95.0% (2169 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 95.9% (2188 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.2; 1328 / 2282 (P = 58.19%) round 23]               
[00:00:00] Finding cutoff p=918 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=918 1.2% (27 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 4.5% (102 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 6.6% (150 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 9.3% (212 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 13.3% (303 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 16.3% (372 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 19.1% (437 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 24.3% (555 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 27.5% (627 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 30.6% (698 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 33.8% (771 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 36.7% (837 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 40.5% (925 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 43.4% (991 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 46.0% (1049 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 48.9% (1116 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 51.6% (1177 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 54.3% (1240 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 55.9% (1276 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 58.3% (1331 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 60.6% (1384 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 63.6% (1452 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 65.4% (1492 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 67.4% (1538 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 70.4% (1607 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 72.0% (1643 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 73.8% (1684 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 76.0% (1734 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 77.7% (1773 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 79.1% (1806 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 80.4% (1835 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 81.5% (1860 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 82.9% (1891 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 84.0% (1918 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 85.1% (1943 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 86.4% (1972 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 88.0% (2009 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 88.9% (2029 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 90.0% (2053 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 90.9% (2074 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 91.7% (2093 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 92.9% (2120 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 93.8% (2141 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 94.8% (2163 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.8; 1388 / 2282 (P = 60.82%) round 24]               
[00:00:00] Finding cutoff p=915 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=915 1.2% (28 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 4.6% (105 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 6.7% (154 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 9.3% (213 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 12.5% (285 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 15.6% (357 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 18.0% (411 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 20.6% (469 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 23.4% (534 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 25.7% (586 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 28.4% (649 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 31.1% (709 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 34.6% (790 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 37.3% (851 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 39.8% (909 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 42.2% (964 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 45.7% (1042 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 48.0% (1095 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 50.5% (1152 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 52.5% (1197 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 54.6% (1246 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 56.8% (1296 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 58.7% (1340 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 60.8% (1387 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 63.4% (1447 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 65.8% (1501 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 67.9% (1550 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 69.9% (1595 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 71.9% (1641 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 74.0% (1689 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 75.3% (1719 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 76.9% (1754 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 78.4% (1789 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 79.7% (1819 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 80.7% (1842 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 82.0% (1871 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 83.9% (1914 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 85.0% (1939 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 87.3% (1993 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 88.4% (2017 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 89.2% (2036 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 89.9% (2052 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 91.0% (2076 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 91.8% (2095 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 92.8% (2118 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 93.9% (2143 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 94.9% (2166 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 95.8% (2187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.5; 1434 / 2282 (P = 62.84%) round 25]               
[00:00:00] Finding cutoff p=911 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=911 1.2% (27 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 4.2% (95 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 6.2% (141 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 9.2% (209 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 12.4% (283 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 15.2% (348 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 17.3% (395 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 19.3% (441 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 22.2% (507 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 25.1% (572 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 27.5% (628 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 30.2% (690 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 33.6% (767 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 36.2% (825 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 38.7% (882 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 41.1% (938 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 44.0% (1004 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 46.5% (1060 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 48.6% (1109 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 51.0% (1163 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 53.5% (1220 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 55.6% (1269 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 58.5% (1334 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 60.4% (1378 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 64.1% (1463 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 66.6% (1520 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 70.2% (1603 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 72.0% (1644 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 73.7% (1681 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 75.4% (1721 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 76.7% (1750 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 77.9% (1778 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 79.2% (1807 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 80.5% (1838 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 82.0% (1871 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 83.0% (1895 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 84.8% (1936 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 86.6% (1976 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 87.5% (1997 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 88.4% (2018 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 89.1% (2033 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 89.8% (2049 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 90.8% (2073 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 91.9% (2098 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 92.9% (2120 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 93.9% (2142 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.1; 1481 / 2282 (P = 64.90%) round 26]               
[00:00:00] Finding cutoff p=908 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=908 1.2% (28 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 4.4% (101 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 6.5% (149 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 9.3% (212 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 12.9% (294 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 15.8% (361 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 18.4% (419 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 20.8% (475 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 23.2% (530 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 25.5% (581 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 28.0% (640 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 30.8% (703 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 34.1% (778 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 38.2% (871 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 40.4% (921 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 42.7% (974 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 45.8% (1045 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 48.6% (1109 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 51.6% (1177 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 53.5% (1221 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 55.3% (1262 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 57.8% (1320 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 59.4% (1355 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 61.3% (1398 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 64.5% (1473 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 67.0% (1528 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 69.1% (1578 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 71.4% (1630 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 73.4% (1675 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 75.5% (1723 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 77.0% (1756 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 78.4% (1789 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 79.4% (1813 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 80.6% (1840 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 81.9% (1868 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 83.1% (1896 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 84.8% (1935 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 86.0% (1962 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 87.3% (1992 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 88.0% (2009 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 89.0% (2030 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 89.8% (2049 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 91.0% (2077 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 91.8% (2095 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 92.9% (2121 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 93.9% (2142 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 94.9% (2166 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.8; 1513 / 2282 (P = 66.30%) round 27]               
[00:00:00] Finding cutoff p=905 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=905 1.1% (26 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 4.0% (92 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 6.1% (140 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 8.9% (204 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 12.9% (295 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 16.6% (378 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 18.8% (430 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 21.6% (493 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 24.0% (547 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 26.4% (602 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 28.7% (656 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 31.6% (722 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 35.6% (812 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 38.3% (874 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 40.5% (924 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 42.8% (977 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 45.3% (1034 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 48.0% (1096 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 50.4% (1149 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 52.5% (1198 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 54.2% (1237 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 56.7% (1293 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 58.3% (1330 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 60.4% (1379 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 63.5% (1448 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 65.8% (1502 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 67.9% (1550 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 69.7% (1590 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 71.1% (1622 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 73.1% (1669 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 74.8% (1708 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 76.1% (1737 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 77.7% (1773 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 79.1% (1805 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 80.4% (1835 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 81.7% (1865 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 83.4% (1904 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 84.4% (1927 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 86.0% (1962 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 86.9% (1983 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 87.9% (2006 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 89.5% (2043 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 90.3% (2060 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 90.9% (2074 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 91.8% (2095 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 93.0% (2122 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 94.9% (2165 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.5; 1547 / 2282 (P = 67.79%) round 28]               
[00:00:00] Finding cutoff p=900 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=900 1.2% (28 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 3.8% (86 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 6.1% (139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 9.1% (208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 12.7% (289 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 15.7% (358 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 18.6% (424 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 20.9% (477 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 23.4% (535 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 26.1% (595 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 27.6% (629 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 30.6% (699 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 33.4% (762 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 35.5% (810 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 37.5% (856 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 40.0% (912 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 42.9% (979 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 45.9% (1047 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 48.4% (1105 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 50.7% (1156 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 52.9% (1207 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 55.4% (1264 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 57.4% (1310 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 58.9% (1344 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 61.7% (1408 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 64.0% (1460 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 66.1% (1509 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 69.5% (1586 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 71.8% (1638 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 73.9% (1687 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 74.7% (1704 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 76.0% (1734 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 77.3% (1765 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 78.5% (1791 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 79.8% (1822 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 80.7% (1841 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 82.5% (1882 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 83.6% (1907 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 85.2% (1945 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 86.1% (1965 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 87.3% (1992 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 88.5% (2019 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 89.3% (2037 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 90.1% (2055 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 91.0% (2077 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 91.8% (2094 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 92.8% (2117 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 94.9% (2165 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 95.8% (2187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.0; 1594 / 2282 (P = 69.85%) round 29]               
[00:00:00] Finding cutoff p=891 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=891 1.3% (30 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 4.4% (100 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 6.3% (144 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 9.4% (215 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 13.4% (306 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 16.6% (379 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 19.2% (438 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 21.5% (491 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 23.9% (546 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 25.9% (591 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 28.2% (644 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 31.9% (728 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 37.7% (860 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 39.5% (901 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 41.6% (949 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 43.9% (1002 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 47.0% (1072 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 50.3% (1148 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 52.7% (1202 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 54.6% (1246 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 57.0% (1300 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 59.4% (1355 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 60.9% (1389 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 63.0% (1437 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 66.0% (1507 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 68.3% (1558 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 70.0% (1598 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 72.1% (1645 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 74.0% (1688 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 76.0% (1734 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 77.0% (1758 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 78.1% (1783 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 79.6% (1816 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 80.7% (1842 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 82.0% (1872 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 83.0% (1893 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 84.6% (1931 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 85.5% (1952 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 87.1% (1987 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 88.1% (2011 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 89.0% (2030 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 89.9% (2052 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 90.8% (2073 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 91.8% (2096 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 92.8% (2118 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 93.8% (2141 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 94.8% (2163 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=891 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.1; 1616 / 2282 (P = 70.82%) round 30]               
[00:00:00] Finding cutoff p=882 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=882 1.1% (24 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 3.6% (83 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 5.2% (119 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 7.4% (168 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 10.6% (242 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 12.4% (284 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 14.6% (334 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 16.7% (381 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 18.7% (427 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 20.9% (478 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 23.3% (531 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 25.6% (585 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 29.1% (663 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 31.2% (711 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 33.3% (761 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 35.8% (816 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 38.0% (868 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 40.3% (920 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 42.6% (972 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 45.1% (1030 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 47.9% (1092 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 50.3% (1148 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 52.6% (1201 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 54.3% (1240 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 57.6% (1315 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 60.0% (1369 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 62.3% (1421 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 65.0% (1484 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 66.8% (1524 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 69.1% (1577 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 70.6% (1611 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 72.0% (1644 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 73.2% (1671 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 74.9% (1710 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 76.3% (1741 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 77.9% (1777 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 79.9% (1823 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 81.3% (1855 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 83.2% (1899 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 84.6% (1931 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 86.3% (1970 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 87.4% (1995 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 88.4% (2017 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 89.3% (2038 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 90.1% (2057 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 91.1% (2078 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 92.0% (2100 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 92.8% (2117 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 94.1% (2148 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 95.0% (2169 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=882 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 88.2; 1656 / 2282 (P = 72.57%) round 31]               
[00:00:00] Finding cutoff p=873 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=873 1.1% (24 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 3.4% (77 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 5.8% (132 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 8.3% (190 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 11.8% (270 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 14.1% (322 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 16.9% (385 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 18.9% (432 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 21.2% (484 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 23.6% (538 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 26.5% (604 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 29.3% (669 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 33.0% (753 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 35.0% (798 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 37.1% (847 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 39.4% (899 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 41.8% (954 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 44.1% (1006 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 46.9% (1071 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 48.9% (1115 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 51.1% (1165 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 54.0% (1233 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 55.9% (1276 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 57.8% (1319 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 61.0% (1391 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 63.3% (1444 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 65.4% (1493 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 67.8% (1548 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 70.3% (1604 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 72.3% (1649 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 73.6% (1680 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 75.0% (1711 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 76.5% (1746 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 77.5% (1768 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 78.7% (1795 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 79.9% (1824 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 81.7% (1865 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 82.9% (1891 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 84.4% (1927 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 85.4% (1949 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 86.7% (1978 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 87.9% (2007 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 89.0% (2032 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 89.9% (2052 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 90.7% (2070 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 91.9% (2097 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 92.8% (2118 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 94.0% (2145 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 94.9% (2166 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=873 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 87.3; 1709 / 2282 (P = 74.89%) round 32]               
[00:00:00] Finding cutoff p=864 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=864 1.2% (28 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 3.7% (85 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 5.8% (133 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 7.9% (180 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 12.1% (277 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 14.5% (331 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 16.5% (377 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 19.0% (434 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 21.3% (485 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 24.9% (568 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 26.9% (614 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 29.7% (678 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 32.8% (749 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 35.2% (803 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 37.1% (847 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 39.1% (893 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 42.1% (960 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 45.0% (1026 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 46.8% (1069 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 49.6% (1132 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 51.8% (1181 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 54.5% (1244 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 56.3% (1284 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 58.0% (1323 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 60.5% (1381 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 62.5% (1426 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 65.0% (1483 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 67.2% (1534 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 69.3% (1582 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 71.0% (1620 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 72.4% (1653 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 74.0% (1688 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 76.0% (1735 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 76.9% (1754 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 78.4% (1789 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 80.1% (1827 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 82.0% (1872 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 82.9% (1892 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 84.3% (1923 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 85.5% (1950 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 86.6% (1976 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 88.0% (2008 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 88.7% (2025 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 90.1% (2055 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 90.9% (2074 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 92.1% (2102 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 92.9% (2120 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 95.0% (2167 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=864 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 86.4; 1750 / 2282 (P = 76.69%) round 33]               
[00:00:00] Finding cutoff p=853 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=853 1.1% (26 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 3.4% (77 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 5.7% (131 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 8.5% (194 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 12.8% (291 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 15.4% (352 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 17.8% (407 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 20.3% (464 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 22.5% (513 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 24.7% (564 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 26.7% (609 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 29.8% (680 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 33.5% (765 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 35.5% (809 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 37.2% (850 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 39.4% (898 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 41.8% (955 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 44.5% (1015 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 46.9% (1070 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 48.8% (1114 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 51.5% (1176 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 54.2% (1237 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 55.9% (1276 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 57.6% (1315 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 60.5% (1381 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 62.5% (1426 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 64.6% (1474 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 66.8% (1525 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 68.9% (1572 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 70.8% (1615 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 72.7% (1658 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 74.0% (1688 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 75.6% (1726 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 76.9% (1755 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 78.2% (1784 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 80.5% (1836 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 82.2% (1876 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 83.3% (1902 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 84.9% (1937 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 85.8% (1957 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 86.8% (1980 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 88.2% (2012 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 89.1% (2034 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 89.8% (2050 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 91.0% (2077 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 91.9% (2097 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 92.9% (2120 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 94.0% (2145 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 94.7% (2162 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.3; 1810 / 2282 (P = 79.32%) round 34]               
[00:00:00] Finding cutoff p=842 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=842 1.3% (29 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 3.3% (76 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 5.6% (127 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 7.6% (174 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 10.3% (234 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 12.5% (285 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 14.7% (336 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 16.8% (383 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 18.5% (422 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 20.5% (468 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 22.7% (517 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 25.2% (575 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 28.6% (653 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 31.1% (710 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 34.1% (778 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 35.8% (818 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 38.7% (884 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 41.9% (956 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 43.6% (994 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 45.4% (1036 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 50.1% (1143 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 52.5% (1199 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 54.3% (1240 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 56.3% (1285 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 59.0% (1347 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 60.7% (1385 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 62.6% (1429 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 65.3% (1490 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 67.2% (1534 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 68.9% (1572 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 70.2% (1602 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 72.0% (1642 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 73.4% (1675 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 74.8% (1708 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 75.9% (1731 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 77.3% (1763 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 79.4% (1813 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 80.4% (1834 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 82.4% (1880 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 83.3% (1900 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 84.4% (1926 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 85.6% (1954 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 86.6% (1977 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 87.5% (1996 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 88.1% (2011 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 89.0% (2031 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 89.7% (2047 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 90.9% (2075 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 92.1% (2101 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 92.8% (2117 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 94.0% (2146 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=842 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.2; 1867 / 2282 (P = 81.81%) round 35]               
[00:00:00] Finding cutoff p=832 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=832 1.1% (26 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 2.8% (65 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 6.1% (140 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 8.9% (203 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 12.1% (277 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 14.9% (341 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 17.5% (399 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 20.6% (471 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 22.7% (518 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 25.0% (571 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 27.1% (619 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 29.3% (668 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 32.9% (750 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 34.8% (793 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 36.5% (833 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 38.3% (875 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 41.2% (941 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 43.1% (984 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 45.2% (1032 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 47.1% (1075 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 48.9% (1117 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 51.3% (1170 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 53.2% (1214 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 54.8% (1251 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 57.5% (1313 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 59.6% (1361 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 61.7% (1407 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 64.2% (1466 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 66.0% (1505 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 67.7% (1546 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 69.0% (1575 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 70.6% (1611 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 72.4% (1652 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 73.2% (1671 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 74.5% (1701 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 76.0% (1735 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 78.0% (1779 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 78.8% (1798 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 80.5% (1838 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 81.7% (1864 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 83.0% (1893 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 84.0% (1918 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 85.3% (1947 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 86.6% (1976 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 88.5% (2020 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 89.3% (2038 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 90.1% (2056 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 91.0% (2077 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 91.7% (2093 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 92.8% (2117 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 93.8% (2140 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 95.8% (2187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 83.2; 1915 / 2282 (P = 83.92%) round 36]               
[00:00:00] Finding cutoff p=822 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=822 1.0% (23 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 2.8% (64 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 4.8% (110 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 6.5% (149 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 9.9% (225 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 11.6% (265 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 14.4% (328 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 15.8% (360 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 18.0% (411 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 19.5% (446 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 21.8% (497 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 24.4% (556 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 27.9% (636 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 30.0% (684 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 32.8% (749 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 34.7% (792 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 37.7% (861 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 40.1% (916 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 41.5% (946 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 43.4% (990 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 45.4% (1035 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 48.2% (1099 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 49.9% (1138 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 51.7% (1180 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 54.6% (1247 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 56.2% (1282 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 59.6% (1360 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 62.1% (1417 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 64.1% (1463 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 66.4% (1515 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 67.6% (1543 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 69.0% (1575 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 70.2% (1602 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 71.5% (1631 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 73.2% (1670 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 74.7% (1704 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 77.1% (1760 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 78.2% (1785 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 80.0% (1826 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 81.5% (1859 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 82.4% (1881 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 83.7% (1909 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 84.8% (1935 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 86.2% (1966 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 86.9% (1983 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 87.9% (2005 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 88.7% (2025 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 90.0% (2053 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 91.1% (2079 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 91.8% (2095 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 92.9% (2121 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 94.0% (2146 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 95.9% (2188 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.2; 1957 / 2282 (P = 85.76%) round 37]               
[00:00:00] Finding cutoff p=812 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=812 1.7% (38 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 3.5% (80 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 5.8% (132 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 8.2% (187 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 10.2% (232 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 12.1% (277 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 14.2% (325 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 17.6% (402 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 19.5% (445 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 20.7% (473 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 23.2% (529 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 26.3% (600 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 28.0% (640 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 29.9% (683 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 32.5% (741 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 34.8% (793 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 37.7% (860 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 39.4% (898 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 41.3% (943 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 43.6% (995 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 46.3% (1057 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 47.9% (1093 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 50.5% (1152 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 53.3% (1217 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 55.2% (1260 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 56.9% (1298 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 59.4% (1355 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 60.9% (1390 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 63.1% (1439 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 64.3% (1468 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 65.5% (1495 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 67.0% (1528 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 68.0% (1552 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 69.2% (1579 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 70.9% (1619 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 73.5% (1678 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 74.7% (1705 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 77.2% (1762 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 78.7% (1795 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 79.7% (1818 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 80.9% (1845 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 82.0% (1871 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 82.8% (1890 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 83.8% (1913 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 84.8% (1934 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 85.9% (1960 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 86.8% (1980 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 87.7% (2001 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 88.9% (2028 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 89.7% (2047 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 90.9% (2075 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 91.7% (2093 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 93.3% (2128 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 93.8% (2141 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 95.1% (2171 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 96.9% (2212 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=812 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 81.2; 1991 / 2282 (P = 87.25%) round 38]               
[00:00:00] Finding cutoff p=802 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=802 1.3% (30 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 2.7% (61 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 4.7% (108 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 6.6% (151 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 10.0% (229 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 11.7% (266 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 13.7% (313 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 16.0% (365 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 17.6% (401 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 19.4% (442 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 21.0% (480 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 24.3% (555 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 28.2% (643 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 30.2% (689 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 31.9% (728 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 33.7% (768 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 36.0% (822 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 38.6% (880 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 40.1% (915 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 41.9% (956 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 44.5% (1016 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 46.5% (1061 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 48.1% (1098 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 49.7% (1134 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 52.7% (1203 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 54.6% (1247 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 56.5% (1290 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 59.4% (1355 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 61.7% (1409 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 63.6% (1452 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 64.8% (1478 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 66.0% (1505 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 67.3% (1535 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 68.4% (1560 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 69.7% (1591 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 70.7% (1613 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 73.4% (1675 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 74.2% (1693 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 76.5% (1745 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 77.3% (1765 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 78.7% (1797 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 79.9% (1824 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 81.0% (1849 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 82.1% (1873 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 83.1% (1896 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 83.8% (1913 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 85.1% (1943 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 85.8% (1959 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 86.7% (1979 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 88.5% (2019 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 89.6% (2044 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 91.1% (2078 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 92.5% (2111 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 93.2% (2127 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 95.1% (2171 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 96.8% (2209 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=802 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.2; 2019 / 2282 (P = 88.48%) round 39]               
[00:00:00] Finding cutoff p=793 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=793 1.2% (27 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 2.5% (57 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 3.9% (89 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 6.2% (141 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 8.7% (199 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 12.1% (277 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 14.3% (326 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 15.5% (354 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 17.7% (404 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 19.3% (441 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 21.0% (480 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 23.3% (531 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 25.9% (591 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 27.6% (630 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 29.5% (674 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 31.7% (723 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 35.6% (812 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 38.3% (874 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 39.9% (910 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 41.5% (947 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 43.3% (989 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 45.3% (1034 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 46.9% (1070 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 49.1% (1120 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 52.4% (1195 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 54.4% (1241 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 55.3% (1262 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 57.7% (1317 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 59.5% (1358 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 61.2% (1396 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 62.4% (1423 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 63.7% (1453 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 65.1% (1485 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 66.3% (1513 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 67.5% (1541 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 68.6% (1566 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 71.0% (1621 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 72.3% (1649 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 73.9% (1687 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 75.1% (1714 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 76.0% (1735 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 77.8% (1776 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 79.1% (1805 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 80.2% (1831 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 81.1% (1851 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 82.2% (1875 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 83.3% (1900 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 84.2% (1922 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 84.8% (1936 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 85.8% (1958 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 86.8% (1981 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 87.7% (2002 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 89.1% (2033 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 90.2% (2059 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 91.6% (2091 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 92.9% (2120 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 93.7% (2139 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 94.8% (2163 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 95.9% (2188 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 96.9% (2211 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=793 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 79.3; 2034 / 2282 (P = 89.13%) round 40]               
[00:00:00] Finding cutoff p=782 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=782 1.4% (31 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 3.1% (71 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 5.0% (114 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 7.1% (161 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 9.5% (217 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 10.7% (244 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 12.6% (288 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 14.7% (335 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 15.9% (362 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 17.6% (402 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 18.9% (431 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 21.1% (481 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 24.3% (554 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 25.9% (592 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 27.4% (625 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 29.4% (672 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 31.8% (726 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 34.0% (776 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 35.7% (815 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 37.6% (859 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 39.3% (897 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 41.3% (942 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 43.6% (996 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 45.9% (1047 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 48.7% (1112 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 50.7% (1156 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 52.5% (1197 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 54.4% (1241 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 56.2% (1283 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 57.8% (1319 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 58.9% (1345 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 59.9% (1367 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 61.3% (1399 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 62.8% (1432 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 63.9% (1458 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 65.3% (1490 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 67.7% (1545 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 69.8% (1592 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 70.9% (1617 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 71.9% (1641 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 73.6% (1680 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 74.9% (1710 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 76.2% (1740 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 77.2% (1761 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 78.0% (1781 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 79.3% (1809 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 80.5% (1836 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 81.4% (1857 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 82.1% (1873 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 82.7% (1887 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 83.7% (1910 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 85.1% (1943 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 86.1% (1965 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 87.6% (1998 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 89.4% (2041 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 90.3% (2060 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 90.8% (2073 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 92.0% (2100 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 93.1% (2124 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 93.8% (2140 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 95.0% (2167 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 95.7% (2185 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=782 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.2; 2052 / 2282 (P = 89.92%) round 41]               
[00:00:00] Finding cutoff p=771 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=771 1.0% (23 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 2.1% (49 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 3.9% (90 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 6.7% (152 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 9.2% (209 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 10.7% (244 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 13.0% (296 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 14.7% (336 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 16.6% (378 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 18.6% (425 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 20.3% (463 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 23.0% (526 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 26.7% (609 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 28.0% (639 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 29.4% (672 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 30.5% (696 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 32.6% (743 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 34.8% (795 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 35.9% (820 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 37.9% (864 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 40.6% (927 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 42.9% (979 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 44.4% (1013 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 46.4% (1059 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 49.6% (1131 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 51.4% (1173 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 53.5% (1220 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 55.9% (1275 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 57.4% (1310 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 59.5% (1358 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 60.5% (1380 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 62.1% (1416 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 63.3% (1444 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 64.2% (1465 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 65.9% (1503 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 68.1% (1553 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 69.7% (1590 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 71.5% (1632 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 72.3% (1650 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 73.7% (1682 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 75.3% (1719 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 76.8% (1752 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 77.9% (1778 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 78.8% (1798 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 79.9% (1823 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 80.9% (1845 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 81.9% (1870 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 82.7% (1888 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 84.5% (1928 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 85.6% (1953 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 87.3% (1992 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 88.5% (2020 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 89.0% (2031 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 89.8% (2050 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 91.3% (2083 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 92.5% (2110 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 93.4% (2131 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 94.1% (2147 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 94.9% (2166 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 96.8% (2209 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=771 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 77.1; 2075 / 2282 (P = 90.93%) round 42]               
[00:00:00] Finding cutoff p=762 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=762 1.2% (28 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 2.4% (54 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 3.9% (89 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 6.0% (138 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 8.3% (189 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 9.2% (209 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 11.4% (261 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 13.5% (308 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 15.6% (357 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 17.5% (399 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 19.3% (441 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 22.0% (502 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 24.8% (566 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 25.9% (591 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 26.8% (612 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 27.7% (632 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 29.7% (677 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 31.7% (723 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 32.7% (746 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 34.4% (784 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 36.0% (822 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 38.2% (871 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 40.0% (913 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 41.6% (950 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 44.9% (1024 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 47.3% (1080 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 49.1% (1120 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 52.1% (1188 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 54.1% (1234 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 56.4% (1288 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 57.8% (1318 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 59.4% (1356 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 60.3% (1376 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 61.4% (1402 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 62.8% (1434 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 65.8% (1502 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 66.8% (1525 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 69.0% (1575 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 69.8% (1592 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 71.2% (1625 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 72.8% (1661 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 73.9% (1686 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 74.7% (1704 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 76.0% (1734 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 77.3% (1763 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 78.1% (1783 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 79.6% (1816 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 80.3% (1833 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 81.0% (1849 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 81.7% (1865 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 82.7% (1887 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 83.7% (1909 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 84.8% (1935 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 86.4% (1972 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 88.0% (2008 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 89.7% (2047 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 90.9% (2074 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 91.8% (2095 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 92.8% (2117 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 93.8% (2140 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 95.9% (2189 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=762 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 76.2; 2090 / 2282 (P = 91.59%) round 43]               
[00:00:00] Finding cutoff p=751 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=751 1.2% (28 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 2.0% (46 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 3.7% (85 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 6.0% (138 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 8.4% (192 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 10.1% (230 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 12.6% (287 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 14.3% (327 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 16.0% (365 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 17.4% (397 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 18.6% (425 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 20.9% (476 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 23.9% (545 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 25.2% (575 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 26.6% (606 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 28.1% (642 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 30.2% (690 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 32.7% (746 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 34.2% (781 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 35.6% (813 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 37.9% (865 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 40.6% (927 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 41.9% (957 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 43.9% (1002 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 47.2% (1076 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 48.7% (1112 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 51.7% (1180 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 54.3% (1240 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 56.1% (1280 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 58.2% (1327 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 59.1% (1349 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 60.1% (1371 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 61.7% (1408 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 63.1% (1441 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 64.2% (1465 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 65.2% (1488 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 67.7% (1544 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 70.0% (1598 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 71.0% (1620 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 72.0% (1642 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 73.4% (1674 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 74.6% (1702 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 75.8% (1730 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 76.9% (1755 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 77.9% (1778 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 78.7% (1795 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 79.9% (1824 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 80.9% (1847 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 81.9% (1869 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 82.9% (1892 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 84.3% (1923 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 85.6% (1954 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 87.1% (1988 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 88.5% (2019 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 89.1% (2034 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 89.9% (2052 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 91.2% (2082 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 92.4% (2109 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 93.3% (2129 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 93.9% (2143 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 94.8% (2164 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 95.8% (2186 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 96.8% (2209 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=751 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 75.1; 2119 / 2282 (P = 92.86%) round 44]               
[00:00:00] Finding cutoff p=741 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=741 1.2% (28 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 2.4% (55 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 3.8% (87 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 6.3% (143 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 8.6% (197 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 9.8% (224 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 11.8% (269 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 13.4% (306 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 14.5% (330 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 15.9% (363 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 17.4% (396 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 19.9% (454 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 22.8% (521 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 24.5% (560 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 26.0% (594 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 28.0% (638 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 29.8% (679 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 31.9% (728 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 33.1% (756 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 34.3% (782 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 36.2% (826 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 38.7% (883 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 39.9% (910 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 41.9% (956 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 44.5% (1016 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 46.8% (1068 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 48.5% (1106 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 50.1% (1144 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 52.1% (1190 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 53.7% (1226 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 55.0% (1254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 55.7% (1271 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 56.9% (1298 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 57.7% (1317 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 58.9% (1343 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 60.3% (1376 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 62.8% (1433 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 64.4% (1470 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 66.5% (1517 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 67.1% (1532 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 68.9% (1572 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 70.2% (1602 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 71.2% (1624 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 72.0% (1642 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 73.2% (1671 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 74.3% (1696 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 75.2% (1715 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 76.5% (1746 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 77.4% (1766 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 78.2% (1784 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 78.9% (1800 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 80.1% (1828 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 80.7% (1842 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 82.4% (1881 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 83.4% (1904 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 84.8% (1934 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 86.2% (1968 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 86.8% (1981 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 87.8% (2003 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 89.0% (2032 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 90.4% (2063 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 91.6% (2090 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 91.9% (2097 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 93.4% (2132 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 94.0% (2146 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 94.9% (2165 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 95.9% (2188 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=741 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 74.1; 2129 / 2282 (P = 93.30%) round 45]               
[00:00:00] Finding cutoff p=731 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=731 1.2% (28 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 2.1% (48 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 3.4% (77 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 5.6% (127 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 7.8% (179 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 9.3% (212 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 11.3% (258 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 12.9% (295 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 14.7% (336 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 16.5% (376 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 17.4% (397 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 19.5% (445 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 22.7% (518 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 24.1% (550 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 25.4% (579 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 26.9% (613 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 29.4% (671 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 31.6% (721 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 33.1% (755 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 34.9% (797 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 36.9% (841 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 39.8% (908 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 40.7% (929 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 42.7% (975 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 44.7% (1021 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 47.1% (1075 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 49.1% (1121 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 51.5% (1175 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 53.2% (1213 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 55.0% (1254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 55.8% (1273 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 56.8% (1297 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 57.9% (1321 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 58.7% (1340 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 60.3% (1375 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 61.3% (1398 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 63.7% (1454 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 64.6% (1475 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 66.6% (1519 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 68.1% (1555 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 69.5% (1587 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 71.3% (1627 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 72.2% (1648 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 73.2% (1670 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 74.4% (1697 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 75.3% (1718 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 76.4% (1744 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 77.3% (1764 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 78.3% (1786 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 79.8% (1820 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 81.6% (1863 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 82.9% (1891 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 84.1% (1919 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 85.5% (1950 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 86.1% (1964 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 86.8% (1980 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 88.2% (2012 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 89.9% (2051 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 91.0% (2076 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 92.6% (2114 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 93.2% (2126 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 93.9% (2142 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 94.9% (2166 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 95.9% (2188 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 96.9% (2211 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=731 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 73.1; 2146 / 2282 (P = 94.04%) round 46]               
[00:00:00] Finding cutoff p=722 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=722 1.3% (30 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 2.5% (56 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 4.1% (93 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 6.4% (145 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 8.6% (197 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 10.0% (228 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 11.9% (272 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 13.1% (300 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 14.3% (327 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 15.8% (360 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 17.4% (397 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 19.5% (445 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 22.3% (508 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 23.4% (535 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 25.3% (578 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 26.4% (602 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 28.8% (657 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 30.9% (706 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 32.6% (745 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 34.3% (782 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 36.9% (843 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 39.4% (898 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 40.4% (921 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 42.3% (966 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 44.9% (1024 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 46.8% (1068 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 48.2% (1099 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 50.7% (1156 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 52.5% (1197 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 54.2% (1236 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 54.6% (1246 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 55.6% (1268 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 56.5% (1290 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 57.5% (1313 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 58.8% (1341 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 59.9% (1366 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 62.7% (1431 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 63.5% (1450 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 65.5% (1494 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 66.2% (1511 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 67.6% (1542 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 69.1% (1576 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 70.1% (1600 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 70.9% (1617 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 71.8% (1639 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 72.7% (1658 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 74.0% (1689 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 75.0% (1711 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 75.9% (1731 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 76.6% (1749 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 77.6% (1771 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 79.9% (1823 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 81.0% (1848 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 82.6% (1886 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 84.1% (1919 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 84.7% (1933 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 85.8% (1958 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 87.0% (1986 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 88.4% (2017 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 89.8% (2049 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 90.9% (2074 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 92.5% (2111 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 93.2% (2127 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 93.8% (2141 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 94.7% (2162 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 96.2% (2195 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 96.9% (2211 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=722 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 72.2; 2161 / 2282 (P = 94.70%) round 47]               
[00:00:00] Finding cutoff p=712 [8.5Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=712 1.2% (27 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 2.8% (64 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 4.3% (99 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 6.7% (153 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 8.8% (201 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 9.8% (223 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 11.5% (262 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 13.1% (299 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 14.2% (325 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 15.6% (357 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 16.9% (386 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 19.4% (442 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 22.6% (515 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 24.1% (549 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 25.2% (576 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 26.7% (610 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 28.2% (643 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 29.8% (680 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 31.0% (707 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 32.9% (750 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 34.9% (797 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 36.9% (843 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 38.4% (877 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 40.2% (918 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 42.8% (977 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 44.3% (1012 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 46.8% (1068 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 48.6% (1110 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 50.2% (1146 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 52.1% (1189 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 52.8% (1205 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 54.4% (1242 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 55.6% (1269 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 57.1% (1302 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 58.1% (1325 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 60.4% (1379 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 61.2% (1396 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 63.6% (1452 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 65.1% (1485 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 66.8% (1525 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 67.7% (1544 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 68.9% (1572 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 69.9% (1595 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 71.3% (1626 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 71.8% (1639 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 72.7% (1660 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 73.6% (1679 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 74.8% (1706 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 76.6% (1748 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 78.2% (1784 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 79.8% (1821 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 81.5% (1860 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 82.4% (1880 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 83.2% (1898 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 83.8% (1912 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 85.2% (1944 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 86.6% (1976 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 87.8% (2004 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 89.2% (2036 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 90.5% (2066 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 91.2% (2082 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 92.1% (2101 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 93.0% (2123 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 94.0% (2144 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 94.8% (2163 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 96.2% (2195 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 96.8% (2208 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 97.8% (2231 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 98.8% (2254 of 2282), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 99.8% (2277 of 2282), ETA 0:00:00                              
[00:00:00] Finished Preparing TNF Graph Building [pTNF = 71.20] [8.4Gb / 503.5Gb]                                            
[00:00:00] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=2282 maxEdges=200
[00:00:00] Building TNF Graph 67.7% (1545 of 2282), ETA 0:00:00     [8.4Gb / 503.5Gb]                           
[00:00:00] Finished Building TNF Graph (102314 edges) [8.4Gb / 503.5Gb]                                          
[00:00:00] Cleaned up after Building TNF Graph (102314 edges) [8.4Gb / 503.5Gb]                                          
[00:00:00] Cleaned up TNF matrix of large contigs [8.4Gb / 503.5Gb]                                             
[00:00:00] Applying coverage correlations to TNF graph with 102314 edges
[00:00:00] Allocated memory for graph edges [8.4Gb / 503.5Gb]
[00:00:00] ... calculating abundance dist 1.0% (1033 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 2.0% (2055 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 3.0% (3080 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 4.0% (4116 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 5.0% (5128 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 6.0% (6151 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 7.0% (7169 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 8.0% (8200 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 9.0% (9220 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 10.0% (10245 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 11.0% (11264 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 12.0% (12289 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 13.0% (13313 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 14.0% (14342 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 15.0% (15366 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 16.0% (16385 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 17.0% (17412 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 18.0% (18432 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 19.0% (19456 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 20.0% (20488 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 21.0% (21507 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 22.0% (22535 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 23.0% (23569 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 24.0% (24589 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 25.0% (25609 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 26.0% (26636 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 27.0% (27653 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 28.0% (28678 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 29.0% (29700 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 30.0% (30727 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 31.0% (31766 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 32.0% (32780 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 33.0% (33811 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 34.0% (34824 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 35.0% (35849 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 36.0% (36876 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 37.0% (37890 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 38.0% (38915 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 39.0% (39943 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 40.0% (40972 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 41.0% (41995 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 42.0% (43011 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 43.0% (44034 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 44.0% (45067 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 45.0% (46091 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 46.0% (47111 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 47.1% (48140 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 48.0% (49157 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 49.0% (50179 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 50.0% (51207 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 51.0% (52224 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 52.1% (53258 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 53.1% (54284 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 54.1% (55316 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 55.1% (56340 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 56.1% (57349 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 57.0% (58368 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 58.1% (59406 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 59.1% (60423 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 60.1% (61443 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 61.1% (62467 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 62.1% (63501 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 63.1% (64523 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 64.1% (65545 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 65.1% (66573 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 66.1% (67600 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 67.1% (68613 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 68.1% (69642 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 69.1% (70668 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 70.1% (71690 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 71.1% (72710 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 72.1% (73731 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 73.1% (74758 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 74.1% (75781 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 75.1% (76807 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 76.1% (77836 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 77.1% (78861 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 78.1% (79872 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 79.1% (80902 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 80.1% (81920 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 81.1% (82958 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 82.1% (83969 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 83.1% (84993 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 84.1% (86030 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 85.1% (87040 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 86.1% (88068 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 87.1% (89092 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 88.1% (90112 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 89.1% (91145 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 90.1% (92163 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 91.1% (93189 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 92.1% (94208 of 102314), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 93.1% (95233 of 102314), ETA 0:00:00                              
[00:00:00] Calculating geometric means [8.4Gb / 503.5Gb]
[00:00:00] Traversing graph with 2282 nodes and 102314 edges [8.4Gb / 503.5Gb]
[00:00:00] Building SCR Graph and Binning (217 vertices and 197 edges) [P = 9.50%; 8.4Gb / 503.5Gb]                           
[00:00:00] Building SCR Graph and Binning (435 vertices and 728 edges) [P = 19.00%; 8.4Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 1.0% (1024 of 102314), ETA 0:00:00                               
[00:00:00] ... traversing graph 2.0% (2048 of 102314), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (651 vertices and 1870 edges) [P = 28.50%; 8.4Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 3.0% (3072 of 102314), ETA 0:00:00                               
[00:00:00] ... traversing graph 4.0% (4096 of 102314), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (868 vertices and 3739 edges) [P = 38.00%; 8.4Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 5.0% (5120 of 102314), ETA 0:00:00                               
[00:00:00] ... traversing graph 6.0% (6144 of 102314), ETA 0:00:00                               
[00:00:00] ... traversing graph 7.0% (7168 of 102314), ETA 0:00:00                               
[00:00:00] ... traversing graph 8.0% (8192 of 102314), ETA 0:00:00                               
[00:00:00] ... traversing graph 9.0% (9216 of 102314), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1084 vertices and 6061 edges) [P = 47.50%; 8.4Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 10.0% (10240 of 102314), ETA 0:00:00                               
[00:00:00] ... traversing graph 11.0% (11264 of 102314), ETA 0:00:00                               
[00:00:00] ... traversing graph 12.0% (12288 of 102314), ETA 0:00:00                               
[00:00:00] ... traversing graph 13.0% (13312 of 102314), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1239 vertices and 8354 edges) [P = 57.00%; 8.4Gb / 503.5Gb]                           
[00:00:00] Finished Traversing graph [8.4Gb / 503.5Gb]                                       
[00:00:00] Dissolved 1430 small clusters leaving 1124 leftover contigs to be re-merged into larger clusters
[00:00:00] Rescuing singleton large contigs                                   
[00:00:00] There are 5 bins already
[00:00:00] Outputting bins
[00:00:00] Writing cluster stats to: 03bins/metabat2_fixed/1507992/1507992.bin.BinInfo.txt
[00:00:00] 56.00% (7097367 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
5 bins (7097367 bases in total) formed.
[00:00:00] Finished
MetaBAT2 generated 5 bins for 1507992
MetaBAT2 binning completed for 1507992
