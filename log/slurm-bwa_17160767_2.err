INFO:    Converting OCI blobs to SIF format
INFO:    Starting build...
Copying blob sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
Copying blob sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying blob sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
Copying blob sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
Copying blob sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
Copying blob sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying blob sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
Copying blob sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
Copying config sha256:1a18e5b322db0c21b435c9bcf96d7aae8ee9294648210e1b1929be9505d910a2
time="2025-04-28T16:14:15-07:00" level=warning msg="Compressor for blob with digest sha256:1a18e5b322db0c21b435c9bcf96d7aae8ee9294648210e1b1929be9505d910a2 previously recorded as gzip, now uncompressed"
Writing manifest to image destination
time="2025-04-28T16:14:15-07:00" level=error msg="Rolling back transaction: cannot rollback - no transaction is active"
2025/04/28 16:14:17  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:17  info unpack layer: sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
2025/04/28 16:14:17  warn rootless{dev/console} creating empty file in place of device 5:1
2025/04/28 16:14:17  info unpack layer: sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
2025/04/28 16:14:17  info unpack layer: sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
2025/04/28 16:14:17  info unpack layer: sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
2025/04/28 16:14:17  info unpack layer: sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
2025/04/28 16:14:17  info unpack layer: sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
2025/04/28 16:14:17  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:17  info unpack layer: sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
2025/04/28 16:14:17  info unpack layer: sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
INFO:    Creating SIF file...
mv: cannot stat 'mulled-v2-fe8faa35dbf6dc65a0f7f5d4ea12e31a79f73e40_219b6c272b25e7e642ae3ff0bf0c5c81a5135ab4-0.sif': No such file or directory
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[bwa_index] Pack FASTA... 0.15 sec
[bwa_index] Construct BWT for the packed sequence...
[bwa_index] 5.99 seconds elapse.
[bwa_index] Update BWT... 0.09 sec
[bwa_index] Pack forward-only FASTA... 0.07 sec
[bwa_index] Construct SA from BWT and Occ... 1.58 sec
[main] Version: 0.7.17-r1188
[main] CMD: /usr/local/bin/bwa index 02mapping/1507992/temp/scaffolds.fasta
[main] Real time: 8.362 sec; CPU: 7.900 sec
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 1071404 sequences (160000062 bp)...
[M::process] read 1071530 sequences (160000170 bp)...
[M::mem_process_seqs] Processed 1071404 reads in 79.621 CPU sec, 6.802 real sec
[M::mem_process_seqs] Processed 1071530 reads in 71.791 CPU sec, 5.442 real sec
[M::process] read 1070290 sequences (160000189 bp)...
[M::mem_process_seqs] Processed 1070290 reads in 57.988 CPU sec, 4.166 real sec
[M::process] read 1070620 sequences (160000239 bp)...
[M::mem_process_seqs] Processed 1070620 reads in 59.851 CPU sec, 4.320 real sec
[M::process] read 1070956 sequences (160000227 bp)...
[M::mem_process_seqs] Processed 1070956 reads in 61.074 CPU sec, 4.193 real sec
[M::process] read 1071014 sequences (160000148 bp)...
[M::mem_process_seqs] Processed 1071014 reads in 61.207 CPU sec, 4.427 real sec
[M::process] read 1071480 sequences (160000000 bp)...
[M::mem_process_seqs] Processed 1071480 reads in 60.612 CPU sec, 4.347 real sec
[M::process] read 1071240 sequences (160000103 bp)...
[M::mem_process_seqs] Processed 1071240 reads in 60.071 CPU sec, 5.835 real sec
[M::process] read 1072882 sequences (160000278 bp)...
[M::mem_process_seqs] Processed 1072882 reads in 92.557 CPU sec, 6.431 real sec
[M::process] read 681126 sequences (99344252 bp)...
[M::mem_process_seqs] Processed 681126 reads in 55.208 CPU sec, 3.776 real sec
[main] Version: 0.7.17-r1188
[main] CMD: bwa mem -t 16 02mapping/1507992/temp/scaffolds.fasta 00data/readsf/1507992.anqdpht.fastq.gz
[main] Real time: 73.532 sec; CPU: 675.385 sec
[bam_sort_core] merging from 0 files and 16 in-memory blocks...
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
