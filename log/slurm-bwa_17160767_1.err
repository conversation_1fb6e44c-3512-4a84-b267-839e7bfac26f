INFO:    Converting OCI blobs to SIF format
INFO:    Starting build...
Copying blob sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
Copying blob sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
Copying blob sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
Copying blob sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
Copying blob sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
Copying blob sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying blob sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
Copying blob sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying config sha256:1a18e5b322db0c21b435c9bcf96d7aae8ee9294648210e1b1929be9505d910a2
Writing manifest to image destination
time="2025-04-28T16:14:16-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
time="2025-04-28T16:14:16-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
time="2025-04-28T16:14:16-07:00" level=error msg="Rolling back transaction: cannot rollback - no transaction is active"
time="2025-04-28T16:14:16-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
2025/04/28 16:14:17  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:17  info unpack layer: sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
2025/04/28 16:14:17  warn rootless{dev/console} creating empty file in place of device 5:1
2025/04/28 16:14:17  info unpack layer: sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
2025/04/28 16:14:17  info unpack layer: sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
2025/04/28 16:14:17  info unpack layer: sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
2025/04/28 16:14:17  info unpack layer: sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
2025/04/28 16:14:17  info unpack layer: sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
2025/04/28 16:14:17  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:17  info unpack layer: sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
2025/04/28 16:14:17  info unpack layer: sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
INFO:    Creating SIF file...
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[bwa_index] Pack FASTA... 0.05 sec
[bwa_index] Construct BWT for the packed sequence...
[bwa_index] 1.81 seconds elapse.
[bwa_index] Update BWT... 0.03 sec
[bwa_index] Pack forward-only FASTA... 0.03 sec
[bwa_index] Construct SA from BWT and Occ... 0.55 sec
[main] Version: 0.7.17-r1188
[main] CMD: /usr/local/bin/bwa index 02mapping/1507990/temp/scaffolds.fasta
[main] Real time: 2.803 sec; CPU: 2.496 sec
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 1071224 sequences (160000212 bp)...
[M::process] read 1073424 sequences (160000298 bp)...
[M::mem_process_seqs] Processed 1071224 reads in 83.622 CPU sec, 5.709 real sec
[M::mem_process_seqs] Processed 1073424 reads in 75.774 CPU sec, 5.480 real sec
[M::process] read 1071160 sequences (160000175 bp)...
[M::mem_process_seqs] Processed 1071160 reads in 60.566 CPU sec, 4.104 real sec
[M::process] read 1071574 sequences (160000025 bp)...
[M::mem_process_seqs] Processed 1071574 reads in 67.509 CPU sec, 4.576 real sec
[M::process] read 1070514 sequences (160000062 bp)...
[M::mem_process_seqs] Processed 1070514 reads in 59.958 CPU sec, 4.231 real sec
[M::process] read 1070610 sequences (160000278 bp)...
[M::mem_process_seqs] Processed 1070610 reads in 60.621 CPU sec, 4.165 real sec
[M::process] read 1070974 sequences (160000188 bp)...
[M::mem_process_seqs] Processed 1070974 reads in 61.444 CPU sec, 4.200 real sec
[M::process] read 1072486 sequences (160000199 bp)...
[M::mem_process_seqs] Processed 1072486 reads in 77.349 CPU sec, 5.528 real sec
[M::process] read 931814 sequences (136780252 bp)...
[M::mem_process_seqs] Processed 931814 reads in 119.154 CPU sec, 7.979 real sec
[main] Version: 0.7.17-r1188
[main] CMD: bwa mem -t 16 02mapping/1507990/temp/scaffolds.fasta 00data/readsf/1507990.anqdpht.fastq.gz
[main] Real time: 64.630 sec; CPU: 677.610 sec
[bam_sort_core] merging from 0 files and 16 in-memory blocks...
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
