[04/29/2025 10:56:16 AM] INFO: Running CheckM2 version 1.0.2
[04/29/2025 10:56:16 AM] INFO: Custom database path provided for predict run. Checking database at /clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database/uniref100.KO.1.dmnd...
[04/29/2025 10:56:46 AM] INFO: Running quality prediction workflow with 16 threads.
[04/29/2025 10:56:48 AM] INFO: Calling genes in 5 bins with 16 threads:
[04/29/2025 10:56:51 AM] INFO: Calculating metadata for 5 bins with 16 threads:
[04/29/2025 10:56:51 AM] INFO: Annotating input genomes with DIAMOND using 16 threads
[04/29/2025 10:57:04 AM] INFO: Processing DIAMOND output
[04/29/2025 10:57:04 AM] INFO: Predicting completeness and contamination using ML models.
[04/29/2025 10:57:11 AM] INFO: Parsing all results and constructing final output table.
[04/29/2025 10:57:11 AM] INFO: CheckM2 finished successfully.
[04/29/2025 10:57:16 AM] INFO: Running CheckM2 version 1.0.2
[04/29/2025 10:57:16 AM] INFO: Custom database path provided for predict run. Checking database at /clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database/uniref100.KO.1.dmnd...
[04/29/2025 10:57:18 AM] INFO: Running quality prediction workflow with 16 threads.
[04/29/2025 10:57:19 AM] INFO: Calling genes in 5 bins with 16 threads:
[04/29/2025 10:57:41 AM] INFO: Calculating metadata for 5 bins with 16 threads:
[04/29/2025 10:57:42 AM] INFO: Annotating input genomes with DIAMOND using 16 threads
[04/29/2025 10:58:16 AM] INFO: Processing DIAMOND output
[04/29/2025 10:58:16 AM] INFO: Predicting completeness and contamination using ML models.
[04/29/2025 10:58:22 AM] INFO: Parsing all results and constructing final output table.
[04/29/2025 10:58:22 AM] INFO: CheckM2 finished successfully.
[04/29/2025 10:58:26 AM] INFO: Running CheckM2 version 1.0.2
[04/29/2025 10:58:26 AM] INFO: Custom database path provided for predict run. Checking database at /clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database/uniref100.KO.1.dmnd...
[04/29/2025 10:58:29 AM] INFO: Running quality prediction workflow with 16 threads.
[04/29/2025 10:58:30 AM] INFO: Calling genes in 4 bins with 16 threads:
[04/29/2025 10:59:27 AM] INFO: Calculating metadata for 4 bins with 16 threads:
[04/29/2025 10:59:27 AM] INFO: Annotating input genomes with DIAMOND using 16 threads
[04/29/2025 11:00:02 AM] INFO: Processing DIAMOND output
[04/29/2025 11:00:02 AM] INFO: Predicting completeness and contamination using ML models.
[04/29/2025 11:00:08 AM] INFO: Parsing all results and constructing final output table.
[04/29/2025 11:00:08 AM] INFO: CheckM2 finished successfully.
[04/29/2025 11:00:12 AM] INFO: Running CheckM2 version 1.0.2
[04/29/2025 11:00:12 AM] INFO: Custom database path provided for predict run. Checking database at /clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database/uniref100.KO.1.dmnd...
[04/29/2025 11:00:14 AM] INFO: Running quality prediction workflow with 16 threads.
[04/29/2025 11:00:15 AM] INFO: Calling genes in 1 bins with 16 threads:
[04/29/2025 11:02:48 AM] INFO: Calculating metadata for 1 bins with 16 threads:
[04/29/2025 11:02:48 AM] INFO: Annotating input genomes with DIAMOND using 16 threads
[04/29/2025 11:03:25 AM] INFO: Processing DIAMOND output
[04/29/2025 11:03:25 AM] INFO: Predicting completeness and contamination using ML models.
[04/29/2025 11:03:31 AM] INFO: Parsing all results and constructing final output table.
[04/29/2025 11:03:31 AM] INFO: CheckM2 finished successfully.
[04/29/2025 11:03:34 AM] INFO: Running CheckM2 version 1.0.2
[04/29/2025 11:03:34 AM] INFO: Custom database path provided for predict run. Checking database at /clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database/uniref100.KO.1.dmnd...
[04/29/2025 11:03:37 AM] INFO: Running quality prediction workflow with 16 threads.
[04/29/2025 11:03:38 AM] INFO: Calling genes in 1 bins with 16 threads:
[04/29/2025 11:06:19 AM] INFO: Calculating metadata for 1 bins with 16 threads:
[04/29/2025 11:06:20 AM] INFO: Annotating input genomes with DIAMOND using 16 threads
[04/29/2025 11:06:57 AM] INFO: Processing DIAMOND output
[04/29/2025 11:06:57 AM] INFO: Predicting completeness and contamination using ML models.
[04/29/2025 11:07:03 AM] INFO: Parsing all results and constructing final output table.
[04/29/2025 11:07:04 AM] INFO: CheckM2 finished successfully.
[04/29/2025 11:07:08 AM] INFO: Running CheckM2 version 1.0.2
[04/29/2025 11:07:08 AM] INFO: Custom database path provided for predict run. Checking database at /clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database/uniref100.KO.1.dmnd...
[04/29/2025 11:07:11 AM] INFO: Running quality prediction workflow with 16 threads.
[04/29/2025 11:07:12 AM] INFO: Calling genes in 1 bins with 16 threads:
[04/29/2025 11:09:44 AM] INFO: Calculating metadata for 1 bins with 16 threads:
[04/29/2025 11:09:44 AM] INFO: Annotating input genomes with DIAMOND using 16 threads
[04/29/2025 11:10:22 AM] INFO: Processing DIAMOND output
[04/29/2025 11:10:22 AM] INFO: Predicting completeness and contamination using ML models.
[04/29/2025 11:10:28 AM] INFO: Parsing all results and constructing final output table.
[04/29/2025 11:10:28 AM] INFO: CheckM2 finished successfully.
[04/29/2025 11:10:32 AM] INFO: Running CheckM2 version 1.0.2
[04/29/2025 11:10:32 AM] INFO: Custom database path provided for predict run. Checking database at /clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database/uniref100.KO.1.dmnd...
[04/29/2025 11:10:34 AM] INFO: Running quality prediction workflow with 16 threads.
[04/29/2025 11:10:35 AM] INFO: Calling genes in 1 bins with 16 threads:
[04/29/2025 11:13:13 AM] INFO: Calculating metadata for 1 bins with 16 threads:
[04/29/2025 11:13:13 AM] INFO: Annotating input genomes with DIAMOND using 16 threads
[04/29/2025 11:13:51 AM] INFO: Processing DIAMOND output
[04/29/2025 11:13:51 AM] INFO: Predicting completeness and contamination using ML models.
[04/29/2025 11:13:56 AM] INFO: Parsing all results and constructing final output table.
[04/29/2025 11:13:56 AM] INFO: CheckM2 finished successfully.
