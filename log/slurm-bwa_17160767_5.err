INFO:    Converting OCI blobs to SIF format
INFO:    Starting build...
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying blob sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
Copying blob sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
Copying blob sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
Copying blob sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
Copying blob sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
Copying blob sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying blob sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
Copying blob sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
Copying config sha256:1a18e5b322db0c21b435c9bcf96d7aae8ee9294648210e1b1929be9505d910a2
Writing manifest to image destination
time="2025-04-28T16:14:15-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
time="2025-04-28T16:14:16-07:00" level=error msg="Rolling back transaction: cannot rollback - no transaction is active"
time="2025-04-28T16:14:16-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
2025/04/28 16:14:16  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:16  info unpack layer: sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
2025/04/28 16:14:16  warn rootless{dev/console} creating empty file in place of device 5:1
2025/04/28 16:14:16  info unpack layer: sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
2025/04/28 16:14:16  info unpack layer: sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
2025/04/28 16:14:16  info unpack layer: sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
2025/04/28 16:14:16  info unpack layer: sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
2025/04/28 16:14:16  info unpack layer: sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
2025/04/28 16:14:16  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:16  info unpack layer: sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
2025/04/28 16:14:16  info unpack layer: sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
INFO:    Creating SIF file...
FATAL:   While making image from oci registry: error copying image out of cache: could not copy file: read /clusterfs/jgi/groups/science/homes/ssiddik/.apptainer/cache/oci-tmp/1fe73557e7dee95bb329b2c80d71e2c35cc1c405b9be9a294ca751b1675b41ae: stale NFS file handle
mv: cannot stat 'mulled-v2-fe8faa35dbf6dc65a0f7f5d4ea12e31a79f73e40_219b6c272b25e7e642ae3ff0bf0c5c81a5135ab4-0.sif': No such file or directory
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[bwa_index] Pack FASTA... 0.36 sec
[bwa_index] Construct BWT for the packed sequence...
[BWTIncCreate] textLength=93049786, availableWord=18547180
[BWTIncConstructFromPacked] 10 iterations done. 30593818 characters processed.
[BWTIncConstructFromPacked] 20 iterations done. 56518010 characters processed.
[BWTIncConstructFromPacked] 30 iterations done. 79555450 characters processed.
[bwt_gen] Finished constructing BWT in 37 iterations.
[bwa_index] 12.85 seconds elapse.
[bwa_index] Update BWT... 0.20 sec
[bwa_index] Pack forward-only FASTA... 0.15 sec
[bwa_index] Construct SA from BWT and Occ... 6.40 sec
[main] Version: 0.7.17-r1188
[main] CMD: /usr/local/bin/bwa index 02mapping/1507995/temp/scaffolds.fasta
[main] Real time: 24.821 sec; CPU: 19.992 sec
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 1084664 sequences (160000205 bp)...
[M::process] read 1094868 sequences (160000099 bp)...
[M::mem_process_seqs] Processed 1084664 reads in 231.635 CPU sec, 16.468 real sec
[M::process] read 1147110 sequences (160000224 bp)...
[M::mem_process_seqs] Processed 1094868 reads in 181.764 CPU sec, 12.780 real sec
[M::mem_process_seqs] Processed 1147110 reads in 141.226 CPU sec, 9.556 real sec
[M::process] read 1103806 sequences (160000285 bp)...
[M::mem_process_seqs] Processed 1103806 reads in 135.642 CPU sec, 10.010 real sec
[M::process] read 1094198 sequences (160000128 bp)...
[M::mem_process_seqs] Processed 1094198 reads in 146.135 CPU sec, 10.980 real sec
[M::process] read 1099452 sequences (160000198 bp)...
[M::process] read 1097414 sequences (160000224 bp)...
[M::mem_process_seqs] Processed 1099452 reads in 143.176 CPU sec, 10.229 real sec
[M::mem_process_seqs] Processed 1097414 reads in 128.566 CPU sec, 9.791 real sec
[M::process] read 1081248 sequences (160000002 bp)...
[M::mem_process_seqs] Processed 1081248 reads in 107.523 CPU sec, 7.459 real sec
[M::process] read 1081474 sequences (160000042 bp)...
[M::mem_process_seqs] Processed 1081474 reads in 103.236 CPU sec, 6.937 real sec
[M::process] read 1081878 sequences (160000027 bp)...
[M::mem_process_seqs] Processed 1081878 reads in 96.453 CPU sec, 7.844 real sec
[M::process] read 1081768 sequences (160000052 bp)...
[M::mem_process_seqs] Processed 1081768 reads in 93.603 CPU sec, 6.329 real sec
[M::process] read 1080504 sequences (160000296 bp)...
[M::process] read 1080998 sequences (160000046 bp)...
[M::mem_process_seqs] Processed 1080504 reads in 86.842 CPU sec, 9.088 real sec
[M::mem_process_seqs] Processed 1080998 reads in 85.335 CPU sec, 6.074 real sec
[M::process] read 1080852 sequences (160000130 bp)...
[M::mem_process_seqs] Processed 1080852 reads in 89.721 CPU sec, 8.907 real sec
[M::process] read 1082106 sequences (160000009 bp)...
[M::mem_process_seqs] Processed 1082106 reads in 89.236 CPU sec, 6.108 real sec
[M::process] read 1080626 sequences (160000220 bp)...
[M::mem_process_seqs] Processed 1080626 reads in 88.096 CPU sec, 6.326 real sec
[M::process] read 1081712 sequences (160000114 bp)...
[M::mem_process_seqs] Processed 1081712 reads in 87.241 CPU sec, 6.224 real sec
[M::process] read 1083436 sequences (160000085 bp)...
[M::mem_process_seqs] Processed 1083436 reads in 86.292 CPU sec, 5.947 real sec
[M::process] read 1082982 sequences (160000011 bp)...
[M::mem_process_seqs] Processed 1082982 reads in 96.574 CPU sec, 6.611 real sec
[M::process] read 1081870 sequences (160000063 bp)...
[M::process] read 1159812 sequences (160000157 bp)...
[M::mem_process_seqs] Processed 1081870 reads in 123.342 CPU sec, 8.328 real sec
[M::mem_process_seqs] Processed 1159812 reads in 167.424 CPU sec, 11.872 real sec
[M::process] read 1062956 sequences (144880136 bp)...
[M::mem_process_seqs] Processed 1062956 reads in 169.219 CPU sec, 12.966 real sec
[main] Version: 0.7.17-r1188
[main] CMD: bwa mem -t 16 02mapping/1507995/temp/scaffolds.fasta 00data/readsf/1507995.anqdpht.fastq.gz
[main] Real time: 226.376 sec; CPU: 2699.160 sec
[bam_sort_core] merging from 0 files and 16 in-memory blocks...
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
