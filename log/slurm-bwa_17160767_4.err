INFO:    Converting OCI blobs to SIF format
INFO:    Starting build...
Copying blob sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
Copying blob sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
Copying blob sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
Copying blob sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
Copying blob sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying blob sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying blob sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
Copying blob sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
Copying config sha256:1a18e5b322db0c21b435c9bcf96d7aae8ee9294648210e1b1929be9505d910a2
Writing manifest to image destination
time="2025-04-28T16:14:15-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
2025/04/28 16:14:16  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:16  info unpack layer: sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
2025/04/28 16:14:16  warn rootless{dev/console} creating empty file in place of device 5:1
2025/04/28 16:14:16  info unpack layer: sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
2025/04/28 16:14:16  info unpack layer: sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
2025/04/28 16:14:16  info unpack layer: sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
2025/04/28 16:14:16  info unpack layer: sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
2025/04/28 16:14:16  info unpack layer: sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
2025/04/28 16:14:16  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:16  info unpack layer: sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
2025/04/28 16:14:16  info unpack layer: sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
INFO:    Creating SIF file...
mv: cannot stat 'mulled-v2-fe8faa35dbf6dc65a0f7f5d4ea12e31a79f73e40_219b6c272b25e7e642ae3ff0bf0c5c81a5135ab4-0.sif': No such file or directory
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[bwa_index] Pack FASTA... 0.27 sec
[bwa_index] Construct BWT for the packed sequence...
[BWTIncCreate] textLength=88872602, availableWord=18252960
[BWTIncConstructFromPacked] 10 iterations done. 30108458 characters processed.
[BWTIncConstructFromPacked] 20 iterations done. 55621322 characters processed.
[BWTIncConstructFromPacked] 30 iterations done. 78293210 characters processed.
[bwt_gen] Finished constructing BWT in 36 iterations.
[bwa_index] 12.10 seconds elapse.
[bwa_index] Update BWT... 0.17 sec
[bwa_index] Pack forward-only FASTA... 0.14 sec
[bwa_index] Construct SA from BWT and Occ... 5.45 sec
[main] Version: 0.7.17-r1188
[main] CMD: /usr/local/bin/bwa index 02mapping/1507994/temp/scaffolds.fasta
[main] Real time: 21.688 sec; CPU: 18.158 sec
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 1138358 sequences (160000088 bp)...
[M::process] read 1118804 sequences (160000286 bp)...
[M::mem_process_seqs] Processed 1138358 reads in 146.915 CPU sec, 10.703 real sec
[M::process] read 1112204 sequences (160000022 bp)...
[M::mem_process_seqs] Processed 1118804 reads in 159.054 CPU sec, 11.086 real sec
[M::mem_process_seqs] Processed 1112204 reads in 153.999 CPU sec, 10.576 real sec
[M::process] read 1085752 sequences (160000015 bp)...
[M::mem_process_seqs] Processed 1085752 reads in 124.090 CPU sec, 8.360 real sec
[M::process] read 1082218 sequences (160000038 bp)...
[M::process] read 1083690 sequences (160000276 bp)...
[M::mem_process_seqs] Processed 1082218 reads in 202.983 CPU sec, 15.017 real sec
[M::mem_process_seqs] Processed 1083690 reads in 153.483 CPU sec, 10.336 real sec
[M::process] read 1084716 sequences (160000282 bp)...
[M::mem_process_seqs] Processed 1084716 reads in 120.250 CPU sec, 8.108 real sec
[M::process] read 1084684 sequences (160000252 bp)...
[M::mem_process_seqs] Processed 1084684 reads in 92.732 CPU sec, 6.324 real sec
[M::process] read 1079378 sequences (160000085 bp)...
[M::mem_process_seqs] Processed 1079378 reads in 92.789 CPU sec, 6.290 real sec
[M::process] read 1078924 sequences (160000056 bp)...
[M::mem_process_seqs] Processed 1078924 reads in 78.447 CPU sec, 5.430 real sec
[M::process] read 1080946 sequences (160000195 bp)...
[M::mem_process_seqs] Processed 1080946 reads in 78.034 CPU sec, 5.435 real sec
[M::process] read 1079760 sequences (160000020 bp)...
[M::mem_process_seqs] Processed 1079760 reads in 76.905 CPU sec, 5.413 real sec
[M::process] read 1082702 sequences (160000009 bp)...
[M::mem_process_seqs] Processed 1082702 reads in 80.854 CPU sec, 5.562 real sec
[M::process] read 1078854 sequences (160000177 bp)...
[M::mem_process_seqs] Processed 1078854 reads in 76.466 CPU sec, 5.343 real sec
[M::process] read 1088694 sequences (160000241 bp)...
[M::mem_process_seqs] Processed 1088694 reads in 74.150 CPU sec, 5.378 real sec
[M::process] read 1086602 sequences (160000058 bp)...
[M::mem_process_seqs] Processed 1086602 reads in 98.640 CPU sec, 6.617 real sec
[M::process] read 1110832 sequences (160000165 bp)...
[M::mem_process_seqs] Processed 1110832 reads in 102.752 CPU sec, 7.189 real sec
[M::process] read 381446 sequences (52003593 bp)...
[M::mem_process_seqs] Processed 381446 reads in 54.480 CPU sec, 3.788 real sec
[main] Version: 0.7.17-r1188
[main] CMD: bwa mem -t 16 02mapping/1507994/temp/scaffolds.fasta 00data/readsf/1507994.anqdpht.fastq.gz
[main] Real time: 159.831 sec; CPU: 1981.281 sec
[bam_sort_core] merging from 0 files and 16 in-memory blocks...
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
