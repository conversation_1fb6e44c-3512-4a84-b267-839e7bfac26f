Decompressing scaffold file for 1507995...
Generating depth file for 1507995...
Running MetaBAT2 for 1507995 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 200000. with random seed=123
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [55.1Gb / 503.5Gb]
[00:00:00] Parsing assembly file [55.1Gb / 503.5Gb]
[00:00:00] ... processed 12 seqs, 12 long (>=2000), 0 short (>=1000) 1.0% (498805 of 49057406), ETA 0:00:02     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 27 seqs, 27 long (>=2000), 0 short (>=1000) 2.0% (985275 of 49057406), ETA 0:00:01     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 45 seqs, 45 long (>=2000), 0 short (>=1000) 3.0% (1491624 of 49057406), ETA 0:00:01     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 65 seqs, 65 long (>=2000), 0 short (>=1000) 4.0% (1980149 of 49057406), ETA 0:00:01     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 86 seqs, 86 long (>=2000), 0 short (>=1000) 5.0% (2453012 of 49057406), ETA 0:00:01     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 110 seqs, 110 long (>=2000), 0 short (>=1000) 6.0% (2959498 of 49057406), ETA 0:00:01     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 135 seqs, 135 long (>=2000), 0 short (>=1000) 7.0% (3448215 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 161 seqs, 161 long (>=2000), 0 short (>=1000) 8.0% (3929289 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 189 seqs, 189 long (>=2000), 0 short (>=1000) 9.0% (4422855 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 218 seqs, 218 long (>=2000), 0 short (>=1000) 10.0% (4907164 of 49057406), ETA 0:00:00     [55.0Gb / 503.5Gb]     
[00:00:00] ... processed 249 seqs, 249 long (>=2000), 0 short (>=1000) 11.0% (5401815 of 49057406), ETA 0:00:00     [55.0Gb / 503.5Gb]     
[00:00:00] ... processed 281 seqs, 281 long (>=2000), 0 short (>=1000) 12.0% (5887168 of 49057406), ETA 0:00:00     [55.0Gb / 503.5Gb]     
[00:00:00] ... processed 315 seqs, 315 long (>=2000), 0 short (>=1000) 13.0% (6380362 of 49057406), ETA 0:00:00     [55.0Gb / 503.5Gb]     
[00:00:00] ... processed 351 seqs, 351 long (>=2000), 0 short (>=1000) 14.0% (6879857 of 49057406), ETA 0:00:00     [55.0Gb / 503.5Gb]     
[00:00:00] ... processed 387 seqs, 387 long (>=2000), 0 short (>=1000) 15.0% (7360139 of 49057406), ETA 0:00:00     [55.0Gb / 503.5Gb]     
[00:00:00] ... processed 426 seqs, 426 long (>=2000), 0 short (>=1000) 16.0% (7861504 of 49057406), ETA 0:00:00     [55.0Gb / 503.5Gb]     
[00:00:00] ... processed 465 seqs, 465 long (>=2000), 0 short (>=1000) 17.0% (8346458 of 49057406), ETA 0:00:00     [55.0Gb / 503.5Gb]     
[00:00:00] ... processed 506 seqs, 506 long (>=2000), 0 short (>=1000) 18.0% (8832572 of 49057406), ETA 0:00:00     [55.0Gb / 503.5Gb]     
[00:00:00] ... processed 549 seqs, 549 long (>=2000), 0 short (>=1000) 19.0% (9323030 of 49057406), ETA 0:00:00     [55.0Gb / 503.5Gb]     
[00:00:00] ... processed 594 seqs, 594 long (>=2000), 0 short (>=1000) 20.0% (9817990 of 49057406), ETA 0:00:00     [55.0Gb / 503.5Gb]     
[00:00:00] ... processed 640 seqs, 640 long (>=2000), 0 short (>=1000) 21.0% (10308924 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 687 seqs, 687 long (>=2000), 0 short (>=1000) 22.0% (10792708 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 737 seqs, 737 long (>=2000), 0 short (>=1000) 23.0% (11287313 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 789 seqs, 789 long (>=2000), 0 short (>=1000) 24.0% (11781322 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 842 seqs, 842 long (>=2000), 0 short (>=1000) 25.0% (12265442 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 898 seqs, 898 long (>=2000), 0 short (>=1000) 26.0% (12758742 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 956 seqs, 956 long (>=2000), 0 short (>=1000) 27.0% (13251246 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1016 seqs, 1016 long (>=2000), 0 short (>=1000) 28.0% (13741894 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1078 seqs, 1078 long (>=2000), 0 short (>=1000) 29.0% (14230210 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1142 seqs, 1142 long (>=2000), 0 short (>=1000) 30.0% (14718464 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1209 seqs, 1209 long (>=2000), 0 short (>=1000) 31.0% (15213738 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1277 seqs, 1277 long (>=2000), 0 short (>=1000) 32.0% (15699559 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1348 seqs, 1348 long (>=2000), 0 short (>=1000) 33.0% (16191326 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1421 seqs, 1421 long (>=2000), 0 short (>=1000) 34.0% (16681665 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1496 seqs, 1496 long (>=2000), 0 short (>=1000) 35.0% (17170973 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1574 seqs, 1574 long (>=2000), 0 short (>=1000) 36.0% (17664068 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1654 seqs, 1654 long (>=2000), 0 short (>=1000) 37.0% (18152813 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1737 seqs, 1737 long (>=2000), 0 short (>=1000) 38.0% (18642307 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1823 seqs, 1823 long (>=2000), 0 short (>=1000) 39.0% (19133479 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1912 seqs, 1912 long (>=2000), 0 short (>=1000) 40.0% (19626790 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2003 seqs, 2003 long (>=2000), 0 short (>=1000) 41.0% (20114173 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2099 seqs, 2099 long (>=2000), 0 short (>=1000) 42.0% (20608093 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2197 seqs, 2197 long (>=2000), 0 short (>=1000) 43.0% (21095420 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2300 seqs, 2300 long (>=2000), 0 short (>=1000) 44.0% (21589130 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2406 seqs, 2406 long (>=2000), 0 short (>=1000) 45.0% (22078193 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2516 seqs, 2516 long (>=2000), 0 short (>=1000) 46.0% (22566471 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2631 seqs, 2631 long (>=2000), 0 short (>=1000) 47.0% (23057812 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2751 seqs, 2751 long (>=2000), 0 short (>=1000) 48.0% (23549540 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2875 seqs, 2875 long (>=2000), 0 short (>=1000) 49.0% (24038323 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3005 seqs, 3005 long (>=2000), 0 short (>=1000) 50.0% (24528816 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3142 seqs, 3142 long (>=2000), 0 short (>=1000) 51.0% (25021903 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3284 seqs, 3284 long (>=2000), 0 short (>=1000) 52.0% (25511746 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3433 seqs, 3433 long (>=2000), 0 short (>=1000) 53.0% (26003191 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3588 seqs, 3588 long (>=2000), 0 short (>=1000) 54.0% (26493261 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3750 seqs, 3750 long (>=2000), 0 short (>=1000) 55.0% (26983126 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3920 seqs, 3920 long (>=2000), 0 short (>=1000) 56.0% (27474661 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 4098 seqs, 4098 long (>=2000), 0 short (>=1000) 57.0% (27963216 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 4288 seqs, 4288 long (>=2000), 0 short (>=1000) 58.0% (28455080 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 4490 seqs, 4490 long (>=2000), 0 short (>=1000) 59.0% (28944949 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 4706 seqs, 4706 long (>=2000), 0 short (>=1000) 60.0% (29435380 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 4937 seqs, 4937 long (>=2000), 0 short (>=1000) 61.0% (29925603 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 5186 seqs, 5013 long (>=2000), 173 short (>=1000) 62.0% (30416870 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 5454 seqs, 5013 long (>=2000), 441 short (>=1000) 63.0% (30906842 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 5744 seqs, 5013 long (>=2000), 731 short (>=1000) 64.0% (31397908 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 6057 seqs, 5013 long (>=2000), 1044 short (>=1000) 65.0% (31887961 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 6394 seqs, 5013 long (>=2000), 1381 short (>=1000) 66.0% (32378618 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 6758 seqs, 5013 long (>=2000), 1745 short (>=1000) 67.0% (32869595 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 7149 seqs, 5013 long (>=2000), 2136 short (>=1000) 68.0% (33359119 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 7569 seqs, 5013 long (>=2000), 2556 short (>=1000) 69.0% (33850060 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 8020 seqs, 5013 long (>=2000), 3007 short (>=1000) 70.0% (34341100 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 8501 seqs, 5013 long (>=2000), 3397 short (>=1000) 71.0% (34831609 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 9012 seqs, 5013 long (>=2000), 3397 short (>=1000) 72.0% (35321997 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 9556 seqs, 5013 long (>=2000), 3397 short (>=1000) 73.0% (35812344 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 10137 seqs, 5013 long (>=2000), 3397 short (>=1000) 74.0% (36302995 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 10759 seqs, 5013 long (>=2000), 3397 short (>=1000) 75.0% (36793537 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 11427 seqs, 5013 long (>=2000), 3397 short (>=1000) 76.0% (37284389 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 12146 seqs, 5013 long (>=2000), 3397 short (>=1000) 77.0% (37774856 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 12922 seqs, 5013 long (>=2000), 3397 short (>=1000) 78.0% (38265199 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 13762 seqs, 5013 long (>=2000), 3397 short (>=1000) 79.0% (38755983 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 14670 seqs, 5013 long (>=2000), 3397 short (>=1000) 80.0% (39246001 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 15657 seqs, 5013 long (>=2000), 3397 short (>=1000) 81.0% (39736909 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 16732 seqs, 5013 long (>=2000), 3397 short (>=1000) 82.0% (40227577 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 17903 seqs, 5013 long (>=2000), 3397 short (>=1000) 83.0% (40718120 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 19173 seqs, 5013 long (>=2000), 3397 short (>=1000) 84.0% (41208306 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 20555 seqs, 5013 long (>=2000), 3397 short (>=1000) 85.0% (41698992 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 22061 seqs, 5013 long (>=2000), 3397 short (>=1000) 86.0% (42189626 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 23715 seqs, 5013 long (>=2000), 3397 short (>=1000) 87.0% (42680077 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 25522 seqs, 5013 long (>=2000), 3397 short (>=1000) 88.0% (43170672 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 27487 seqs, 5013 long (>=2000), 3397 short (>=1000) 89.0% (43661387 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 29620 seqs, 5013 long (>=2000), 3397 short (>=1000) 90.0% (44151864 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 31986 seqs, 5013 long (>=2000), 3397 short (>=1000) 91.0% (44642511 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 34683 seqs, 5013 long (>=2000), 3397 short (>=1000) 92.0% (45132918 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 37767 seqs, 5013 long (>=2000), 3397 short (>=1000) 93.0% (45623598 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 41500 seqs, 5013 long (>=2000), 3397 short (>=1000) 94.0% (46114094 of 49057406), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 5013, and small contigs >= 1000 bp are 3397                                                                  
[00:00:00] Allocating 5013 contigs by 1 samples abundances [55.1Gb / 503.5Gb]
[00:00:00] Allocating 5013 contigs by 1 samples variances [55.1Gb / 503.5Gb]
[00:00:00] Allocating 3397 small contigs by 1 samples abundances [55.1Gb / 503.5Gb]
[00:00:00] Reading 0.002741Gb abundance file [55.1Gb / 503.5Gb]
[00:00:00] ... processed 448 lines 448 contigs and 0 short contigs 1.0% (29451 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 902 lines 902 contigs and 0 short contigs 2.0% (58858 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 1359 lines 1359 contigs and 0 short contigs 3.0% (88326 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 1813 lines 1813 contigs and 0 short contigs 4.0% (117727 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 2268 lines 2268 contigs and 0 short contigs 5.0% (147162 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 2724 lines 2724 contigs and 0 short contigs 6.0% (176623 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 3179 lines 3179 contigs and 0 short contigs 7.0% (206065 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 3634 lines 3634 contigs and 0 short contigs 8.0% (235475 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 4089 lines 4089 contigs and 0 short contigs 9.0% (264870 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 4544 lines 4544 contigs and 0 short contigs 10.0% (294290 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 5001 lines 5001 contigs and 0 short contigs 11.0% (323764 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 5457 lines 5013 contigs and 444 short contigs 12.0% (353212 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 5912 lines 5013 contigs and 899 short contigs 13.0% (382583 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 6368 lines 5013 contigs and 1355 short contigs 14.0% (412013 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 6825 lines 5013 contigs and 1812 short contigs 15.0% (441478 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 7281 lines 5013 contigs and 2268 short contigs 16.0% (470889 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 7737 lines 5013 contigs and 2724 short contigs 17.0% (500294 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8193 lines 5013 contigs and 3180 short contigs 18.0% (529727 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 19.0% (559180 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 20.0% (588606 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 21.0% (618024 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 22.0% (647461 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 23.0% (676913 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 24.0% (706318 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 25.0% (735730 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 26.0% (765165 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 27.0% (794593 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 28.0% (824024 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 29.0% (853454 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 30.0% (882885 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 31.0% (912361 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 32.0% (941783 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 33.0% (971167 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 34.0% (1000632 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 35.0% (1030042 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 36.0% (1059503 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 37.0% (1088911 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 38.0% (1118360 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 39.0% (1147758 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 40.0% (1177196 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 41.0% (1206650 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 42.0% (1236053 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 43.0% (1265447 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 44.0% (1294927 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 45.0% (1324307 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 46.0% (1353777 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 47.0% (1383179 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 48.0% (1412643 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 49.0% (1442033 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 50.0% (1471466 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 51.0% (1500895 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 52.0% (1530312 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 53.0% (1559792 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 54.0% (1589192 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 55.0% (1618643 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 56.0% (1648046 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 57.0% (1677511 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 58.0% (1706939 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 59.0% (1736332 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 60.0% (1765765 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 61.0% (1795172 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 62.0% (1824649 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 63.0% (1854037 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 64.0% (1883504 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 65.0% (1912937 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 66.0% (1942351 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 67.0% (1971784 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 68.0% (2001192 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 69.0% (2030650 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 70.0% (2060033 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 71.0% (2089464 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 72.0% (2118894 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 73.0% (2148374 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 74.0% (2177788 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 75.0% (2207181 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 76.0% (2236620 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 77.0% (2266066 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 78.0% (2295480 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 79.0% (2324915 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 80.0% (2354379 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 81.0% (2383809 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 82.0% (2413236 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 83.0% (2442657 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 84.0% (2472084 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 85.0% (2501482 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 86.0% (2530934 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 87.0% (2560331 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 88.0% (2589805 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 89.0% (2619192 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 90.0% (2648647 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 91.0% (2678080 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 92.0% (2707491 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 93.0% (2736949 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 94.0% (2766375 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 95.0% (2795801 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 96.0% (2825237 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 97.0% (2854651 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 98.0% (2884060 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8410 lines 5013 contigs and 3397 short contigs 99.0% (2913482 of 2942852), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] Finished reading 47143 contigs and 1 coverages from 03bins/metabat2_fixed/1507995/temp/1507995.depth.txt [55.1Gb / 503.5Gb]. Ignored 38733 too small contigs.                                     
[00:00:00] Number of target contigs: 5013 of large (>= 2000) and 3397 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 5013
[00:00:00] Allocated memory for TNF [55.1Gb / 503.5Gb]
[00:00:00] Calculating TNF 1.1% (55 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 2.1% (104 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 3.2% (162 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 4.3% (217 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 5.1% (255 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 6.2% (313 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 7.2% (359 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 8.3% (416 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 9.3% (464 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 10.3% (514 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 11.5% (577 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 12.4% (623 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 13.2% (663 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 14.5% (725 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 15.4% (771 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 16.4% (821 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 17.5% (876 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 18.5% (927 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 19.5% (980 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 20.3% (1020 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 21.6% (1081 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 22.6% (1131 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 23.4% (1174 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 24.5% (1226 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 25.5% (1280 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 26.6% (1334 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 27.6% (1386 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 28.8% (1442 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 29.6% (1486 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 30.6% (1536 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 31.8% (1593 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 32.8% (1644 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 33.7% (1689 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 34.8% (1745 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 35.6% (1786 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 36.6% (1837 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 37.7% (1892 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 38.7% (1941 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 39.8% (1993 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 40.7% (2042 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 41.8% (2094 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 42.8% (2148 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 43.8% (2197 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 45.0% (2254 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 45.9% (2301 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 47.1% (2362 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 48.1% (2411 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 49.2% (2464 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 49.9% (2500 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 50.9% (2552 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 51.9% (2603 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 53.0% (2655 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 54.1% (2713 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 55.2% (2766 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 56.2% (2816 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 57.1% (2862 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 58.1% (2915 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 59.2% (2966 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 60.2% (3016 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 61.1% (3061 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 62.4% (3128 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 63.1% (3162 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 64.3% (3223 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 65.4% (3277 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 66.5% (3332 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 67.5% (3382 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 68.4% (3429 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 69.3% (3472 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 70.4% (3530 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 71.4% (3579 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 72.5% (3634 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 73.2% (3672 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 74.3% (3727 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 75.3% (3777 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 76.3% (3827 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 77.5% (3884 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 78.5% (3934 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 79.5% (3985 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 80.6% (4042 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 81.5% (4084 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 82.5% (4137 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 83.5% (4187 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 84.5% (4237 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 85.6% (4291 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 86.7% (4345 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 87.6% (4393 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 88.7% (4446 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 89.8% (4501 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 90.8% (4552 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 91.8% (4601 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 92.8% (4653 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 93.6% (4694 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 94.7% (4745 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 95.8% (4801 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 96.8% (4854 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 97.7% (4899 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 99.0% (4962 of 5013), ETA 0:00:00    
[00:00:00] Calculating TNF 99.7% (4998 of 5013), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [55.1Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.3% (64 of 5013), ETA 0:00:02                   
[00:00:00] ... processing TNF matrix 2.2% (112 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 3.2% (160 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 4.1% (208 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 5.1% (256 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 6.4% (320 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 7.3% (368 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 8.3% (416 of 5013), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 9.3% (464 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 10.2% (512 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 11.5% (576 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 12.4% (624 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 13.4% (672 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 14.4% (720 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 15.3% (768 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 16.3% (816 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 17.6% (880 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 18.5% (928 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 19.5% (976 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 20.5% (1026 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 21.5% (1076 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 22.5% (1126 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 23.5% (1178 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.5% (1226 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 25.7% (1290 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.7% (1340 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.7% (1388 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 28.7% (1439 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.7% (1487 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 30.6% (1535 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.6% (1583 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.9% (1647 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 33.8% (1695 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.8% (1743 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 35.7% (1791 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.7% (1839 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.6% (1887 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 38.9% (1951 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.9% (1999 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 40.9% (2048 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.8% (2097 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.8% (2145 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.8% (2195 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 44.8% (2246 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 45.8% (2296 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 47.1% (2361 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.1% (2409 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.0% (2457 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.0% (2505 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.9% (2553 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 51.9% (2601 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.2% (2665 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 54.1% (2714 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.1% (2764 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.1% (2813 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 57.1% (2862 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 58.0% (2910 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.0% (2959 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 60.3% (3023 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.3% (3071 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 62.2% (3119 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.2% (3169 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 64.2% (3217 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 65.2% (3268 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.2% (3318 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 67.2% (3367 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.4% (3431 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 69.4% (3479 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 70.4% (3528 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 71.3% (3576 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 72.3% (3624 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 73.2% (3672 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.5% (3736 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 75.5% (3784 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 76.4% (3832 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 77.4% (3880 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 78.4% (3928 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 79.7% (3994 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 80.6% (4042 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 81.6% (4091 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 82.6% (4140 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.6% (4190 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 84.6% (4240 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 85.6% (4289 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 86.6% (4340 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 87.6% (4389 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 88.5% (4437 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 89.8% (4501 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 90.7% (4549 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 91.7% (4597 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 92.7% (4646 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 93.6% (4694 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 94.6% (4743 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 95.9% (4807 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 96.8% (4855 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 97.8% (4903 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 98.8% (4952 of 5013), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 99.7% (4999 of 5013), ETA 0:00:00                   
[00:00:00] Finding cutoff p=999 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=999 1.0% (52 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 2.1% (107 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 3.1% (156 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 4.2% (209 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 5.3% (265 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 6.3% (316 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 7.4% (373 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 8.6% (431 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 9.6% (481 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 10.8% (540 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 11.9% (597 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 13.1% (656 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 14.4% (723 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 16.3% (817 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 17.6% (880 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 18.9% (945 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 19.6% (981 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 21.0% (1053 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 21.6% (1085 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 22.9% (1150 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 23.7% (1189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 24.4% (1224 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 25.6% (1283 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 26.8% (1344 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 27.6% (1384 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 29.0% (1454 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 29.8% (1495 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 30.6% (1534 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 31.8% (1596 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 32.6% (1634 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 33.9% (1701 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 34.7% (1740 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 35.9% (1802 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 36.7% (1838 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 38.0% (1906 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 39.1% (1961 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 39.9% (2000 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 40.8% (2046 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 42.3% (2123 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 43.3% (2171 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 44.1% (2212 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 45.3% (2270 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 46.1% (2312 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 46.8% (2347 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 48.3% (2419 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 49.1% (2460 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 50.5% (2531 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 51.2% (2569 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 52.0% (2605 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 53.3% (2673 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 54.1% (2711 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 55.4% (2779 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.1% (2813 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 57.3% (2871 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 58.4% (2930 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 59.0% (2959 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 60.1% (3013 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 61.5% (3082 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 62.1% (3111 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 63.5% (3182 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 64.5% (3232 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 65.5% (3282 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 66.5% (3332 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 67.4% (3379 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 68.2% (3417 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 69.2% (3468 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 70.3% (3523 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 71.5% (3582 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 72.3% (3626 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 73.5% (3684 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 74.6% (3738 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 75.3% (3776 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 76.4% (3830 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 77.4% (3881 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 78.5% (3935 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.7% (3994 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 80.4% (4030 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 81.5% (4088 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 82.5% (4136 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 83.6% (4193 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 84.5% (4237 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 85.6% (4291 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 86.5% (4335 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 87.6% (4389 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.5% (4438 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 89.6% (4494 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 90.6% (4542 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 91.6% (4592 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 92.6% (4644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 94.7% (4745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 95.7% (4796 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 96.7% (4847 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.9; 0 / 5013 (P = 0.00%) round 1]               
[00:00:00] Finding cutoff p=997 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=997 1.8% (89 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 3.0% (150 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 4.2% (210 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 5.3% (266 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 6.6% (332 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 8.2% (410 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 10.3% (517 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 13.5% (675 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 14.5% (728 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 15.4% (771 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 16.3% (818 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 17.8% (892 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 18.6% (933 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 19.5% (976 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 21.0% (1052 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 22.0% (1105 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 23.0% (1152 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 23.9% (1199 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 24.8% (1242 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 25.8% (1293 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 26.9% (1346 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 27.8% (1394 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 28.8% (1443 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 29.8% (1494 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 30.8% (1543 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 32.1% (1608 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 33.1% (1661 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 34.1% (1707 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 35.2% (1765 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 36.4% (1825 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 37.8% (1896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 38.8% (1943 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 40.1% (2010 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 41.0% (2053 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 42.3% (2120 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 43.1% (2162 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 44.0% (2204 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 44.8% (2244 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 46.1% (2313 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 47.0% (2355 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 48.6% (2434 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 49.5% (2479 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 50.5% (2530 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 51.4% (2576 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 52.5% (2633 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 53.4% (2676 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 54.1% (2710 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 55.5% (2781 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 56.3% (2822 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 57.1% (2860 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 58.3% (2925 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 59.4% (2976 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 60.1% (3013 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 61.2% (3068 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 62.4% (3129 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 63.1% (3162 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 64.4% (3228 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 65.2% (3270 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 66.2% (3319 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 67.2% (3369 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 68.4% (3430 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 69.4% (3478 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 70.3% (3523 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 71.2% (3570 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 72.4% (3628 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 73.3% (3674 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 74.4% (3732 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 75.4% (3778 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 76.4% (3829 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 77.6% (3888 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 78.6% (3941 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 79.6% (3989 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 80.5% (4036 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 81.4% (4083 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 82.6% (4143 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 83.4% (4183 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 84.5% (4234 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 85.5% (4287 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 86.5% (4338 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 87.5% (4387 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 88.6% (4440 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 89.6% (4491 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 90.6% (4541 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 91.7% (4596 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 92.6% (4643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 93.6% (4693 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 94.7% (4746 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 95.6% (4794 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 96.7% (4847 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 97.7% (4898 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.7; 4 / 5013 (P = 0.08%) round 2]               
[00:00:00] Finding cutoff p=994 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=994 1.5% (75 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 2.9% (146 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 4.2% (211 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 5.6% (282 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 7.0% (353 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 8.4% (423 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 9.9% (494 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 11.5% (577 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 13.2% (660 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 14.8% (743 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 16.8% (843 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 18.7% (939 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 19.9% (996 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 22.4% (1122 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 23.5% (1178 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 24.6% (1234 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 25.7% (1289 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 26.8% (1344 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 28.0% (1402 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 29.1% (1460 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 30.4% (1522 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 31.5% (1581 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 32.8% (1646 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 34.2% (1714 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 35.4% (1773 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 36.7% (1841 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 37.9% (1900 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 38.9% (1951 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 39.8% (1995 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 40.8% (2045 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 42.3% (2118 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 43.3% (2171 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 44.2% (2216 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 45.2% (2265 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 46.2% (2318 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 47.2% (2367 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 48.2% (2416 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 49.2% (2468 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 50.2% (2519 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 51.3% (2572 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 52.2% (2617 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 53.1% (2660 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 54.0% (2705 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 55.4% (2779 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 56.3% (2823 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 57.1% (2863 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 58.2% (2919 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 59.4% (2976 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 60.0% (3010 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 61.2% (3066 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 62.3% (3122 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 63.4% (3180 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 64.4% (3227 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 65.3% (3273 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 66.2% (3317 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 67.4% (3379 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 68.3% (3426 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 69.3% (3475 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 70.3% (3522 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 71.2% (3571 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 72.5% (3633 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 73.3% (3674 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 74.3% (3726 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 75.4% (3782 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 76.5% (3834 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 77.4% (3879 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 78.5% (3935 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 79.5% (3986 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 80.5% (4033 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 81.4% (4082 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 82.5% (4138 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 83.6% (4189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 84.6% (4239 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 85.6% (4293 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 86.6% (4343 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 87.5% (4386 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 88.6% (4440 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 89.5% (4488 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 90.5% (4539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 92.6% (4643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 94.6% (4744 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 95.7% (4796 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 96.7% (4849 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.4; 92 / 5013 (P = 1.84%) round 3]               
[00:00:00] Finding cutoff p=992 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=992 1.8% (92 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 3.6% (179 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 5.1% (256 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 6.6% (332 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 8.2% (411 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 10.5% (528 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 12.3% (615 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 14.0% (700 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 15.6% (782 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 17.5% (875 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 19.5% (978 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 21.8% (1091 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 23.0% (1155 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 24.4% (1221 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 25.6% (1284 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 26.9% (1347 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 28.2% (1412 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 29.6% (1485 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 30.9% (1549 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 32.2% (1616 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 33.7% (1687 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 35.1% (1759 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 36.4% (1827 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 37.7% (1892 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 38.9% (1948 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 39.9% (1999 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 41.0% (2054 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 42.0% (2106 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 43.0% (2158 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 44.1% (2211 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 45.3% (2270 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 46.4% (2324 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 47.4% (2376 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 48.5% (2432 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 49.6% (2487 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 50.4% (2529 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 51.4% (2575 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 52.2% (2617 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 53.1% (2661 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 54.0% (2705 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 55.4% (2777 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 56.3% (2821 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 57.2% (2866 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 58.0% (2908 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 59.2% (2969 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 60.4% (3029 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 61.5% (3085 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 62.3% (3125 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 63.1% (3164 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 64.2% (3220 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 65.3% (3272 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 66.2% (3321 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 67.2% (3369 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 68.2% (3417 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 69.5% (3483 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 70.2% (3519 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 71.4% (3577 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 72.5% (3634 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 73.4% (3679 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 74.5% (3735 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 75.4% (3780 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 76.6% (3838 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 77.4% (3879 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 78.4% (3931 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 79.4% (3980 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 80.4% (4030 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 81.4% (4082 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 82.4% (4131 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 83.5% (4185 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 84.5% (4235 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 85.5% (4286 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 86.7% (4345 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 87.6% (4390 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 88.6% (4442 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 89.7% (4495 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 90.7% (4545 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 91.7% (4597 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 92.7% (4645 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 94.7% (4745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.2; 192 / 5013 (P = 3.83%) round 4]               
[00:00:00] Finding cutoff p=990 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=990 1.7% (84 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 3.3% (165 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 4.8% (240 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 6.3% (315 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 7.8% (393 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 9.5% (477 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 11.2% (559 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 12.8% (644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 14.5% (726 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 16.2% (814 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 18.0% (902 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 20.3% (1019 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 22.2% (1114 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 23.5% (1177 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 24.8% (1244 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 26.1% (1310 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 27.4% (1376 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 28.8% (1446 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 30.1% (1508 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 31.6% (1585 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 33.0% (1653 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 34.3% (1717 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 35.6% (1787 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 37.0% (1853 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 38.2% (1917 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 39.3% (1968 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 40.3% (2022 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 41.4% (2074 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 42.4% (2128 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 43.6% (2184 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 44.6% (2236 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 45.6% (2288 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 46.8% (2347 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 47.9% (2399 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 49.0% (2455 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 50.0% (2504 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 50.9% (2550 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 52.2% (2617 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 53.2% (2666 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 54.0% (2708 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 54.9% (2754 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 56.4% (2825 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 57.9% (2902 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 58.8% (2947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 59.5% (2982 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 60.2% (3019 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 61.4% (3078 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 62.2% (3119 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 63.4% (3178 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 64.4% (3226 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 65.3% (3274 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 66.3% (3323 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 67.3% (3373 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 68.2% (3418 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 69.2% (3470 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 70.2% (3520 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 71.5% (3583 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 72.3% (3626 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 73.4% (3681 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 74.3% (3723 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 75.4% (3779 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 76.5% (3834 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 77.6% (3890 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 78.4% (3930 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 79.4% (3980 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 81.6% (4089 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 82.6% (4141 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 83.6% (4189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 84.6% (4239 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 85.5% (4286 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 86.6% (4339 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 87.6% (4391 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 88.6% (4444 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 89.6% (4493 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 90.6% (4543 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 91.6% (4594 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 92.7% (4645 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 93.7% (4698 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 94.6% (4743 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 96.7% (4847 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.0; 301 / 5013 (P = 6.00%) round 5]               
[00:00:00] Finding cutoff p=987 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=987 1.8% (88 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 3.3% (166 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 4.9% (245 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 6.5% (326 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 8.0% (400 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 9.6% (479 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 11.2% (561 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 12.9% (645 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 14.5% (728 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 16.4% (821 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 18.3% (915 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 20.4% (1024 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 22.0% (1102 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 23.2% (1164 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 24.4% (1222 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 25.8% (1291 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 27.1% (1357 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 28.4% (1423 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 29.7% (1491 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 31.0% (1553 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 32.3% (1618 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 33.6% (1684 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 35.0% (1756 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 36.4% (1824 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 37.5% (1878 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 38.4% (1926 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 39.6% (1983 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 40.7% (2038 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 41.6% (2086 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 42.8% (2145 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 43.8% (2196 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 44.8% (2246 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 45.8% (2296 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 46.8% (2348 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 47.8% (2398 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 48.9% (2450 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 50.2% (2516 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 51.6% (2586 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 52.5% (2630 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 53.4% (2676 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 54.2% (2717 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 55.0% (2759 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 56.4% (2828 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 57.4% (2875 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 58.2% (2918 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 59.0% (2960 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 60.4% (3026 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 61.1% (3062 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 62.3% (3121 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 63.4% (3176 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 64.1% (3213 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 65.2% (3269 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 66.3% (3324 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 67.3% (3373 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 68.2% (3421 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 69.2% (3469 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 70.2% (3519 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 71.5% (3585 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 72.6% (3638 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 73.3% (3673 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 74.4% (3731 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 75.5% (3787 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 76.3% (3827 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 77.5% (3886 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 78.4% (3928 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 79.4% (3982 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 80.5% (4033 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 81.4% (4082 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 82.7% (4144 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 83.6% (4193 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 84.6% (4239 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 85.5% (4286 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 86.5% (4338 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 87.6% (4390 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 88.6% (4442 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 89.6% (4493 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 90.6% (4542 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 91.6% (4592 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 92.6% (4642 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 93.7% (4695 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 94.7% (4746 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 95.7% (4798 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 96.7% (4846 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.7; 484 / 5013 (P = 9.65%) round 6]               
[00:00:00] Finding cutoff p=982 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=982 1.7% (86 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 3.2% (162 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 4.7% (238 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 6.4% (322 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 7.9% (394 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 9.4% (469 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 10.9% (548 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 12.5% (626 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 14.1% (707 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 15.7% (788 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 17.8% (890 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 19.7% (987 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 21.8% (1091 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 22.9% (1149 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 24.2% (1211 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 25.5% (1279 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 26.7% (1338 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 27.9% (1401 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 29.2% (1462 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 30.4% (1523 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 31.6% (1586 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 32.9% (1649 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 34.2% (1715 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 35.4% (1777 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 36.8% (1845 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 38.1% (1908 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 39.2% (1964 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 40.1% (2011 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 41.0% (2057 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 42.1% (2111 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 43.1% (2160 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 44.8% (2244 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 45.8% (2296 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 46.9% (2349 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 47.9% (2399 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 49.4% (2475 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 50.4% (2528 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 51.3% (2574 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 52.2% (2617 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 53.1% (2663 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 54.0% (2709 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 55.0% (2755 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 56.3% (2824 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 57.2% (2867 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 58.0% (2908 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 59.3% (2973 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 60.2% (3016 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 61.4% (3079 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 62.1% (3113 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 63.2% (3170 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 64.4% (3227 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 65.5% (3285 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 66.2% (3321 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 67.3% (3372 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 68.2% (3420 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 69.2% (3469 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 70.5% (3533 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 71.4% (3581 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 72.4% (3629 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 73.4% (3678 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 74.3% (3726 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 75.5% (3783 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 76.3% (3827 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 77.4% (3881 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 78.6% (3940 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 79.4% (3979 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 80.6% (4041 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 81.4% (4080 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 82.6% (4140 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 83.6% (4189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 84.6% (4240 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 85.6% (4292 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 86.6% (4341 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 87.6% (4392 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 88.6% (4442 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 89.5% (4488 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 90.7% (4545 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 92.6% (4641 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 94.6% (4744 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 95.7% (4797 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 96.7% (4847 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.2; 821 / 5013 (P = 16.38%) round 7]               
[00:00:00] Finding cutoff p=979 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=979 1.7% (83 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 3.1% (155 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 4.4% (223 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 6.2% (310 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 7.7% (386 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 9.2% (460 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 10.6% (533 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 12.2% (611 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 13.7% (689 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 15.5% (775 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 17.1% (856 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 19.0% (951 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 21.0% (1055 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 22.5% (1128 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 23.7% (1189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 25.1% (1258 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 26.5% (1326 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 27.6% (1386 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 28.8% (1444 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 30.1% (1509 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 31.3% (1569 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 32.5% (1628 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 33.9% (1697 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 35.1% (1759 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 36.4% (1823 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 38.1% (1909 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 39.1% (1958 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 40.0% (2007 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 41.0% (2055 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 42.0% (2106 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 43.0% (2155 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 44.0% (2204 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 45.0% (2255 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 46.1% (2309 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 47.0% (2358 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 48.0% (2407 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 49.0% (2456 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 50.1% (2510 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 51.0% (2556 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 51.9% (2601 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 53.1% (2664 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 53.9% (2704 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 55.3% (2772 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 56.2% (2816 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 57.0% (2858 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 58.3% (2922 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 59.1% (2962 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 60.4% (3029 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 61.2% (3068 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 62.3% (3124 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 63.4% (3179 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 64.2% (3216 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 65.2% (3269 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 66.3% (3325 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 67.4% (3381 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 68.2% (3419 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 69.2% (3471 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 70.3% (3523 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 71.3% (3572 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 72.5% (3635 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 73.5% (3683 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 74.5% (3736 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 75.5% (3783 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 76.5% (3836 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 77.5% (3883 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 78.6% (3941 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 79.4% (3979 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 80.4% (4031 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 81.4% (4082 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 82.6% (4143 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 83.6% (4192 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 84.6% (4243 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 85.5% (4284 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 86.6% (4339 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 87.6% (4389 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 88.6% (4442 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 89.6% (4492 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 90.6% (4541 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 91.6% (4592 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 92.6% (4644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 93.7% (4696 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 94.6% (4744 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 95.7% (4797 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.9; 1009 / 5013 (P = 20.13%) round 8]               
[00:00:00] Finding cutoff p=974 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=974 1.6% (81 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 3.1% (153 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 4.4% (220 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 6.1% (308 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 7.6% (381 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 9.0% (453 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 10.4% (523 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 12.0% (601 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 13.4% (673 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 15.0% (751 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 16.6% (833 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 18.3% (918 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 20.1% (1006 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 22.0% (1101 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 23.1% (1159 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 24.3% (1217 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 25.5% (1277 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 26.7% (1336 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 28.5% (1431 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 29.8% (1494 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 31.0% (1553 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 32.2% (1615 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 33.6% (1683 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 34.7% (1740 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 35.9% (1800 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 37.2% (1865 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 38.5% (1930 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 39.6% (1987 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 40.6% (2035 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 41.5% (2082 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 42.5% (2130 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 43.6% (2185 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 44.5% (2233 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 45.5% (2281 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 46.5% (2329 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 47.5% (2383 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 48.5% (2429 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 49.5% (2479 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 50.6% (2535 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 51.5% (2581 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 52.3% (2621 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 53.1% (2660 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 53.9% (2704 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 55.2% (2766 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 56.0% (2809 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 57.4% (2875 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 58.2% (2917 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 59.4% (2978 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 60.2% (3020 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 61.1% (3064 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 62.3% (3123 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 63.4% (3177 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 64.4% (3229 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 65.1% (3264 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 66.2% (3318 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 67.3% (3373 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 68.4% (3430 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 69.4% (3481 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 70.4% (3528 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 71.3% (3576 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 72.3% (3622 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 73.2% (3672 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 74.6% (3740 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 75.6% (3788 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 76.4% (3832 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 77.3% (3877 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 78.5% (3933 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 79.5% (3986 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 80.6% (4042 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 81.6% (4090 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 82.5% (4138 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 83.5% (4187 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 84.5% (4235 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 85.7% (4295 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 86.5% (4337 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 87.5% (4388 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 88.5% (4439 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 89.6% (4492 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 90.6% (4540 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 91.6% (4591 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 92.6% (4643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 93.7% (4696 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 94.6% (4743 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 95.8% (4802 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 96.7% (4847 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=974 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.4; 1330 / 5013 (P = 26.53%) round 9]               
[00:00:00] Finding cutoff p=971 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=971 1.6% (79 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 3.0% (149 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 4.3% (214 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 6.1% (305 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 7.4% (373 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 8.8% (443 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 10.3% (515 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 11.9% (595 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 13.4% (670 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 14.9% (746 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 18.3% (918 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 20.1% (1006 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 22.1% (1107 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 23.2% (1164 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 24.5% (1230 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 25.7% (1286 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 26.9% (1347 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 28.1% (1407 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 29.3% (1467 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 30.4% (1525 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 31.5% (1581 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 32.8% (1642 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 34.1% (1707 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 35.2% (1764 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 36.5% (1829 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 37.7% (1892 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 38.7% (1941 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 39.7% (1992 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 40.7% (2042 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 41.7% (2091 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 42.8% (2144 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 43.9% (2199 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 44.9% (2249 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 46.3% (2321 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 47.2% (2367 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 48.2% (2418 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 49.2% (2466 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 50.4% (2527 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 51.3% (2571 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 52.2% (2615 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 52.9% (2652 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 54.2% (2715 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 55.0% (2755 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 56.3% (2822 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 57.1% (2864 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 58.4% (2926 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 59.2% (2967 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 60.4% (3028 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 61.2% (3070 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 62.1% (3111 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 63.1% (3164 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 64.1% (3213 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 65.2% (3266 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 66.3% (3323 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 67.4% (3377 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 68.5% (3435 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 69.2% (3470 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 70.3% (3524 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 71.5% (3585 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 72.5% (3635 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 73.5% (3684 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 74.4% (3732 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 75.4% (3779 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 76.5% (3835 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 77.6% (3889 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 78.4% (3930 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 79.5% (3984 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 80.6% (4041 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 81.5% (4086 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 82.4% (4132 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 83.4% (4182 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 84.4% (4233 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 85.6% (4290 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 86.5% (4336 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 87.5% (4386 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 88.6% (4440 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 89.6% (4490 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 90.7% (4547 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 91.7% (4596 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 92.7% (4645 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 94.7% (4745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 95.8% (4800 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 96.7% (4846 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.1; 1508 / 5013 (P = 30.08%) round 10]               
[00:00:00] Finding cutoff p=968 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=968 1.6% (81 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 3.0% (149 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 4.3% (215 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 6.8% (342 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 8.2% (413 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 9.8% (489 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 11.4% (569 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 12.8% (640 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 14.3% (715 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 16.0% (800 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 17.5% (875 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 19.1% (959 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 21.2% (1065 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 22.7% (1138 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 23.8% (1192 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 24.9% (1249 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 26.1% (1310 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 27.3% (1368 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 28.5% (1428 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 29.7% (1489 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 30.8% (1543 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 32.0% (1604 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 33.3% (1668 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 34.5% (1727 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 35.7% (1790 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 36.9% (1851 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 38.1% (1912 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 39.1% (1960 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 40.1% (2008 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 41.1% (2058 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 42.0% (2103 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 43.0% (2158 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 44.0% (2204 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 45.0% (2257 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 46.0% (2304 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 46.9% (2352 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 47.9% (2401 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 49.5% (2483 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 50.5% (2530 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 51.3% (2573 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 52.1% (2613 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 53.3% (2672 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 54.2% (2716 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 55.0% (2755 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 56.2% (2818 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 57.0% (2859 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 58.3% (2925 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 59.1% (2964 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 60.4% (3026 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 61.2% (3067 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 62.3% (3121 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 63.4% (3178 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 64.4% (3226 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 65.4% (3277 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 66.1% (3315 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 67.2% (3367 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 68.3% (3424 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 69.3% (3473 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 70.3% (3522 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 71.5% (3585 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 72.4% (3631 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 73.5% (3686 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 74.5% (3735 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 75.4% (3782 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 76.3% (3826 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 77.4% (3881 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 78.5% (3936 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 79.9% (4003 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 80.6% (4041 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 81.6% (4092 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 82.6% (4141 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 83.6% (4189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 84.6% (4240 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 85.6% (4290 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 86.6% (4342 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 87.6% (4392 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 88.7% (4445 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 89.5% (4488 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 90.6% (4540 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 92.6% (4644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 93.6% (4693 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 94.7% (4747 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.8; 1677 / 5013 (P = 33.45%) round 11]               
[00:00:00] Finding cutoff p=963 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=963 1.6% (81 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 2.9% (147 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 4.1% (207 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 6.0% (300 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 7.4% (373 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 8.8% (443 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 10.2% (513 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 11.7% (585 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 13.1% (656 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 14.6% (731 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 16.0% (804 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 17.6% (880 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 19.2% (960 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 21.0% (1052 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 22.6% (1132 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 23.7% (1190 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 25.0% (1252 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 26.1% (1309 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 27.3% (1367 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 28.4% (1423 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 29.6% (1482 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 30.8% (1542 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 32.0% (1605 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 33.2% (1664 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 34.4% (1722 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 35.5% (1782 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 36.6% (1836 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 37.8% (1897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 39.0% (1954 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 39.9% (1998 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 40.8% (2044 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 41.8% (2097 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 42.7% (2143 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 44.2% (2217 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 45.2% (2267 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 46.1% (2313 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 47.7% (2389 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 49.0% (2454 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 50.0% (2504 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 51.2% (2569 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 52.0% (2607 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 53.2% (2665 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 54.0% (2707 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 55.1% (2764 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 56.4% (2826 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 57.2% (2865 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 58.4% (2928 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 59.1% (2965 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 60.4% (3030 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 61.3% (3073 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 62.4% (3126 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 63.4% (3177 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 64.3% (3225 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 65.4% (3281 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 66.4% (3330 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 67.5% (3383 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 68.5% (3435 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 69.2% (3469 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 70.2% (3519 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 71.4% (3579 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 72.5% (3633 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 73.2% (3672 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 74.5% (3735 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 75.4% (3782 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 76.3% (3826 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 77.3% (3877 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 78.7% (3943 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 79.5% (3984 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 80.5% (4034 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 81.5% (4084 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 82.6% (4142 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 83.6% (4192 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 84.5% (4235 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 85.5% (4288 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 86.6% (4341 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 87.5% (4386 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 88.5% (4437 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 89.5% (4488 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 90.5% (4539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 91.6% (4594 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 92.7% (4645 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 93.7% (4696 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 94.6% (4743 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 95.8% (4801 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 96.7% (4846 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.3; 1947 / 5013 (P = 38.84%) round 12]               
[00:00:00] Finding cutoff p=959 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=959 1.6% (78 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 3.0% (148 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 4.0% (203 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 5.8% (291 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 7.1% (355 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 8.4% (423 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 9.8% (490 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 11.2% (563 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 12.7% (638 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 14.1% (709 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 15.6% (783 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 17.0% (854 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 18.6% (931 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 20.2% (1012 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 22.0% (1102 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 23.1% (1157 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 24.2% (1213 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 25.3% (1266 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 26.3% (1318 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 27.4% (1376 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 28.6% (1433 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 29.7% (1488 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 31.1% (1558 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 32.2% (1616 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 33.4% (1674 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 34.4% (1725 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 35.5% (1782 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 36.7% (1839 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 38.5% (1929 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 39.6% (1983 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 40.6% (2035 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 41.7% (2088 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 42.6% (2136 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 43.5% (2181 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 44.5% (2233 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 45.4% (2278 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 46.4% (2324 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 47.3% (2373 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 48.6% (2436 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 49.7% (2489 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 50.6% (2537 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 51.5% (2584 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 52.3% (2624 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 53.0% (2658 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 54.2% (2719 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 55.1% (2761 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 56.3% (2823 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 57.1% (2862 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 58.3% (2921 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 59.0% (2960 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 60.2% (3018 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 61.4% (3080 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 62.2% (3118 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 63.3% (3175 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 64.1% (3214 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 65.1% (3265 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 66.1% (3315 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 67.3% (3374 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 68.3% (3426 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 69.5% (3484 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 70.2% (3519 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 71.2% (3571 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 72.5% (3634 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 73.3% (3674 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 74.5% (3736 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 75.5% (3783 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 76.4% (3828 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 77.4% (3881 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 78.4% (3932 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 79.7% (3997 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 80.4% (4030 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 81.6% (4089 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 82.4% (4133 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 83.6% (4192 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 84.7% (4245 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 85.6% (4291 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 86.5% (4337 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 87.5% (4386 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 88.5% (4439 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 89.5% (4489 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 90.5% (4539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 92.6% (4644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 93.7% (4695 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 94.6% (4743 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 95.7% (4797 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 96.7% (4847 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.9; 2144 / 5013 (P = 42.77%) round 13]               
[00:00:00] Finding cutoff p=954 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=954 1.6% (78 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 2.7% (137 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 3.6% (179 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 4.5% (228 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 5.1% (256 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 6.3% (318 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 7.4% (369 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 8.6% (433 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 10.0% (502 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 11.4% (569 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 12.6% (634 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 14.1% (706 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 15.5% (777 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 16.8% (842 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 18.2% (911 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 19.6% (984 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 21.1% (1060 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 22.9% (1148 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 25.6% (1283 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 26.7% (1337 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 28.1% (1408 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 29.1% (1461 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 30.4% (1524 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 31.6% (1583 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 32.6% (1636 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 33.8% (1695 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 35.0% (1757 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 36.2% (1816 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 37.1% (1859 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 38.2% (1914 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 39.0% (1957 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 39.9% (2002 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 40.8% (2046 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 42.2% (2114 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 42.9% (2153 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 44.2% (2218 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 45.3% (2273 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 46.1% (2311 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 47.0% (2356 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 48.0% (2405 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 48.9% (2451 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 50.4% (2525 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 51.3% (2572 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 52.2% (2618 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 53.1% (2664 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 53.9% (2704 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 55.1% (2764 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 56.3% (2822 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 57.1% (2863 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 58.3% (2924 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 59.1% (2964 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 60.3% (3024 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 61.1% (3064 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 62.2% (3119 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 63.4% (3176 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 64.3% (3222 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 65.2% (3270 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 66.3% (3325 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 67.3% (3374 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 68.3% (3422 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 69.3% (3473 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 70.7% (3542 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 71.4% (3579 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 72.3% (3626 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 73.2% (3672 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 74.4% (3731 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 75.3% (3774 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 76.3% (3827 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 77.5% (3883 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 78.5% (3933 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 79.4% (3982 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 80.5% (4033 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 81.5% (4087 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 82.8% (4152 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 83.6% (4190 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 84.6% (4242 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 85.5% (4286 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 86.6% (4342 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 87.6% (4391 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 88.5% (4438 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 89.5% (4488 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 90.6% (4544 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 92.7% (4645 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 93.6% (4694 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 94.7% (4747 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 96.7% (4846 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.4; 2365 / 5013 (P = 47.18%) round 14]               
[00:00:00] Finding cutoff p=950 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=950 1.5% (77 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 2.7% (137 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 3.9% (197 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 5.7% (284 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 7.0% (349 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 8.3% (415 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 9.5% (477 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 11.4% (572 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 12.8% (643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 14.2% (714 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 15.7% (785 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 17.2% (861 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 18.6% (933 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 20.1% (1006 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 22.0% (1103 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 23.2% (1161 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 24.2% (1213 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 25.2% (1264 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 26.4% (1324 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 27.5% (1377 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 28.5% (1431 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 29.6% (1486 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 31.0% (1556 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 32.2% (1612 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 33.3% (1668 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 34.3% (1721 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 35.4% (1777 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 36.6% (1834 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 37.7% (1888 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 38.7% (1942 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 39.8% (1996 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 41.1% (2060 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 41.9% (2100 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 42.8% (2146 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 43.8% (2194 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 45.0% (2256 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 45.8% (2297 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 47.3% (2369 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 48.3% (2422 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 49.2% (2466 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 50.1% (2512 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 51.0% (2556 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 52.2% (2618 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 53.0% (2657 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 54.1% (2714 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 55.3% (2774 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 56.1% (2811 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 57.4% (2875 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 58.5% (2931 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 59.3% (2971 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 60.4% (3027 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 61.1% (3065 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 62.3% (3123 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 63.5% (3181 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 64.2% (3217 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 65.3% (3271 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 66.2% (3319 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 67.2% (3368 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 68.2% (3420 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 69.3% (3475 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 70.4% (3528 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 71.3% (3576 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 72.6% (3637 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 73.4% (3682 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 74.3% (3726 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 75.3% (3774 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 76.3% (3825 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 77.8% (3902 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 78.6% (3940 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 79.6% (3992 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 80.4% (4031 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 81.5% (4085 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 82.5% (4138 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 83.6% (4189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 84.5% (4236 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 85.6% (4291 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 86.5% (4336 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 87.6% (4393 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 88.6% (4442 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 89.6% (4492 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 90.6% (4544 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 91.7% (4597 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 92.6% (4644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 93.6% (4693 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 94.7% (4746 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 95.7% (4797 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 96.7% (4847 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 97.7% (4898 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.0; 2556 / 5013 (P = 50.99%) round 15]               
[00:00:00] Finding cutoff p=947 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=947 1.5% (75 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 2.7% (136 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 3.8% (193 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 5.6% (282 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 6.9% (345 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 8.2% (409 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 9.3% (468 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 10.7% (537 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 12.1% (606 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 13.6% (684 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 14.9% (745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 16.3% (819 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 17.8% (891 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 19.3% (968 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 21.3% (1066 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 22.4% (1124 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 23.4% (1175 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 24.5% (1226 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 25.6% (1282 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 26.6% (1332 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 27.6% (1386 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 28.7% (1439 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 30.2% (1512 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 31.2% (1563 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 32.5% (1629 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 33.5% (1677 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 34.4% (1726 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 35.4% (1774 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 36.6% (1835 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 37.9% (1898 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 38.7% (1942 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 40.2% (2016 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 41.2% (2064 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 42.1% (2110 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 42.9% (2152 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 43.8% (2194 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 45.1% (2263 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 46.6% (2336 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 48.1% (2412 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 49.0% (2457 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 50.0% (2507 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 50.9% (2551 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 52.1% (2610 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 52.9% (2652 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 54.0% (2706 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 55.1% (2761 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 56.2% (2819 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 57.1% (2860 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 58.2% (2917 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 59.3% (2972 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 60.4% (3027 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 61.2% (3066 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 62.3% (3125 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 63.1% (3162 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 64.1% (3213 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 65.4% (3277 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 66.4% (3329 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 67.4% (3378 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 68.4% (3429 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 69.4% (3480 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 70.6% (3540 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 71.3% (3573 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 72.3% (3622 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 73.2% (3672 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 74.4% (3731 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 75.6% (3789 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 76.3% (3826 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 77.5% (3883 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 78.4% (3930 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 79.4% (3982 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 80.4% (4031 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 81.4% (4082 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 82.8% (4152 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 83.7% (4198 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 84.6% (4241 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 85.7% (4297 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 86.6% (4339 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 87.5% (4388 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 88.5% (4438 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 89.6% (4490 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 90.6% (4544 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 92.6% (4644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 93.6% (4694 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 94.6% (4743 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 95.7% (4797 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 96.7% (4848 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.7; 2689 / 5013 (P = 53.64%) round 16]               
[00:00:00] Finding cutoff p=944 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=944 1.4% (72 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 2.6% (131 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 3.7% (184 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 5.6% (279 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 6.8% (343 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 8.2% (411 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 9.5% (478 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 10.8% (539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 12.1% (607 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 13.4% (671 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 14.7% (735 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 16.0% (800 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 17.4% (874 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 18.7% (937 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 20.5% (1026 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 22.1% (1107 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 23.1% (1156 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 24.1% (1209 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 25.1% (1259 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 26.2% (1311 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 27.0% (1355 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 28.1% (1408 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 30.0% (1504 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 31.0% (1554 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 32.3% (1617 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 33.4% (1674 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 34.4% (1726 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 35.4% (1773 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 36.9% (1852 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 38.2% (1915 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 39.2% (1967 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 40.7% (2040 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 42.0% (2104 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 43.2% (2165 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 44.1% (2209 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 44.9% (2249 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 46.3% (2321 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 47.5% (2379 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 48.3% (2422 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 49.3% (2469 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 50.2% (2515 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 51.0% (2558 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 52.3% (2622 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 53.0% (2656 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 54.2% (2716 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 55.3% (2773 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 56.2% (2815 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 57.0% (2856 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 58.1% (2914 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 59.1% (2963 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 60.3% (3021 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 61.4% (3078 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 62.2% (3117 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 63.4% (3177 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 64.1% (3213 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 65.4% (3277 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 66.4% (3329 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 67.4% (3379 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 68.4% (3429 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 69.5% (3482 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 70.9% (3552 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 71.5% (3583 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 72.4% (3630 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 73.3% (3677 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 74.3% (3724 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 75.4% (3779 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 76.5% (3837 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 77.7% (3893 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 78.6% (3939 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 79.4% (3981 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 80.5% (4037 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 81.6% (4090 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 82.9% (4156 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 83.5% (4188 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 84.4% (4233 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 85.5% (4284 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 86.6% (4340 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 87.6% (4391 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 88.5% (4438 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 89.7% (4496 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 90.6% (4544 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 91.7% (4596 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 92.7% (4645 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 93.7% (4695 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 94.7% (4748 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 95.8% (4802 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 96.7% (4846 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 97.7% (4899 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.4; 2805 / 5013 (P = 55.95%) round 17]               
[00:00:00] Finding cutoff p=940 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=940 1.4% (72 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 2.6% (128 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 3.5% (175 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 5.2% (259 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 6.8% (339 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 8.0% (403 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 9.3% (464 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 10.5% (525 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 11.7% (585 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 13.0% (650 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 14.1% (705 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 15.5% (775 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 16.8% (841 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 18.3% (917 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 19.7% (990 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 21.6% (1083 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 22.5% (1130 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 23.5% (1177 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 24.8% (1242 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 25.8% (1292 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 26.8% (1345 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 28.1% (1407 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 29.1% (1458 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 30.2% (1512 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 31.0% (1554 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 32.2% (1616 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 33.2% (1662 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 34.0% (1705 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 35.0% (1757 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 36.2% (1815 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 37.9% (1901 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 38.9% (1948 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 39.9% (2000 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 40.9% (2050 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 41.7% (2092 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 43.1% (2163 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 44.6% (2235 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 45.6% (2285 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 46.3% (2323 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 47.2% (2367 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 48.1% (2409 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 49.1% (2459 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 49.9% (2503 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 51.2% (2569 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 52.1% (2611 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 52.9% (2652 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 53.9% (2704 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 55.1% (2762 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 56.4% (2825 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 57.0% (2859 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 58.2% (2917 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 59.1% (2965 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 60.4% (3028 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 61.1% (3064 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 62.3% (3125 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 63.5% (3181 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 64.1% (3214 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 65.2% (3266 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 66.3% (3323 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 67.3% (3373 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 68.2% (3421 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 69.6% (3490 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 70.3% (3524 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 71.6% (3591 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 72.6% (3640 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 73.3% (3675 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 74.3% (3724 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 75.4% (3778 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 76.8% (3852 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 77.6% (3889 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 78.4% (3931 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 79.5% (3985 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 80.5% (4035 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 81.8% (4103 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 82.4% (4132 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 83.5% (4184 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 84.6% (4240 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 85.5% (4285 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 86.6% (4343 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 87.5% (4387 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 88.6% (4444 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 89.6% (4493 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 90.5% (4539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 92.7% (4649 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 93.7% (4695 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 94.7% (4749 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 95.7% (4796 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 96.7% (4846 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.0; 2909 / 5013 (P = 58.03%) round 18]               
[00:00:00] Finding cutoff p=936 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=936 1.4% (71 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 2.5% (126 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 3.7% (185 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 5.4% (269 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 6.6% (329 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 7.8% (389 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 9.8% (490 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 11.0% (549 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 12.2% (610 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 13.4% (673 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 14.4% (724 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 15.7% (786 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 17.0% (852 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 18.2% (912 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 19.7% (987 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 21.9% (1099 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 22.8% (1145 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 23.7% (1188 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 24.6% (1231 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 25.5% (1280 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 26.5% (1329 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 28.5% (1427 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 29.4% (1476 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 30.6% (1532 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 31.6% (1584 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 32.7% (1637 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 33.9% (1698 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 34.9% (1749 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 35.9% (1798 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 37.5% (1881 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 38.5% (1930 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 39.5% (1978 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 40.5% (2031 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 41.4% (2074 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 42.2% (2116 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 42.9% (2152 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 44.5% (2233 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 45.5% (2280 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 46.3% (2321 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 47.3% (2369 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 48.1% (2413 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 49.1% (2462 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 49.9% (2501 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 51.3% (2573 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 52.0% (2609 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 53.2% (2668 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 54.2% (2717 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 55.4% (2777 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 56.2% (2816 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 57.0% (2857 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 58.0% (2907 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 59.3% (2975 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 60.0% (3010 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 61.2% (3069 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 62.2% (3117 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 63.2% (3166 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 64.2% (3217 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 65.2% (3266 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 66.4% (3331 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 67.3% (3376 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 68.3% (3423 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 69.5% (3482 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 70.4% (3528 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 71.4% (3577 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 72.3% (3622 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 73.2% (3672 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 74.3% (3726 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 75.3% (3777 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 76.4% (3830 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 77.5% (3883 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 78.5% (3933 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 79.6% (3989 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 80.5% (4035 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 81.9% (4105 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 82.5% (4137 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 83.7% (4194 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 84.6% (4242 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 85.5% (4285 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 86.6% (4340 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 87.6% (4390 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 88.5% (4438 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 89.5% (4488 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 90.6% (4542 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 91.7% (4595 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 92.8% (4651 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 93.7% (4698 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 94.7% (4747 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 95.7% (4796 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.6; 3043 / 5013 (P = 60.70%) round 19]               
[00:00:00] Finding cutoff p=931 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=931 1.4% (71 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 2.5% (126 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 3.3% (166 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 4.8% (243 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 5.9% (296 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 7.1% (357 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 8.2% (411 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 9.3% (467 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 10.4% (519 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 11.4% (572 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 12.6% (630 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 13.7% (688 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 15.0% (752 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 16.3% (818 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 18.0% (902 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 19.2% (964 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 20.6% (1033 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 22.4% (1122 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 23.6% (1183 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 24.6% (1231 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 25.5% (1279 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 26.5% (1327 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 28.0% (1402 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 28.9% (1447 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 30.0% (1502 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 30.9% (1549 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 31.9% (1598 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 32.9% (1648 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 33.8% (1694 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 34.7% (1738 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 35.6% (1787 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 37.2% (1867 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 38.4% (1925 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 39.2% (1965 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 40.2% (2016 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 41.6% (2086 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 42.3% (2120 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 43.2% (2166 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 44.8% (2247 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 45.9% (2302 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 47.1% (2363 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 48.0% (2404 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 49.3% (2473 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 50.5% (2531 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 51.2% (2566 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 51.9% (2603 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 53.3% (2671 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 54.0% (2705 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 55.1% (2760 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 56.3% (2824 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 57.1% (2860 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 58.1% (2915 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 59.1% (2963 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 60.3% (3024 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 61.2% (3070 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 62.3% (3123 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 63.4% (3177 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 64.5% (3233 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 65.4% (3276 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 66.2% (3317 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 67.4% (3379 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 68.5% (3434 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 69.2% (3470 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 70.5% (3535 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 71.4% (3581 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 72.4% (3627 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 73.6% (3690 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 74.4% (3730 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 75.7% (3796 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 76.5% (3835 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 77.4% (3881 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 78.5% (3933 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 79.5% (3985 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 80.5% (4036 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 81.7% (4097 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 82.5% (4134 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 83.6% (4191 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 84.8% (4249 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 85.5% (4285 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 86.5% (4337 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 87.7% (4395 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 88.6% (4440 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 89.6% (4493 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 90.5% (4539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 91.6% (4593 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 92.6% (4643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 93.6% (4693 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 94.8% (4750 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 95.7% (4796 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 96.7% (4846 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=931 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.1; 3178 / 5013 (P = 63.40%) round 20]               
[00:00:00] Finding cutoff p=928 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=928 1.3% (63 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 2.3% (117 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 3.3% (167 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 4.9% (247 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 5.7% (288 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 6.9% (348 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 8.0% (403 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 9.3% (464 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 10.5% (524 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 11.6% (582 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 12.8% (644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 14.0% (702 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 15.1% (756 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 16.7% (838 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 18.0% (903 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 19.2% (961 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 20.5% (1026 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 22.0% (1102 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 23.5% (1176 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 24.8% (1243 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 26.3% (1319 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 27.2% (1365 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 28.7% (1441 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 29.6% (1486 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 30.7% (1537 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 32.0% (1606 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 33.1% (1661 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 33.9% (1701 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 35.4% (1775 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 36.5% (1828 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 37.4% (1874 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 38.3% (1921 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 39.4% (1973 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 40.3% (2022 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 41.2% (2066 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 43.0% (2155 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 44.2% (2218 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 44.9% (2250 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 46.1% (2311 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 47.1% (2360 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 48.4% (2428 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 49.3% (2471 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 50.1% (2514 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 51.1% (2561 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 52.2% (2617 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 53.3% (2672 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 54.0% (2706 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 55.1% (2760 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 56.2% (2815 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 57.2% (2869 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 58.4% (2930 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 59.1% (2965 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 60.3% (3022 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 61.4% (3080 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 62.3% (3124 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 63.6% (3189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 64.4% (3226 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 65.2% (3266 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 66.3% (3322 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 67.2% (3369 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 68.3% (3422 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 69.3% (3476 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 70.3% (3524 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 71.3% (3572 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 72.5% (3635 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 73.4% (3679 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 74.3% (3723 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 75.4% (3780 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 76.3% (3827 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 77.6% (3890 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 78.4% (3932 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 79.7% (3994 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 80.6% (4039 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 81.6% (4092 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 82.6% (4142 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 83.6% (4189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 84.4% (4233 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 85.8% (4299 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 86.5% (4337 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 87.5% (4387 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 88.6% (4442 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 89.6% (4490 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 90.7% (4545 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 92.6% (4642 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 93.7% (4697 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 94.8% (4754 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 96.7% (4846 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.8; 3271 / 5013 (P = 65.25%) round 21]               
[00:00:00] Finding cutoff p=924 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=924 1.3% (67 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 2.3% (117 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 3.3% (164 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 4.8% (241 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 5.7% (284 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 6.8% (341 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 7.7% (385 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 8.8% (439 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 10.0% (502 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 11.1% (558 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 12.9% (649 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 14.1% (708 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 15.2% (764 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 16.7% (835 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 18.1% (908 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 19.5% (976 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 20.9% (1050 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 22.4% (1123 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 23.8% (1192 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 24.5% (1228 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 25.5% (1277 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 26.8% (1345 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 27.7% (1387 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 28.9% (1447 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 29.9% (1500 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 31.0% (1554 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 32.0% (1606 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 32.8% (1645 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 33.8% (1696 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 35.9% (1799 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 36.7% (1841 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 37.8% (1894 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 38.7% (1942 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 40.2% (2014 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 41.0% (2057 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 42.7% (2142 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 44.0% (2205 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 44.8% (2245 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 46.0% (2307 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 47.0% (2354 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 48.2% (2417 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 49.0% (2457 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 49.9% (2499 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 51.1% (2563 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 51.9% (2602 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 53.2% (2665 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 54.0% (2707 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 55.0% (2755 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 56.0% (2809 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 57.2% (2866 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 58.0% (2908 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 59.6% (2986 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 60.3% (3022 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 61.4% (3080 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 62.3% (3123 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 63.1% (3162 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 64.1% (3215 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 65.3% (3275 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 66.8% (3348 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 67.4% (3377 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 68.3% (3424 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 69.4% (3478 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 70.3% (3526 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 71.4% (3581 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 72.3% (3622 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 74.0% (3711 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 74.6% (3739 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 75.4% (3778 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 76.4% (3831 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 77.5% (3883 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 78.5% (3935 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 79.4% (3979 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 80.5% (4037 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 81.6% (4092 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 82.6% (4142 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 83.6% (4191 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 84.8% (4252 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 85.5% (4287 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 86.6% (4339 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 87.6% (4393 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 88.6% (4440 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 89.6% (4491 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 90.7% (4549 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 91.8% (4603 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 92.6% (4644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 94.6% (4744 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 95.7% (4796 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 96.7% (4849 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.4; 3392 / 5013 (P = 67.66%) round 22]               
[00:00:00] Finding cutoff p=919 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=919 1.3% (66 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 2.3% (113 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 3.2% (161 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 4.7% (234 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 5.3% (268 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 6.3% (318 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 7.5% (375 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 8.4% (422 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 9.5% (474 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 10.4% (522 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 11.2% (563 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 12.4% (621 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 13.6% (682 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 14.5% (729 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 16.3% (817 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 17.4% (871 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 18.4% (922 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 19.6% (981 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 21.4% (1072 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 22.8% (1141 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 23.8% (1195 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 25.3% (1269 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 26.2% (1314 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 27.2% (1363 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 28.1% (1410 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 29.1% (1461 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 30.0% (1506 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 31.0% (1552 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 32.1% (1609 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 33.0% (1654 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 34.3% (1719 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 35.2% (1764 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 36.1% (1810 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 37.1% (1858 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 38.0% (1906 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 39.0% (1956 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 39.9% (2001 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 42.0% (2104 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 43.2% (2164 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 43.9% (2203 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 45.4% (2275 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 46.2% (2316 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 47.0% (2357 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 48.3% (2423 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 49.1% (2462 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 49.9% (2502 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 51.1% (2564 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 52.2% (2618 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 53.0% (2655 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 54.4% (2725 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 55.0% (2756 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 56.4% (2825 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 57.3% (2871 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 58.2% (2919 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 59.6% (2988 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 60.0% (3009 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 61.1% (3063 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 62.1% (3112 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 63.3% (3173 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 64.4% (3226 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 65.3% (3271 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 66.6% (3341 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 67.2% (3367 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 68.4% (3431 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 69.4% (3478 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 70.2% (3521 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 71.4% (3577 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 72.2% (3621 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 73.5% (3686 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 74.5% (3733 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 75.5% (3787 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 76.3% (3826 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 77.3% (3877 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 78.6% (3938 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 79.9% (4003 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 81.9% (4104 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 82.5% (4138 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 83.5% (4187 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 84.7% (4246 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 85.5% (4286 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 86.5% (4338 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 87.6% (4392 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 88.6% (4444 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 89.5% (4488 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 90.6% (4544 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 91.8% (4602 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 92.6% (4642 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 94.6% (4744 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 96.7% (4847 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.9; 3508 / 5013 (P = 69.98%) round 23]               
[00:00:00] Finding cutoff p=915 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=915 1.3% (67 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 2.2% (110 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 4.3% (214 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 5.2% (261 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 6.2% (311 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 7.3% (365 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 8.3% (415 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 9.2% (459 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 10.3% (515 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 11.7% (586 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 12.8% (644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 13.9% (698 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 15.5% (777 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 16.7% (839 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 17.8% (890 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 18.9% (945 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 20.1% (1010 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 21.8% (1095 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 22.7% (1137 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 23.5% (1179 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 25.0% (1252 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 25.8% (1295 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 26.9% (1347 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 27.7% (1390 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 28.8% (1443 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 29.9% (1499 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 30.8% (1544 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 32.0% (1604 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 32.9% (1647 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 34.4% (1725 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 35.3% (1769 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 35.9% (1801 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 36.8% (1844 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 37.9% (1898 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 38.9% (1952 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 41.2% (2065 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 42.6% (2136 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 43.4% (2175 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 44.3% (2219 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 45.2% (2265 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 45.9% (2299 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 47.2% (2367 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 47.9% (2401 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 49.2% (2467 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 49.9% (2501 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 51.1% (2563 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 51.9% (2604 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 53.4% (2677 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 54.2% (2717 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 55.1% (2760 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 56.1% (2811 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 57.2% (2866 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 58.5% (2935 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 59.4% (2977 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 60.3% (3021 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 61.3% (3073 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 62.2% (3119 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 63.1% (3165 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 64.1% (3214 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 65.8% (3297 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 66.3% (3323 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 67.5% (3385 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 68.5% (3432 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 69.4% (3481 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 70.2% (3521 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 71.5% (3585 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 72.6% (3637 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 73.7% (3696 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 74.3% (3723 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 75.4% (3778 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 76.5% (3834 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 77.5% (3883 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 78.5% (3937 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 79.7% (3997 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 80.4% (4031 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 81.7% (4097 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 82.5% (4137 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 83.5% (4184 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 84.7% (4244 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 85.5% (4285 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 86.5% (4337 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 87.7% (4394 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 88.6% (4444 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 89.6% (4491 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 90.6% (4542 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 91.6% (4594 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 92.6% (4643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 94.7% (4745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 95.7% (4796 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.5; 3602 / 5013 (P = 71.85%) round 24]               
[00:00:00] Finding cutoff p=911 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=911 1.2% (62 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 2.3% (115 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 3.1% (156 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 4.8% (239 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 5.5% (276 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 6.6% (332 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 7.7% (385 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 8.7% (436 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 9.7% (485 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 10.7% (536 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 11.3% (568 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 13.2% (662 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 14.3% (718 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 15.6% (783 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 17.6% (880 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 18.8% (943 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 19.9% (997 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 21.4% (1071 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 22.8% (1144 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 23.4% (1174 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 25.5% (1277 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 26.7% (1338 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 27.5% (1379 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 28.9% (1447 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 29.6% (1484 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 30.7% (1538 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 31.7% (1590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 33.1% (1660 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 33.8% (1693 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 34.7% (1741 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 35.9% (1798 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 37.3% (1869 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 39.1% (1959 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 40.4% (2027 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 41.4% (2074 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 42.1% (2111 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 43.0% (2154 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 44.1% (2211 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 44.8% (2245 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 46.0% (2307 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 47.1% (2361 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 47.9% (2400 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 48.9% (2453 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 50.5% (2531 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 51.4% (2578 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 51.9% (2601 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 53.0% (2659 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 54.0% (2709 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 55.2% (2768 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 56.7% (2840 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 57.4% (2877 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 58.5% (2931 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 59.2% (2970 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 60.3% (3021 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 61.4% (3080 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 62.2% (3119 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 63.1% (3165 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 64.7% (3244 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 65.3% (3275 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 66.2% (3320 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 67.3% (3372 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 68.3% (3426 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 69.2% (3470 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 70.3% (3526 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 71.8% (3598 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 72.3% (3625 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 73.5% (3686 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 74.6% (3741 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 75.4% (3782 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 76.4% (3832 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 77.4% (3881 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 78.4% (3932 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 81.6% (4089 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 83.0% (4159 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 83.7% (4194 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 84.4% (4233 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 85.5% (4288 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 86.5% (4338 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 87.6% (4393 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 88.5% (4439 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 89.6% (4491 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 90.6% (4540 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 91.6% (4591 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 92.8% (4652 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 93.7% (4699 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 94.7% (4745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 95.7% (4797 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 96.7% (4848 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.1; 3681 / 5013 (P = 73.43%) round 25]               
[00:00:00] Finding cutoff p=907 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=907 1.2% (59 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 2.2% (110 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 4.3% (215 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 5.4% (269 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 6.3% (315 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 7.3% (366 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 8.3% (417 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 9.3% (464 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 10.4% (520 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 11.8% (592 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 13.1% (658 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 14.6% (734 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 15.6% (784 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 16.8% (844 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 17.8% (892 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 18.8% (943 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 20.2% (1012 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 21.6% (1084 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 22.9% (1147 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 24.3% (1220 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 25.2% (1263 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 26.4% (1323 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 27.3% (1369 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 28.3% (1421 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 29.1% (1461 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 30.3% (1518 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 31.1% (1561 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 31.9% (1601 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 33.4% (1672 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 34.2% (1713 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 35.0% (1754 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 35.7% (1791 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 37.2% (1863 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 38.0% (1907 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 40.1% (2011 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 41.3% (2071 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 42.1% (2111 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 42.9% (2149 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 44.3% (2219 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 45.2% (2265 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 46.1% (2310 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 46.8% (2346 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 48.0% (2404 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 49.1% (2463 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 50.3% (2523 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 51.3% (2571 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 52.0% (2606 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 53.2% (2665 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 54.0% (2707 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 55.2% (2766 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 56.5% (2830 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 57.1% (2860 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 58.0% (2909 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 59.4% (2976 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 60.3% (3022 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 61.1% (3064 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 62.2% (3116 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 63.7% (3193 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 64.4% (3228 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 65.5% (3283 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 66.2% (3318 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 67.2% (3367 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 68.2% (3419 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 69.2% (3470 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 70.5% (3532 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 71.3% (3576 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 72.3% (3622 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 73.6% (3690 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 74.3% (3727 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 75.3% (3774 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 76.3% (3827 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 77.4% (3879 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 78.6% (3940 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 79.4% (3978 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 80.5% (4033 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 81.6% (4089 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 82.4% (4131 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 83.4% (4182 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 84.5% (4238 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 85.6% (4291 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 86.6% (4343 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 87.9% (4405 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 88.5% (4439 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 89.6% (4493 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 90.6% (4543 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 91.6% (4592 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 92.6% (4642 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 94.7% (4746 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 95.7% (4799 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 96.7% (4846 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.7; 3754 / 5013 (P = 74.89%) round 26]               
[00:00:00] Finding cutoff p=904 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=904 1.2% (61 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 2.1% (107 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 3.9% (197 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 4.8% (239 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 5.5% (277 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 6.7% (336 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 7.6% (379 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 8.5% (424 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 9.2% (461 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 10.8% (543 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 11.9% (598 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 13.3% (667 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 14.3% (718 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 15.4% (770 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 16.3% (819 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 17.8% (893 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 19.0% (952 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 20.5% (1028 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 23.1% (1156 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 23.8% (1194 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 25.1% (1258 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 25.9% (1300 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 26.8% (1344 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 27.5% (1380 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 29.0% (1456 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 30.4% (1523 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 31.9% (1601 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 32.8% (1643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 33.9% (1699 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 34.8% (1743 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 36.2% (1817 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 38.1% (1908 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 39.6% (1983 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 40.5% (2030 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 41.4% (2075 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 42.4% (2125 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 43.5% (2183 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 44.1% (2212 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 45.3% (2269 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 45.8% (2298 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 46.9% (2353 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 48.1% (2409 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 49.3% (2469 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 50.2% (2518 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 51.2% (2565 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 52.1% (2614 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 52.9% (2654 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 54.2% (2716 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 55.0% (2755 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 56.2% (2816 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 57.0% (2856 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 58.0% (2910 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 59.4% (2980 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 60.2% (3016 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 61.1% (3063 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 62.1% (3111 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 63.2% (3166 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 64.2% (3217 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 65.2% (3266 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 66.2% (3319 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 67.2% (3367 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 68.2% (3420 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 70.1% (3514 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 70.6% (3539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 71.6% (3587 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 72.3% (3623 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 73.5% (3686 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 74.3% (3723 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 75.5% (3783 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 76.4% (3828 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 77.4% (3879 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 78.4% (3932 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 79.5% (3986 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 80.4% (4031 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 81.5% (4085 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 82.4% (4131 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 83.6% (4191 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 84.5% (4235 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 85.5% (4287 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 86.6% (4341 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 87.6% (4393 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 88.6% (4441 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 89.6% (4491 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 90.8% (4553 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 92.6% (4644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 93.8% (4702 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 94.7% (4745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 96.7% (4846 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.4; 3817 / 5013 (P = 76.14%) round 27]               
[00:00:00] Finding cutoff p=900 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=900 1.2% (60 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 2.0% (102 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 4.1% (205 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 5.2% (259 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 6.7% (334 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 7.6% (381 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 8.5% (425 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 9.3% (467 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 10.3% (514 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 11.7% (589 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 13.4% (670 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 14.3% (718 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 15.8% (791 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 16.8% (842 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 17.8% (893 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 18.9% (947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 20.1% (1007 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 21.3% (1067 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 23.4% (1171 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 23.9% (1198 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 25.0% (1254 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 25.6% (1285 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 26.5% (1327 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 27.8% (1393 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 28.5% (1431 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 30.7% (1538 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 31.9% (1601 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 32.7% (1638 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 34.1% (1707 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 34.8% (1746 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 36.8% (1843 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 38.0% (1906 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 38.7% (1941 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 40.2% (2015 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 41.5% (2080 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 42.2% (2117 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 43.3% (2171 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 43.9% (2202 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 45.2% (2268 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 46.1% (2313 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 47.4% (2374 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 48.2% (2415 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 49.1% (2461 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 50.1% (2513 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 50.9% (2550 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 51.9% (2603 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 53.2% (2665 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 54.2% (2719 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 55.1% (2764 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 56.0% (2806 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 57.4% (2878 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 58.1% (2914 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 59.1% (2963 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 60.2% (3016 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 61.1% (3064 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 62.3% (3122 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 63.3% (3174 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 64.2% (3219 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 65.4% (3277 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 66.3% (3325 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 67.1% (3366 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 69.0% (3460 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 69.6% (3489 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 70.3% (3524 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 71.4% (3578 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 72.5% (3633 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 73.3% (3675 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 74.5% (3735 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 75.4% (3780 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 76.4% (3832 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 77.5% (3886 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 78.6% (3938 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 79.4% (3982 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 80.4% (4032 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 81.7% (4097 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 82.5% (4138 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 83.6% (4189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 84.7% (4248 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 85.6% (4289 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 86.6% (4339 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 87.6% (4390 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 88.8% (4451 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 89.6% (4490 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 90.5% (4539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 91.6% (4590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 92.6% (4644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 94.7% (4745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 95.7% (4796 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 97.7% (4898 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.0; 3890 / 5013 (P = 77.60%) round 28]               
[00:00:00] Finding cutoff p=890 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=890 1.2% (61 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 2.4% (118 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 4.0% (202 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 4.9% (248 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 5.7% (285 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 6.7% (338 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 7.7% (384 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 8.5% (424 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 9.4% (471 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 10.4% (520 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 11.2% (563 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 12.4% (622 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 14.3% (716 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 15.3% (767 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 16.7% (838 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 17.7% (889 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 19.1% (957 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 20.4% (1024 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 21.6% (1083 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 23.3% (1167 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 24.0% (1202 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 25.0% (1252 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 25.8% (1294 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 26.6% (1332 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 27.9% (1397 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 28.8% (1443 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 29.7% (1490 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 31.2% (1566 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 32.0% (1606 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 32.8% (1643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 33.7% (1687 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 34.9% (1750 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 35.8% (1797 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 37.8% (1897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 39.3% (1969 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 40.1% (2012 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 41.1% (2062 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 41.8% (2097 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 42.8% (2145 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 44.1% (2210 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 45.2% (2265 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 45.8% (2296 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 46.9% (2353 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 48.0% (2404 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 49.4% (2474 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 50.4% (2528 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 51.1% (2560 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 52.3% (2623 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 53.0% (2659 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 54.0% (2709 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 55.4% (2778 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 56.1% (2811 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 57.1% (2864 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 58.3% (2921 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 59.1% (2961 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 60.1% (3012 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 61.1% (3061 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 62.6% (3140 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 63.2% (3169 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 64.2% (3216 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 65.2% (3268 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 66.3% (3322 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 67.4% (3379 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 69.3% (3476 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 70.3% (3525 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 71.4% (3577 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 72.3% (3626 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 73.3% (3675 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 74.3% (3725 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 75.3% (3774 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 76.3% (3827 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 77.4% (3879 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 78.5% (3935 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 79.5% (3986 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 80.5% (4034 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 81.9% (4106 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 82.6% (4139 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 83.5% (4187 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 84.5% (4235 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 85.5% (4287 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 86.6% (4343 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 87.5% (4386 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 88.6% (4443 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 89.6% (4492 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 90.7% (4545 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 91.6% (4592 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 92.6% (4643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 93.6% (4692 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 94.6% (4744 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 95.7% (4797 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 96.8% (4851 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.0; 3940 / 5013 (P = 78.60%) round 29]               
[00:00:00] Finding cutoff p=880 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=880 1.2% (61 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 2.0% (102 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 4.2% (212 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 5.2% (260 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 6.8% (343 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 7.7% (388 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 8.8% (440 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 9.5% (474 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 10.5% (528 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 11.5% (579 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 12.6% (634 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 14.0% (704 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 15.0% (754 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 16.2% (811 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 17.0% (853 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 18.1% (906 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 19.4% (975 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 21.0% (1051 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 21.9% (1098 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 23.5% (1179 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 24.9% (1247 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 25.5% (1277 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 26.9% (1348 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 27.7% (1390 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 28.7% (1440 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 30.6% (1535 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 31.7% (1590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 32.9% (1649 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 33.6% (1684 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 35.0% (1756 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 37.0% (1853 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 38.4% (1926 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 39.2% (1967 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 40.2% (2014 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 41.0% (2057 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 42.4% (2125 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 43.0% (2157 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 43.9% (2202 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 44.9% (2253 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 45.8% (2295 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 46.9% (2352 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 47.9% (2402 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 48.9% (2451 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 49.9% (2502 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 51.0% (2555 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 52.0% (2605 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 53.6% (2689 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 54.1% (2714 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 55.3% (2773 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 56.1% (2811 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 57.1% (2864 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 58.2% (2920 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 59.5% (2982 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 60.1% (3015 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 61.2% (3066 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 62.9% (3152 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 63.3% (3171 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 64.2% (3216 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 65.2% (3267 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 66.3% (3323 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 67.3% (3376 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 68.3% (3422 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 69.7% (3494 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 70.3% (3526 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 71.4% (3578 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 72.5% (3633 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 73.5% (3683 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 74.3% (3726 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 75.6% (3790 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 76.6% (3841 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 77.4% (3881 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 78.7% (3947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 79.6% (3992 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 81.7% (4094 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 82.5% (4134 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 83.7% (4195 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 84.5% (4237 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 85.5% (4284 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 86.6% (4342 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 87.6% (4390 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 88.5% (4439 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 89.7% (4496 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 90.6% (4541 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 91.8% (4603 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 92.6% (4643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 93.7% (4695 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 94.7% (4747 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 95.7% (4799 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 96.7% (4847 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 88.0; 4029 / 5013 (P = 80.37%) round 30]               
[00:00:00] Finding cutoff p=871 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=871 1.2% (58 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 2.4% (118 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 3.9% (196 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 5.1% (254 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 5.8% (293 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 7.0% (350 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 7.9% (396 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 8.8% (441 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 9.7% (488 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 10.5% (528 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 11.2% (561 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 12.3% (616 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 13.5% (679 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 15.7% (785 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 16.5% (829 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 17.7% (885 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 18.7% (935 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 19.9% (997 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 21.3% (1067 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 22.4% (1124 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 24.5% (1230 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 25.9% (1299 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 26.6% (1333 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 27.6% (1382 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 28.7% (1440 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 29.7% (1491 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 31.6% (1584 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 32.8% (1646 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 33.6% (1684 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 34.7% (1738 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 35.7% (1792 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 37.7% (1891 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 38.8% (1946 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 40.1% (2012 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 41.1% (2060 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 42.1% (2110 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 42.9% (2149 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 43.8% (2195 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 44.9% (2249 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 46.1% (2309 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 47.0% (2355 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 48.4% (2427 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 49.4% (2475 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 49.9% (2503 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 51.1% (2563 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 52.1% (2612 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 53.3% (2671 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 54.7% (2744 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 55.3% (2770 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 56.3% (2821 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 57.1% (2860 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 58.4% (2930 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 59.2% (2970 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 60.1% (3014 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 61.2% (3069 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 62.7% (3145 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 63.3% (3173 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 64.1% (3213 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 65.3% (3274 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 66.2% (3318 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 67.2% (3370 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 68.2% (3418 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 69.6% (3490 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 70.5% (3532 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 71.4% (3579 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 72.5% (3633 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 73.4% (3678 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 74.4% (3730 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 75.4% (3781 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 76.6% (3838 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 77.8% (3898 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 78.6% (3941 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 79.5% (3986 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 80.5% (4035 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 81.5% (4087 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 82.5% (4137 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 83.6% (4189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 84.6% (4240 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 85.8% (4302 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 86.5% (4337 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 87.6% (4390 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 88.6% (4443 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 89.6% (4492 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 90.7% (4548 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 91.6% (4594 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 92.7% (4646 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 93.6% (4693 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 94.6% (4743 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 95.6% (4794 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=871 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 87.1; 4109 / 5013 (P = 81.97%) round 31]               
[00:00:00] Finding cutoff p=862 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=862 1.1% (55 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 2.3% (117 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 3.8% (192 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 4.7% (236 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 5.3% (268 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 6.2% (312 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 7.5% (378 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 8.5% (426 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 9.4% (469 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 10.3% (516 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 11.3% (565 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 13.0% (654 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 14.3% (715 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 15.5% (777 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 16.6% (831 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 17.9% (895 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 18.9% (948 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 20.3% (1018 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 22.2% (1112 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 22.8% (1142 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 23.9% (1196 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 24.7% (1237 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 25.5% (1276 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 26.6% (1333 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 27.7% (1390 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 29.6% (1485 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 30.7% (1539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 31.6% (1583 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 32.9% (1649 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 33.8% (1695 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 35.5% (1780 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 37.0% (1853 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 37.6% (1887 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 38.7% (1940 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 40.2% (2015 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 40.9% (2052 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 42.0% (2105 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 42.8% (2145 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 43.9% (2201 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 44.9% (2250 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 46.0% (2305 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 47.2% (2368 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 48.4% (2427 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 49.0% (2455 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 50.1% (2510 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 51.0% (2555 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 52.4% (2626 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 53.0% (2656 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 54.0% (2706 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 55.4% (2776 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 56.2% (2816 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 57.1% (2860 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 58.3% (2924 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 59.1% (2964 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 60.3% (3022 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 61.5% (3083 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 62.2% (3118 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 63.3% (3171 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 64.3% (3223 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 65.2% (3270 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 66.4% (3328 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 67.3% (3373 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 68.6% (3439 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 69.2% (3469 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 70.3% (3523 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 71.4% (3579 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 72.2% (3621 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 73.5% (3684 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 74.8% (3752 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 75.4% (3780 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 76.8% (3851 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 77.5% (3885 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 78.5% (3935 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 79.4% (3982 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 80.4% (4029 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 81.8% (4101 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 82.5% (4136 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 83.5% (4184 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 84.5% (4235 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 85.7% (4295 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 86.8% (4349 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 87.5% (4387 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 88.6% (4441 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 89.6% (4492 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 90.6% (4543 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 91.6% (4591 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 92.6% (4642 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 93.8% (4702 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 94.6% (4744 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 95.7% (4796 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 96.8% (4852 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 98.7% (4948 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=862 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 86.2; 4220 / 5013 (P = 84.18%) round 32]               
[00:00:00] Finding cutoff p=853 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=853 1.1% (53 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 2.2% (112 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 3.8% (190 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 4.8% (240 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 5.4% (271 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 6.2% (311 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 7.6% (380 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 8.4% (422 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 9.2% (462 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 10.5% (524 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 11.4% (570 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 12.9% (647 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 13.9% (697 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 14.8% (742 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 15.7% (787 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 16.7% (838 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 17.9% (897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 19.1% (955 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 20.1% (1010 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 22.0% (1104 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 22.7% (1137 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 23.7% (1186 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 25.0% (1251 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 25.6% (1284 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 27.0% (1353 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 27.8% (1393 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 29.1% (1461 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 30.3% (1517 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 30.8% (1544 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 31.8% (1596 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 32.8% (1643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 33.7% (1691 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 35.3% (1770 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 36.4% (1823 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 37.1% (1862 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 38.0% (1904 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 38.9% (1949 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 40.1% (2012 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 41.0% (2057 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 42.0% (2104 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 42.9% (2150 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 44.2% (2214 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 44.9% (2252 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 45.8% (2296 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 47.3% (2371 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 48.4% (2424 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 49.1% (2459 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 50.2% (2518 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 51.2% (2567 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 52.1% (2612 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 53.6% (2685 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 54.2% (2719 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 55.2% (2768 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 56.4% (2829 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 57.2% (2869 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 58.2% (2916 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 59.2% (2968 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 60.1% (3012 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 61.2% (3069 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 62.2% (3118 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 63.5% (3185 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 64.2% (3220 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 65.4% (3276 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 66.3% (3326 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 68.0% (3411 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 68.7% (3444 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 69.3% (3476 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 70.3% (3526 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 71.7% (3595 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 72.2% (3621 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 73.6% (3688 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 74.5% (3733 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 75.7% (3793 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 76.4% (3831 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 78.0% (3909 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 78.4% (3930 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 79.4% (3981 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 80.5% (4035 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 81.7% (4097 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 82.5% (4134 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 83.5% (4187 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 84.8% (4251 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 85.5% (4284 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 86.6% (4342 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 87.5% (4387 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 88.5% (4437 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 89.6% (4492 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 90.6% (4542 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 91.6% (4592 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 92.6% (4643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 93.9% (4705 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 94.7% (4747 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 95.6% (4794 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 96.8% (4852 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=853 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.3; 4306 / 5013 (P = 85.90%) round 33]               
[00:00:00] Finding cutoff p=844 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=844 1.1% (54 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 2.2% (111 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 3.7% (186 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 4.5% (225 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 5.3% (264 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 6.2% (312 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 7.5% (376 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 8.4% (419 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 9.2% (460 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 10.4% (521 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 11.8% (592 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 13.6% (684 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 14.3% (716 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 15.5% (775 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 16.4% (820 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 17.3% (869 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 18.3% (919 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 19.3% (969 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 22.4% (1125 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 24.0% (1204 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 24.7% (1238 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 25.6% (1281 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 26.7% (1340 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 27.9% (1398 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 28.6% (1432 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 30.0% (1505 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 30.7% (1538 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 31.8% (1593 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 32.6% (1636 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 34.2% (1716 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 35.9% (1799 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 37.0% (1856 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 37.7% (1892 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 38.7% (1938 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 39.8% (1993 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 41.0% (2057 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 41.9% (2102 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 43.1% (2163 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 43.9% (2203 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 45.1% (2263 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 45.9% (2303 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 47.3% (2371 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 48.2% (2417 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 49.1% (2463 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 50.0% (2507 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 51.0% (2555 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 52.0% (2608 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 53.0% (2655 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 54.2% (2717 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 55.1% (2763 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 56.1% (2811 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 57.1% (2860 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 58.0% (2907 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 59.0% (2959 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 60.2% (3016 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 61.7% (3091 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 62.3% (3122 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 63.4% (3176 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 64.2% (3218 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 65.3% (3271 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 66.5% (3333 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 67.4% (3379 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 68.8% (3449 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 69.4% (3478 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 70.5% (3532 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 71.4% (3580 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 72.4% (3628 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 73.3% (3675 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 74.6% (3742 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 75.9% (3803 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 76.6% (3839 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 77.8% (3902 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 78.3% (3927 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 79.6% (3989 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 80.4% (4030 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 81.4% (4082 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 82.4% (4133 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 83.6% (4191 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 84.5% (4236 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 85.5% (4286 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 86.8% (4350 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 87.7% (4397 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 88.6% (4442 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 89.9% (4507 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 90.5% (4539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 91.6% (4592 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 92.7% (4649 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 93.6% (4693 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 94.7% (4745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 95.7% (4796 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 96.7% (4850 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 98.7% (4949 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=844 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.4; 4380 / 5013 (P = 87.37%) round 34]               
[00:00:00] Finding cutoff p=833 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=833 1.3% (66 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 2.2% (109 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 3.7% (185 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 4.5% (228 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 5.3% (265 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 6.1% (307 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 7.2% (362 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 8.4% (422 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 9.2% (461 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 10.5% (524 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 11.2% (563 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 12.8% (644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 13.7% (687 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 14.6% (733 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 15.4% (774 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 16.4% (820 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 17.7% (887 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 18.4% (924 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 19.3% (969 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 22.0% (1105 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 22.9% (1146 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 23.8% (1195 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 24.6% (1233 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 25.5% (1279 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 26.7% (1337 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 27.5% (1381 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 29.4% (1475 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 30.0% (1502 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 30.8% (1542 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 31.8% (1594 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 32.6% (1634 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 33.7% (1689 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 35.4% (1775 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 36.7% (1839 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 37.8% (1896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 38.8% (1945 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 39.9% (2001 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 41.1% (2059 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 42.2% (2117 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 42.7% (2142 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 44.0% (2207 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 45.2% (2265 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 46.0% (2307 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 46.9% (2351 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 48.2% (2418 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 48.8% (2448 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 50.2% (2519 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 50.9% (2550 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 52.1% (2614 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 52.9% (2654 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 53.9% (2704 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 55.0% (2759 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 56.4% (2825 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 57.2% (2868 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 58.0% (2907 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 59.5% (2984 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 60.1% (3014 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 61.4% (3077 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 62.1% (3115 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 63.1% (3162 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 64.4% (3228 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 65.5% (3283 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 66.6% (3339 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 67.4% (3379 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 68.3% (3423 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 69.7% (3492 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 70.3% (3523 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 71.2% (3570 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 72.7% (3646 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 74.1% (3716 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 75.1% (3763 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 75.8% (3799 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 76.7% (3843 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 77.7% (3893 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 78.6% (3939 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 79.5% (3985 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 80.6% (4039 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 81.5% (4086 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 82.5% (4136 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 83.5% (4188 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 84.5% (4235 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 85.7% (4294 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 86.6% (4339 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 87.8% (4402 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 88.5% (4439 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 89.7% (4495 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 90.7% (4548 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 91.7% (4598 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 92.9% (4656 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 93.6% (4693 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 94.6% (4744 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 95.7% (4797 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 96.7% (4847 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 97.7% (4898 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 98.7% (4948 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=833 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 83.3; 4462 / 5013 (P = 89.01%) round 35]               
[00:00:00] Finding cutoff p=823 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=823 1.4% (68 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 3.2% (160 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 4.1% (207 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 5.2% (261 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 6.2% (312 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 7.4% (372 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 8.2% (411 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 9.7% (488 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 10.8% (543 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 11.6% (581 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 12.3% (619 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 13.3% (666 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 14.2% (714 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 15.6% (783 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 16.5% (829 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 18.8% (944 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 19.8% (993 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 21.7% (1090 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 22.4% (1125 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 23.6% (1182 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 24.9% (1250 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 25.5% (1278 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 28.0% (1402 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 28.6% (1432 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 29.5% (1480 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 30.7% (1539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 32.0% (1603 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 34.0% (1704 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 35.2% (1764 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 35.8% (1793 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 37.2% (1865 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 38.1% (1912 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 38.9% (1950 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 40.1% (2009 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 40.9% (2051 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 42.3% (2119 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 43.0% (2157 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 43.7% (2193 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 44.8% (2244 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 46.3% (2320 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 47.1% (2359 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 48.0% (2408 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 48.9% (2451 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 50.2% (2515 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 51.5% (2582 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 52.0% (2606 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 53.0% (2658 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 54.3% (2723 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 55.2% (2765 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 56.1% (2813 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 57.1% (2862 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 58.6% (2940 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 59.4% (2977 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 60.1% (3011 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 61.1% (3062 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 62.1% (3115 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 63.5% (3181 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 64.2% (3219 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 66.0% (3309 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 66.5% (3333 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 67.4% (3378 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 68.6% (3438 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 69.5% (3482 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 70.4% (3527 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 71.6% (3591 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 72.6% (3637 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 73.7% (3695 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 74.8% (3748 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 75.4% (3781 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 76.3% (3827 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 77.3% (3877 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 78.3% (3927 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 79.9% (4003 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 80.5% (4034 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 81.6% (4091 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 82.4% (4132 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 83.8% (4203 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 84.5% (4235 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 85.7% (4298 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 86.7% (4345 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 87.5% (4386 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 88.5% (4439 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 89.5% (4489 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 90.6% (4542 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 91.6% (4592 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 92.7% (4647 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 93.6% (4693 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 94.7% (4745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 95.7% (4797 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 96.8% (4852 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=823 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.3; 4542 / 5013 (P = 90.60%) round 36]               
[00:00:00] Finding cutoff p=813 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=813 1.2% (61 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 3.2% (162 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 4.2% (211 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 5.3% (268 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 6.2% (309 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 7.4% (370 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 8.6% (432 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 9.6% (480 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 10.8% (541 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 11.9% (597 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 12.6% (633 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 13.3% (667 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 14.5% (726 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 15.3% (767 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 16.5% (829 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 18.6% (932 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 19.5% (978 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 21.0% (1054 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 22.2% (1114 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 23.1% (1158 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 23.5% (1180 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 24.9% (1248 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 25.5% (1279 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 27.0% (1355 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 27.6% (1385 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 28.7% (1437 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 29.5% (1480 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 30.6% (1536 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 32.7% (1637 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 33.8% (1695 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 34.8% (1745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 36.2% (1813 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 36.7% (1841 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 37.7% (1888 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 38.7% (1939 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 39.8% (1993 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 41.0% (2054 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 42.2% (2115 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 43.3% (2171 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 44.4% (2227 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 45.1% (2260 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 45.8% (2296 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 47.0% (2356 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 48.0% (2407 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 49.5% (2481 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 50.1% (2512 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 51.2% (2566 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 52.0% (2608 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 53.6% (2686 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 54.3% (2723 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 54.9% (2754 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 56.1% (2814 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 57.6% (2889 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 58.2% (2920 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 59.5% (2983 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 60.2% (3019 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 61.1% (3065 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 62.4% (3129 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 63.1% (3165 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 64.8% (3247 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 65.4% (3277 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 66.4% (3331 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 67.5% (3382 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 68.3% (3422 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 69.3% (3473 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 70.3% (3526 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 71.3% (3575 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 72.8% (3647 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 73.7% (3693 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 74.4% (3731 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 75.4% (3780 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 76.5% (3836 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 77.5% (3884 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 78.5% (3937 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 79.4% (3981 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 80.5% (4036 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 81.5% (4084 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 82.4% (4132 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 83.4% (4182 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 84.5% (4237 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 85.5% (4284 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 86.9% (4354 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 87.6% (4391 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 88.9% (4455 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 89.6% (4490 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 90.6% (4542 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 91.7% (4596 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 92.7% (4646 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 93.7% (4695 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 94.7% (4745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 95.6% (4794 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 96.7% (4850 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=813 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 81.3; 4599 / 5013 (P = 91.74%) round 37]               
[00:00:00] Finding cutoff p=803 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=803 1.3% (64 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 3.1% (157 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 4.5% (224 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 5.2% (260 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 6.2% (310 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 7.4% (369 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 8.5% (426 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 9.3% (468 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 10.5% (525 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 11.5% (574 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 12.2% (613 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 13.3% (665 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 14.2% (714 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 15.4% (774 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 17.6% (884 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 19.5% (976 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 20.7% (1039 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 22.4% (1124 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 23.5% (1179 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 24.6% (1234 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 26.0% (1301 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 26.6% (1331 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 27.7% (1388 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 28.8% (1446 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 30.0% (1505 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 31.8% (1595 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 33.2% (1665 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 33.9% (1700 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 34.6% (1737 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 36.1% (1810 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 36.8% (1845 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 37.7% (1889 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 39.3% (1970 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 39.9% (2001 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 40.9% (2048 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 41.8% (2094 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 43.1% (2159 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 43.9% (2200 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 45.0% (2258 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 46.1% (2310 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 47.1% (2363 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 48.8% (2444 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 49.2% (2466 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 50.1% (2514 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 51.1% (2564 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 51.9% (2601 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 53.3% (2672 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 54.2% (2717 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 54.9% (2754 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 56.4% (2828 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 57.0% (2857 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 58.4% (2926 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 59.1% (2962 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 60.1% (3015 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 61.1% (3063 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 63.1% (3165 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 64.4% (3230 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 65.2% (3268 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 66.3% (3326 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 67.3% (3376 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 68.3% (3424 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 69.4% (3479 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 70.8% (3551 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 71.8% (3601 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 72.5% (3635 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 73.8% (3698 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 74.3% (3723 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 75.7% (3793 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 76.4% (3830 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 77.4% (3881 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 78.7% (3947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 79.4% (3979 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 80.5% (4033 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 81.6% (4090 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 82.4% (4133 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 83.5% (4185 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 84.6% (4241 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 85.5% (4286 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 86.5% (4335 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 87.8% (4401 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 88.6% (4442 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 89.8% (4504 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 90.6% (4540 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 91.8% (4602 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 92.6% (4643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 93.7% (4695 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 94.7% (4747 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 96.7% (4849 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=803 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.3; 4642 / 5013 (P = 92.60%) round 38]               
[00:00:00] Finding cutoff p=794 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=794 1.2% (59 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 3.0% (148 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 4.1% (205 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 5.4% (269 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 6.1% (308 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 7.6% (380 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 8.2% (409 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 9.8% (489 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 11.2% (563 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 12.5% (625 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 13.3% (665 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 14.4% (720 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 15.3% (768 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 16.6% (833 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 18.8% (943 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 20.0% (1002 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 21.3% (1068 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 22.1% (1109 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 23.0% (1155 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 23.7% (1187 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 24.5% (1227 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 25.6% (1283 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 27.1% (1358 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 27.9% (1401 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 28.7% (1438 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 29.6% (1486 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 31.3% (1568 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 32.9% (1650 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 34.3% (1719 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 34.8% (1747 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 35.8% (1793 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 37.3% (1872 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 38.1% (1911 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 39.2% (1967 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 39.9% (1999 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 41.1% (2060 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 41.8% (2097 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 43.2% (2164 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 43.9% (2201 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 44.9% (2250 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 46.0% (2306 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 46.8% (2348 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 48.0% (2406 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 49.6% (2484 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 50.1% (2511 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 51.1% (2564 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 52.0% (2608 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 53.3% (2672 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 54.0% (2705 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 55.0% (2759 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 57.0% (2855 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 57.5% (2883 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 58.1% (2915 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 59.0% (2958 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 60.3% (3022 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 61.1% (3063 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 62.1% (3112 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 64.0% (3208 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 64.5% (3234 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 65.5% (3282 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 66.7% (3342 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 67.4% (3378 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 68.2% (3420 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 69.2% (3469 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 70.4% (3530 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 71.8% (3601 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 72.7% (3645 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 73.4% (3678 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 74.3% (3727 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 75.6% (3790 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 76.7% (3844 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 77.3% (3877 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 78.5% (3935 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 79.5% (3986 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 80.6% (4038 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 81.7% (4096 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 82.4% (4133 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 83.8% (4199 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 84.5% (4235 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 85.5% (4287 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 86.6% (4339 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 87.9% (4406 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 88.6% (4440 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 89.5% (4489 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 90.6% (4541 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 91.9% (4609 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 92.6% (4642 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 93.6% (4693 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 94.7% (4746 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 95.7% (4797 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 96.7% (4847 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 79.4; 4671 / 5013 (P = 93.18%) round 39]               
[00:00:00] Finding cutoff p=784 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=784 1.1% (57 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 2.8% (142 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 3.9% (194 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 4.6% (231 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 5.5% (275 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 6.3% (315 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 7.2% (359 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 8.4% (422 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 9.3% (464 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 10.4% (522 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 11.9% (598 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 12.7% (638 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 13.4% (672 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 14.8% (740 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 15.7% (789 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 16.4% (821 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 19.2% (960 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 20.3% (1020 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 21.8% (1091 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 22.5% (1127 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 23.4% (1174 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 24.5% (1227 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 25.5% (1277 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 27.3% (1367 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 27.7% (1391 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 28.6% (1432 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 29.8% (1493 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 30.9% (1548 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 32.8% (1643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 34.4% (1725 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 35.0% (1755 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 35.6% (1787 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 37.1% (1858 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 37.6% (1887 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 38.8% (1943 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 40.0% (2003 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 40.7% (2040 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 42.3% (2121 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 43.4% (2177 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 44.5% (2229 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 45.0% (2258 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 46.0% (2305 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 46.9% (2352 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 47.9% (2402 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 49.7% (2489 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 50.2% (2516 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 51.0% (2558 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 52.6% (2639 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 53.6% (2688 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 54.1% (2713 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 55.1% (2763 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 56.3% (2820 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 57.5% (2882 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 58.1% (2911 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 59.5% (2982 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 60.3% (3023 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 61.1% (3061 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 62.3% (3125 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 63.3% (3174 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 64.5% (3235 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 65.3% (3273 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 66.8% (3347 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 67.4% (3381 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 68.3% (3423 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 69.5% (3482 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 70.4% (3529 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 71.8% (3598 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 72.6% (3637 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 73.3% (3673 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 74.3% (3725 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 75.5% (3784 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 76.4% (3832 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 77.5% (3886 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 78.9% (3956 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 79.4% (3980 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 80.4% (4031 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 81.6% (4091 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 82.5% (4134 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 83.7% (4196 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 84.5% (4236 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 85.6% (4290 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 86.5% (4337 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 87.9% (4407 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 88.5% (4437 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 89.6% (4491 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 90.5% (4539 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 91.9% (4605 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 92.6% (4644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 93.7% (4696 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 94.7% (4748 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 95.7% (4795 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 96.7% (4849 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=784 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.4; 4715 / 5013 (P = 94.06%) round 40]               
[00:00:00] Finding cutoff p=773 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=773 1.3% (63 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 2.9% (144 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 3.9% (197 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 4.7% (238 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 5.4% (270 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 6.2% (312 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 7.5% (376 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 8.2% (411 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 9.2% (459 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 10.9% (546 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 11.7% (587 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 12.3% (617 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 13.6% (684 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 14.5% (729 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 15.6% (784 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 18.0% (902 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 18.9% (946 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 20.7% (1038 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 22.1% (1108 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 22.6% (1131 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 24.0% (1201 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 24.5% (1227 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 26.0% (1305 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 26.5% (1327 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 27.5% (1379 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 28.5% (1430 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 29.8% (1493 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 31.4% (1573 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 32.8% (1644 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 34.0% (1704 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 34.7% (1739 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 35.6% (1786 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 37.0% (1854 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 37.8% (1894 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 38.8% (1945 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 39.7% (1989 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 41.2% (2064 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 42.1% (2111 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 43.3% (2172 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 44.0% (2208 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 45.0% (2255 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 45.9% (2299 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 46.9% (2352 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 48.5% (2432 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 49.0% (2457 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 50.1% (2510 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 51.4% (2579 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 52.3% (2624 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 53.0% (2655 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 54.1% (2712 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 56.0% (2805 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 57.1% (2862 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 58.1% (2911 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 59.3% (2974 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 60.2% (3017 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 61.2% (3066 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 62.8% (3150 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 63.4% (3177 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 64.4% (3227 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 65.6% (3289 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 66.2% (3318 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 67.3% (3376 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 68.2% (3421 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 69.3% (3474 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 70.8% (3549 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 71.8% (3598 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 72.4% (3630 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 73.4% (3679 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 74.7% (3745 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 75.4% (3779 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 76.5% (3836 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 77.6% (3891 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 78.5% (3935 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 79.5% (3985 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 80.5% (4033 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 81.7% (4096 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 82.9% (4157 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 83.6% (4189 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 84.7% (4248 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 85.7% (4296 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 86.6% (4339 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 87.7% (4398 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 88.7% (4447 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 89.6% (4493 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 90.8% (4550 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 91.6% (4591 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 92.6% (4643 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 93.6% (4694 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 94.6% (4744 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 95.7% (4799 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 97.7% (4897 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 98.7% (4947 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=773 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 77.3; 4745 / 5013 (P = 94.65%) round 41]               
[00:00:00] Finding cutoff p=763 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=763 1.3% (64 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 2.9% (144 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 4.0% (200 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 4.6% (229 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 5.4% (273 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 6.4% (320 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 7.4% (373 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 8.7% (434 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 9.7% (488 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 11.2% (561 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 12.6% (631 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 13.5% (677 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 14.6% (733 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 15.3% (769 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 18.2% (912 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 19.3% (966 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 21.0% (1053 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 21.5% (1077 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 22.4% (1123 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 24.2% (1213 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 24.7% (1238 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 26.2% (1313 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 26.7% (1339 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 27.7% (1387 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 28.8% (1442 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 29.9% (1500 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 31.7% (1590 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 33.2% (1662 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 33.7% (1689 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 34.7% (1740 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 36.1% (1810 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 36.7% (1842 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 37.8% (1894 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 39.2% (1963 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 39.8% (1994 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 40.8% (2046 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 42.5% (2132 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 43.6% (2185 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 44.2% (2216 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 44.9% (2253 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 46.0% (2304 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 46.8% (2346 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 48.4% (2426 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 48.9% (2450 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 49.9% (2499 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 51.2% (2566 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 52.2% (2617 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 52.9% (2653 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 54.0% (2709 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 55.8% (2795 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 56.4% (2829 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 57.0% (2856 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 58.0% (2907 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 59.3% (2971 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 60.0% (3010 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 61.1% (3062 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 62.9% (3155 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 63.6% (3186 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 64.2% (3218 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 65.2% (3268 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 66.2% (3321 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 67.3% (3374 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 68.6% (3440 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 69.7% (3493 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 71.2% (3567 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 72.1% (3612 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 72.8% (3650 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 73.8% (3700 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 74.3% (3726 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 75.7% (3795 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 76.4% (3832 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 77.9% (3904 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 78.6% (3942 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 79.5% (3984 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 80.4% (4031 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 81.9% (4108 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 82.6% (4139 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 83.8% (4202 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 84.6% (4240 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 85.8% (4300 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 86.5% (4337 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 87.7% (4395 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 88.8% (4450 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 89.6% (4493 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 90.7% (4548 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 91.9% (4605 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 92.7% (4646 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 93.7% (4697 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 94.7% (4749 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 95.6% (4794 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 96.6% (4845 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 97.7% (4896 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 98.7% (4948 of 5013), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 99.7% (4998 of 5013), ETA 0:00:00                              
[00:00:00] Finished Preparing TNF Graph Building [pTNF = 76.30] [55.1Gb / 503.5Gb]                                            
[00:00:00] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=5013 maxEdges=200
[00:00:00] Building TNF Graph 30.8% (1545 of 5013), ETA 0:00:00     [55.1Gb / 503.5Gb]                           
[00:00:00] Building TNF Graph 65.7% (3296 of 5013), ETA 0:00:00     [55.1Gb / 503.5Gb]                           
[00:00:00] Building TNF Graph 98.6% (4944 of 5013), ETA 0:00:00     [55.1Gb / 503.5Gb]                           
[00:00:00] Finished Building TNF Graph (210475 edges) [55.1Gb / 503.5Gb]                                          
[00:00:00] Cleaned up after Building TNF Graph (210475 edges) [55.1Gb / 503.5Gb]                                          
[00:00:00] Cleaned up TNF matrix of large contigs [55.1Gb / 503.5Gb]                                             
[00:00:00] Applying coverage correlations to TNF graph with 210475 edges
[00:00:00] Allocated memory for graph edges [55.1Gb / 503.5Gb]
[00:00:00] ... calculating abundance dist 1.0% (2113 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 2.0% (4210 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 3.0% (6318 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 4.0% (8423 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 5.0% (10532 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 6.0% (12638 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 7.0% (14748 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 8.0% (16840 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 9.0% (18953 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 10.0% (21066 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 11.0% (23164 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 12.0% (25262 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 13.0% (27379 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 14.0% (29484 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 15.0% (31575 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 16.0% (33692 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 17.0% (35791 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 18.0% (37895 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 19.0% (40001 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 20.0% (42104 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 21.0% (44212 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 22.0% (46321 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 23.0% (48419 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 24.0% (50527 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 25.0% (52629 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 26.0% (54742 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 27.0% (56839 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 28.0% (58947 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 29.0% (61045 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 30.0% (63163 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 31.0% (65269 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 32.0% (67372 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 33.0% (69469 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 34.0% (71573 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 35.0% (73689 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 36.0% (75789 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 37.0% (77892 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 38.0% (79999 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 39.0% (82108 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 40.0% (84214 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 41.0% (86313 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 42.0% (88421 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 43.0% (90518 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 44.0% (92634 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 45.0% (94727 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 46.0% (96839 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 47.0% (98948 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 48.0% (101045 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 49.0% (103153 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 50.0% (105272 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 51.0% (107361 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 52.0% (109463 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 53.0% (111573 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 54.0% (113681 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 55.0% (115779 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 56.0% (117884 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 57.0% (119996 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 58.0% (122104 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 59.0% (124200 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 60.0% (126315 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 61.0% (128420 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 62.0% (130520 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 63.0% (132618 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 64.0% (134723 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 65.0% (136828 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 66.0% (138950 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 67.0% (141036 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 68.0% (143168 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 69.0% (145267 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 70.0% (147356 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 71.0% (149465 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 72.0% (151560 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 73.0% (153669 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 74.0% (155795 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 75.0% (157886 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 76.0% (159992 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 77.0% (162091 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 78.0% (164205 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 79.0% (166297 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 80.0% (168401 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 81.0% (170520 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 82.0% (172610 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 83.0% (174725 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 84.0% (176825 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 85.0% (178948 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 86.0% (181043 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 87.0% (183139 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 88.0% (185241 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 89.0% (187355 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 90.0% (189451 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 91.0% (191563 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 92.0% (193667 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 93.0% (195779 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 94.0% (197885 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 95.0% (199991 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 96.0% (202081 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 97.0% (204191 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 98.0% (206292 of 210475), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 99.0% (208395 of 210475), ETA 0:00:00                              
[00:00:00] Calculating geometric means [55.1Gb / 503.5Gb]
[00:00:00] Traversing graph with 5013 nodes and 210475 edges [55.1Gb / 503.5Gb]
[00:00:00] Building SCR Graph and Binning (477 vertices and 988 edges) [P = 9.50%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 1.0% (2105 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 2.0% (4210 of 210475), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (953 vertices and 3536 edges) [P = 19.00%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 3.0% (6315 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 4.0% (8420 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 5.0% (10525 of 210475), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1430 vertices and 5280 edges) [P = 28.50%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 6.0% (12630 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 7.0% (14735 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 8.0% (16840 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 9.0% (18945 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 10.0% (21050 of 210475), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1905 vertices and 6235 edges) [P = 38.00%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 11.0% (23155 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 12.0% (25260 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 13.0% (27365 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 14.0% (29470 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 15.0% (31575 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 16.0% (33680 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 17.0% (35785 of 210475), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (2382 vertices and 7371 edges) [P = 47.50%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 18.0% (37890 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 19.0% (39995 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 20.0% (42100 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 21.0% (44205 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 22.0% (46310 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 23.0% (48415 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 24.0% (50520 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 25.0% (52625 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 26.0% (54730 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 27.0% (56835 of 210475), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (2858 vertices and 8600 edges) [P = 57.00%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 28.0% (58940 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 29.0% (61045 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 30.0% (63150 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 31.0% (65255 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 32.0% (67360 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 33.0% (69465 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 34.0% (71570 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 35.0% (73675 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 36.0% (75780 of 210475), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (3334 vertices and 9574 edges) [P = 66.50%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 37.0% (77885 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 38.0% (79990 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 39.0% (82095 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 40.0% (84200 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 41.0% (86305 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 42.0% (88410 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 43.0% (90515 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 44.0% (92620 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 45.0% (94725 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 46.0% (96830 of 210475), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (3810 vertices and 10885 edges) [P = 76.00%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 47.0% (98935 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 48.0% (101040 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 49.0% (103145 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 50.0% (105250 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 51.0% (107355 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 52.0% (109460 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 53.0% (111565 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 54.0% (113670 of 210475), ETA 0:00:00                               
[00:00:00] ... traversing graph 55.0% (115775 of 210475), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (4080 vertices and 12175 edges) [P = 85.50%; 55.1Gb / 503.5Gb]                           
[00:00:00] Finished Traversing graph [55.1Gb / 503.5Gb]                                       
[00:00:00] Dissolved 1529 small clusters leaving 1088 leftover contigs to be re-merged into larger clusters
[00:00:00] Rescuing singleton large contigs                                   
[00:00:00] There are 1 bins already
[00:00:00] Outputting bins
[00:00:00] Writing cluster stats to: 03bins/metabat2_fixed/1507995/1507995.bin.BinInfo.txt
[00:00:01] 82.17% (24709931 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
1 bins (24709931 bases in total) formed.
[00:00:01] Finished
MetaBAT2 generated 1 bins for 1507995
MetaBAT2 binning completed for 1507995
