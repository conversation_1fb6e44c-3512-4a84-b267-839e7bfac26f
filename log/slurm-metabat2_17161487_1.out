Pulling MetaBAT2 Docker image...
Processing binning for sample: 1507990
Decompressing scaffold file for 1507990...
Generating depth file for 1507990...
Running MetaBAT2 for 1507990...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 1500, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 200000. with random seed=1745882681
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [91.0Gb / 503.5Gb]
[00:00:00] Parsing assembly file [91.0Gb / 503.5Gb]
[00:00:00] ... processed 2 seqs, 2 long (>=1500), 0 short (>=1000) 1.2% (115080 of 9913441), ETA 0:00:02     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 5 seqs, 5 long (>=1500), 0 short (>=1000) 2.1% (209049 of 9913441), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 9 seqs, 9 long (>=1500), 0 short (>=1000) 3.2% (319185 of 9913441), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 12 seqs, 12 long (>=1500), 0 short (>=1000) 4.0% (397293 of 9913441), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 17 seqs, 17 long (>=1500), 0 short (>=1000) 5.2% (517609 of 9913441), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 21 seqs, 21 long (>=1500), 0 short (>=1000) 6.1% (604212 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 26 seqs, 26 long (>=1500), 0 short (>=1000) 7.1% (705461 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 31 seqs, 31 long (>=1500), 0 short (>=1000) 8.1% (799094 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 37 seqs, 37 long (>=1500), 0 short (>=1000) 9.2% (908131 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 42 seqs, 42 long (>=1500), 0 short (>=1000) 10.0% (995558 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 48 seqs, 48 long (>=1500), 0 short (>=1000) 11.1% (1096223 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 54 seqs, 54 long (>=1500), 0 short (>=1000) 12.0% (1191950 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 61 seqs, 61 long (>=1500), 0 short (>=1000) 13.1% (1297675 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 68 seqs, 68 long (>=1500), 0 short (>=1000) 14.1% (1397876 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 75 seqs, 75 long (>=1500), 0 short (>=1000) 15.1% (1492923 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 83 seqs, 83 long (>=1500), 0 short (>=1000) 16.1% (1596130 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 91 seqs, 91 long (>=1500), 0 short (>=1000) 17.1% (1695792 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 99 seqs, 99 long (>=1500), 0 short (>=1000) 18.1% (1792586 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 107 seqs, 107 long (>=1500), 0 short (>=1000) 19.0% (1887263 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 116 seqs, 116 long (>=1500), 0 short (>=1000) 20.1% (1990590 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 125 seqs, 125 long (>=1500), 0 short (>=1000) 21.1% (2089952 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 134 seqs, 134 long (>=1500), 0 short (>=1000) 22.0% (2184062 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 144 seqs, 144 long (>=1500), 0 short (>=1000) 23.0% (2281894 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 155 seqs, 155 long (>=1500), 0 short (>=1000) 24.0% (2383085 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 166 seqs, 166 long (>=1500), 0 short (>=1000) 25.0% (2479264 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 178 seqs, 178 long (>=1500), 0 short (>=1000) 26.0% (2579796 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 190 seqs, 190 long (>=1500), 0 short (>=1000) 27.0% (2677160 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 203 seqs, 203 long (>=1500), 0 short (>=1000) 28.0% (2778271 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 217 seqs, 217 long (>=1500), 0 short (>=1000) 29.1% (2881516 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 231 seqs, 231 long (>=1500), 0 short (>=1000) 30.1% (2980762 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 245 seqs, 245 long (>=1500), 0 short (>=1000) 31.0% (3076136 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 260 seqs, 260 long (>=1500), 0 short (>=1000) 32.0% (3175287 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 276 seqs, 276 long (>=1500), 0 short (>=1000) 33.1% (3276620 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 292 seqs, 292 long (>=1500), 0 short (>=1000) 34.0% (3374644 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 309 seqs, 309 long (>=1500), 0 short (>=1000) 35.1% (3474695 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 326 seqs, 326 long (>=1500), 0 short (>=1000) 36.0% (3571456 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 344 seqs, 344 long (>=1500), 0 short (>=1000) 37.0% (3670186 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 363 seqs, 363 long (>=1500), 0 short (>=1000) 38.0% (3770214 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 382 seqs, 382 long (>=1500), 0 short (>=1000) 39.0% (3866629 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 403 seqs, 403 long (>=1500), 0 short (>=1000) 40.0% (3968971 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 424 seqs, 424 long (>=1500), 0 short (>=1000) 41.0% (4064876 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 447 seqs, 447 long (>=1500), 0 short (>=1000) 42.0% (4164217 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 471 seqs, 471 long (>=1500), 0 short (>=1000) 43.0% (4262917 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 497 seqs, 497 long (>=1500), 0 short (>=1000) 44.0% (4364215 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 524 seqs, 524 long (>=1500), 0 short (>=1000) 45.0% (4464611 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 552 seqs, 552 long (>=1500), 0 short (>=1000) 46.0% (4563445 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 581 seqs, 581 long (>=1500), 0 short (>=1000) 47.0% (4660307 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 612 seqs, 612 long (>=1500), 0 short (>=1000) 48.0% (4759437 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 644 seqs, 644 long (>=1500), 0 short (>=1000) 49.0% (4858041 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 678 seqs, 678 long (>=1500), 0 short (>=1000) 50.0% (4957105 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 715 seqs, 715 long (>=1500), 0 short (>=1000) 51.0% (5058197 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 753 seqs, 753 long (>=1500), 0 short (>=1000) 52.0% (5155105 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 795 seqs, 795 long (>=1500), 0 short (>=1000) 53.0% (5255393 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 839 seqs, 839 long (>=1500), 0 short (>=1000) 54.0% (5355356 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 884 seqs, 884 long (>=1500), 0 short (>=1000) 55.0% (5453006 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 933 seqs, 933 long (>=1500), 0 short (>=1000) 56.0% (5552761 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 985 seqs, 985 long (>=1500), 0 short (>=1000) 57.0% (5651702 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1041 seqs, 1041 long (>=1500), 0 short (>=1000) 58.0% (5751212 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1100 seqs, 1100 long (>=1500), 0 short (>=1000) 59.0% (5850368 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1163 seqs, 1163 long (>=1500), 0 short (>=1000) 60.0% (5949498 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1230 seqs, 1181 long (>=1500), 49 short (>=1000) 61.0% (6048102 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1302 seqs, 1181 long (>=1500), 121 short (>=1000) 62.0% (6146822 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1379 seqs, 1181 long (>=1500), 198 short (>=1000) 63.0% (6246522 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1460 seqs, 1181 long (>=1500), 279 short (>=1000) 64.0% (6345218 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1546 seqs, 1181 long (>=1500), 365 short (>=1000) 65.0% (6443858 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1638 seqs, 1181 long (>=1500), 457 short (>=1000) 66.0% (6543739 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1735 seqs, 1181 long (>=1500), 541 short (>=1000) 67.0% (6642563 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1839 seqs, 1181 long (>=1500), 541 short (>=1000) 68.0% (6741359 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1950 seqs, 1181 long (>=1500), 541 short (>=1000) 69.0% (6840591 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2068 seqs, 1181 long (>=1500), 541 short (>=1000) 70.0% (6939952 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2193 seqs, 1181 long (>=1500), 541 short (>=1000) 71.0% (7039087 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2326 seqs, 1181 long (>=1500), 541 short (>=1000) 72.0% (7138011 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2468 seqs, 1181 long (>=1500), 541 short (>=1000) 73.0% (7236979 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2618 seqs, 1181 long (>=1500), 541 short (>=1000) 74.0% (7336190 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2776 seqs, 1181 long (>=1500), 541 short (>=1000) 75.0% (7435717 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2942 seqs, 1181 long (>=1500), 541 short (>=1000) 76.0% (7534388 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3121 seqs, 1181 long (>=1500), 541 short (>=1000) 77.0% (7633817 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3310 seqs, 1181 long (>=1500), 541 short (>=1000) 78.0% (7732844 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3510 seqs, 1181 long (>=1500), 541 short (>=1000) 79.0% (7831670 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3724 seqs, 1181 long (>=1500), 541 short (>=1000) 80.0% (7930849 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3952 seqs, 1181 long (>=1500), 541 short (>=1000) 81.0% (8029940 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4196 seqs, 1181 long (>=1500), 541 short (>=1000) 82.0% (8129247 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4456 seqs, 1181 long (>=1500), 541 short (>=1000) 83.0% (8228320 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4733 seqs, 1181 long (>=1500), 541 short (>=1000) 84.0% (8327496 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 5026 seqs, 1181 long (>=1500), 541 short (>=1000) 85.0% (8426507 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 5336 seqs, 1181 long (>=1500), 541 short (>=1000) 86.0% (8525909 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 5663 seqs, 1181 long (>=1500), 541 short (>=1000) 87.0% (8624790 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 6010 seqs, 1181 long (>=1500), 541 short (>=1000) 88.0% (8723910 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 6372 seqs, 1181 long (>=1500), 541 short (>=1000) 89.0% (8823160 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 6746 seqs, 1181 long (>=1500), 541 short (>=1000) 90.0% (8922334 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 7134 seqs, 1181 long (>=1500), 541 short (>=1000) 91.0% (9021452 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 7538 seqs, 1181 long (>=1500), 541 short (>=1000) 92.0% (9120498 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 7962 seqs, 1181 long (>=1500), 541 short (>=1000) 93.0% (9219591 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 8411 seqs, 1181 long (>=1500), 541 short (>=1000) 94.0% (9318754 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 8934 seqs, 1181 long (>=1500), 541 short (>=1000) 95.0% (9417836 of 9913441), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 1500 bp are 1181, and small contigs >= 1000 bp are 541                                                                  
[00:00:00] Allocating 1181 contigs by 1 samples abundances [91.0Gb / 503.5Gb]
[00:00:00] Allocating 1181 contigs by 1 samples variances [91.0Gb / 503.5Gb]
[00:00:00] Allocating 541 small contigs by 1 samples abundances [91.0Gb / 503.5Gb]
[00:00:00] Reading 0.000532Gb abundance file [91.0Gb / 503.5Gb]
[00:00:00] ... processed 86 lines 86 contigs and 0 short contigs 1.0% (5752 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 174 lines 174 contigs and 0 short contigs 2.0% (11466 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 263 lines 263 contigs and 0 short contigs 3.0% (17158 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 353 lines 353 contigs and 0 short contigs 4.0% (22888 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 443 lines 443 contigs and 0 short contigs 5.0% (28601 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 533 lines 533 contigs and 0 short contigs 6.0% (34308 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 623 lines 623 contigs and 0 short contigs 7.0% (40016 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 713 lines 713 contigs and 0 short contigs 8.0% (45707 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 803 lines 803 contigs and 0 short contigs 9.0% (51385 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 894 lines 894 contigs and 0 short contigs 10.0% (57108 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 984 lines 984 contigs and 0 short contigs 11.0% (62802 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1074 lines 1074 contigs and 0 short contigs 12.0% (68546 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1163 lines 1163 contigs and 0 short contigs 13.0% (74240 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1252 lines 1181 contigs and 71 short contigs 14.0% (79947 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1341 lines 1181 contigs and 160 short contigs 15.0% (85637 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1431 lines 1181 contigs and 250 short contigs 16.0% (91386 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1520 lines 1181 contigs and 339 short contigs 17.0% (97085 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1609 lines 1181 contigs and 428 short contigs 18.0% (102781 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1698 lines 1181 contigs and 517 short contigs 19.0% (108477 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 20.0% (114219 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 21.0% (119925 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 22.0% (125613 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 23.0% (131313 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 24.0% (137057 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 25.0% (142736 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 26.0% (148490 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 27.0% (154181 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 28.0% (159870 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 29.0% (165622 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 30.0% (171305 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 31.0% (176986 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 32.0% (182748 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 33.0% (188454 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 34.0% (194138 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 35.0% (199875 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 36.0% (205571 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 37.0% (211258 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 38.0% (216980 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 39.0% (222689 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 40.0% (228384 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 41.0% (234088 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 42.0% (239778 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 43.0% (245529 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 44.0% (251225 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 45.0% (256911 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 46.0% (262619 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 47.0% (268333 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 48.0% (274042 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 49.0% (279742 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 50.0% (285506 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 51.0% (291219 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 52.0% (296893 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 53.0% (302635 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 54.0% (308305 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 55.0% (314026 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 56.0% (319729 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 57.0% (325418 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 58.0% (331133 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 59.0% (336861 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 60.0% (342575 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 61.0% (348280 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 62.0% (353988 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 63.0% (359684 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 64.0% (365396 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 65.0% (371125 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 66.0% (376851 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 67.0% (382547 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 68.0% (388254 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 69.0% (393940 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 70.0% (399671 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 71.0% (405347 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 72.0% (411063 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 73.0% (416810 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 74.0% (422526 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 75.0% (428230 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 76.0% (433920 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 77.0% (439594 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 78.0% (445348 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 79.0% (451043 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 80.0% (456777 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 81.0% (462455 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 82.0% (468162 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 83.0% (473902 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 84.0% (479575 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 85.0% (485300 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 86.0% (491027 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 87.0% (496695 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 88.0% (502415 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 89.0% (508156 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 90.0% (513819 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 91.0% (519521 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 92.0% (525269 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 93.0% (530990 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 94.0% (536685 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 95.0% (542383 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 96.0% (548081 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 97.0% (553788 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 98.0% (559536 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 1181 contigs and 541 short contigs 99.0% (565191 of 570893), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] Finished reading 9192 contigs and 1 coverages from 03bins/metabat2/1507990/temp/1507990.depth.txt [91.0Gb / 503.5Gb]. Ignored 7470 too small contigs.                                     
[00:00:00] Number of target contigs: 1181 of large (>= 1500) and 541 of small ones (>=1000 & <1500). 
[00:00:00] Start contig TNF calculation. nobs = 1181
[00:00:00] Allocated memory for TNF [91.0Gb / 503.5Gb]
[00:00:00] Calculating TNF 1.9% (23 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 3.5% (41 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 4.8% (57 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 6.2% (73 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 7.5% (88 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 8.7% (103 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 10.2% (120 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 11.7% (138 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 12.8% (151 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 14.1% (167 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 15.6% (184 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 16.8% (198 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 18.0% (213 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 19.5% (230 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 21.1% (249 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 22.5% (266 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 23.6% (279 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 25.3% (299 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 27.2% (321 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 28.7% (339 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 30.1% (355 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 31.6% (373 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 32.9% (389 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 34.0% (402 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 35.6% (420 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 37.1% (438 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 38.7% (457 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 40.1% (473 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 41.6% (491 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 43.1% (509 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 44.8% (529 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 46.6% (550 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 48.3% (570 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 49.9% (589 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 51.4% (607 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 52.8% (624 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 54.4% (643 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 56.1% (662 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 57.8% (683 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 59.3% (700 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 60.9% (719 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 62.3% (736 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 63.8% (753 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 65.2% (770 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 66.6% (787 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 68.2% (806 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 69.7% (823 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 71.3% (842 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 73.2% (865 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 74.4% (879 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 76.3% (901 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 77.7% (918 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 79.5% (939 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 81.1% (958 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 83.1% (981 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 84.7% (1000 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 86.4% (1020 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 87.8% (1037 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 89.8% (1060 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 91.2% (1077 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 92.7% (1095 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 94.1% (1111 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 96.4% (1138 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 97.6% (1153 of 1181), ETA 0:00:00    
[00:00:00] Calculating TNF 99.5% (1175 of 1181), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [91.0Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.6% (19 of 1181), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 3.6% (43 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 5.4% (64 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 6.8% (80 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 8.1% (96 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 9.5% (112 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 10.8% (128 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 12.2% (144 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 13.5% (160 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 14.9% (176 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 16.3% (192 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 17.6% (208 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 19.0% (224 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 20.3% (240 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 21.7% (256 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 23.0% (272 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.4% (288 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 25.7% (304 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.1% (320 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 28.5% (336 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.8% (352 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.2% (368 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.5% (384 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 33.9% (400 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 35.2% (416 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.6% (432 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.9% (448 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.3% (464 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 40.6% (480 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.0% (496 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.4% (512 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 44.7% (528 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.1% (544 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 47.5% (561 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.0% (579 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.6% (597 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.2% (616 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.5% (632 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.0% (649 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.5% (667 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 57.9% (684 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.3% (700 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 60.6% (716 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 62.0% (732 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.3% (748 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 64.7% (764 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.2% (782 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 67.6% (798 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.9% (814 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 70.3% (830 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 71.6% (846 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 73.0% (862 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.3% (878 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 75.7% (894 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 77.0% (909 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 78.3% (925 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 79.7% (941 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 81.0% (957 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 82.4% (973 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.8% (990 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 85.3% (1007 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 86.7% (1024 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 88.1% (1041 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 89.5% (1057 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 90.9% (1074 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 92.3% (1090 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 93.7% (1107 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 95.2% (1124 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 96.6% (1141 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 98.0% (1157 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 99.3% (1173 of 1181), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 99.7% (1177 of 1181), ETA 0:00:00                   
[00:00:00] Finding cutoff p=999 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=999 3.0% (35 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 8.6% (102 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 14.6% (173 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 21.5% (254 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 26.3% (311 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 30.8% (364 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 34.5% (408 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 37.9% (448 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 41.8% (494 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 48.3% (570 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 52.4% (619 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.6% (669 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 59.5% (703 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 63.3% (747 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 66.8% (789 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 69.3% (818 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 72.0% (850 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 74.5% (880 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 77.1% (910 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.7% (941 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 82.0% (968 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 84.4% (997 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 85.9% (1015 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.0% (1039 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 89.3% (1055 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 90.9% (1073 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 92.5% (1093 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 93.9% (1109 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 94.7% (1118 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.9; 0 / 1181 (P = 0.00%) round 1]               
[00:00:00] Finding cutoff p=997 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=997 2.8% (33 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 12.5% (148 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 22.0% (260 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 31.3% (370 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 41.6% (491 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 50.8% (600 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 59.2% (699 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 68.3% (807 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 76.4% (902 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 83.1% (981 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 85.4% (1008 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 87.5% (1033 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 89.3% (1055 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 91.7% (1083 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 94.1% (1111 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 94.9% (1121 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.7; 0 / 1181 (P = 0.00%) round 2]               
[00:00:00] Finding cutoff p=995 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=995 2.3% (27 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 10.4% (123 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 20.8% (246 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 30.4% (359 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 39.9% (471 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 50.5% (596 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 58.7% (693 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 66.0% (779 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 74.2% (876 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 82.2% (971 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 85.0% (1004 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 87.2% (1030 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 89.0% (1051 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 90.6% (1070 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 92.3% (1090 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 93.6% (1106 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 94.7% (1118 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.5; 2 / 1181 (P = 0.17%) round 3]               
[00:00:00] Finding cutoff p=994 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=994 1.6% (19 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 10.9% (129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 22.2% (262 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 32.6% (385 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 42.8% (506 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 51.6% (609 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 60.6% (716 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 69.9% (825 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 77.6% (917 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 85.4% (1009 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 87.8% (1037 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 89.4% (1056 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 91.4% (1079 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 92.8% (1096 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 94.0% (1110 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 94.8% (1120 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.4; 4 / 1181 (P = 0.34%) round 4]               
[00:00:00] Finding cutoff p=991 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=991 2.2% (26 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 10.5% (124 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 19.8% (234 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 30.1% (355 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 39.5% (467 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 50.2% (593 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 59.4% (701 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 66.7% (788 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 75.6% (893 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 82.3% (972 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 85.2% (1006 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 87.2% (1030 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 89.2% (1053 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 90.7% (1071 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 92.4% (1091 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 93.6% (1105 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 94.8% (1119 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.1; 16 / 1181 (P = 1.35%) round 5]               
[00:00:00] Finding cutoff p=990 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=990 2.1% (25 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 10.6% (125 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 20.7% (245 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 38.1% (450 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 47.9% (566 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 56.1% (662 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 65.5% (773 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 75.5% (892 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 82.0% (968 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 84.5% (998 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 86.7% (1024 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 88.7% (1047 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 90.3% (1067 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 92.0% (1087 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 93.2% (1101 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 94.4% (1115 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 95.2% (1124 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.0; 22 / 1181 (P = 1.86%) round 6]               
[00:00:00] Finding cutoff p=987 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=987 2.3% (27 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 10.7% (126 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 19.8% (234 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 32.2% (380 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 40.8% (482 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 50.7% (599 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 59.4% (701 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 67.0% (791 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 75.7% (894 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 82.6% (975 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 85.6% (1011 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 87.6% (1035 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 89.4% (1056 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 91.1% (1076 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 92.6% (1094 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 93.9% (1109 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 95.0% (1122 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.7; 59 / 1181 (P = 5.00%) round 7]               
[00:00:00] Finding cutoff p=983 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=983 2.3% (27 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 11.6% (137 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 21.5% (254 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 31.9% (377 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 41.8% (494 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 51.0% (602 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 59.4% (701 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 67.7% (799 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 75.6% (893 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 81.5% (962 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 84.4% (997 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 86.9% (1026 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 88.9% (1050 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 90.8% (1072 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 92.5% (1093 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 93.8% (1108 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 95.0% (1122 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.3; 96 / 1181 (P = 8.13%) round 8]               
[00:00:00] Finding cutoff p=979 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=979 2.3% (27 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 10.2% (121 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 19.5% (230 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 29.6% (349 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 38.7% (457 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 47.0% (555 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 55.7% (658 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 63.1% (745 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 71.9% (849 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 79.2% (935 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 84.0% (992 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 86.7% (1024 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 88.7% (1047 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 91.9% (1085 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 93.2% (1101 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 94.5% (1116 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.9; 132 / 1181 (P = 11.18%) round 9]               
[00:00:00] Finding cutoff p=975 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=975 2.0% (24 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 11.6% (137 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 21.9% (259 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 32.3% (381 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 41.2% (487 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 50.8% (600 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 59.4% (701 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 68.8% (812 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 76.4% (902 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 82.6% (975 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 85.5% (1010 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 88.1% (1040 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 89.7% (1059 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 91.3% (1078 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 92.8% (1096 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 94.2% (1112 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 95.1% (1123 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.5; 167 / 1181 (P = 14.14%) round 10]               
[00:00:00] Finding cutoff p=972 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=972 2.0% (24 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 10.7% (126 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 19.5% (230 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 29.2% (345 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 37.1% (438 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 45.6% (539 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 55.1% (651 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 68.3% (807 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 76.7% (906 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 81.8% (966 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 85.4% (1008 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 88.0% (1039 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 89.7% (1059 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 91.2% (1077 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 92.8% (1096 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 94.2% (1113 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 95.3% (1125 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.2; 191 / 1181 (P = 16.17%) round 11]               
[00:00:00] Finding cutoff p=969 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=969 2.4% (28 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 10.2% (120 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 20.1% (237 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 29.0% (343 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 38.5% (455 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 48.2% (569 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 56.6% (669 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 64.2% (758 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 72.2% (853 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 78.8% (931 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 82.8% (978 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 86.1% (1017 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 88.7% (1048 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 90.3% (1067 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 92.1% (1088 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 93.6% (1106 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 94.8% (1119 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.9; 219 / 1181 (P = 18.54%) round 12]               
[00:00:00] Finding cutoff p=966 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=966 2.0% (24 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 9.7% (115 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 19.6% (232 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 28.9% (341 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 38.7% (457 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 49.0% (579 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 56.6% (668 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 66.0% (780 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 74.5% (880 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 80.7% (953 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 84.0% (992 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 86.8% (1025 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 89.0% (1051 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 90.5% (1069 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 92.2% (1089 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 93.8% (1108 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 95.0% (1122 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.6; 246 / 1181 (P = 20.83%) round 13]               
[00:00:00] Finding cutoff p=961 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=961 2.0% (24 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 9.8% (116 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 18.6% (220 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 28.2% (333 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 36.9% (436 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 45.6% (538 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 55.1% (651 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 62.2% (734 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 69.9% (826 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 76.6% (905 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 81.5% (962 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 85.0% (1004 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 87.6% (1035 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 89.8% (1060 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 91.4% (1080 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 93.0% (1098 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 94.2% (1112 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 95.3% (1126 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.1; 279 / 1181 (P = 23.62%) round 14]               
[00:00:00] Finding cutoff p=958 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=958 1.9% (23 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 10.2% (120 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 19.4% (229 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 28.2% (333 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 35.7% (422 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 45.6% (539 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 53.2% (628 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 59.8% (706 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 66.6% (786 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 71.9% (849 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 76.5% (903 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 80.1% (946 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 83.2% (983 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 85.2% (1006 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 87.4% (1032 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 89.6% (1058 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 91.4% (1080 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 92.9% (1097 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 93.8% (1108 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 94.6% (1117 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 95.7% (1130 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.8; 296 / 1181 (P = 25.06%) round 15]               
[00:00:00] Finding cutoff p=954 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=954 2.1% (25 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 10.2% (121 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 18.0% (213 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 26.7% (315 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 34.0% (401 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 42.5% (502 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 55.6% (657 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 62.2% (734 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 69.5% (821 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 74.4% (879 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 77.1% (911 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 80.5% (951 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 83.0% (980 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 84.8% (1002 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 86.3% (1019 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 88.3% (1043 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 90.4% (1068 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 92.1% (1088 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 93.0% (1098 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 93.6% (1106 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 94.5% (1116 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.4; 323 / 1181 (P = 27.35%) round 16]               
[00:00:00] Finding cutoff p=951 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=951 1.9% (23 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 10.4% (123 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 19.1% (226 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 28.1% (332 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 35.6% (421 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 44.2% (522 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 51.5% (608 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 60.5% (715 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 66.1% (781 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 73.1% (863 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 79.9% (944 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 83.9% (991 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 87.0% (1028 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 89.4% (1056 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 91.2% (1077 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 92.6% (1094 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 94.3% (1114 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 95.3% (1125 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.1; 345 / 1181 (P = 29.21%) round 17]               
[00:00:00] Finding cutoff p=946 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=946 2.1% (25 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 9.7% (114 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 19.1% (226 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 27.3% (323 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 35.6% (420 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 43.1% (509 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 51.7% (610 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 58.8% (695 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 66.2% (782 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 72.1% (852 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 78.6% (928 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 82.0% (969 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 85.7% (1012 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 87.9% (1038 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 90.4% (1068 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 92.0% (1086 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 93.4% (1103 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 94.5% (1116 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=946 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.6; 371 / 1181 (P = 31.41%) round 18]               
[00:00:00] Finding cutoff p=942 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=942 2.1% (25 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 9.9% (117 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 17.5% (207 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 25.8% (305 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 34.2% (404 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 43.5% (514 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 51.1% (604 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 58.3% (689 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 64.9% (766 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 70.7% (835 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 80.4% (950 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 84.1% (993 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 86.8% (1025 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 89.6% (1058 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 91.8% (1084 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 93.3% (1102 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 94.9% (1121 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.2; 400 / 1181 (P = 33.87%) round 19]               
[00:00:00] Finding cutoff p=938 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=938 2.0% (24 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 8.6% (102 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 16.8% (198 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 24.7% (292 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 32.7% (386 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 41.1% (485 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 47.9% (566 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 54.8% (647 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 62.1% (733 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 68.0% (803 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 73.8% (872 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 78.6% (928 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 83.0% (980 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 85.7% (1012 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 88.8% (1049 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 90.8% (1072 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 92.7% (1095 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 94.2% (1113 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 95.2% (1124 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.8; 418 / 1181 (P = 35.39%) round 20]               
[00:00:00] Finding cutoff p=935 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=935 2.0% (24 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 15.5% (183 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 24.2% (286 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 32.2% (380 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 39.6% (468 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 47.3% (559 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 55.0% (649 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 62.7% (740 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 67.1% (793 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 72.9% (861 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 77.8% (919 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 81.7% (965 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 84.5% (998 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 87.1% (1029 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 89.4% (1056 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 91.2% (1077 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 92.7% (1095 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 94.2% (1112 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 95.3% (1126 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.5; 438 / 1181 (P = 37.09%) round 21]               
[00:00:00] Finding cutoff p=932 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=932 2.1% (25 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 8.1% (96 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 15.3% (181 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 22.1% (261 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 29.6% (349 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 36.9% (436 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 42.4% (501 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 49.9% (589 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 57.2% (675 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 63.1% (745 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 68.0% (803 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 73.7% (870 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 79.3% (937 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 83.3% (984 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 86.3% (1019 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 88.6% (1046 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 91.4% (1079 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 94.1% (1111 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 95.3% (1125 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.2; 458 / 1181 (P = 38.78%) round 22]               
[00:00:00] Finding cutoff p=928 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=928 2.1% (25 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 8.7% (103 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 15.6% (184 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 23.6% (279 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 29.0% (343 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 39.1% (462 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 45.9% (542 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 52.6% (621 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 60.5% (715 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 65.5% (774 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 72.7% (859 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 77.5% (915 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 82.2% (971 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 85.4% (1008 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 88.1% (1041 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 90.8% (1072 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 92.5% (1093 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 94.3% (1114 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 95.3% (1125 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.8; 491 / 1181 (P = 41.57%) round 23]               
[00:00:00] Finding cutoff p=924 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=924 2.0% (24 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 8.1% (96 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 16.1% (190 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 24.0% (284 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 31.1% (367 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 40.2% (475 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 50.8% (600 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 58.9% (696 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 63.6% (751 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 67.9% (802 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 73.0% (862 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 78.2% (923 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 83.4% (985 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 86.1% (1017 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 89.1% (1052 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 91.2% (1077 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 93.0% (1098 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 94.6% (1117 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.4; 512 / 1181 (P = 43.35%) round 24]               
[00:00:00] Finding cutoff p=920 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=920 2.0% (24 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 7.5% (89 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 14.1% (166 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 20.3% (240 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 28.7% (339 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 37.3% (441 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 43.3% (511 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 50.3% (594 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 56.1% (662 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 62.7% (740 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 67.1% (793 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 71.8% (848 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 75.9% (896 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 80.4% (950 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 84.8% (1001 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 87.1% (1029 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 90.2% (1065 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 92.3% (1090 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 94.1% (1111 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 95.0% (1122 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=920 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.0; 534 / 1181 (P = 45.22%) round 25]               
[00:00:00] Finding cutoff p=916 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=916 1.7% (20 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 7.9% (93 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 15.9% (188 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 22.5% (266 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 29.3% (346 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 38.2% (451 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 42.9% (507 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 49.8% (588 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 56.4% (666 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 62.1% (733 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 66.6% (787 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 70.5% (833 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 75.1% (887 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 80.1% (946 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 84.6% (999 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 87.0% (1027 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 89.7% (1059 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 91.4% (1079 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 93.6% (1105 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 95.1% (1123 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.6; 547 / 1181 (P = 46.32%) round 26]               
[00:00:00] Finding cutoff p=911 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=911 2.6% (31 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 7.9% (93 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 14.2% (168 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 19.9% (235 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 26.0% (307 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 33.6% (397 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 40.4% (477 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 47.2% (558 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 53.4% (631 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 62.4% (737 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 66.3% (783 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 70.8% (836 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 74.8% (883 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 79.3% (936 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 83.3% (984 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 86.4% (1020 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 89.4% (1056 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 91.8% (1084 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 93.6% (1105 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 94.9% (1121 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.1; 571 / 1181 (P = 48.35%) round 27]               
[00:00:00] Finding cutoff p=908 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=908 2.3% (27 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 7.5% (89 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 12.9% (152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 19.7% (233 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 26.9% (318 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 36.9% (436 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 43.7% (516 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 51.1% (604 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 56.9% (672 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 62.6% (739 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 66.9% (790 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 71.5% (844 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 76.4% (902 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 79.4% (938 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 84.4% (997 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 87.4% (1032 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 90.1% (1064 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 92.0% (1086 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 93.4% (1103 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 94.8% (1120 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.8; 578 / 1181 (P = 48.94%) round 28]               
[00:00:00] Finding cutoff p=904 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=904 2.1% (25 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 7.4% (87 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 12.7% (150 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 19.4% (229 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 25.7% (304 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 34.1% (403 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 40.0% (472 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 45.2% (534 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 50.1% (592 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 56.1% (663 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 60.9% (719 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 66.1% (781 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 69.8% (824 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 73.5% (868 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 78.5% (927 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 82.6% (976 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 86.5% (1021 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 89.3% (1055 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 91.2% (1077 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 93.2% (1101 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 94.9% (1121 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.4; 596 / 1181 (P = 50.47%) round 29]               
[00:00:00] Finding cutoff p=899 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=899 2.1% (25 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 8.9% (105 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 15.0% (177 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 20.0% (236 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 26.2% (310 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 34.2% (404 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 40.1% (473 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 47.1% (556 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 52.3% (618 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 60.9% (719 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 66.0% (779 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 71.7% (847 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 76.5% (904 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 80.7% (953 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 83.7% (989 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 86.5% (1022 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 89.8% (1060 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 91.9% (1085 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 93.4% (1103 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 94.6% (1117 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.9; 620 / 1181 (P = 52.50%) round 30]               
[00:00:00] Finding cutoff p=890 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=890 2.3% (27 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 7.9% (93 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 12.6% (149 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 18.4% (217 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 25.4% (300 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 32.0% (378 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 37.3% (441 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 43.7% (516 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 47.9% (566 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 52.7% (622 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 58.5% (691 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 64.4% (760 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 67.7% (800 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 71.0% (838 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 75.5% (892 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 79.5% (939 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 83.2% (983 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 87.3% (1031 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 89.5% (1057 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 91.3% (1078 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 93.6% (1105 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 95.1% (1123 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 95.8% (1131 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.0; 627 / 1181 (P = 53.09%) round 31]               
[00:00:00] Finding cutoff p=879 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=879 4.1% (48 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 8.1% (96 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 14.8% (175 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 21.1% (249 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 25.8% (305 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 33.4% (394 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 39.5% (466 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 44.6% (527 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 50.7% (599 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 57.7% (681 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 62.5% (738 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 66.7% (788 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 70.9% (837 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 74.9% (885 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 80.6% (952 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 84.3% (995 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 88.0% (1039 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 90.1% (1064 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 92.6% (1094 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 94.6% (1117 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 87.9; 646 / 1181 (P = 54.70%) round 32]               
[00:00:00] Finding cutoff p=869 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=869 3.1% (37 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 8.4% (99 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 15.2% (180 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 21.3% (252 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 26.0% (307 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 32.8% (387 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 37.5% (443 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 44.3% (523 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 53.0% (626 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 58.5% (691 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 64.1% (757 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 67.1% (793 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 70.5% (833 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 74.5% (880 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 79.8% (943 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 83.3% (984 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 87.2% (1030 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 89.6% (1058 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 91.5% (1081 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 93.6% (1105 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 95.0% (1122 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 95.7% (1130 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 86.9; 667 / 1181 (P = 56.48%) round 33]               
[00:00:00] Finding cutoff p=859 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=859 1.8% (21 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 7.7% (91 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 13.2% (156 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 18.8% (222 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 23.4% (276 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 29.8% (352 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 39.3% (464 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 43.7% (516 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 48.3% (570 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 53.7% (634 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 59.1% (698 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 63.4% (749 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 67.3% (795 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 70.0% (827 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 74.9% (884 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 77.6% (917 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 82.1% (970 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 85.7% (1012 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 88.9% (1050 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 90.9% (1073 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 93.5% (1104 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 95.2% (1124 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 95.8% (1131 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.9; 685 / 1181 (P = 58.00%) round 34]               
[00:00:00] Finding cutoff p=850 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=850 3.9% (46 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 9.1% (107 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 15.4% (182 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 20.8% (246 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 26.0% (307 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 33.9% (400 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 41.6% (491 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 48.1% (568 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 54.0% (638 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 59.9% (707 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 63.3% (748 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 66.9% (790 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 71.3% (842 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 75.2% (888 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 78.8% (931 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 82.9% (979 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 87.1% (1029 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 89.3% (1055 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 91.8% (1084 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 93.7% (1107 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 95.1% (1123 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 95.7% (1130 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.0; 704 / 1181 (P = 59.61%) round 35]               
[00:00:00] Finding cutoff p=840 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=840 1.9% (23 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 7.2% (85 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 10.8% (128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 16.3% (192 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 21.7% (256 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 27.4% (324 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 37.3% (440 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 43.0% (508 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 48.1% (568 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 53.6% (633 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 57.7% (681 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 63.0% (744 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 66.8% (789 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 69.9% (825 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 75.4% (890 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 78.2% (924 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 81.9% (967 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 84.8% (1001 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 87.8% (1037 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 89.7% (1059 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 91.9% (1085 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 93.6% (1106 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 95.0% (1122 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 95.8% (1131 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.0; 727 / 1181 (P = 61.56%) round 36]               
[00:00:00] Finding cutoff p=829 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=829 2.6% (31 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 6.4% (76 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 11.2% (132 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 16.0% (189 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 20.9% (247 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 26.2% (309 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 33.1% (391 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 39.7% (469 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 44.0% (520 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 47.2% (558 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 51.4% (607 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 56.3% (665 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 60.9% (719 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 64.7% (764 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 69.4% (820 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 73.1% (863 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 77.2% (912 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 84.8% (1001 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 88.6% (1046 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 90.9% (1074 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 93.7% (1107 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 94.6% (1117 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=829 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.9; 771 / 1181 (P = 65.28%) round 37]               
[00:00:00] Finding cutoff p=820 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=820 3.3% (39 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 8.2% (97 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 12.7% (150 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 18.0% (212 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 21.8% (257 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 26.7% (315 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 32.7% (386 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 39.2% (463 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 44.0% (520 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 47.5% (561 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 52.8% (623 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 57.1% (674 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 61.6% (727 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 65.5% (773 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 70.5% (833 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 73.4% (867 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 78.2% (924 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 81.5% (963 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 84.6% (999 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 88.7% (1047 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 91.4% (1080 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 93.3% (1102 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 94.8% (1120 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 95.7% (1130 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=820 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.0; 800 / 1181 (P = 67.74%) round 38]               
[00:00:00] Finding cutoff p=809 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=809 2.6% (31 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 7.3% (86 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 11.3% (133 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 16.1% (190 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 20.7% (244 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 26.2% (310 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 33.0% (390 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 40.0% (472 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 44.6% (527 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 48.6% (574 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 52.6% (621 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 57.9% (684 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 61.9% (731 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 65.1% (769 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 68.3% (807 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 71.8% (848 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 75.7% (894 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 78.7% (929 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 81.3% (960 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 85.0% (1004 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 88.7% (1047 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 91.3% (1078 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 92.7% (1095 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 93.9% (1109 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 95.0% (1122 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.9; 822 / 1181 (P = 69.60%) round 39]               
[00:00:00] Finding cutoff p=799 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=799 2.8% (33 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 6.1% (72 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 10.2% (120 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 14.1% (166 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 18.1% (214 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 23.6% (279 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 29.8% (352 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 36.4% (430 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 42.8% (506 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 46.7% (551 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 52.1% (615 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 57.2% (676 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 60.9% (719 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 64.1% (757 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 68.3% (807 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 71.1% (840 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 75.8% (895 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 78.8% (931 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 82.0% (969 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 85.7% (1012 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 89.2% (1054 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 91.6% (1082 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 93.4% (1103 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 94.6% (1117 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=799 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 79.9; 849 / 1181 (P = 71.89%) round 40]               
[00:00:00] Finding cutoff p=788 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=788 2.5% (30 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 7.0% (83 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 10.0% (118 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 13.6% (161 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 17.9% (211 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 23.0% (272 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 28.9% (341 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 35.0% (413 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 39.4% (465 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 43.4% (512 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 47.2% (557 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 50.4% (595 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 54.7% (646 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 58.6% (692 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 64.1% (757 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 67.7% (800 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 71.6% (846 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 75.0% (886 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 81.2% (959 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 85.4% (1009 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 88.4% (1044 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 89.8% (1061 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 92.0% (1086 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 93.6% (1105 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 94.5% (1116 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 95.7% (1130 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=788 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.8; 872 / 1181 (P = 73.84%) round 41]               
[00:00:00] Finding cutoff p=779 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=779 3.2% (38 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 7.4% (87 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 11.3% (133 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 15.3% (181 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 18.6% (220 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 24.6% (290 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 29.8% (352 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 38.3% (452 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 42.5% (502 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 46.7% (551 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 50.6% (598 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 55.6% (657 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 60.0% (709 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 63.0% (744 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 67.7% (799 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 70.5% (833 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 74.9% (885 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 76.5% (904 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 78.9% (932 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 83.1% (981 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 85.8% (1013 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 88.5% (1045 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 90.8% (1072 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 92.4% (1091 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 93.9% (1109 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 95.2% (1124 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 96.1% (1135 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=779 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 77.9; 889 / 1181 (P = 75.28%) round 42]               
[00:00:00] Finding cutoff p=769 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=769 2.9% (34 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 6.9% (81 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 11.3% (133 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 14.6% (173 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 18.0% (213 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 23.0% (272 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 26.7% (315 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 33.4% (394 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 37.5% (443 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 41.4% (489 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 45.3% (535 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 48.9% (577 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 51.7% (611 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 55.9% (660 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 59.4% (702 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 63.3% (748 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 67.7% (799 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 70.3% (830 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 74.3% (878 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 78.4% (926 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 82.4% (973 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 85.4% (1008 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 87.6% (1034 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 89.9% (1062 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 92.3% (1090 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 93.8% (1108 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 94.7% (1118 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 95.8% (1131 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 76.9; 907 / 1181 (P = 76.80%) round 43]               
[00:00:00] Finding cutoff p=758 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=758 2.5% (30 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 6.3% (74 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 11.0% (130 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 14.6% (172 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 18.1% (214 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 22.6% (267 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 27.1% (320 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 34.4% (406 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 39.2% (463 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 41.9% (495 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 45.1% (533 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 48.7% (575 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 51.7% (611 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 55.0% (649 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 59.9% (708 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 63.5% (750 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 66.8% (789 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 70.0% (827 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 72.5% (856 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 75.8% (895 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 79.3% (936 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 82.0% (968 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 84.8% (1001 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 87.0% (1028 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 90.3% (1066 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 92.0% (1087 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 94.2% (1112 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=758 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 75.8; 934 / 1181 (P = 79.09%) round 44]               
[00:00:00] Finding cutoff p=747 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=747 2.3% (27 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 7.6% (90 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 10.9% (129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 13.2% (156 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 17.4% (205 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 23.0% (272 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 28.8% (340 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 35.1% (415 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 38.2% (451 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 43.5% (514 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 47.1% (556 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 50.2% (593 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 55.0% (649 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 57.6% (680 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 63.4% (749 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 65.5% (773 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 69.0% (815 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 70.6% (834 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 73.8% (871 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 77.9% (920 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 81.1% (958 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 82.6% (976 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 84.7% (1000 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 86.7% (1024 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 89.5% (1057 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 91.8% (1084 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 93.7% (1107 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 94.8% (1120 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=747 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 74.7; 952 / 1181 (P = 80.61%) round 45]               
[00:00:00] Finding cutoff p=736 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=736 2.8% (33 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 7.5% (88 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 10.3% (122 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 13.5% (160 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 16.9% (200 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 21.3% (251 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 25.4% (300 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 30.7% (363 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 35.5% (419 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 39.9% (471 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 42.9% (507 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 46.8% (553 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 48.8% (576 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 52.0% (614 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 57.0% (673 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 62.2% (734 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 67.1% (793 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 70.6% (834 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 72.7% (859 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 75.0% (886 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 78.0% (921 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 79.9% (944 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 82.2% (971 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 84.2% (994 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 86.0% (1016 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 89.1% (1052 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 91.7% (1083 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 93.9% (1109 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 94.8% (1120 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=736 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 73.6; 966 / 1181 (P = 81.80%) round 46]               
[00:00:00] Finding cutoff p=725 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=725 2.2% (26 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 6.4% (76 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 9.7% (115 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 12.6% (149 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 16.6% (196 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 20.8% (246 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 25.3% (299 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 30.6% (361 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 34.5% (408 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 37.2% (439 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 40.1% (473 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 43.9% (518 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 47.2% (558 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 50.3% (594 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 54.3% (641 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 57.6% (680 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 62.4% (737 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 65.1% (769 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 68.5% (809 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 71.5% (845 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 75.7% (894 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 77.9% (920 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 79.8% (942 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 81.6% (964 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 84.2% (994 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 86.5% (1022 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 89.4% (1056 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 92.0% (1087 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 93.1% (1100 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 93.8% (1108 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 94.6% (1117 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 95.6% (1129 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 96.6% (1141 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=725 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 72.5; 983 / 1181 (P = 83.23%) round 47]               
[00:00:00] Finding cutoff p=714 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=714 2.6% (31 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 6.5% (77 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 9.2% (109 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 11.9% (141 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 16.2% (191 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 20.5% (242 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 24.5% (289 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 30.6% (361 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 35.6% (420 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 38.7% (457 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 41.8% (494 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 45.5% (537 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 48.4% (572 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 51.1% (604 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 56.0% (661 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 59.8% (706 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 64.5% (762 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 67.7% (800 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 71.2% (841 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 75.2% (888 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 78.3% (925 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 81.5% (962 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 82.5% (974 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 85.2% (1006 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 88.3% (1043 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 90.9% (1074 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 93.1% (1100 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 94.6% (1117 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 96.5% (1140 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=714 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 71.4; 995 / 1181 (P = 84.25%) round 48]               
[00:00:00] Finding cutoff p=705 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=705 2.6% (31 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 6.7% (79 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 10.2% (121 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 12.5% (148 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 16.3% (192 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 20.4% (241 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 23.6% (279 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 28.9% (341 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 34.6% (409 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 36.7% (433 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 41.1% (485 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 45.1% (533 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 47.2% (557 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 49.3% (582 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 54.3% (641 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 56.2% (664 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 61.6% (727 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 64.4% (761 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 67.5% (797 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 71.9% (849 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 75.0% (886 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 77.0% (909 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 78.7% (930 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 80.6% (952 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 82.6% (975 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 84.9% (1003 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 87.2% (1030 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 90.7% (1071 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 92.7% (1095 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 93.6% (1105 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 95.0% (1122 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 95.5% (1128 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 96.6% (1141 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 97.5% (1152 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 98.6% (1164 of 1181), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=705 99.6% (1176 of 1181), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 70.5; 1004 / 1181 (P = 85.01%) round 49]               
[00:00:00] Finished Preparing TNF Graph Building [pTNF = 69.60] [91.0Gb / 503.5Gb]                                            
[00:00:00] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=1181 maxEdges=200
[00:00:00] Finished Building TNF Graph (37093 edges) [91.0Gb / 503.5Gb]                                          
[00:00:00] Cleaned up after Building TNF Graph (37093 edges) [91.0Gb / 503.5Gb]                                          
[00:00:00] Cleaned up TNF matrix of large contigs [91.0Gb / 503.5Gb]                                             
[00:00:00] Applying coverage correlations to TNF graph with 37093 edges
[00:00:00] Allocated memory for graph edges [91.0Gb / 503.5Gb]
[00:00:00] ... calculating abundance dist 1.0% (382 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 2.0% (751 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 3.0% (1123 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 4.1% (1505 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 5.3% (1957 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 6.0% (2234 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 7.0% (2605 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 8.0% (2977 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 9.0% (3346 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 10.0% (3717 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 11.0% (4085 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 12.0% (4466 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 13.0% (4825 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 14.0% (5194 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 15.0% (5578 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 16.0% (5948 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 17.0% (6316 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 18.0% (6685 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 19.0% (7058 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 20.0% (7431 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 21.0% (7800 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 22.0% (8167 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 23.0% (8541 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 24.0% (8911 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 25.0% (9286 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 26.0% (9648 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 27.0% (10031 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 28.0% (10388 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 29.0% (10771 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 30.0% (11131 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 31.0% (11503 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 32.0% (11882 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 33.0% (12248 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 34.0% (12616 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 35.0% (12990 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 36.0% (13361 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 37.0% (13729 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 38.0% (14112 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 39.0% (14471 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 40.0% (14853 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 41.0% (15213 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 42.0% (15582 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 43.0% (15958 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 44.0% (16325 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 45.0% (16710 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 46.0% (17066 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 47.0% (17451 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 48.0% (17808 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 49.0% (18182 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 50.0% (18565 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 51.0% (18926 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 52.0% (19300 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 53.0% (19672 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 54.0% (20040 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 55.0% (20407 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 56.0% (20781 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 57.1% (21164 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 58.0% (21520 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 59.1% (21904 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 60.0% (22268 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 61.0% (22638 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 62.0% (23010 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 63.0% (23381 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 64.0% (23747 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 65.0% (24120 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 66.0% (24489 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 67.1% (24872 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 68.1% (25243 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 69.0% (25609 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 70.0% (25981 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 71.0% (26350 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 72.0% (26716 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 73.0% (27085 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 74.0% (27458 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 75.0% (27827 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 76.0% (28206 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 77.1% (28581 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 78.0% (28950 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 79.0% (29321 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 80.0% (29681 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 81.1% (30066 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 82.0% (30431 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 83.0% (30804 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 84.0% (31174 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 85.0% (31543 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 86.1% (31928 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 87.0% (32285 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 88.0% (32658 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 89.0% (33025 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 90.0% (33396 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 91.0% (33761 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 92.1% (34147 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 93.0% (34513 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 94.0% (34884 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 95.0% (35248 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 96.0% (35621 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 97.0% (35992 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 98.0% (36358 of 37093), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 99.0% (36729 of 37093), ETA 0:00:00                              
[00:00:00] Calculating geometric means [91.0Gb / 503.5Gb]
[00:00:00] Traversing graph with 1181 nodes and 37093 edges [91.0Gb / 503.5Gb]
[00:00:00] Building SCR Graph and Binning (113 vertices and 103 edges) [P = 9.50%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 1.0% (371 of 37093), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (225 vertices and 337 edges) [P = 19.00%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 2.0% (742 of 37093), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (337 vertices and 833 edges) [P = 28.50%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 3.0% (1113 of 37093), ETA 0:00:00                               
[00:00:00] ... traversing graph 4.0% (1484 of 37093), ETA 0:00:00                               
[00:00:00] ... traversing graph 5.0% (1855 of 37093), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (450 vertices and 1762 edges) [P = 38.00%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 6.0% (2226 of 37093), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (487 vertices and 2104 edges) [P = 47.50%; 91.1Gb / 503.5Gb]                           
[00:00:00] Finished Traversing graph [91.1Gb / 503.5Gb]                                       
[00:00:00] Dissolved 1059 small clusters leaving 763 leftover contigs to be re-merged into larger clusters
[00:00:00] Rescuing singleton large contigs                                   
[00:00:00] There are 5 bins already
[00:00:00] Outputting bins
[00:00:00] Writing cluster stats to: 03bins/metabat2/1507990/1507990.bin.BinInfo.txt
[00:00:00] 21.03% (1256468 bases) of large (>=1500) and 0.00% (0 bases) of small (<1500) contigs were binned.
5 bins (1256468 bases in total) formed.
[00:00:00] Finished
MetaBAT2 generated 5 bins for 1507990
MetaBAT2 binning completed for 1507990
Binning completed for sample: 1507990
