INFO:    Converting OCI blobs to SIF format
INFO:    Starting build...
Copying blob sha256:79578f19b677c48727475ff0c2a4daaf7a503c13bbdbbffa9d0918214b460a22
Copying blob sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1
Copying blob sha256:3069b7806bca5bd9d6b58e9582f26a6301b0fda4d087dbf33b74055179b4988f
Copying blob sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1
Copying blob sha256:6414378b647780fee8fd903ddb9541d134a1947ce092d08bdeb23a54cb3684ac
time="2025-04-28T16:24:30-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
Copying config sha256:4094e04a5277293d0f9242f032d90dcf74996cb55c1c9ceeeebbb22d9bc691f8
Writing manifest to image destination
time="2025-04-28T16:24:32-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
time="2025-04-28T16:24:33-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
2025/04/28 16:24:33  info unpack layer: sha256:6414378b647780fee8fd903ddb9541d134a1947ce092d08bdeb23a54cb3684ac
2025/04/28 16:24:33  info unpack layer: sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1
2025/04/28 16:24:33  info unpack layer: sha256:3069b7806bca5bd9d6b58e9582f26a6301b0fda4d087dbf33b74055179b4988f
2025/04/28 16:24:34  info unpack layer: sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1
2025/04/28 16:24:34  info unpack layer: sha256:79578f19b677c48727475ff0c2a4daaf7a503c13bbdbbffa9d0918214b460a22
INFO:    Creating SIF file...
scripts/run_metabat2.sh: line 30: /usr/local/bin/jgi_summarize_bam_contig_depths: Input/output error
scripts/run_metabat2.sh: line 34: /usr/local/bin/metabat2: Input/output error
scripts/run_metabat2.sh: line 42: /usr/bin/wc: Input/output error
scripts/run_metabat2.sh: line 51: [: -gt: unary operator expected
scripts/run_metabat2.sh: line 61: /usr/bin/rm: Input/output error
