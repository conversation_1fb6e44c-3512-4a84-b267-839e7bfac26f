Decompressing scaffold file for 1507999...
Generating depth file for 1507999...
Running MetaBAT2 for 1507999 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 200000. with random seed=123
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [8.4Gb / 503.5Gb]
[00:00:00] Parsing assembly file [8.4Gb / 503.5Gb]
[00:00:00] ... processed 12 seqs, 12 long (>=2000), 0 short (>=1000) 1.0% (511114 of 50243910), ETA 0:00:02     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 28 seqs, 28 long (>=2000), 0 short (>=1000) 2.0% (1015016 of 50243910), ETA 0:00:01     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 46 seqs, 46 long (>=2000), 0 short (>=1000) 3.0% (1528452 of 50243910), ETA 0:00:01     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 66 seqs, 66 long (>=2000), 0 short (>=1000) 4.0% (2031825 of 50243910), ETA 0:00:01     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 88 seqs, 88 long (>=2000), 0 short (>=1000) 5.0% (2528833 of 50243910), ETA 0:00:01     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 111 seqs, 111 long (>=2000), 0 short (>=1000) 6.0% (3018283 of 50243910), ETA 0:00:01     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 137 seqs, 137 long (>=2000), 0 short (>=1000) 7.0% (3526853 of 50243910), ETA 0:00:01     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 164 seqs, 164 long (>=2000), 0 short (>=1000) 8.0% (4020931 of 50243910), ETA 0:00:01     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 194 seqs, 194 long (>=2000), 0 short (>=1000) 9.0% (4538285 of 50243910), ETA 0:00:01     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 224 seqs, 224 long (>=2000), 0 short (>=1000) 10.0% (5027731 of 50243910), ETA 0:00:01     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 257 seqs, 257 long (>=2000), 0 short (>=1000) 11.0% (5539442 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 291 seqs, 291 long (>=2000), 0 short (>=1000) 12.0% (6042574 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 326 seqs, 326 long (>=2000), 0 short (>=1000) 13.0% (6541568 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 362 seqs, 362 long (>=2000), 0 short (>=1000) 14.0% (7034160 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 401 seqs, 401 long (>=2000), 0 short (>=1000) 15.0% (7542116 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 441 seqs, 441 long (>=2000), 0 short (>=1000) 16.0% (8040830 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 483 seqs, 483 long (>=2000), 0 short (>=1000) 17.0% (8545122 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 526 seqs, 526 long (>=2000), 0 short (>=1000) 18.0% (9044112 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 571 seqs, 571 long (>=2000), 0 short (>=1000) 19.0% (9550032 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 617 seqs, 617 long (>=2000), 0 short (>=1000) 20.0% (10048919 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 666 seqs, 666 long (>=2000), 0 short (>=1000) 21.0% (10559764 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 716 seqs, 716 long (>=2000), 0 short (>=1000) 22.0% (11062206 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 767 seqs, 767 long (>=2000), 0 short (>=1000) 23.0% (11557248 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 822 seqs, 822 long (>=2000), 0 short (>=1000) 24.0% (12065788 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 878 seqs, 878 long (>=2000), 0 short (>=1000) 25.0% (12561747 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 937 seqs, 937 long (>=2000), 0 short (>=1000) 26.0% (13066271 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 998 seqs, 998 long (>=2000), 0 short (>=1000) 27.0% (13571913 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1060 seqs, 1060 long (>=2000), 0 short (>=1000) 28.0% (14068507 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1125 seqs, 1125 long (>=2000), 0 short (>=1000) 29.0% (14574221 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1192 seqs, 1192 long (>=2000), 0 short (>=1000) 30.0% (15078742 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1261 seqs, 1261 long (>=2000), 0 short (>=1000) 31.0% (15580781 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1332 seqs, 1332 long (>=2000), 0 short (>=1000) 32.0% (16080855 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1406 seqs, 1406 long (>=2000), 0 short (>=1000) 33.0% (16585155 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1482 seqs, 1482 long (>=2000), 0 short (>=1000) 34.0% (17084692 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1561 seqs, 1561 long (>=2000), 0 short (>=1000) 35.0% (17589091 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1642 seqs, 1642 long (>=2000), 0 short (>=1000) 36.0% (18088529 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1728 seqs, 1728 long (>=2000), 0 short (>=1000) 37.0% (18595078 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1816 seqs, 1816 long (>=2000), 0 short (>=1000) 38.0% (19092764 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 1909 seqs, 1909 long (>=2000), 0 short (>=1000) 39.0% (19599613 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2005 seqs, 2005 long (>=2000), 0 short (>=1000) 40.0% (20102205 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2104 seqs, 2104 long (>=2000), 0 short (>=1000) 41.0% (20602485 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2207 seqs, 2207 long (>=2000), 0 short (>=1000) 42.0% (21104648 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2314 seqs, 2314 long (>=2000), 0 short (>=1000) 43.0% (21606058 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2426 seqs, 2426 long (>=2000), 0 short (>=1000) 44.0% (22110308 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2542 seqs, 2542 long (>=2000), 0 short (>=1000) 45.0% (22613310 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2662 seqs, 2662 long (>=2000), 0 short (>=1000) 46.0% (23112900 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2788 seqs, 2788 long (>=2000), 0 short (>=1000) 47.0% (23616288 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 2919 seqs, 2919 long (>=2000), 0 short (>=1000) 48.0% (24118913 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3056 seqs, 3056 long (>=2000), 0 short (>=1000) 49.0% (24620386 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3199 seqs, 3199 long (>=2000), 0 short (>=1000) 50.0% (25122532 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3349 seqs, 3349 long (>=2000), 0 short (>=1000) 51.0% (25627555 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3505 seqs, 3505 long (>=2000), 0 short (>=1000) 52.0% (26129624 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3668 seqs, 3668 long (>=2000), 0 short (>=1000) 53.0% (26629685 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 3841 seqs, 3841 long (>=2000), 0 short (>=1000) 54.0% (27133588 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 4023 seqs, 4023 long (>=2000), 0 short (>=1000) 55.0% (27636298 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 4215 seqs, 4215 long (>=2000), 0 short (>=1000) 56.0% (28137533 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 4419 seqs, 4419 long (>=2000), 0 short (>=1000) 57.0% (28640930 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 4636 seqs, 4636 long (>=2000), 0 short (>=1000) 58.0% (29142933 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 4868 seqs, 4868 long (>=2000), 0 short (>=1000) 59.0% (29645000 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 5117 seqs, 5022 long (>=2000), 95 short (>=1000) 60.0% (30147864 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 5385 seqs, 5022 long (>=2000), 363 short (>=1000) 61.0% (30650201 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 5674 seqs, 5022 long (>=2000), 652 short (>=1000) 62.0% (31151736 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 5987 seqs, 5022 long (>=2000), 965 short (>=1000) 63.0% (31654869 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 6324 seqs, 5022 long (>=2000), 1302 short (>=1000) 64.0% (32156858 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 6687 seqs, 5022 long (>=2000), 1665 short (>=1000) 65.0% (32658786 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 7078 seqs, 5022 long (>=2000), 2056 short (>=1000) 66.0% (33161515 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 7501 seqs, 5022 long (>=2000), 2479 short (>=1000) 67.0% (33664622 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 7957 seqs, 5022 long (>=2000), 2935 short (>=1000) 68.0% (34166972 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 8444 seqs, 5022 long (>=2000), 3422 short (>=1000) 69.0% (34668408 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 8963 seqs, 5022 long (>=2000), 3432 short (>=1000) 70.0% (35170931 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 9517 seqs, 5022 long (>=2000), 3432 short (>=1000) 71.0% (35673340 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 10108 seqs, 5022 long (>=2000), 3432 short (>=1000) 72.0% (36175800 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 10744 seqs, 5022 long (>=2000), 3432 short (>=1000) 73.0% (36678858 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 11427 seqs, 5022 long (>=2000), 3432 short (>=1000) 74.0% (37180732 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 12166 seqs, 5022 long (>=2000), 3432 short (>=1000) 75.0% (37683305 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 12964 seqs, 5022 long (>=2000), 3432 short (>=1000) 76.0% (38185964 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 13828 seqs, 5022 long (>=2000), 3432 short (>=1000) 77.0% (38688411 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 14763 seqs, 5022 long (>=2000), 3432 short (>=1000) 78.0% (39190552 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 15785 seqs, 5022 long (>=2000), 3432 short (>=1000) 79.0% (39692882 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 16902 seqs, 5022 long (>=2000), 3432 short (>=1000) 80.0% (40195565 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 18124 seqs, 5022 long (>=2000), 3432 short (>=1000) 81.0% (40697882 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 19458 seqs, 5022 long (>=2000), 3432 short (>=1000) 82.0% (41200137 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 20915 seqs, 5022 long (>=2000), 3432 short (>=1000) 83.0% (41702799 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 22512 seqs, 5022 long (>=2000), 3432 short (>=1000) 84.0% (42205126 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 24275 seqs, 5022 long (>=2000), 3432 short (>=1000) 85.0% (42707663 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 26208 seqs, 5022 long (>=2000), 3432 short (>=1000) 86.0% (43209840 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 28312 seqs, 5022 long (>=2000), 3432 short (>=1000) 87.0% (43712429 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 30610 seqs, 5022 long (>=2000), 3432 short (>=1000) 88.0% (44214902 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 33218 seqs, 5022 long (>=2000), 3432 short (>=1000) 89.0% (44717336 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 36176 seqs, 5022 long (>=2000), 3432 short (>=1000) 90.0% (45219613 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 39511 seqs, 5022 long (>=2000), 3432 short (>=1000) 91.0% (45722057 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 43657 seqs, 5022 long (>=2000), 3432 short (>=1000) 92.0% (46224517 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 48860 seqs, 5022 long (>=2000), 3432 short (>=1000) 93.0% (46726959 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] ... processed 56390 seqs, 5022 long (>=2000), 3432 short (>=1000) 94.0% (47229379 of 50243910), ETA 0:00:00     [8.4Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 5022, and small contigs >= 1000 bp are 3432                                                                  
[00:00:00] Allocating 5022 contigs by 1 samples abundances [8.4Gb / 503.5Gb]
[00:00:00] Allocating 5022 contigs by 1 samples variances [8.4Gb / 503.5Gb]
[00:00:00] Allocating 3432 small contigs by 1 samples abundances [8.4Gb / 503.5Gb]
[00:00:00] Reading 0.003364Gb abundance file [8.4Gb / 503.5Gb]
[00:00:00] ... processed 550 lines 550 contigs and 0 short contigs 1.0% (36136 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 1111 lines 1111 contigs and 0 short contigs 2.0% (72264 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 1670 lines 1670 contigs and 0 short contigs 3.0% (108438 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 2229 lines 2229 contigs and 0 short contigs 4.0% (144540 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 2788 lines 2788 contigs and 0 short contigs 5.0% (180656 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 3347 lines 3347 contigs and 0 short contigs 6.0% (216759 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 3907 lines 3907 contigs and 0 short contigs 7.0% (252914 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 4467 lines 4467 contigs and 0 short contigs 8.0% (289016 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 5027 lines 5022 contigs and 5 short contigs 9.0% (325138 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 5588 lines 5022 contigs and 566 short contigs 10.0% (361284 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 6149 lines 5022 contigs and 1127 short contigs 11.0% (397423 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 6710 lines 5022 contigs and 1688 short contigs 12.0% (433575 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 7270 lines 5022 contigs and 2248 short contigs 13.0% (469646 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 7832 lines 5022 contigs and 2810 short contigs 14.0% (505801 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8392 lines 5022 contigs and 3370 short contigs 15.0% (541899 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 16.0% (578055 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 17.0% (614193 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 18.0% (650268 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 19.0% (686427 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 20.0% (722549 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 21.0% (758653 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 22.0% (794795 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 23.0% (830920 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 24.0% (867062 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 25.0% (903155 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 26.0% (939329 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 27.0% (975426 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 28.0% (1011535 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 29.0% (1047684 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 30.0% (1083812 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 31.0% (1119965 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 32.0% (1156039 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 33.0% (1192184 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 34.0% (1228308 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 35.0% (1264442 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 36.0% (1300592 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 37.0% (1336689 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 38.0% (1372795 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 39.0% (1408938 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 40.0% (1445092 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 41.0% (1481168 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 42.0% (1517327 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 43.0% (1553453 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 44.0% (1589568 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 45.0% (1625690 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 46.0% (1661804 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 47.0% (1697969 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 48.0% (1734069 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 49.0% (1770197 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 50.0% (1806353 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 51.0% (1842432 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 52.0% (1878596 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 53.0% (1914717 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 54.0% (1950833 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 55.0% (1986930 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 56.0% (2023061 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 57.0% (2059220 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 58.0% (2095336 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 59.0% (2131444 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 60.0% (2167615 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 61.0% (2203704 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 62.0% (2239870 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 63.0% (2275949 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 64.0% (2312068 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 65.0% (2348222 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 66.0% (2384368 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 67.0% (2420461 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 68.0% (2456602 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 69.0% (2492750 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 70.0% (2528850 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 71.0% (2564956 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 72.0% (2601098 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 73.0% (2637206 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 74.0% (2673381 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 75.0% (2709487 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 76.0% (2745616 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 77.0% (2781743 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 78.0% (2817878 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 79.0% (2854005 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 80.0% (2890090 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 81.0% (2926232 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 82.0% (2962365 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 83.0% (2998495 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 84.0% (3034618 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 85.0% (3070763 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 86.0% (3106865 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 87.0% (3142970 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 88.0% (3179107 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 89.0% (3215223 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 90.0% (3251371 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 91.0% (3287522 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 92.0% (3323647 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 93.0% (3359762 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 94.0% (3395868 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 95.0% (3431998 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 96.0% (3468133 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 97.0% (3504279 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 98.0% (3540372 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 99.0% (3576501 of 3612525), ETA 0:00:00     [8.4Gb / 503.5Gb]                 
[00:00:00] Finished reading 58452 contigs and 1 coverages from 03bins/metabat2_fixed/1507999/temp/1507999.depth.txt [8.4Gb / 503.5Gb]. Ignored 49998 too small contigs.                                     
[00:00:00] Number of target contigs: 5022 of large (>= 2000) and 3432 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 5022
[00:00:00] Allocated memory for TNF [8.4Gb / 503.5Gb]
[00:00:00] Calculating TNF 42.7% (2145 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 43.7% (2194 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 44.7% (2245 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 46.0% (2308 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 46.9% (2356 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 47.8% (2402 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 48.8% (2451 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 49.9% (2507 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 50.8% (2552 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 52.1% (2618 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 52.9% (2657 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 53.8% (2704 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 54.9% (2755 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 55.9% (2805 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 57.1% (2869 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 58.2% (2924 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 59.2% (2973 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 60.2% (3022 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 61.1% (3067 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 62.1% (3118 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 63.1% (3170 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 64.1% (3221 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 65.1% (3268 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 66.0% (3315 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 67.3% (3378 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 68.2% (3425 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 69.2% (3477 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 70.2% (3527 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 71.2% (3577 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 72.2% (3625 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 73.4% (3684 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 74.4% (3737 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 75.4% (3787 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 76.3% (3830 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 77.6% (3896 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 81.6% (4098 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 82.4% (4139 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 83.4% (4189 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 84.4% (4238 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 85.4% (4289 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 86.4% (4337 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 87.3% (4386 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 88.6% (4449 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 89.7% (4504 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 90.6% (4549 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 91.7% (4604 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 92.5% (4647 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 93.6% (4700 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 94.6% (4753 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 99.8% (5011 of 5022), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [8.4Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 4.4% (222 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 5.4% (269 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 6.3% (315 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 7.3% (366 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 8.4% (422 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 9.6% (481 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 10.7% (538 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 11.5% (578 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 12.7% (638 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 13.4% (672 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 14.9% (749 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 15.4% (775 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 16.3% (817 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 17.5% (881 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 18.4% (925 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 19.7% (991 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 20.6% (1037 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 21.6% (1083 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 22.5% (1129 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 23.4% (1175 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.9% (1250 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 25.8% (1298 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.7% (1343 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.6% (1388 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 28.5% (1432 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.7% (1490 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 30.7% (1540 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.9% (1603 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.8% (1648 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.9% (1754 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 35.6% (1787 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.1% (1861 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.9% (1905 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 38.8% (1950 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.7% (1994 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.0% (2061 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.9% (2105 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.8% (2149 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.7% (2193 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 45.1% (2264 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 45.9% (2304 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.7% (2347 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.0% (2412 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.9% (2458 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.8% (2503 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 51.1% (2568 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.0% (2609 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.2% (2674 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 54.0% (2710 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.1% (2766 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 57.9% (2909 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.0% (2962 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 60.0% (3012 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.0% (3062 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 62.0% (3112 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.2% (3176 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 64.2% (3225 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 65.1% (3270 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.5% (3339 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 67.5% (3390 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.5% (3440 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 69.2% (3475 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 70.1% (3520 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 71.8% (3606 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 72.1% (3621 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 73.4% (3686 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.4% (3738 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 75.4% (3786 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 76.3% (3833 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 77.3% (3882 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 78.3% (3932 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 79.3% (3980 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 80.5% (4044 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 81.3% (4082 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 82.6% (4147 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.5% (4195 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 84.5% (4244 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 85.5% (4292 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 86.4% (4341 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 87.4% (4389 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 88.4% (4439 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 89.7% (4503 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 90.6% (4551 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 91.6% (4600 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 92.6% (4648 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 93.5% (4696 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 94.5% (4744 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 95.7% (4808 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 96.5% (4847 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 97.5% (4898 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 98.5% (4948 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 99.5% (4998 of 5022), ETA 0:00:00                   
[00:00:00] Finding cutoff p=999 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=999 1.1% (53 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 2.3% (113 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 3.2% (162 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 4.3% (217 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 5.3% (268 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 6.5% (326 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 7.4% (372 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 8.3% (419 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 9.2% (464 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 10.3% (515 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 11.3% (568 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 12.2% (614 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 13.9% (698 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 14.8% (742 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 15.7% (786 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 16.8% (846 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 18.7% (939 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 19.4% (973 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 20.5% (1029 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 21.7% (1090 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 22.9% (1150 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 23.6% (1186 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 24.4% (1227 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 25.9% (1301 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 26.7% (1339 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 27.5% (1382 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 29.1% (1461 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 30.0% (1505 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 30.7% (1544 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 31.6% (1589 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 33.2% (1666 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 34.1% (1710 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 35.0% (1756 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 35.7% (1794 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 37.3% (1871 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 37.9% (1905 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 38.6% (1940 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 40.0% (2010 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 41.2% (2068 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 41.8% (2099 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 42.8% (2148 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 43.8% (2199 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 44.8% (2252 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 45.9% (2304 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 47.1% (2365 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 47.8% (2399 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 49.0% (2460 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 50.1% (2514 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 51.2% (2571 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 51.9% (2604 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 53.0% (2663 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 54.0% (2710 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 54.9% (2758 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.0% (2810 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 57.1% (2866 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 58.4% (2931 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 59.1% (2966 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 60.3% (3028 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 61.0% (3061 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 62.1% (3117 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 63.3% (3177 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 64.1% (3218 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 65.3% (3280 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 66.1% (3322 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 67.0% (3367 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 68.3% (3429 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 69.1% (3472 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 70.5% (3540 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 71.5% (3591 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 72.5% (3639 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 73.3% (3681 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 74.2% (3726 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 75.2% (3776 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 76.5% (3843 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 77.5% (3892 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 78.5% (3941 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.4% (3989 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 80.3% (4031 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 81.4% (4086 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 82.4% (4137 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 83.4% (4188 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 84.5% (4242 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 85.4% (4291 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 86.4% (4339 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 87.3% (4386 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.4% (4438 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 89.4% (4488 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 90.4% (4542 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 91.5% (4593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 92.5% (4646 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 93.5% (4694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 94.5% (4746 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 95.5% (4796 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 96.5% (4846 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 97.5% (4898 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 98.5% (4948 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.9; 0 / 5022 (P = 0.00%) round 1]               
[00:00:00] Finding cutoff p=998 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=998 1.2% (62 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 2.9% (146 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 4.5% (227 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 6.3% (316 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 8.1% (406 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 10.4% (524 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 13.6% (684 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 14.4% (725 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 15.4% (773 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 16.3% (819 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 17.4% (874 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 18.9% (947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 19.9% (997 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 20.7% (1040 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 21.5% (1082 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 22.5% (1130 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 23.4% (1175 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 24.5% (1228 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 26.1% (1310 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 27.1% (1362 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 28.3% (1419 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 30.1% (1511 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 31.3% (1573 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 32.2% (1617 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 33.1% (1660 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 34.1% (1711 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 35.0% (1758 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 36.0% (1807 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 37.0% (1857 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 38.2% (1918 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 39.3% (1974 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 40.2% (2021 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 40.9% (2052 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 41.8% (2099 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 42.8% (2147 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 43.7% (2194 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 44.9% (2254 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 45.8% (2300 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 46.9% (2357 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 48.0% (2409 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 49.0% (2459 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 49.8% (2502 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 51.0% (2563 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 51.9% (2604 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 53.1% (2669 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 53.9% (2709 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 55.4% (2783 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 56.3% (2825 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 57.0% (2864 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 58.2% (2924 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 59.0% (2961 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 60.1% (3020 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 61.3% (3077 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 62.0% (3115 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 63.2% (3176 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 64.2% (3222 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 65.1% (3270 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 66.0% (3317 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 67.3% (3381 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 68.2% (3424 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 69.1% (3471 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 70.3% (3531 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 71.2% (3574 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 72.4% (3636 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 73.2% (3677 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 74.5% (3739 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 75.4% (3786 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 76.3% (3832 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 77.4% (3886 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 78.4% (3935 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 79.3% (3983 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 80.3% (4033 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 81.4% (4090 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 82.4% (4138 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 83.4% (4190 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 84.3% (4236 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 85.4% (4291 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 86.5% (4345 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 87.4% (4387 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 88.5% (4443 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 89.5% (4495 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 90.4% (4540 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 91.5% (4593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 92.4% (4642 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 93.5% (4698 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 94.6% (4749 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 95.6% (4800 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 97.5% (4898 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.8; 0 / 5022 (P = 0.00%) round 2]               
[00:00:00] Finding cutoff p=995 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=995 1.0% (51 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 2.5% (127 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 4.0% (199 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 5.7% (285 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 7.2% (360 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 8.7% (439 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 10.4% (524 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 12.2% (613 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 14.0% (702 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 16.0% (804 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 18.3% (920 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 20.1% (1009 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 21.3% (1070 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 22.4% (1127 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 23.6% (1183 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 24.7% (1239 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 25.9% (1300 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 27.1% (1361 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 28.3% (1422 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 29.5% (1482 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 30.7% (1544 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 32.0% (1609 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 33.3% (1671 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 34.4% (1730 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 35.7% (1794 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 36.9% (1852 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 38.1% (1914 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 39.1% (1964 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 40.2% (2019 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 41.2% (2067 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 42.1% (2115 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 43.2% (2168 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 44.2% (2220 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 45.2% (2272 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 46.3% (2325 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 47.3% (2377 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 48.3% (2425 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 49.1% (2468 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 50.0% (2511 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 50.8% (2551 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 52.1% (2618 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 53.0% (2661 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 53.8% (2703 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 55.2% (2772 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 56.1% (2817 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 56.9% (2857 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 58.3% (2926 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 59.2% (2972 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 59.9% (3010 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 61.0% (3065 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 62.1% (3120 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 63.5% (3190 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 64.3% (3231 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 65.3% (3281 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 66.1% (3318 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 67.2% (3374 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 68.1% (3420 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 69.1% (3468 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 70.4% (3534 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 71.4% (3584 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 72.4% (3635 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 73.3% (3679 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 74.3% (3733 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 75.2% (3775 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 76.3% (3831 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 77.5% (3890 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 78.3% (3930 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 79.4% (3989 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 80.2% (4029 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 81.4% (4089 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 82.4% (4138 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 83.4% (4190 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 84.5% (4243 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 88.1% (4425 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 88.5% (4444 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 89.4% (4490 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 90.4% (4542 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 91.5% (4593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 92.4% (4641 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 93.5% (4696 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 95.5% (4794 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.5; 41 / 5022 (P = 0.82%) round 3]               
[00:00:00] Finding cutoff p=993 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=993 1.0% (52 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 2.5% (128 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 4.0% (202 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 5.6% (281 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 8.1% (408 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 9.8% (492 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 11.5% (576 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 13.2% (661 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 14.8% (743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 16.6% (833 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 18.4% (925 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 20.3% (1017 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 22.4% (1126 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 23.8% (1194 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 25.0% (1254 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 26.1% (1312 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 27.4% (1375 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 28.6% (1436 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 65.1% (3268 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 66.1% (3320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 67.3% (3380 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 68.4% (3433 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 69.4% (3486 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 70.4% (3537 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 71.4% (3587 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 72.4% (3635 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 73.4% (3686 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 74.3% (3732 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 75.1% (3774 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 76.4% (3835 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 77.5% (3890 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 78.4% (3938 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 79.5% (3990 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 80.4% (4038 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 81.4% (4089 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 82.5% (4141 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 83.3% (4182 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 84.3% (4235 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 85.3% (4285 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 86.3% (4336 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 87.4% (4391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 88.5% (4442 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 89.4% (4492 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 90.4% (4542 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 91.5% (4595 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 92.4% (4641 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 93.4% (4693 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 94.5% (4744 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 95.5% (4796 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.3; 126 / 5022 (P = 2.51%) round 4]               
[00:00:00] Finding cutoff p=990 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=990 1.8% (89 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 3.3% (167 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 4.8% (243 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 6.3% (316 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 7.7% (388 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 9.3% (466 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 10.8% (543 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 12.4% (621 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 14.0% (701 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 15.8% (793 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 17.4% (872 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 19.0% (955 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 20.9% (1050 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 22.8% (1145 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 24.4% (1226 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 25.5% (1283 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 26.8% (1346 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 28.1% (1412 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 29.3% (1470 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 30.4% (1529 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 31.8% (1596 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 33.0% (1657 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 34.2% (1718 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 35.4% (1779 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 36.8% (1846 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 38.0% (1906 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 39.1% (1962 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 40.0% (2009 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 41.0% (2060 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 42.1% (2116 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 43.3% (2173 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 44.3% (2223 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 45.9% (2307 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 47.0% (2362 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 48.1% (2414 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 49.1% (2466 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 50.0% (2513 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 50.9% (2555 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 52.2% (2621 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 53.1% (2668 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 53.9% (2705 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 55.2% (2774 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 56.2% (2820 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 57.0% (2862 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 57.9% (2907 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 59.2% (2972 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 60.1% (3018 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 60.9% (3060 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 62.1% (3118 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 63.2% (3176 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 64.0% (3215 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 65.2% (3275 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 66.4% (3333 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 67.2% (3373 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 68.2% (3426 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 69.3% (3481 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 70.3% (3530 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 71.3% (3580 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 72.3% (3630 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 73.3% (3683 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 74.3% (3730 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 75.3% (3781 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 76.4% (3838 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 77.3% (3883 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 78.4% (3936 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 79.4% (3988 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 80.4% (4037 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 81.3% (4084 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 82.3% (4133 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 83.4% (4188 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 84.4% (4240 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 85.5% (4295 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 86.5% (4343 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 87.4% (4388 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 88.5% (4443 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 89.5% (4494 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 90.5% (4543 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 91.5% (4596 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 92.5% (4643 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 93.4% (4692 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 94.5% (4747 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 95.5% (4794 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.0; 309 / 5022 (P = 6.15%) round 5]               
[00:00:00] Finding cutoff p=986 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=986 1.7% (84 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 3.2% (159 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 4.6% (230 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 6.0% (302 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 7.4% (374 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 8.9% (449 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 10.3% (519 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 12.1% (608 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 13.7% (687 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 15.3% (768 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 16.9% (847 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 18.6% (934 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 20.2% (1016 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 22.2% (1114 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 24.1% (1209 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 25.2% (1264 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 26.4% (1328 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 27.7% (1391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 28.9% (1452 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 30.2% (1516 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 31.4% (1576 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 32.7% (1640 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 33.9% (1700 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 35.1% (1761 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 36.3% (1823 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 37.6% (1887 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 39.5% (1982 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 40.5% (2033 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 41.5% (2082 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 42.5% (2135 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 43.5% (2183 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 44.5% (2234 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 45.6% (2289 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 46.6% (2341 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 47.6% (2391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 48.7% (2444 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 49.7% (2497 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 50.7% (2548 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 51.6% (2589 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 52.4% (2633 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 53.3% (2676 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 54.1% (2717 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 55.1% (2765 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 55.9% (2806 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 57.1% (2870 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 58.0% (2915 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 59.3% (2980 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 60.3% (3027 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 61.2% (3072 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 62.3% (3127 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 63.0% (3163 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 64.0% (3216 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 65.2% (3274 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 66.3% (3330 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 67.4% (3386 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 68.1% (3421 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 69.2% (3475 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 70.2% (3523 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 71.2% (3576 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 72.2% (3625 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 73.3% (3681 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 74.3% (3730 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 75.2% (3778 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 76.2% (3825 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 77.3% (3884 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 78.2% (3929 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 79.2% (3979 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 80.3% (4033 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 81.3% (4081 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 82.5% (4143 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 83.4% (4187 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 84.4% (4238 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 85.4% (4290 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 86.5% (4343 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 87.3% (4386 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 88.4% (4440 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 89.4% (4488 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 90.5% (4545 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 91.4% (4592 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 92.4% (4642 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 93.5% (4695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 94.5% (4744 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 95.5% (4795 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 97.5% (4898 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.6; 598 / 5022 (P = 11.91%) round 6]               
[00:00:00] Finding cutoff p=983 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=983 1.7% (86 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 3.3% (164 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 4.8% (241 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 6.3% (314 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 7.6% (380 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 9.1% (459 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 10.7% (535 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 12.1% (607 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 13.7% (687 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 15.3% (767 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 16.8% (842 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 18.5% (927 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 20.1% (1011 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 22.2% (1117 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 24.0% (1205 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 25.1% (1263 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 26.3% (1319 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 27.7% (1390 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 28.9% (1452 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 31.0% (1555 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 32.3% (1623 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 33.5% (1681 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 34.8% (1747 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 36.0% (1808 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 37.2% (1866 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 38.4% (1929 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 39.5% (1982 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 40.4% (2027 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 41.3% (2074 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 42.4% (2128 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 43.3% (2175 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 44.3% (2225 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 45.4% (2279 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 46.5% (2333 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 47.3% (2376 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 48.3% (2427 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 49.3% (2477 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 50.4% (2529 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 51.2% (2569 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 52.0% (2610 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 53.2% (2673 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 54.1% (2718 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 54.9% (2758 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 56.1% (2819 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 57.0% (2861 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 58.3% (2926 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 59.2% (2973 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 59.9% (3010 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 61.3% (3079 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 62.1% (3118 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 63.3% (3179 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 64.1% (3221 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 65.2% (3274 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 66.3% (3330 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 67.0% (3366 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 68.2% (3424 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 69.3% (3481 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 70.1% (3520 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 71.4% (3586 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 72.3% (3633 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 73.3% (3683 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 74.4% (3734 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 75.3% (3782 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 76.3% (3831 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 77.7% (3902 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 78.2% (3929 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 79.2% (3978 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 80.4% (4039 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 81.4% (4086 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 82.3% (4135 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 83.5% (4193 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 84.3% (4235 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 85.4% (4289 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 86.5% (4343 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 87.4% (4391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 88.5% (4444 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 89.4% (4492 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 90.5% (4544 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 91.5% (4593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 92.5% (4643 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 93.5% (4694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 95.5% (4797 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 96.5% (4847 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.3; 789 / 5022 (P = 15.71%) round 7]               
[00:00:00] Finding cutoff p=980 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=980 1.8% (88 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 3.2% (160 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 4.6% (231 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 6.1% (304 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 7.5% (376 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 9.0% (450 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 10.4% (523 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 12.1% (606 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 13.6% (682 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 15.0% (755 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 16.6% (832 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 18.1% (910 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 45.3% (2277 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 46.8% (2349 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 47.9% (2405 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 48.9% (2456 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 49.9% (2504 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 51.2% (2572 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 52.0% (2612 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 52.8% (2654 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 54.1% (2718 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 55.0% (2761 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 56.3% (2826 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 57.1% (2870 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 57.9% (2909 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 59.2% (2975 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 60.2% (3023 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 61.0% (3064 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 62.3% (3131 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 63.0% (3163 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 64.2% (3222 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 65.3% (3279 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 66.0% (3315 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 67.1% (3370 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 68.3% (3428 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 69.4% (3485 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 70.2% (3525 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 71.3% (3580 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 72.3% (3632 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 73.3% (3683 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 74.3% (3732 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 75.3% (3780 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 76.3% (3833 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 77.3% (3880 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 78.2% (3929 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 79.3% (3983 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 80.4% (4037 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 81.3% (4085 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 82.3% (4132 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 83.3% (4183 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 84.4% (4238 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 85.5% (4292 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 86.4% (4338 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 87.5% (4392 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 88.5% (4444 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 89.5% (4494 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 90.5% (4545 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 91.5% (4595 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 92.4% (4642 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 93.5% (4694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 94.5% (4745 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 95.5% (4797 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 96.5% (4847 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.0; 993 / 5022 (P = 19.77%) round 8]               
[00:00:00] Finding cutoff p=976 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=976 1.8% (91 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 3.2% (161 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 4.6% (232 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 6.1% (308 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 7.6% (383 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 9.0% (453 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 10.5% (526 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 12.1% (610 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 13.7% (690 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 15.2% (763 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 16.9% (847 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 18.3% (921 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 19.8% (996 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 21.6% (1087 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 23.8% (1197 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 25.0% (1253 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 26.2% (1314 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 27.6% (1385 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 28.8% (1446 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 29.9% (1503 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 31.2% (1565 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 32.4% (1628 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 33.7% (1690 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 34.8% (1748 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 36.0% (1810 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 37.3% (1873 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 39.4% (1978 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 40.3% (2025 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 41.3% (2073 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 42.3% (2126 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 43.4% (2178 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 44.3% (2223 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 45.3% (2275 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 46.2% (2322 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 47.3% (2373 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 48.2% (2419 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 49.2% (2472 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 50.1% (2517 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 51.0% (2559 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 51.8% (2602 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 53.1% (2667 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 53.9% (2707 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 55.1% (2769 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 56.0% (2812 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 57.3% (2879 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 58.1% (2917 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 59.0% (2963 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 60.4% (3033 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 61.2% (3074 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 62.0% (3113 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 63.1% (3168 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 64.2% (3225 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 65.3% (3279 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 66.4% (3335 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 67.1% (3371 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 68.3% (3428 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 69.3% (3481 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 70.5% (3541 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 71.2% (3576 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 72.3% (3629 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 73.3% (3683 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 74.3% (3732 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 75.3% (3784 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 76.2% (3826 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 77.3% (3880 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 78.2% (3928 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 79.4% (3989 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 80.3% (4035 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 81.3% (4082 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 82.3% (4132 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 83.4% (4188 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 84.4% (4239 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 85.5% (4293 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 86.4% (4337 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 87.4% (4391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 88.4% (4437 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 90.1% (4526 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 90.5% (4543 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 91.5% (4593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 92.4% (4642 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 93.4% (4693 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 94.5% (4746 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 95.5% (4795 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.6; 1204 / 5022 (P = 23.97%) round 9]               
[00:00:00] Finding cutoff p=972 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=972 1.9% (97 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 3.4% (171 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 4.7% (236 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 6.1% (304 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 7.4% (370 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 8.9% (446 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 10.2% (513 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 11.8% (593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 13.3% (667 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 14.7% (739 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 16.2% (814 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 24.4% (1225 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 25.6% (1285 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 26.9% (1349 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 28.0% (1407 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 29.2% (1464 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 30.4% (1527 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 31.8% (1599 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 33.0% (1659 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 35.3% (1774 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 36.5% (1834 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 37.8% (1899 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 38.8% (1949 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 39.8% (1997 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 40.7% (2045 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 42.2% (2120 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 43.2% (2170 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 44.2% (2221 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 45.2% (2269 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 46.1% (2317 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 47.1% (2363 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 48.0% (2413 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 49.0% (2459 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 50.0% (2511 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 50.8% (2551 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 52.1% (2617 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 52.9% (2659 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 53.8% (2703 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 55.0% (2764 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 55.9% (2808 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 57.2% (2871 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 58.1% (2919 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 58.9% (2959 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 60.2% (3023 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 61.0% (3065 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 62.1% (3120 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 63.2% (3176 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 64.0% (3214 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 65.1% (3267 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 66.1% (3322 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 67.3% (3380 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 68.3% (3430 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 69.3% (3481 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 70.3% (3528 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 71.6% (3596 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 72.2% (3628 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 73.4% (3685 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 74.1% (3723 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 75.3% (3780 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 76.2% (3828 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 77.4% (3885 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 78.3% (3932 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 79.2% (3979 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 80.4% (4039 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 81.4% (4086 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 82.4% (4138 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 83.3% (4185 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 84.4% (4238 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 85.5% (4293 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 86.4% (4338 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 87.5% (4394 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 88.4% (4439 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 89.4% (4489 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 90.5% (4545 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 91.5% (4596 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 92.4% (4641 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 93.4% (4692 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 94.6% (4749 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 95.5% (4796 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 96.5% (4848 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.2; 1450 / 5022 (P = 28.87%) round 10]               
[00:00:00] Finding cutoff p=967 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=967 2.1% (104 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 3.4% (172 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 4.9% (247 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 6.1% (305 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 7.4% (372 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 8.8% (444 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 10.1% (509 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 11.5% (576 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 13.1% (659 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 14.6% (733 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 16.0% (803 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 17.6% (886 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 19.3% (968 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 21.2% (1065 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 23.2% (1165 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 24.7% (1239 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 26.5% (1332 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 28.1% (1410 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 29.3% (1471 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 30.5% (1532 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 31.8% (1595 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 32.9% (1654 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 34.0% (1709 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 35.1% (1765 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 36.3% (1823 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 37.5% (1885 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 38.7% (1946 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 39.8% (1997 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 40.6% (2040 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 42.1% (2115 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 43.0% (2161 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 44.0% (2212 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 45.1% (2265 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 46.0% (2312 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 47.0% (2362 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 48.0% (2413 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 48.9% (2458 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 50.2% (2519 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 50.9% (2558 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 52.2% (2619 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 53.0% (2663 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 54.3% (2727 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 55.1% (2765 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 55.9% (2806 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 57.3% (2876 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 58.1% (2920 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 59.0% (2963 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 60.4% (3034 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 61.2% (3074 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 62.3% (3128 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 63.0% (3163 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 64.0% (3214 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 65.2% (3273 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 66.3% (3329 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 67.0% (3367 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 68.1% (3421 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 69.5% (3492 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 70.7% (3550 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 71.3% (3579 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 72.2% (3625 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 73.3% (3680 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 74.2% (3725 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 75.1% (3774 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 76.3% (3831 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 77.4% (3887 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 78.3% (3931 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 79.4% (3988 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 80.3% (4035 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 81.4% (4086 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 82.3% (4134 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 83.4% (4187 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 84.4% (4240 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 85.4% (4288 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 86.5% (4342 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 87.4% (4391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 88.6% (4447 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 89.4% (4492 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 90.4% (4540 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 91.5% (4595 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 92.5% (4645 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 93.4% (4693 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 95.5% (4798 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 96.5% (4846 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.7; 1719 / 5022 (P = 34.23%) round 11]               
[00:00:00] Finding cutoff p=964 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=964 2.2% (108 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 3.6% (179 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 4.9% (247 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 6.3% (314 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 7.5% (378 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 8.9% (447 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 10.2% (514 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 11.6% (585 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 13.1% (659 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 15.4% (773 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 16.8% (843 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 18.3% (918 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 19.8% (995 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 21.9% (1100 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 23.7% (1192 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 25.1% (1262 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 26.2% (1318 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 27.9% (1399 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 29.1% (1459 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 30.1% (1514 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 31.2% (1568 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 32.5% (1634 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 33.6% (1688 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 34.7% (1743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 36.0% (1809 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 37.2% (1866 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 38.4% (1930 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 39.5% (1984 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 40.5% (2032 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 41.4% (2080 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 42.5% (2134 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 43.5% (2183 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 44.4% (2230 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 45.4% (2280 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 46.3% (2327 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 47.3% (2375 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 48.2% (2422 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 49.1% (2464 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 50.0% (2512 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 51.0% (2561 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 52.2% (2622 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 53.0% (2664 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 54.2% (2724 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 55.1% (2766 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 55.9% (2806 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 57.1% (2869 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 58.2% (2922 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 59.0% (2962 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 60.4% (3033 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 61.2% (3072 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 62.3% (3130 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 63.0% (3164 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 64.0% (3216 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 65.1% (3268 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 66.2% (3327 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 67.3% (3380 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 68.1% (3420 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 69.2% (3474 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 70.7% (3550 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 71.3% (3583 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 72.6% (3646 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 73.3% (3682 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 74.3% (3729 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 75.2% (3777 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 76.4% (3837 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 77.3% (3884 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 78.3% (3930 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 79.4% (3987 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 80.3% (4035 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 81.3% (4085 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 82.3% (4134 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 83.4% (4186 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 84.4% (4239 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 85.3% (4286 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 86.3% (4336 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 87.3% (4386 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 88.4% (4437 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 89.4% (4489 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 90.4% (4541 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 91.5% (4594 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 92.5% (4647 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 93.5% (4695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 94.5% (4744 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 95.5% (4795 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.4; 1876 / 5022 (P = 37.36%) round 12]               
[00:00:00] Finding cutoff p=960 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=960 2.1% (107 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 4.2% (209 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 5.5% (278 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 6.8% (343 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 8.0% (404 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 9.4% (471 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 10.6% (530 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 12.1% (607 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 13.5% (676 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 14.9% (748 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 16.5% (827 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 18.2% (912 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 19.6% (986 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 22.3% (1118 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 24.0% (1204 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 25.1% (1260 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 26.2% (1314 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 27.9% (1401 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 29.0% (1456 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 30.1% (1513 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 31.2% (1568 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 32.5% (1632 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 33.6% (1686 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 34.7% (1745 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 35.8% (1797 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 37.1% (1862 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 38.5% (1932 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 39.8% (1997 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 40.8% (2048 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 41.7% (2092 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 42.8% (2148 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 45.2% (2272 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 46.1% (2317 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 47.0% (2362 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 47.9% (2408 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 48.9% (2457 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 49.8% (2499 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 51.2% (2570 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 52.0% (2610 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 53.2% (2673 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 54.1% (2717 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 55.7% (2796 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 56.4% (2834 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 57.3% (2880 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 58.1% (2916 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 59.1% (2966 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 60.3% (3027 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 61.3% (3077 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 62.4% (3132 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 63.0% (3166 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 64.1% (3218 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 65.1% (3269 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 66.3% (3328 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 67.3% (3378 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 68.1% (3418 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 69.2% (3473 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 70.5% (3540 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 71.1% (3572 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 72.4% (3637 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 73.3% (3680 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 74.4% (3736 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 75.3% (3780 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 76.2% (3825 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 77.3% (3883 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 78.5% (3940 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 79.4% (3986 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 80.3% (4031 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 81.3% (4084 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 82.3% (4131 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 83.3% (4184 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 84.3% (4234 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 85.6% (4300 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 86.5% (4345 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 87.5% (4394 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 88.6% (4448 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 89.5% (4494 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 90.4% (4539 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 91.5% (4595 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 92.5% (4645 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 93.5% (4694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 95.5% (4798 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=960 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.0; 2083 / 5022 (P = 41.48%) round 13]               
[00:00:00] Finding cutoff p=955 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=955 2.2% (110 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 3.4% (171 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 4.7% (236 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 5.9% (298 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 7.2% (363 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 8.5% (427 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 9.6% (480 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 11.0% (550 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 12.5% (630 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 13.6% (685 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 15.3% (766 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 16.7% (841 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 18.1% (911 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 20.5% (1028 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 22.0% (1107 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 23.6% (1187 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 25.2% (1264 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 26.9% (1349 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 28.0% (1407 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 29.1% (1463 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 30.2% (1517 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 31.6% (1585 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 32.7% (1642 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 33.8% (1697 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 34.9% (1752 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 36.0% (1810 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 37.3% (1872 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 38.2% (1919 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 39.4% (1981 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 40.6% (2038 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 41.4% (2078 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 42.2% (2119 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 43.2% (2169 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 44.1% (2215 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 45.1% (2263 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 46.6% (2338 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 47.5% (2386 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 48.4% (2431 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 49.4% (2480 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 50.2% (2520 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 51.1% (2564 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 52.2% (2621 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 52.9% (2659 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 54.1% (2718 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 54.9% (2758 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 56.2% (2823 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 57.3% (2876 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 57.9% (2910 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 59.3% (2980 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 60.2% (3021 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 61.0% (3063 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 62.1% (3120 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 63.2% (3173 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 64.3% (3227 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 65.0% (3264 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 66.0% (3317 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 67.2% (3376 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 68.4% (3433 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 69.5% (3489 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 70.1% (3519 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 71.3% (3579 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 72.2% (3626 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 73.1% (3672 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 74.3% (3730 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 75.3% (3784 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 76.3% (3832 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 77.2% (3876 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 78.4% (3936 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 79.3% (3980 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 80.2% (4029 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 81.5% (4091 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 82.4% (4136 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 83.4% (4186 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 84.4% (4237 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 85.3% (4286 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 86.4% (4340 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 87.5% (4392 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 88.6% (4448 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 89.4% (4491 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 90.5% (4543 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 91.5% (4597 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 92.6% (4648 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 93.5% (4695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 94.5% (4744 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 95.5% (4796 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.5; 2320 / 5022 (P = 46.20%) round 14]               
[00:00:00] Finding cutoff p=951 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=951 2.2% (111 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 3.5% (176 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 4.7% (236 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 5.9% (296 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 7.2% (360 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 8.5% (428 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 9.6% (484 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 10.8% (541 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 12.1% (609 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 13.4% (672 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 14.7% (736 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 16.0% (805 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 17.5% (881 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 20.0% (1002 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 21.7% (1092 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 23.6% (1187 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 24.7% (1240 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 26.5% (1330 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 27.6% (1386 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 28.7% (1442 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 29.8% (1495 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 30.8% (1547 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 32.0% (1606 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 33.0% (1658 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 34.2% (1720 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 35.2% (1770 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 37.5% (1882 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 38.6% (1940 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 39.8% (1999 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 40.9% (2052 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 41.8% (2099 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 42.7% (2144 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 44.0% (2212 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 45.0% (2260 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 45.9% (2306 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 46.7% (2347 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 48.1% (2414 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 49.0% (2461 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 49.8% (2500 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 51.1% (2564 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 52.1% (2614 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 52.8% (2653 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 54.0% (2713 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 55.0% (2760 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 56.4% (2833 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 57.1% (2870 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 57.9% (2908 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 58.9% (2958 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 60.1% (3020 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 61.0% (3065 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 62.2% (3123 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 63.3% (3177 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 64.0% (3215 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 65.1% (3270 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 66.4% (3334 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 67.1% (3369 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 68.1% (3420 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 69.3% (3479 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 70.2% (3525 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 71.1% (3572 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 72.3% (3633 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 73.2% (3677 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 74.1% (3723 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 75.2% (3779 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 76.2% (3826 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 77.3% (3882 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 78.3% (3933 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 79.4% (3986 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 80.4% (4037 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 81.4% (4087 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 82.4% (4140 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 83.4% (4188 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 84.4% (4241 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 85.4% (4290 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 86.5% (4343 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 87.4% (4388 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 88.5% (4442 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 89.4% (4490 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 90.5% (4543 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 91.4% (4590 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 92.4% (4642 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 93.4% (4692 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 94.6% (4749 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 95.5% (4794 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 96.6% (4849 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.1; 2509 / 5022 (P = 49.96%) round 15]               
[00:00:00] Finding cutoff p=947 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=947 2.4% (119 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 3.6% (179 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 4.8% (243 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 6.0% (299 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 7.2% (363 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 8.4% (422 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 9.5% (476 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 10.7% (539 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 12.1% (609 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 13.4% (675 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 14.4% (723 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 15.9% (801 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 17.4% (873 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 20.0% (1004 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 21.8% (1097 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 23.6% (1184 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 24.7% (1241 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 26.4% (1324 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 28.2% (1416 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 29.3% (1469 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 30.2% (1519 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 31.5% (1583 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 32.7% (1642 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 33.7% (1692 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 34.9% (1751 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 35.8% (1798 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 37.1% (1865 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 38.0% (1910 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 39.2% (1971 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 40.2% (2017 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 41.1% (2066 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 42.0% (2107 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 42.7% (2145 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 44.1% (2214 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 45.0% (2262 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 45.9% (2305 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 46.8% (2350 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 48.1% (2418 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 49.0% (2461 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 49.9% (2504 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 51.2% (2572 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 51.9% (2608 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 53.0% (2664 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 53.9% (2707 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 55.5% (2788 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 56.2% (2821 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 57.0% (2863 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 57.9% (2910 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 59.0% (2964 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 60.3% (3030 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 61.1% (3070 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 62.2% (3124 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 63.0% (3163 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 64.0% (3214 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 65.3% (3278 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 66.0% (3315 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 67.0% (3367 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 68.4% (3433 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 69.2% (3475 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 72.4% (3637 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 73.3% (3681 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 74.2% (3727 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 75.4% (3789 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 76.4% (3839 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 77.4% (3887 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 78.3% (3934 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 79.5% (3990 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 80.2% (4029 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 81.4% (4088 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 82.4% (4138 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 83.5% (4193 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 84.5% (4243 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 85.5% (4292 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 86.4% (4337 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 87.4% (4391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 88.5% (4442 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 89.4% (4488 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 90.4% (4540 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 91.5% (4594 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 92.4% (4642 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 93.5% (4694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 94.5% (4745 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 95.5% (4795 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.7; 2679 / 5022 (P = 53.35%) round 16]               
[00:00:00] Finding cutoff p=943 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=943 2.2% (112 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 3.4% (173 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 4.6% (232 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 5.9% (296 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 6.9% (348 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 8.0% (403 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 9.2% (460 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 10.3% (516 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 11.5% (579 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 12.9% (647 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 13.9% (699 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 16.2% (812 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 17.7% (890 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 20.4% (1022 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 22.1% (1111 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 23.8% (1195 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 24.8% (1245 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 26.5% (1331 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 27.5% (1383 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 28.6% (1438 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 29.6% (1489 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 30.9% (1550 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 32.0% (1605 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 33.0% (1657 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 34.2% (1718 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 35.2% (1767 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 36.5% (1834 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 37.4% (1879 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 38.5% (1933 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 39.8% (1998 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 40.9% (2055 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 42.1% (2112 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 42.9% (2152 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 43.8% (2199 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 45.2% (2272 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 46.2% (2320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 47.2% (2369 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 48.0% (2411 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 48.8% (2449 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 50.0% (2510 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 50.9% (2554 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 52.1% (2618 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 53.0% (2662 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 53.9% (2708 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 55.4% (2782 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 56.2% (2820 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 56.9% (2857 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 58.1% (2918 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 59.0% (2963 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 60.2% (3023 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 61.0% (3061 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 62.1% (3121 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 63.2% (3173 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 64.2% (3223 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 65.5% (3288 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 66.1% (3322 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 67.3% (3379 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 68.6% (3444 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 69.1% (3471 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 70.5% (3538 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 71.3% (3583 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 72.2% (3627 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 73.3% (3681 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 74.5% (3740 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 75.3% (3784 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 76.3% (3833 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 77.2% (3876 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 78.2% (3929 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 79.3% (3984 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 80.2% (4030 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 81.4% (4087 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 82.4% (4138 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 83.8% (4208 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 84.3% (4236 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 85.4% (4289 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 86.3% (4336 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 87.5% (4392 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 88.4% (4437 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 89.4% (4492 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 90.5% (4544 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 91.4% (4590 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 92.5% (4645 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 93.4% (4693 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 95.5% (4798 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 96.5% (4846 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.3; 2812 / 5022 (P = 55.99%) round 17]               
[00:00:00] Finding cutoff p=940 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=940 2.3% (115 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 3.4% (171 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 5.2% (262 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 6.4% (320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 7.4% (370 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 8.6% (431 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 9.5% (476 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 10.7% (537 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 12.0% (603 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 13.2% (661 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 14.3% (720 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 15.8% (795 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 17.3% (867 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 19.8% (996 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 21.7% (1089 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 23.2% (1163 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 24.5% (1230 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 26.2% (1318 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 27.4% (1374 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 28.5% (1429 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 29.5% (1482 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 30.5% (1532 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 31.6% (1585 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 32.5% (1634 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 33.6% (1686 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 34.6% (1740 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 35.9% (1803 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 36.9% (1852 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 37.9% (1901 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 39.2% (1969 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 40.3% (2022 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 41.1% (2062 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 41.9% (2102 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 43.0% (2161 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 43.9% (2205 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 44.7% (2247 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 46.2% (2320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 46.9% (2355 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 47.8% (2400 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 49.1% (2468 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 50.2% (2521 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 51.0% (2560 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 52.6% (2643 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 53.8% (2701 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 55.0% (2762 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 56.1% (2816 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 57.0% (2865 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 58.2% (2924 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 58.9% (2959 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 60.1% (3018 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 61.2% (3075 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 62.3% (3128 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 63.3% (3178 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 64.6% (3242 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 65.2% (3272 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 66.3% (3332 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 67.7% (3398 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 68.2% (3426 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 69.5% (3491 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 70.5% (3540 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 71.2% (3577 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 72.4% (3635 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 73.5% (3693 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 74.4% (3735 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 75.4% (3785 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 76.3% (3832 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 77.2% (3877 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 78.2% (3927 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 79.4% (3987 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 80.3% (4032 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 81.3% (4081 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 82.4% (4138 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 83.4% (4187 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 84.5% (4245 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 85.6% (4299 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 86.5% (4343 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 87.6% (4401 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 88.5% (4443 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 89.4% (4489 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 90.4% (4541 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 91.5% (4595 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 92.6% (4648 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 93.5% (4694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 95.5% (4798 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 96.5% (4848 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.0; 2912 / 5022 (P = 57.98%) round 18]               
[00:00:00] Finding cutoff p=936 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=936 2.3% (113 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 3.6% (180 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 4.6% (230 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 5.7% (284 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 6.8% (340 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 7.9% (395 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 8.8% (440 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 9.8% (494 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 11.1% (557 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 12.3% (620 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 13.3% (669 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 14.8% (743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 16.2% (814 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 18.8% (943 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 20.3% (1020 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 21.6% (1085 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 23.1% (1161 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 25.2% (1267 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 26.4% (1326 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 28.0% (1405 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 29.2% (1468 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 30.1% (1513 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 31.1% (1562 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 32.2% (1616 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 33.3% (1672 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 34.7% (1745 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 35.6% (1790 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 37.3% (1874 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 38.6% (1936 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 39.6% (1989 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 40.9% (2054 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 41.7% (2094 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 42.7% (2146 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 44.1% (2215 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 45.4% (2281 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 46.3% (2325 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 47.2% (2368 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 48.0% (2410 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 48.8% (2451 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 50.0% (2512 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 50.8% (2550 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 52.4% (2630 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 53.6% (2690 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 54.4% (2730 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 55.0% (2764 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 56.1% (2817 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 57.2% (2874 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 57.9% (2909 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 59.0% (2963 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 60.1% (3018 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 61.0% (3065 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 62.2% (3122 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 63.8% (3203 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 64.4% (3233 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 65.1% (3268 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 67.0% (3366 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 68.9% (3458 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 69.7% (3502 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 70.3% (3530 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 71.6% (3594 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 72.1% (3621 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 73.4% (3686 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 74.2% (3726 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 75.2% (3777 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 76.3% (3834 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 77.2% (3877 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 78.3% (3934 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 79.3% (3983 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 80.3% (4033 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 81.3% (4085 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 82.3% (4133 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 83.3% (4183 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 84.5% (4243 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 85.4% (4287 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 86.3% (4335 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 87.4% (4391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 88.4% (4437 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 89.4% (4491 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 90.4% (4542 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 91.5% (4593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 92.5% (4644 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 93.5% (4696 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 94.5% (4748 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 95.5% (4795 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 96.5% (4847 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=936 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.6; 3034 / 5022 (P = 60.41%) round 19]               
[00:00:00] Finding cutoff p=932 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=932 2.4% (120 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 3.5% (177 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 4.5% (225 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 5.6% (283 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 6.6% (333 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 7.7% (387 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 8.6% (432 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 9.4% (473 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 10.6% (534 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 12.0% (601 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 12.9% (648 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 14.4% (723 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 15.8% (795 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 18.3% (921 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 20.0% (1006 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 21.5% (1079 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 22.9% (1149 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 25.2% (1264 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 26.3% (1320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 27.1% (1363 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 28.0% (1405 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 29.1% (1460 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 30.0% (1506 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 31.0% (1558 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 32.1% (1613 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 33.1% (1661 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 35.1% (1762 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 36.2% (1820 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 37.1% (1865 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 38.4% (1929 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 39.6% (1991 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 40.9% (2054 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 41.7% (2092 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 42.8% (2149 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 43.7% (2196 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 44.9% (2255 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 45.8% (2300 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 47.0% (2358 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 47.8% (2402 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 49.2% (2470 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 49.8% (2502 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 51.3% (2575 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 52.3% (2624 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 53.5% (2687 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 54.2% (2721 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 54.9% (2756 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 55.9% (2808 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 57.2% (2872 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 58.1% (2917 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 59.0% (2963 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 60.4% (3033 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 61.2% (3071 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 62.3% (3127 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 63.5% (3190 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 64.3% (3228 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 65.4% (3282 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 66.9% (3361 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 67.5% (3390 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 69.0% (3463 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 69.8% (3506 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 70.4% (3534 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 71.2% (3576 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 72.4% (3634 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 73.3% (3683 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 74.2% (3728 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 75.2% (3779 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 76.3% (3834 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 77.4% (3885 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 78.3% (3933 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 79.3% (3982 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 80.3% (4035 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 81.6% (4097 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 82.4% (4136 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 83.4% (4187 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 84.5% (4243 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 85.7% (4302 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 86.6% (4348 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 87.4% (4389 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 88.4% (4441 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 89.5% (4496 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 90.5% (4544 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 91.4% (4592 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 92.5% (4645 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 93.4% (4693 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 94.6% (4749 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 95.5% (4794 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 96.6% (4849 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.2; 3161 / 5022 (P = 62.94%) round 20]               
[00:00:00] Finding cutoff p=928 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=928 2.4% (122 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 3.4% (169 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 4.3% (217 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 5.4% (270 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 6.2% (311 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 7.4% (371 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 8.3% (415 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 9.4% (470 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 10.5% (529 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 11.7% (589 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 12.8% (643 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 14.4% (721 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 15.8% (794 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 18.0% (905 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 19.5% (978 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 20.9% (1048 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 22.7% (1140 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 25.3% (1270 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 26.3% (1320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 27.2% (1368 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 28.1% (1411 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 29.3% (1469 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 30.1% (1510 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 31.0% (1555 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 32.1% (1610 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 33.1% (1660 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 34.3% (1721 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 35.2% (1770 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 36.0% (1807 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 37.2% (1867 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 38.3% (1924 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 39.2% (1971 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 40.1% (2016 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 41.0% (2060 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 41.7% (2096 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 42.9% (2154 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 43.8% (2199 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 45.1% (2265 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 45.7% (2296 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 46.9% (2357 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 48.1% (2418 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 49.0% (2460 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 50.5% (2534 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 51.3% (2577 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 52.8% (2651 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 53.5% (2686 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 54.1% (2718 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 55.1% (2769 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 55.9% (2806 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 57.1% (2868 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 57.9% (2910 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 59.0% (2963 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 60.0% (3012 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 61.1% (3070 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 62.8% (3154 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 63.5% (3191 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 64.2% (3225 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 65.4% (3284 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 66.9% (3362 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 67.5% (3390 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 68.1% (3418 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 69.2% (3473 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 70.4% (3534 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 71.2% (3577 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 72.3% (3629 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 73.3% (3682 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 74.1% (3723 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 75.3% (3782 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 76.5% (3841 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 77.2% (3876 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 78.3% (3933 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 79.4% (3985 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 80.3% (4034 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 81.5% (4095 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 82.4% (4137 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 83.4% (4187 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 84.4% (4241 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 85.7% (4302 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 86.5% (4344 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 87.4% (4391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 88.4% (4438 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 89.4% (4492 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 90.4% (4541 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 91.4% (4590 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 92.4% (4641 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 93.5% (4695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 94.5% (4744 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 95.5% (4798 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 96.5% (4848 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 97.5% (4898 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.8; 3277 / 5022 (P = 65.25%) round 21]               
[00:00:00] Finding cutoff p=925 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=925 2.4% (121 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 3.5% (177 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 4.6% (229 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 5.7% (285 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 7.2% (364 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 8.4% (422 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 9.2% (462 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 10.4% (524 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 11.9% (598 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 12.9% (649 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 13.8% (694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 15.2% (762 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 16.5% (827 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 19.2% (962 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 21.0% (1053 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 24.0% (1207 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 25.0% (1255 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 26.8% (1348 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 28.0% (1405 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 29.0% (1454 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 29.7% (1493 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 30.9% (1554 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 31.9% (1601 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 32.7% (1640 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 33.9% (1704 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 34.7% (1744 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 36.2% (1816 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 36.9% (1852 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 37.7% (1893 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 39.0% (1959 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 40.2% (2019 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 40.9% (2055 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 41.6% (2091 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 42.7% (2143 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 43.9% (2205 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 44.8% (2251 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 45.9% (2307 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 47.1% (2367 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 47.9% (2405 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 49.0% (2460 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 50.1% (2514 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 51.3% (2574 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 51.9% (2604 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 53.4% (2682 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 54.0% (2713 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 55.3% (2778 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 56.5% (2835 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 57.1% (2867 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 58.1% (2920 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 59.0% (2962 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 60.0% (3012 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 61.0% (3065 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 62.2% (3123 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 63.7% (3200 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 64.4% (3236 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 65.2% (3274 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 67.1% (3370 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 68.8% (3454 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 69.7% (3499 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 70.2% (3525 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 71.5% (3593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 72.8% (3658 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 73.4% (3687 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 74.3% (3733 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 75.2% (3776 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 76.7% (3850 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 77.2% (3876 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 78.6% (3948 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 79.3% (3982 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 80.6% (4046 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 81.4% (4089 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 82.7% (4155 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 83.8% (4206 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 84.6% (4251 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 85.3% (4286 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 86.4% (4338 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 87.5% (4395 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 88.5% (4442 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 89.4% (4491 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 90.4% (4541 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 91.4% (4592 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 92.4% (4642 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 93.4% (4693 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 94.5% (4746 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 95.5% (4798 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 96.5% (4846 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.5; 3365 / 5022 (P = 67.01%) round 22]               
[00:00:00] Finding cutoff p=922 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=922 2.4% (122 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 3.5% (175 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 4.3% (217 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 5.4% (271 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 6.2% (312 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 7.6% (382 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 8.4% (423 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 9.6% (481 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 10.6% (532 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 11.8% (592 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 13.3% (668 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 14.5% (729 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 17.2% (865 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 18.7% (940 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 19.8% (995 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 21.3% (1070 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 24.0% (1205 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 25.1% (1262 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 26.0% (1308 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 26.9% (1351 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 28.2% (1415 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 28.9% (1450 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 29.8% (1495 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 30.8% (1545 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 31.5% (1583 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 33.0% (1659 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 34.0% (1709 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 35.0% (1757 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 36.2% (1816 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 37.3% (1872 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 38.1% (1912 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 39.0% (1958 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 40.0% (2008 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 40.9% (2055 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 41.8% (2101 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 43.0% (2158 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 43.8% (2199 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 45.1% (2265 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 46.0% (2311 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 46.8% (2351 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 48.2% (2421 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 48.9% (2458 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 49.9% (2508 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 51.5% (2586 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 52.1% (2618 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 52.9% (2657 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 54.1% (2718 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 55.3% (2778 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 56.0% (2812 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 57.0% (2862 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 57.9% (2909 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 59.2% (2975 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 60.0% (3015 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 61.4% (3082 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 62.0% (3115 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 63.2% (3174 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 64.6% (3243 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 65.2% (3275 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 66.9% (3359 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 68.0% (3416 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 68.6% (3443 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 69.1% (3471 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 70.3% (3531 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 71.4% (3588 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 72.2% (3626 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 73.2% (3676 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 74.5% (3741 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 75.5% (3791 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 76.4% (3835 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 77.5% (3892 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 78.2% (3927 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 79.4% (3987 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 80.3% (4032 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 81.7% (4103 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 82.3% (4133 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 83.4% (4190 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 84.3% (4233 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 85.5% (4293 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 86.8% (4358 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 87.4% (4387 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 88.4% (4438 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 89.4% (4491 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 90.6% (4549 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 91.6% (4599 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 92.4% (4642 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 93.5% (4698 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 94.5% (4748 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 95.5% (4795 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 96.5% (4847 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.2; 3420 / 5022 (P = 68.10%) round 23]               
[00:00:00] Finding cutoff p=919 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=919 2.4% (120 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 3.5% (176 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 4.3% (216 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 5.5% (274 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 6.5% (327 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 7.2% (361 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 8.2% (410 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 9.6% (484 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 10.4% (522 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 11.6% (584 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 13.2% (664 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 14.2% (714 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 16.8% (844 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 18.3% (917 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 19.3% (967 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 20.7% (1041 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 23.6% (1183 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 24.4% (1226 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 25.8% (1295 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 27.0% (1355 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 27.9% (1400 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 28.6% (1438 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 29.6% (1486 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 30.5% (1533 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 31.9% (1601 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 33.5% (1682 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 34.4% (1727 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 35.5% (1785 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 37.0% (1856 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 37.7% (1894 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 38.6% (1938 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 39.6% (1991 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 40.9% (2053 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 41.8% (2100 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 43.2% (2168 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 44.0% (2209 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 44.9% (2255 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 45.8% (2298 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 47.3% (2375 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 48.2% (2419 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 49.6% (2490 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 50.4% (2529 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 50.9% (2556 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 51.8% (2602 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 53.3% (2676 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 54.1% (2716 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 55.2% (2770 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 66.6% (3343 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 67.6% (3395 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 68.2% (3424 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 69.6% (3496 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 70.2% (3524 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 72.2% (3624 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 73.8% (3706 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 74.6% (3744 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 75.3% (3781 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 76.2% (3827 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 77.2% (3876 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 78.3% (3932 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 79.3% (3980 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 80.4% (4036 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 81.3% (4083 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 82.3% (4131 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 83.4% (4186 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 84.4% (4238 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 85.9% (4312 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 86.4% (4337 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 87.4% (4391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 88.5% (4442 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 89.6% (4499 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 90.6% (4549 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 91.5% (4593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 92.5% (4643 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 93.5% (4695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 94.5% (4748 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 95.5% (4795 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 96.5% (4846 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.9; 3495 / 5022 (P = 69.59%) round 24]               
[00:00:00] Finding cutoff p=916 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=916 2.3% (114 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 3.2% (159 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 4.5% (226 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 5.6% (279 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 6.5% (325 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 7.2% (364 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 8.2% (412 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 9.3% (468 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 10.3% (516 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 11.4% (571 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 12.7% (640 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 14.2% (711 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 16.6% (835 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 18.1% (908 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 19.2% (966 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 20.6% (1034 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 23.7% (1190 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 25.0% (1255 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 25.8% (1297 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 26.6% (1334 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 27.7% (1393 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 28.6% (1435 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 29.8% (1497 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 30.6% (1538 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 32.1% (1614 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 33.5% (1681 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 34.4% (1727 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 35.9% (1803 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 37.0% (1857 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 37.8% (1897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 38.9% (1956 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 39.7% (1996 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 40.7% (2042 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 41.7% (2092 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 42.9% (2154 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 43.9% (2206 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 44.7% (2247 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 46.0% (2312 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 47.5% (2383 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 48.1% (2414 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 49.3% (2477 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 49.9% (2504 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 51.5% (2584 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 52.2% (2623 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 53.1% (2669 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 54.2% (2721 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 54.9% (2757 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 55.9% (2808 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 56.9% (2857 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 58.1% (2916 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 59.3% (2979 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 60.0% (3013 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 61.1% (3070 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 62.7% (3151 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 63.4% (3184 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 64.0% (3216 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 65.6% (3293 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 66.9% (3358 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 67.4% (3387 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 68.1% (3422 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 69.2% (3475 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 70.2% (3524 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 71.1% (3571 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 72.1% (3621 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 73.1% (3672 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 74.5% (3741 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 75.2% (3779 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 76.2% (3827 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 77.6% (3898 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 78.3% (3933 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 79.4% (3987 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 80.3% (4034 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 81.8% (4106 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 82.3% (4133 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 83.3% (4182 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 84.4% (4237 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 85.3% (4285 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 86.6% (4350 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 87.3% (4386 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 88.5% (4444 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 89.4% (4491 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 90.5% (4544 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 91.5% (4597 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 92.5% (4647 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 93.4% (4693 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 94.5% (4748 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 95.5% (4798 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 96.5% (4848 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.6; 3557 / 5022 (P = 70.83%) round 25]               
[00:00:00] Finding cutoff p=911 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=911 2.3% (115 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 3.1% (158 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 4.6% (231 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 5.7% (284 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 6.5% (328 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 7.2% (363 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 8.7% (439 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 9.5% (475 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 10.5% (527 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 12.2% (612 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 13.3% (668 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 15.9% (797 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 17.3% (871 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 18.7% (940 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 22.8% (1145 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 23.6% (1186 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 24.7% (1242 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 25.7% (1293 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 26.6% (1334 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 27.7% (1391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 28.5% (1431 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 29.9% (1502 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 30.6% (1538 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 32.5% (1630 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 33.7% (1691 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 35.0% (1757 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 35.9% (1805 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 36.6% (1838 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 37.9% (1903 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 38.7% (1945 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 39.8% (1997 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 40.7% (2043 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 41.8% (2100 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 42.7% (2144 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 44.4% (2231 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 45.2% (2270 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 46.4% (2331 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 47.0% (2358 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 48.4% (2429 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 48.9% (2457 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 49.8% (2501 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 50.8% (2551 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 51.8% (2601 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 53.4% (2682 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 54.0% (2714 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 54.9% (2757 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 56.6% (2842 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 57.1% (2869 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 58.1% (2920 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 59.9% (3009 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 61.9% (3111 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 63.2% (3173 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 64.4% (3234 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 65.6% (3294 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 66.2% (3324 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 68.2% (3424 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 69.2% (3475 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 70.3% (3529 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 71.4% (3584 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 72.2% (3626 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 73.2% (3676 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 74.2% (3725 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 75.4% (3788 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 76.3% (3830 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 77.3% (3884 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 78.3% (3930 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 79.4% (3989 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 80.7% (4054 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 81.2% (4080 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 82.3% (4134 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 83.3% (4182 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 84.6% (4247 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 85.4% (4289 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 86.4% (4338 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 87.5% (4396 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 88.4% (4438 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 89.5% (4495 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 90.4% (4539 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 91.4% (4590 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 92.5% (4644 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 93.4% (4693 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 95.6% (4803 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 96.6% (4853 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.1; 3659 / 5022 (P = 72.86%) round 26]               
[00:00:00] Finding cutoff p=907 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=907 2.3% (116 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 3.5% (175 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 4.4% (221 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 5.3% (267 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 6.2% (310 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 7.3% (367 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 8.4% (420 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 9.5% (477 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 10.4% (521 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 11.8% (593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 12.7% (638 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 15.2% (764 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 16.5% (831 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 17.5% (877 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 20.6% (1037 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 22.0% (1106 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 23.2% (1165 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 24.6% (1234 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 25.5% (1280 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 26.6% (1337 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 27.4% (1378 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 29.1% (1459 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 29.6% (1487 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 31.4% (1579 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 32.8% (1647 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 33.6% (1689 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 34.5% (1734 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 35.8% (1798 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 36.8% (1849 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 37.6% (1889 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 38.6% (1941 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 39.9% (2003 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 40.7% (2043 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 41.7% (2095 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 42.8% (2150 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 44.1% (2217 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 44.7% (2247 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 46.1% (2317 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 46.9% (2357 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 48.1% (2416 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 48.8% (2450 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 49.9% (2508 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 50.9% (2556 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 51.9% (2604 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 53.1% (2665 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 53.9% (2706 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 54.8% (2754 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 56.2% (2824 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 57.1% (2866 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 57.9% (2909 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 59.8% (3002 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 60.3% (3030 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 61.0% (3061 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 62.3% (3130 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 63.4% (3186 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 64.2% (3224 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 65.2% (3273 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 66.4% (3336 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 67.0% (3366 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 68.1% (3422 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 69.1% (3468 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 70.1% (3521 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 71.5% (3591 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 72.3% (3631 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 73.2% (3675 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 76.0% (3819 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 76.5% (3840 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 77.6% (3896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 78.2% (3929 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 79.2% (3979 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 80.4% (4038 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 81.4% (4087 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 82.3% (4132 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 83.5% (4194 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 84.4% (4239 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 85.6% (4300 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 86.4% (4338 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 87.4% (4388 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 88.4% (4441 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 89.4% (4490 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 90.4% (4542 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 91.5% (4593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 92.5% (4645 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 93.5% (4695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 94.5% (4746 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 95.6% (4799 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 96.5% (4848 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 97.6% (4899 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=907 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.7; 3717 / 5022 (P = 74.01%) round 27]               
[00:00:00] Finding cutoff p=903 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=903 2.3% (114 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 3.1% (156 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 4.3% (218 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 5.4% (269 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 6.1% (308 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 7.3% (365 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 8.1% (408 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 9.3% (469 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 10.6% (530 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 12.0% (605 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 14.2% (712 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 15.6% (781 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 16.6% (835 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 17.5% (879 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 20.0% (1005 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 21.9% (1101 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 23.1% (1160 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 23.8% (1194 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 25.0% (1254 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 25.7% (1293 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 26.6% (1335 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 27.7% (1391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 29.1% (1459 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 29.8% (1499 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 30.6% (1537 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 31.8% (1599 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 33.0% (1659 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 33.9% (1702 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 34.6% (1737 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 35.9% (1804 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 36.7% (1844 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 37.7% (1895 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 39.1% (1963 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 39.8% (1997 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 40.8% (2047 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 41.6% (2091 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 42.9% (2152 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 43.9% (2205 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 45.1% (2264 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 46.5% (2337 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 47.1% (2363 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 48.5% (2436 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 49.1% (2467 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 49.9% (2506 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 50.8% (2550 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 52.1% (2617 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 53.3% (2677 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 54.1% (2718 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 54.8% (2754 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 56.2% (2822 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 56.9% (2856 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 57.9% (2908 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 59.7% (2999 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 60.2% (3024 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 61.9% (3109 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 63.0% (3166 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 64.3% (3230 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 65.6% (3292 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 66.9% (3361 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 67.6% (3396 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 68.1% (3419 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 69.2% (3476 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 70.3% (3530 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 71.1% (3573 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 72.2% (3628 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 73.1% (3673 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 74.2% (3724 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 75.2% (3776 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 76.4% (3837 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 77.5% (3891 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 78.3% (3933 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 79.3% (3982 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 80.3% (4035 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 81.4% (4087 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 82.3% (4132 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 83.4% (4190 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 84.4% (4241 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 85.4% (4289 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 86.3% (4336 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 87.6% (4401 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 88.4% (4440 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 89.5% (4494 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 90.4% (4540 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 91.5% (4594 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 92.6% (4651 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 93.5% (4696 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 94.6% (4752 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 95.6% (4800 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.3; 3778 / 5022 (P = 75.23%) round 28]               
[00:00:00] Finding cutoff p=899 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=899 2.3% (114 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 3.5% (174 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 4.2% (213 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 5.3% (268 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 6.1% (306 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 7.1% (359 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 8.1% (409 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 9.3% (465 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 10.5% (526 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 11.9% (598 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 14.0% (703 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 15.3% (770 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 16.4% (825 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 17.5% (877 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 20.5% (1029 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 22.1% (1108 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 22.6% (1133 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 24.1% (1211 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 25.0% (1254 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 25.5% (1281 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 26.6% (1337 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 27.7% (1392 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 28.8% (1444 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 29.9% (1502 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 31.1% (1563 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 31.9% (1600 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 33.0% (1657 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 33.6% (1687 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 34.8% (1747 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 35.6% (1788 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 36.7% (1841 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 37.8% (1899 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 38.8% (1950 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 39.8% (1999 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 40.7% (2042 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 42.3% (2124 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 43.1% (2163 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 44.4% (2229 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 45.0% (2260 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 45.7% (2295 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 46.8% (2350 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 47.8% (2402 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 49.1% (2467 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 50.0% (2511 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 51.1% (2564 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 52.1% (2614 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 53.6% (2690 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 54.2% (2721 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 55.2% (2774 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 55.9% (2809 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 57.6% (2893 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 58.0% (2914 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 59.7% (2997 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 60.9% (3058 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 61.5% (3088 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 62.1% (3121 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 63.6% (3192 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 64.0% (3216 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 65.7% (3301 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 66.3% (3332 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 67.0% (3367 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 68.1% (3421 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 69.4% (3483 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 70.5% (3540 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 71.1% (3570 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 72.1% (3622 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 73.2% (3674 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 74.4% (3734 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 75.1% (3774 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 76.2% (3826 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 77.2% (3876 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 78.7% (3951 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 79.3% (3981 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 80.3% (4034 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 81.5% (4094 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 82.6% (4147 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 83.4% (4189 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 84.4% (4241 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 85.5% (4295 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 86.4% (4337 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 87.5% (4396 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 88.4% (4437 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 89.4% (4491 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 90.5% (4547 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 91.4% (4591 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 92.6% (4649 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 93.5% (4694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 94.5% (4745 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 95.5% (4796 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 96.6% (4849 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.9; 3832 / 5022 (P = 76.30%) round 29]               
[00:00:00] Finding cutoff p=890 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=890 2.2% (111 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 3.0% (153 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 4.2% (209 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 5.1% (258 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 6.4% (320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 7.1% (357 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 8.2% (412 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 9.2% (462 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 10.6% (532 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 12.3% (620 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 14.7% (738 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 16.1% (809 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 16.8% (844 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 18.4% (923 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 21.1% (1061 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 22.3% (1121 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 23.3% (1169 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 23.9% (1199 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 25.2% (1267 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 26.0% (1304 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 26.5% (1333 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 27.5% (1382 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 28.7% (1443 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 29.9% (1500 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 31.3% (1571 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 32.7% (1644 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 33.6% (1689 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 34.8% (1747 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 36.0% (1806 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 37.1% (1863 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 37.8% (1897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 38.6% (1938 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 39.9% (2005 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 40.7% (2043 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 41.7% (2095 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 43.5% (2185 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 44.3% (2227 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 45.8% (2298 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 47.5% (2387 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 48.0% (2411 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 49.0% (2463 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 50.0% (2509 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 50.9% (2557 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 52.3% (2629 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 53.1% (2669 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 53.9% (2709 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 55.4% (2782 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 56.1% (2816 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 57.0% (2864 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 59.0% (2963 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 60.9% (3060 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 62.0% (3116 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 63.2% (3172 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 64.4% (3234 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 65.5% (3288 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 67.0% (3363 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 67.7% (3402 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 68.1% (3419 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 69.1% (3469 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 70.4% (3533 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 71.2% (3575 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 72.3% (3629 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 73.2% (3676 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 74.2% (3728 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 75.2% (3779 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 76.3% (3834 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 77.7% (3900 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 78.3% (3932 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 79.3% (3982 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 80.3% (4035 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 81.4% (4087 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 82.5% (4141 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 83.3% (4182 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 84.3% (4235 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 87.0% (4368 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 87.4% (4390 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 88.5% (4442 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 89.5% (4495 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 90.4% (4541 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 91.4% (4592 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 92.4% (4642 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 93.5% (4696 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 94.5% (4745 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 95.5% (4797 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 96.5% (4846 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=890 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.0; 3885 / 5022 (P = 77.36%) round 30]               
[00:00:00] Finding cutoff p=880 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=880 2.2% (109 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 3.4% (169 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 4.1% (207 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 5.5% (277 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 6.4% (320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 7.3% (365 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 8.3% (417 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 9.5% (478 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 10.7% (539 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 12.3% (617 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 14.7% (739 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 16.4% (823 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 17.7% (891 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 19.9% (998 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 21.8% (1097 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 23.0% (1153 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 23.7% (1189 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 24.9% (1252 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 25.7% (1289 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 27.0% (1354 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 28.3% (1422 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 28.8% (1444 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 29.5% (1483 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 30.6% (1539 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 31.7% (1594 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 32.9% (1650 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 33.8% (1696 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 34.9% (1752 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 35.7% (1795 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 36.8% (1850 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 37.8% (1898 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 38.7% (1946 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 40.0% (2009 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 40.9% (2054 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 42.5% (2133 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 43.2% (2172 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 44.7% (2245 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 46.6% (2339 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 47.1% (2365 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 48.0% (2409 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 49.0% (2460 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 49.9% (2506 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 51.2% (2571 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 51.9% (2607 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 53.8% (2701 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 54.5% (2738 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 55.9% (2807 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 57.7% (2896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 58.1% (2920 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 60.1% (3020 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 61.2% (3075 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 62.2% (3122 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 63.5% (3188 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 64.2% (3223 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 65.6% (3295 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 66.2% (3326 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 67.2% (3377 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 68.3% (3429 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 69.4% (3484 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 70.2% (3526 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 71.3% (3582 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 72.1% (3622 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 73.3% (3680 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 74.2% (3727 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 75.3% (3782 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 76.6% (3847 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 77.2% (3879 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 78.5% (3942 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 79.5% (3993 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 80.4% (4039 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 81.2% (4080 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 82.4% (4138 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 83.7% (4201 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 84.4% (4239 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 85.4% (4287 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 86.7% (4353 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 87.4% (4390 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 88.4% (4441 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 89.5% (4493 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 90.6% (4551 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 91.5% (4595 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 92.5% (4644 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 93.4% (4692 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 94.5% (4747 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 95.5% (4797 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 96.6% (4850 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 97.9% (4917 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=880 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 88.0; 4001 / 5022 (P = 79.67%) round 31]               
[00:00:00] Finding cutoff p=869 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=869 2.1% (106 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 3.4% (171 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 4.2% (211 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 5.3% (265 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 6.1% (308 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 7.4% (374 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 8.6% (430 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 9.7% (489 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 10.8% (543 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 13.4% (672 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 14.5% (730 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 15.4% (774 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 16.5% (829 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 18.8% (946 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 20.2% (1013 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 21.5% (1080 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 22.7% (1138 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 23.7% (1192 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 24.6% (1237 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 25.8% (1296 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 26.5% (1331 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 27.9% (1400 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 28.4% (1428 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 30.0% (1505 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 31.3% (1571 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 32.1% (1611 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 32.7% (1644 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 33.5% (1683 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 34.8% (1748 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 35.8% (1800 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 37.0% (1858 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 37.6% (1889 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 38.8% (1948 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 39.9% (2004 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 41.6% (2088 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 42.4% (2129 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 43.8% (2200 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 44.7% (2247 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 46.3% (2327 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 46.9% (2357 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 47.9% (2404 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 49.0% (2461 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 50.0% (2511 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 51.0% (2559 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 51.9% (2605 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 53.6% (2691 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 54.2% (2722 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 55.1% (2769 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 57.2% (2873 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 58.0% (2914 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 59.6% (2995 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 60.8% (3053 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 61.3% (3078 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 62.2% (3124 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 63.1% (3168 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 64.8% (3254 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 65.6% (3295 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 66.1% (3320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 67.0% (3367 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 68.2% (3427 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 69.2% (3476 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 70.2% (3525 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 71.2% (3574 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 72.1% (3623 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 73.3% (3683 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 74.1% (3723 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 75.2% (3776 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 76.6% (3848 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 77.2% (3876 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 78.5% (3943 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 79.4% (3989 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 80.4% (4036 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 81.3% (4085 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 82.4% (4136 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 83.6% (4197 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 84.4% (4237 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 85.4% (4289 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 86.3% (4336 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 87.4% (4391 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 88.4% (4441 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 89.4% (4490 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 90.4% (4542 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 91.5% (4593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 92.5% (4645 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 93.4% (4692 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 95.5% (4797 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 96.6% (4849 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 86.9; 4125 / 5022 (P = 82.14%) round 32]               
[00:00:00] Finding cutoff p=859 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=859 2.1% (105 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 3.3% (164 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 4.1% (206 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 5.2% (261 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 6.4% (319 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 7.3% (369 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 8.5% (427 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 9.7% (486 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 10.8% (542 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 13.2% (662 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 14.5% (727 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 15.6% (781 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 16.4% (825 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 18.8% (946 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 20.0% (1004 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 21.2% (1066 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 22.3% (1119 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 24.0% (1206 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 24.8% (1247 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 25.4% (1278 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 26.5% (1330 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 27.6% (1384 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 28.6% (1434 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 29.9% (1503 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 31.0% (1559 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 31.6% (1588 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 32.7% (1641 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 33.6% (1687 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 34.6% (1740 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 35.6% (1786 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 36.7% (1844 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 37.9% (1901 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 38.6% (1940 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 39.6% (1989 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 40.8% (2048 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 42.4% (2130 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 43.2% (2170 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 44.5% (2237 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 45.2% (2268 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 45.8% (2299 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 47.1% (2366 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 48.1% (2417 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 48.9% (2456 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 50.1% (2514 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 51.5% (2585 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 52.0% (2610 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 53.1% (2665 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 54.4% (2730 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 54.8% (2754 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 55.9% (2807 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 57.7% (2900 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 58.5% (2938 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 59.0% (2961 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 60.4% (3033 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 61.4% (3084 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 62.0% (3115 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 63.8% (3204 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 64.4% (3232 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 65.7% (3297 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 66.4% (3334 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 67.2% (3373 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 68.1% (3421 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 69.4% (3484 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 70.2% (3523 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 71.3% (3581 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 72.1% (3621 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 73.4% (3687 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 74.2% (3726 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 75.3% (3782 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 76.6% (3845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 77.2% (3877 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 78.4% (3939 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 79.5% (3993 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 80.5% (4043 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 81.4% (4086 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 82.4% (4139 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 83.6% (4197 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 84.4% (4240 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 85.4% (4287 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 86.6% (4348 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 87.4% (4389 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 88.6% (4447 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 89.5% (4494 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 90.4% (4539 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 91.5% (4593 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 92.5% (4645 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 93.5% (4696 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 95.5% (4794 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 96.6% (4853 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.9; 4226 / 5022 (P = 84.15%) round 33]               
[00:00:00] Finding cutoff p=850 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=850 2.0% (102 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 3.2% (163 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 4.4% (219 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 5.3% (264 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 6.1% (306 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 7.6% (382 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 8.4% (423 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 9.2% (461 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 10.2% (512 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 11.6% (584 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 14.0% (703 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 15.3% (770 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 16.6% (833 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 20.2% (1013 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 22.2% (1116 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 22.8% (1146 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 23.4% (1176 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 24.5% (1228 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 25.6% (1287 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 26.4% (1327 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 28.0% (1405 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 28.5% (1431 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 30.1% (1510 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 31.5% (1580 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 32.2% (1615 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 33.0% (1656 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 33.8% (1695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 34.9% (1753 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 35.6% (1790 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 36.6% (1838 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 37.9% (1905 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 38.6% (1939 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 39.6% (1990 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 40.8% (2048 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 42.0% (2110 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 42.9% (2154 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 44.2% (2218 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 44.9% (2254 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 46.3% (2326 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 46.8% (2350 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 47.7% (2398 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 49.0% (2462 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 49.9% (2507 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 50.9% (2555 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 51.9% (2607 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 53.7% (2699 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 54.4% (2730 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 55.1% (2769 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 57.4% (2882 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 57.9% (2908 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 59.8% (3002 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 60.9% (3058 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 61.6% (3094 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 62.2% (3126 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 63.0% (3166 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 64.1% (3218 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 65.9% (3309 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 66.7% (3352 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 67.1% (3370 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 68.3% (3430 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 69.1% (3471 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 70.4% (3534 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 71.3% (3581 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 72.3% (3632 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 73.5% (3691 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 74.5% (3740 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 75.3% (3781 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 77.5% (3892 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 78.4% (3938 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 79.3% (3981 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 80.4% (4037 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 81.3% (4084 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 82.3% (4133 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 83.4% (4186 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 84.4% (4240 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 85.3% (4284 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 86.3% (4336 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 87.5% (4396 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 88.4% (4441 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 89.5% (4493 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 90.5% (4544 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 91.4% (4591 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 92.5% (4646 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 93.5% (4695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 94.5% (4748 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 95.7% (4808 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 96.5% (4846 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=850 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.0; 4306 / 5022 (P = 85.74%) round 34]               
[00:00:00] Finding cutoff p=841 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=841 1.9% (94 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 2.4% (123 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 3.5% (174 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 4.4% (219 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 5.1% (255 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 6.3% (316 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 7.6% (380 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 8.4% (421 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 9.2% (463 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 10.5% (526 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 11.7% (586 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 13.9% (697 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 15.2% (763 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 16.2% (814 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 17.1% (859 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 19.7% (988 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 21.3% (1068 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 22.5% (1130 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 23.8% (1194 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 24.5% (1228 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 25.8% (1294 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 26.4% (1326 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 27.5% (1383 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 28.5% (1432 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 29.6% (1489 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 30.8% (1548 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 31.5% (1582 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 32.6% (1637 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 33.8% (1695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 34.8% (1750 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 35.9% (1804 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 36.6% (1836 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 37.8% (1899 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 38.7% (1943 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 39.9% (2002 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 41.3% (2072 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 42.2% (2118 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 43.4% (2180 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 44.1% (2213 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 45.4% (2282 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 46.0% (2312 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 47.1% (2364 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 47.8% (2400 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 49.1% (2467 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 50.3% (2526 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 50.8% (2553 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 52.0% (2610 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 53.3% (2676 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 53.9% (2708 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 54.9% (2759 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 56.9% (2858 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 59.2% (2974 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 60.2% (3022 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 61.2% (3074 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 62.5% (3137 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 64.1% (3219 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 65.2% (3274 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 66.0% (3315 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 67.1% (3372 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 68.4% (3437 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 69.1% (3472 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 70.4% (3534 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 71.1% (3571 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 72.4% (3637 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 73.4% (3684 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 74.2% (3727 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 75.8% (3805 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 76.5% (3843 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 77.3% (3882 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 78.2% (3928 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 79.2% (3978 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 80.6% (4047 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 81.6% (4097 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 82.4% (4136 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 83.4% (4189 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 84.4% (4237 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 85.6% (4297 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 86.3% (4335 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 87.4% (4387 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 88.4% (4441 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 89.4% (4489 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 90.5% (4545 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 91.4% (4591 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 92.7% (4654 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 93.7% (4704 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 94.5% (4747 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 95.5% (4797 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 96.6% (4851 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 97.5% (4898 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.1; 4388 / 5022 (P = 87.38%) round 35]               
[00:00:00] Finding cutoff p=832 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=832 1.8% (92 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 2.5% (126 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 3.3% (168 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 4.3% (216 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 5.2% (259 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 6.4% (320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 7.6% (383 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 8.3% (416 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 9.8% (493 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 11.7% (587 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 13.6% (684 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 15.2% (764 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 16.1% (808 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 17.0% (854 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 19.5% (979 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 21.4% (1073 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 22.4% (1124 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 23.6% (1184 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 24.6% (1233 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 25.6% (1285 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 26.9% (1352 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 27.5% (1380 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 29.0% (1455 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 30.0% (1508 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 30.7% (1541 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 31.6% (1589 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 33.1% (1660 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 33.8% (1697 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 34.5% (1734 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 35.6% (1789 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 36.9% (1855 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 38.0% (1907 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 39.0% (1957 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 39.9% (2005 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 41.2% (2067 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 42.0% (2108 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 43.1% (2163 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 43.7% (2197 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 45.2% (2270 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 45.7% (2297 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 46.9% (2356 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 47.7% (2398 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 49.4% (2479 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 50.2% (2520 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 51.0% (2562 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 52.4% (2631 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 53.1% (2668 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 54.0% (2710 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 55.6% (2793 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 56.3% (2826 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 58.2% (2924 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 59.4% (2985 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 60.2% (3021 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 61.6% (3096 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 62.1% (3119 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 63.5% (3187 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 64.4% (3234 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 65.1% (3269 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 66.4% (3334 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 67.7% (3398 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 68.6% (3443 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 69.2% (3476 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 70.3% (3528 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 71.7% (3600 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 72.1% (3623 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 73.3% (3680 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 74.4% (3738 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 75.3% (3781 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 76.3% (3832 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 77.5% (3891 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 78.5% (3944 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 79.5% (3993 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 80.3% (4033 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 81.5% (4093 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 82.8% (4157 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 83.4% (4187 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 84.4% (4241 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 85.3% (4285 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 86.5% (4344 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 87.4% (4390 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 91.3% (4587 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 91.6% (4602 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 92.4% (4641 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 93.5% (4697 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 94.6% (4749 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 95.5% (4796 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=832 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 83.2; 4447 / 5022 (P = 88.55%) round 36]               
[00:00:00] Finding cutoff p=821 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=821 2.0% (100 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 2.6% (133 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 3.3% (165 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 4.2% (210 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 5.3% (268 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 6.7% (334 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 7.6% (381 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 8.4% (422 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 9.3% (469 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 10.7% (538 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 13.3% (668 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 14.8% (745 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 15.8% (792 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 16.7% (840 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 18.6% (935 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 20.2% (1012 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 21.8% (1093 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 22.4% (1126 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 23.5% (1180 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 24.7% (1241 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 26.2% (1317 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 26.7% (1343 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 28.2% (1414 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 29.4% (1477 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 30.5% (1534 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 31.5% (1584 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 32.5% (1634 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 33.6% (1688 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 34.6% (1738 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 35.6% (1788 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 36.8% (1846 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 38.0% (1907 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 39.3% (1972 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 40.0% (2010 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 41.6% (2087 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 42.2% (2119 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 43.2% (2169 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 43.7% (2196 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 44.7% (2247 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 45.9% (2306 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 47.4% (2379 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 48.1% (2418 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 49.1% (2464 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 50.4% (2531 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 51.1% (2564 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 52.0% (2610 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 54.0% (2711 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 56.1% (2819 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 57.4% (2885 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 58.2% (2925 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 59.0% (2962 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 60.1% (3016 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 61.6% (3095 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 62.4% (3135 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 63.1% (3168 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 64.0% (3213 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 65.7% (3301 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 66.8% (3356 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 67.6% (3396 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 68.1% (3419 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 69.2% (3473 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 70.5% (3538 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 71.5% (3589 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 72.2% (3625 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 73.2% (3675 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 74.2% (3725 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 75.3% (3784 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 76.3% (3834 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 77.7% (3900 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 78.3% (3930 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 79.4% (3989 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 80.5% (4043 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 81.3% (4085 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 82.7% (4152 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 83.3% (4185 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 84.3% (4236 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 85.3% (4285 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 86.3% (4335 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 87.4% (4387 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 88.5% (4442 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 89.5% (4495 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 90.4% (4542 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 91.6% (4600 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 92.5% (4646 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 93.7% (4704 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 94.6% (4751 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 95.5% (4797 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.1; 4527 / 5022 (P = 90.14%) round 37]               
[00:00:00] Finding cutoff p=811 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=811 2.0% (98 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 2.5% (126 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 3.2% (162 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 4.1% (208 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 5.4% (271 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 6.2% (309 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 7.2% (363 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 8.5% (425 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 9.5% (475 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 10.4% (524 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 12.9% (648 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 14.1% (710 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 15.0% (752 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 16.3% (818 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 18.6% (933 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 20.0% (1003 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 21.0% (1053 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 21.8% (1097 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 22.9% (1149 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 23.6% (1183 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 24.8% (1246 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 26.5% (1330 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 28.2% (1415 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 29.4% (1478 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 29.9% (1503 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 30.5% (1531 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 31.8% (1599 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 32.9% (1650 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 33.7% (1694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 34.9% (1751 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 35.8% (1796 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 36.7% (1845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 38.4% (1930 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 39.2% (1970 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 40.8% (2047 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 41.7% (2094 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 42.9% (2152 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 43.9% (2204 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 45.1% (2263 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 45.9% (2306 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 46.8% (2351 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 48.0% (2410 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 49.6% (2489 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 50.2% (2522 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 51.2% (2572 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 53.2% (2672 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 53.9% (2706 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 55.3% (2775 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 56.6% (2843 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 57.6% (2891 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 58.3% (2927 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 59.5% (2986 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 61.0% (3065 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 62.0% (3114 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 63.1% (3171 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 64.1% (3219 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 65.1% (3268 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 66.0% (3315 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 67.1% (3368 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 68.1% (3419 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 69.7% (3498 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 70.2% (3527 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 71.2% (3577 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 72.5% (3641 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 73.4% (3684 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 74.2% (3727 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 75.7% (3801 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 76.2% (3825 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 77.3% (3880 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 78.4% (3937 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 79.2% (3978 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 80.5% (4041 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 81.7% (4101 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 82.3% (4131 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 83.5% (4192 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 84.3% (4235 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 85.3% (4285 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 86.4% (4341 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 87.7% (4403 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 88.4% (4438 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 89.4% (4490 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 90.7% (4556 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 91.6% (4602 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 92.5% (4645 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 93.5% (4694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 94.5% (4746 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 95.6% (4799 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 96.5% (4847 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 97.5% (4898 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 81.1; 4586 / 5022 (P = 91.32%) round 38]               
[00:00:00] Finding cutoff p=801 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=801 1.8% (90 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 2.5% (126 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 3.5% (174 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 4.4% (222 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 5.1% (257 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 6.1% (308 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 7.2% (363 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 8.4% (421 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 9.4% (473 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 10.7% (536 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 13.0% (655 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 14.4% (721 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 15.3% (770 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 18.2% (916 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 19.6% (986 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 20.6% (1034 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 22.1% (1110 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 23.0% (1156 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 23.6% (1185 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 24.5% (1231 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 26.3% (1320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 26.6% (1337 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 28.3% (1420 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 29.4% (1475 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 29.9% (1504 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 30.7% (1540 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 31.8% (1595 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 32.6% (1639 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 33.7% (1690 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 34.8% (1746 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 35.9% (1805 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 36.7% (1841 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 38.2% (1919 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 39.2% (1968 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 40.5% (2034 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 40.9% (2052 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 42.3% (2126 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 42.9% (2153 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 43.7% (2197 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 44.8% (2249 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 46.3% (2325 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 46.9% (2357 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 48.9% (2455 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 50.3% (2525 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 50.8% (2552 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 52.4% (2632 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 53.0% (2661 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 54.8% (2750 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 56.0% (2810 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 57.2% (2873 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 58.7% (2947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 59.1% (2968 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 60.6% (3041 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 61.1% (3069 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 62.2% (3124 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 63.1% (3169 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 64.3% (3227 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 65.4% (3284 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 66.1% (3320 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 67.3% (3382 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 68.2% (3426 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 69.3% (3482 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 70.5% (3539 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 71.2% (3576 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 72.1% (3621 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 73.3% (3679 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 74.7% (3753 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 75.3% (3781 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 76.3% (3833 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 77.6% (3898 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 78.4% (3938 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 79.3% (3984 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 80.3% (4032 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 81.5% (4091 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 82.4% (4136 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 83.4% (4187 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 84.4% (4240 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 85.3% (4286 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 86.5% (4346 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 87.5% (4394 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 88.6% (4449 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 89.4% (4490 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 90.4% (4540 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 91.7% (4607 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 92.4% (4641 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 93.5% (4697 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 94.5% (4744 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 95.5% (4796 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 97.5% (4897 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=801 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.1; 4639 / 5022 (P = 92.37%) round 39]               
[00:00:00] Finding cutoff p=791 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=791 1.8% (89 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 2.3% (118 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 3.1% (154 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 4.1% (207 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 5.1% (257 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 6.3% (318 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 7.2% (361 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 8.6% (430 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 9.8% (491 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 12.2% (612 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 13.4% (674 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 14.3% (716 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 17.4% (874 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 19.0% (956 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 20.3% (1017 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 21.3% (1069 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 22.3% (1120 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 23.0% (1154 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 23.6% (1184 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 24.6% (1237 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 26.2% (1316 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 26.7% (1341 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 28.3% (1420 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 29.5% (1480 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 30.6% (1539 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 31.6% (1589 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 32.5% (1632 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 33.6% (1688 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 34.6% (1736 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 35.9% (1804 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 36.7% (1843 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 38.7% (1944 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 40.6% (2040 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 42.3% (2124 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 43.0% (2158 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 43.9% (2203 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 44.9% (2257 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 46.5% (2333 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 47.3% (2375 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 47.8% (2400 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 49.3% (2476 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 49.9% (2505 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 50.9% (2554 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 52.9% (2658 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 55.1% (2767 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 56.1% (2819 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 57.4% (2884 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 58.6% (2941 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 59.0% (2965 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 60.4% (3033 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 61.1% (3070 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 62.0% (3116 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 63.0% (3164 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 64.0% (3215 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 65.1% (3269 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 66.4% (3336 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 67.2% (3374 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 68.7% (3450 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 69.2% (3475 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 70.1% (3520 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 71.1% (3573 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 72.5% (3640 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 73.2% (3675 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 74.6% (3748 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 76.0% (3817 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 76.4% (3839 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 77.2% (3879 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 78.2% (3929 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 79.4% (3988 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 80.6% (4047 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 81.4% (4086 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 82.6% (4148 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 83.4% (4188 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 84.4% (4237 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 85.6% (4297 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 86.4% (4338 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 87.5% (4393 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 88.5% (4442 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 89.4% (4489 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 90.5% (4544 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 91.5% (4597 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 92.5% (4644 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 93.5% (4695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 95.5% (4795 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 96.5% (4846 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 97.5% (4898 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 79.1; 4683 / 5022 (P = 93.25%) round 40]               
[00:00:00] Finding cutoff p=780 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=780 4.2% (210 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 5.4% (271 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 6.3% (315 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 7.3% (365 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 8.6% (431 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 9.4% (472 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 10.2% (513 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 11.8% (591 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 12.9% (649 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 15.2% (765 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 17.1% (859 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 18.3% (917 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 19.1% (961 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 21.4% (1073 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 22.5% (1129 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 24.0% (1203 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 24.5% (1232 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 25.6% (1285 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 27.2% (1368 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 27.8% (1395 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 29.1% (1459 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 30.4% (1525 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 31.1% (1564 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 31.6% (1589 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 32.5% (1632 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 33.6% (1687 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 34.7% (1742 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 35.8% (1796 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 36.7% (1844 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 37.7% (1894 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 39.6% (1988 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 40.3% (2023 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 41.6% (2089 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 42.1% (2112 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 43.5% (2185 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 43.9% (2204 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 44.7% (2247 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 45.8% (2298 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 47.2% (2368 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 48.0% (2412 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 49.5% (2485 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 50.1% (2518 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 51.0% (2561 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 52.9% (2658 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 54.9% (2755 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 56.3% (2825 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 57.6% (2891 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 58.6% (2944 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 59.1% (2967 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 60.4% (3033 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 61.1% (3070 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 62.2% (3123 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 63.0% (3162 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 64.1% (3218 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 65.1% (3267 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 66.5% (3340 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 67.3% (3379 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 68.8% (3454 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 69.4% (3486 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 70.2% (3525 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 71.6% (3598 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 72.5% (3639 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 73.1% (3673 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 74.7% (3753 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 75.2% (3778 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 76.6% (3845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 77.3% (3883 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 78.2% (3929 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 79.6% (3996 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 80.5% (4041 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 81.5% (4093 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 82.3% (4131 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 83.4% (4187 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 84.4% (4239 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 85.4% (4288 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 86.8% (4357 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 87.4% (4389 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 88.4% (4439 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 89.5% (4495 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 90.4% (4542 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 91.5% (4596 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 92.5% (4644 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 93.5% (4694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 94.5% (4745 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 95.5% (4798 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 96.6% (4849 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.0; 4724 / 5022 (P = 94.07%) round 41]               
[00:00:00] Finding cutoff p=770 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=770 1.9% (94 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 2.4% (119 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 3.3% (166 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 4.1% (205 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 5.3% (265 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 6.3% (315 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 7.3% (369 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 8.6% (431 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 9.7% (486 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 12.2% (615 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 13.6% (684 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 14.8% (741 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 15.4% (775 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 18.1% (907 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 19.4% (974 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 20.8% (1045 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 21.4% (1074 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 22.5% (1131 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 23.4% (1177 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 24.4% (1224 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 26.0% (1306 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 26.5% (1332 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 27.9% (1402 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 29.1% (1462 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 29.6% (1488 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 30.5% (1531 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 31.8% (1595 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 32.5% (1633 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 33.6% (1686 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 34.7% (1741 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 35.8% (1800 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 37.1% (1862 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 37.9% (1903 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 39.6% (1987 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 40.1% (2012 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 41.5% (2085 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 42.2% (2117 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 43.0% (2159 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 43.8% (2200 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 45.5% (2283 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 46.3% (2326 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 46.8% (2350 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 48.2% (2421 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 48.9% (2454 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 49.9% (2504 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 51.5% (2585 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 51.9% (2604 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 53.6% (2694 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 55.1% (2765 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 56.5% (2836 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 57.8% (2902 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 58.3% (2926 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 59.6% (2992 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 60.5% (3040 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 61.0% (3063 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 62.2% (3123 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 63.4% (3183 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 64.6% (3242 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 65.5% (3290 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 66.2% (3324 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 67.1% (3370 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 68.8% (3456 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 69.3% (3479 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 70.2% (3523 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 71.1% (3570 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 72.7% (3649 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 73.1% (3673 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 74.8% (3758 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 75.1% (3774 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 76.4% (3835 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 77.4% (3885 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 78.3% (3931 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 79.5% (3992 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 80.5% (4045 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 81.5% (4094 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 82.4% (4137 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 83.4% (4186 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 84.5% (4244 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 85.4% (4287 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 86.7% (4352 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 87.4% (4388 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 88.5% (4443 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 89.8% (4509 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 90.4% (4539 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 91.4% (4592 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 92.6% (4651 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 93.5% (4695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 95.5% (4798 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 96.5% (4848 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 97.6% (4901 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 98.5% (4948 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 77.0; 4769 / 5022 (P = 94.96%) round 42]               
[00:00:00] Finding cutoff p=760 [8.6Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=760 1.7% (85 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 2.3% (113 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 3.1% (156 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 4.2% (213 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 5.9% (295 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 6.5% (327 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 7.3% (366 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 8.5% (426 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 9.7% (489 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 12.1% (609 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 13.6% (683 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 14.7% (736 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 15.3% (769 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 17.8% (896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 19.7% (990 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 20.6% (1035 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 22.2% (1115 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 22.7% (1142 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 23.6% (1186 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 25.5% (1281 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 27.4% (1374 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 28.7% (1443 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 29.5% (1482 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 30.7% (1541 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 31.6% (1587 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 32.5% (1632 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 33.8% (1695 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 34.7% (1745 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 35.6% (1790 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 36.9% (1853 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 38.0% (1909 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 39.8% (1999 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 41.6% (2088 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 41.9% (2106 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 43.0% (2157 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 43.7% (2194 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 44.8% (2248 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 45.8% (2299 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 46.9% (2355 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 48.0% (2412 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 48.8% (2450 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 50.3% (2525 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 50.8% (2553 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 52.4% (2634 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 53.0% (2661 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 54.8% (2751 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 56.0% (2813 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 56.9% (2856 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 58.0% (2914 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 59.1% (2970 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 60.7% (3048 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 61.6% (3095 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 62.1% (3120 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 63.0% (3165 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 64.5% (3239 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 65.7% (3299 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 66.4% (3335 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 67.1% (3372 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 68.2% (3427 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 69.2% (3474 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 70.2% (3524 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 71.2% (3578 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 72.7% (3653 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 73.3% (3680 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 74.8% (3755 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 75.3% (3780 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 76.2% (3828 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 77.3% (3883 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 78.2% (3927 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 79.5% (3991 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 80.5% (4044 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 81.2% (4080 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 82.4% (4137 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 83.6% (4200 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 84.4% (4241 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 85.4% (4288 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 86.6% (4347 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 87.3% (4386 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 88.4% (4441 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 89.7% (4506 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 90.7% (4557 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 91.4% (4592 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 92.6% (4652 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 93.5% (4698 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 94.5% (4744 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 95.6% (4800 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 98.5% (4948 of 5022), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:00] Finished Preparing TNF Graph Building [pTNF = 77.00] [8.4Gb / 503.5Gb]                                            
[00:00:00] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=5022 maxEdges=200
[00:00:00] Building TNF Graph 30.8% (1545 of 5022), ETA 0:00:00     [8.4Gb / 503.5Gb]                           
[00:00:01] Building TNF Graph 63.6% (3193 of 5022), ETA 0:00:00     [8.4Gb / 503.5Gb]                           
[00:00:01] Building TNF Graph 98.4% (4944 of 5022), ETA 0:00:00     [8.4Gb / 503.5Gb]                           
[00:00:01] Finished Building TNF Graph (203715 edges) [8.4Gb / 503.5Gb]                                          
[00:00:01] Cleaned up after Building TNF Graph (203715 edges) [8.4Gb / 503.5Gb]                                          
[00:00:01] Cleaned up TNF matrix of large contigs [8.4Gb / 503.5Gb]                                             
[00:00:01] Applying coverage correlations to TNF graph with 203715 edges
[00:00:01] Allocated memory for graph edges [8.4Gb / 503.5Gb]
[00:00:01] ... calculating abundance dist 1.0% (2054 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 2.0% (4089 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 3.0% (6128 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 4.0% (8165 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 5.0% (10200 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 6.0% (12233 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 7.0% (14275 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 8.0% (16311 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 9.0% (18364 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 10.0% (20393 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 11.0% (22436 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 12.0% (24463 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 13.0% (26504 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 14.0% (28533 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 15.0% (30572 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 16.0% (32610 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 17.0% (34657 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 18.0% (36688 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 19.0% (38732 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 20.0% (40762 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 21.0% (42808 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 22.0% (44846 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 23.0% (46877 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 24.0% (48921 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 25.0% (50965 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 26.0% (53001 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 27.0% (55030 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 28.0% (57070 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 29.0% (59112 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 30.0% (61153 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 31.0% (63192 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 32.0% (65223 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 33.0% (67265 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 34.0% (69293 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 35.0% (71342 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 36.0% (73377 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 37.0% (75419 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 38.0% (77451 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 39.0% (79505 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 40.0% (81534 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 41.0% (83569 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 42.0% (85609 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 43.0% (87644 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 44.0% (89674 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 45.0% (91719 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 46.0% (93757 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 47.0% (95797 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 48.0% (97835 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 49.0% (99873 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 50.0% (101900 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 51.0% (103962 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 52.0% (105989 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 53.0% (108024 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 54.0% (110068 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 55.0% (112104 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 56.0% (114134 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 57.0% (116175 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 58.0% (118214 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 59.0% (120255 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 60.0% (122289 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 61.0% (124318 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 62.0% (126371 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 63.0% (128399 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 64.0% (130444 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 65.0% (132482 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 66.0% (134522 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 67.0% (136547 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 68.0% (138588 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 69.0% (140645 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 70.0% (142671 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 71.0% (144701 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 72.0% (146747 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 73.0% (148801 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 74.0% (150814 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 75.0% (152875 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 76.0% (154894 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 77.0% (156930 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 78.0% (158973 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 79.0% (161016 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 80.0% (163044 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 81.0% (165082 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 83.1% (169271 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 84.0% (171196 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 85.0% (173233 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 86.0% (175274 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 87.0% (177309 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 88.0% (179346 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 89.0% (181390 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 90.0% (183428 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 91.0% (185464 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 92.0% (187508 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 93.0% (189542 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 94.0% (191581 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 95.0% (193617 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 96.0% (195649 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 97.0% (197697 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 98.0% (199725 of 203715), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 99.0% (201764 of 203715), ETA 0:00:00                              
[00:00:01] Calculating geometric means [8.4Gb / 503.5Gb]
[00:00:01] Traversing graph with 5022 nodes and 203715 edges [8.4Gb / 503.5Gb]
[00:00:01] Building SCR Graph and Binning (478 vertices and 881 edges) [P = 9.50%; 8.4Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 1.0% (2038 of 203715), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (955 vertices and 3302 edges) [P = 19.00%; 8.4Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 2.0% (4076 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 3.0% (6114 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 4.0% (8152 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 5.0% (10190 of 203715), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (1432 vertices and 6519 edges) [P = 28.50%; 8.4Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 6.0% (12228 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 7.0% (14266 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 8.0% (16304 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 9.0% (18342 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 10.0% (20380 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 11.0% (22418 of 203715), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (1909 vertices and 10222 edges) [P = 38.00%; 8.4Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 12.0% (24456 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 13.0% (26494 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 14.0% (28532 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 15.0% (30570 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 16.0% (32608 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 17.0% (34646 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 18.0% (36684 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 19.0% (38722 of 203715), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (2386 vertices and 11343 edges) [P = 47.50%; 8.4Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 20.0% (40760 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 21.0% (42798 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 22.0% (44836 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 23.0% (46874 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 24.0% (48912 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 25.0% (50950 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 26.0% (52988 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 27.0% (55026 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 28.0% (57064 of 203715), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (2863 vertices and 12211 edges) [P = 57.00%; 8.4Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 29.0% (59102 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 30.0% (61140 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 31.0% (63178 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 32.0% (65216 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 33.0% (67254 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 34.0% (69292 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 35.0% (71330 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 36.0% (73368 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 37.0% (75406 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 38.0% (77444 of 203715), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (3340 vertices and 13322 edges) [P = 66.50%; 8.4Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 39.0% (79482 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 40.0% (81520 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 41.0% (83558 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 42.0% (85596 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 43.0% (87634 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 44.0% (89672 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 45.0% (91710 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 46.0% (93748 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 47.0% (95786 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 48.0% (97824 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 49.0% (99862 of 203715), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (3817 vertices and 15008 edges) [P = 76.00%; 8.4Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 50.0% (101900 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 51.0% (103938 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 52.0% (105976 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 53.0% (108014 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 54.0% (110052 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 55.0% (112090 of 203715), ETA 0:00:00                               
[00:00:01] ... traversing graph 56.0% (114128 of 203715), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (4029 vertices and 16053 edges) [P = 85.50%; 8.4Gb / 503.5Gb]                           
[00:00:01] Finished Traversing graph [8.4Gb / 503.5Gb]                                       
[00:00:01] Dissolved 1639 small clusters leaving 1158 leftover contigs to be re-merged into larger clusters
[00:00:01] Rescuing singleton large contigs                                   
[00:00:01] There are 1 bins already
[00:00:01] Outputting bins
[00:00:01] Writing cluster stats to: 03bins/metabat2_fixed/1507999/1507999.bin.BinInfo.txt
[00:00:01] 80.91% (24234445 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
1 bins (24234445 bases in total) formed.
[00:00:01] Finished
MetaBAT2 generated 1 bins for 1507999
MetaBAT2 binning completed for 1507999
