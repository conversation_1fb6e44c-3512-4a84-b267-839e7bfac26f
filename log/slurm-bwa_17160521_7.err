INFO:    Converting OCI blobs to SIF format
INFO:    Starting build...
Copying blob sha256:0e6dd20ee818794f2f70b3dad6316630256644ecd340435fb273da623245ca78
Copying blob sha256:59856638ac9f32d4caa0f5761b2597fe251642786fdfe1b917ddbb074b890c29
Copying blob sha256:a9dde5e2a643eca8fde0eed52f4aed31f3ecd9c1b2f24d5e3729cd8d2ae68177
Copying blob sha256:6f317d6d954b9a59c54b2cb09e1f30cd3e872796e431cd2ceac5ed570beb2939
Copying blob sha256:675cac559d077f6708a9931d90e801a5834a419b80aedf8fa01499be09d0e08d
Copying blob sha256:9ff7e2e5f967fb9c4e8099e63508ab0dddebe3f820d08ca7fd568431b0d10c0e
Copying blob sha256:374c558e71daf84e430cf7bbb4af95cec0a3d2984eff3a27858ee0d03be6049d
Copying blob sha256:0df3c64bb19acab06b69728650bd3c3ed7193efe01f7027a81c44e6d9910bc81
time="2025-04-28T16:12:24-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
Copying blob sha256:e936d7784cf988634bb8c3f94b8d895416e17be11ea0540abca85d225043da4d
time="2025-04-28T16:12:25-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
Copying blob sha256:4dfd8d164cc5809f0fb8556fb27c931ef5c319353819af28a0b4df9301e54f8d
Copying blob sha256:473490461998ab1efd3fb4c270d5d051d2bb65aca7c361efacb342dda4b5fef3
Copying blob sha256:8f5e491552e6efef12e515fdeb085d6cc85925d6167f91d1aa3cfb727c557914
Copying blob sha256:a66ab3a674d95f89aeb3259740e46128cec4f2d406cc1f631d14b5806483aac5
Copying blob sha256:18f922275a6f0d7dde75027a285dd23437bd26de80f7e66a025a49739b63db79
Copying blob sha256:c8e106a5860b6452d83af4d44183b408fabcfbf2c91043e0442c0308cdcf5757
Copying config sha256:a24bd272b49d6e66ade4d8447b73ca8c034ff1b0bc6e96b143a1991869e81582
Writing manifest to image destination
time="2025-04-28T16:12:30-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
time="2025-04-28T16:12:31-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
FATAL:   While making image from oci registry: error fetching image to cache: while building SIF from layers: conveyor failed to get: while fetching image: writing blob: happened during read: read /clusterfs/jgi/groups/science/homes/ssiddik/.apptainer/cache/blob/blobs/sha256/c8e106a5860b6452d83af4d44183b408fabcfbf2c91043e0442c0308cdcf5757: stale NFS file handle
mv: cannot stat 'bwa_v0.7.17_cv1.sif': No such file or directory
INFO:    Using cached SIF image
FATAL:   While checking container encryption: could not open image /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/bwa_latest.sif: failed to retrieve path for /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/bwa_latest.sif: lstat /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/bwa_latest.sif: no such file or directory
FATAL:   While checking container encryption: could not open image /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/bwa_latest.sif: failed to retrieve path for /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/bwa_latest.sif: lstat /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/bwa_latest.sif: no such file or directory
