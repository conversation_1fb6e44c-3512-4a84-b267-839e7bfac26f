Decompressing scaffold file for 1507990...
Generating depth file for 1507990...
Running MetaBAT2 for 1507990 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 123. with random seed=1745943194
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [19.9Gb / 503.5Gb]
[00:00:00] Parsing assembly file [19.9Gb / 503.5Gb]
[00:00:00] ... processed 2 seqs, 2 long (>=2000), 0 short (>=1000) 1.2% (115080 of 9913441), ETA 0:00:01     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 5 seqs, 5 long (>=2000), 0 short (>=1000) 2.1% (209049 of 9913441), ETA 0:00:01     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 9 seqs, 9 long (>=2000), 0 short (>=1000) 3.2% (319185 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 12 seqs, 12 long (>=2000), 0 short (>=1000) 4.0% (397293 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 17 seqs, 17 long (>=2000), 0 short (>=1000) 5.2% (517609 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 21 seqs, 21 long (>=2000), 0 short (>=1000) 6.1% (604212 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 26 seqs, 26 long (>=2000), 0 short (>=1000) 7.1% (705461 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 31 seqs, 31 long (>=2000), 0 short (>=1000) 8.1% (799094 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 37 seqs, 37 long (>=2000), 0 short (>=1000) 9.2% (908131 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 42 seqs, 42 long (>=2000), 0 short (>=1000) 10.0% (995558 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 48 seqs, 48 long (>=2000), 0 short (>=1000) 11.1% (1096223 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 54 seqs, 54 long (>=2000), 0 short (>=1000) 12.0% (1191950 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 61 seqs, 61 long (>=2000), 0 short (>=1000) 13.1% (1297675 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 68 seqs, 68 long (>=2000), 0 short (>=1000) 14.1% (1397876 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 75 seqs, 75 long (>=2000), 0 short (>=1000) 15.1% (1492923 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 83 seqs, 83 long (>=2000), 0 short (>=1000) 16.1% (1596130 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 91 seqs, 91 long (>=2000), 0 short (>=1000) 17.1% (1695792 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 99 seqs, 99 long (>=2000), 0 short (>=1000) 18.1% (1792586 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 107 seqs, 107 long (>=2000), 0 short (>=1000) 19.0% (1887263 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 116 seqs, 116 long (>=2000), 0 short (>=1000) 20.1% (1990590 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 125 seqs, 125 long (>=2000), 0 short (>=1000) 21.1% (2089952 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 134 seqs, 134 long (>=2000), 0 short (>=1000) 22.0% (2184062 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 144 seqs, 144 long (>=2000), 0 short (>=1000) 23.0% (2281894 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 155 seqs, 155 long (>=2000), 0 short (>=1000) 24.0% (2383085 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 166 seqs, 166 long (>=2000), 0 short (>=1000) 25.0% (2479264 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 178 seqs, 178 long (>=2000), 0 short (>=1000) 26.0% (2579796 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 190 seqs, 190 long (>=2000), 0 short (>=1000) 27.0% (2677160 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 203 seqs, 203 long (>=2000), 0 short (>=1000) 28.0% (2778271 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 217 seqs, 217 long (>=2000), 0 short (>=1000) 29.1% (2881516 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 231 seqs, 231 long (>=2000), 0 short (>=1000) 30.1% (2980762 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 245 seqs, 245 long (>=2000), 0 short (>=1000) 31.0% (3076136 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 260 seqs, 260 long (>=2000), 0 short (>=1000) 32.0% (3175287 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 276 seqs, 276 long (>=2000), 0 short (>=1000) 33.1% (3276620 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 292 seqs, 292 long (>=2000), 0 short (>=1000) 34.0% (3374644 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 309 seqs, 309 long (>=2000), 0 short (>=1000) 35.1% (3474695 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 326 seqs, 326 long (>=2000), 0 short (>=1000) 36.0% (3571456 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 344 seqs, 344 long (>=2000), 0 short (>=1000) 37.0% (3670186 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 363 seqs, 363 long (>=2000), 0 short (>=1000) 38.0% (3770214 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 382 seqs, 382 long (>=2000), 0 short (>=1000) 39.0% (3866629 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 403 seqs, 403 long (>=2000), 0 short (>=1000) 40.0% (3968971 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 424 seqs, 424 long (>=2000), 0 short (>=1000) 41.0% (4064876 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 447 seqs, 447 long (>=2000), 0 short (>=1000) 42.0% (4164217 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 471 seqs, 471 long (>=2000), 0 short (>=1000) 43.0% (4262917 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 497 seqs, 497 long (>=2000), 0 short (>=1000) 44.0% (4364215 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 524 seqs, 524 long (>=2000), 0 short (>=1000) 45.0% (4464611 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 552 seqs, 552 long (>=2000), 0 short (>=1000) 46.0% (4563445 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 581 seqs, 581 long (>=2000), 0 short (>=1000) 47.0% (4660307 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 612 seqs, 612 long (>=2000), 0 short (>=1000) 48.0% (4759437 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 644 seqs, 644 long (>=2000), 0 short (>=1000) 49.0% (4858041 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 678 seqs, 678 long (>=2000), 0 short (>=1000) 50.0% (4957105 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 715 seqs, 715 long (>=2000), 0 short (>=1000) 51.0% (5058197 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 753 seqs, 753 long (>=2000), 0 short (>=1000) 52.0% (5155105 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 795 seqs, 795 long (>=2000), 0 short (>=1000) 53.0% (5255393 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 839 seqs, 839 long (>=2000), 0 short (>=1000) 54.0% (5355356 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 884 seqs, 884 long (>=2000), 0 short (>=1000) 55.0% (5453006 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 933 seqs, 923 long (>=2000), 10 short (>=1000) 56.0% (5552761 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 985 seqs, 923 long (>=2000), 62 short (>=1000) 57.0% (5651702 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1041 seqs, 923 long (>=2000), 118 short (>=1000) 58.0% (5751212 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1100 seqs, 923 long (>=2000), 177 short (>=1000) 59.0% (5850368 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1163 seqs, 923 long (>=2000), 240 short (>=1000) 60.0% (5949498 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1230 seqs, 923 long (>=2000), 307 short (>=1000) 61.0% (6048102 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1302 seqs, 923 long (>=2000), 379 short (>=1000) 62.0% (6146822 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1379 seqs, 923 long (>=2000), 456 short (>=1000) 63.0% (6246522 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1460 seqs, 923 long (>=2000), 537 short (>=1000) 64.0% (6345218 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1546 seqs, 923 long (>=2000), 623 short (>=1000) 65.0% (6443858 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1638 seqs, 923 long (>=2000), 715 short (>=1000) 66.0% (6543739 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1735 seqs, 923 long (>=2000), 799 short (>=1000) 67.0% (6642563 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1839 seqs, 923 long (>=2000), 799 short (>=1000) 68.0% (6741359 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 1950 seqs, 923 long (>=2000), 799 short (>=1000) 69.0% (6840591 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 2068 seqs, 923 long (>=2000), 799 short (>=1000) 70.0% (6939952 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 2193 seqs, 923 long (>=2000), 799 short (>=1000) 71.0% (7039087 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 2326 seqs, 923 long (>=2000), 799 short (>=1000) 72.0% (7138011 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 2468 seqs, 923 long (>=2000), 799 short (>=1000) 73.0% (7236979 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 2618 seqs, 923 long (>=2000), 799 short (>=1000) 74.0% (7336190 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 2776 seqs, 923 long (>=2000), 799 short (>=1000) 75.0% (7435717 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 2942 seqs, 923 long (>=2000), 799 short (>=1000) 76.0% (7534388 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 3121 seqs, 923 long (>=2000), 799 short (>=1000) 77.0% (7633817 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 3310 seqs, 923 long (>=2000), 799 short (>=1000) 78.0% (7732844 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 3510 seqs, 923 long (>=2000), 799 short (>=1000) 79.0% (7831670 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 3724 seqs, 923 long (>=2000), 799 short (>=1000) 80.0% (7930849 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 3952 seqs, 923 long (>=2000), 799 short (>=1000) 81.0% (8029940 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 4196 seqs, 923 long (>=2000), 799 short (>=1000) 82.0% (8129247 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 4456 seqs, 923 long (>=2000), 799 short (>=1000) 83.0% (8228320 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 4733 seqs, 923 long (>=2000), 799 short (>=1000) 84.0% (8327496 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 5026 seqs, 923 long (>=2000), 799 short (>=1000) 85.0% (8426507 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 5336 seqs, 923 long (>=2000), 799 short (>=1000) 86.0% (8525909 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 5663 seqs, 923 long (>=2000), 799 short (>=1000) 87.0% (8624790 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 6010 seqs, 923 long (>=2000), 799 short (>=1000) 88.0% (8723910 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 6372 seqs, 923 long (>=2000), 799 short (>=1000) 89.0% (8823160 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 6746 seqs, 923 long (>=2000), 799 short (>=1000) 90.0% (8922334 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 7134 seqs, 923 long (>=2000), 799 short (>=1000) 91.0% (9021452 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 7538 seqs, 923 long (>=2000), 799 short (>=1000) 92.0% (9120498 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 7962 seqs, 923 long (>=2000), 799 short (>=1000) 93.0% (9219591 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 8411 seqs, 923 long (>=2000), 799 short (>=1000) 94.0% (9318754 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] ... processed 8934 seqs, 923 long (>=2000), 799 short (>=1000) 95.0% (9417836 of 9913441), ETA 0:00:00     [19.9Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 923, and small contigs >= 1000 bp are 799                                                                  
[00:00:00] Allocating 923 contigs by 1 samples abundances [19.9Gb / 503.5Gb]
[00:00:00] Allocating 923 contigs by 1 samples variances [19.9Gb / 503.5Gb]
[00:00:00] Allocating 799 small contigs by 1 samples abundances [19.9Gb / 503.5Gb]
[00:00:00] Reading 0.000532Gb abundance file [19.9Gb / 503.5Gb]
[00:00:00] ... processed 86 lines 86 contigs and 0 short contigs 1.0% (5752 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 174 lines 174 contigs and 0 short contigs 2.0% (11466 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 263 lines 263 contigs and 0 short contigs 3.0% (17158 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 353 lines 353 contigs and 0 short contigs 4.0% (22888 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 443 lines 443 contigs and 0 short contigs 5.0% (28601 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 533 lines 533 contigs and 0 short contigs 6.0% (34308 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 623 lines 623 contigs and 0 short contigs 7.0% (40016 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 713 lines 713 contigs and 0 short contigs 8.0% (45707 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 803 lines 803 contigs and 0 short contigs 9.0% (51385 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 894 lines 894 contigs and 0 short contigs 10.0% (57108 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 984 lines 923 contigs and 61 short contigs 11.0% (62802 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1074 lines 923 contigs and 151 short contigs 12.0% (68546 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1163 lines 923 contigs and 240 short contigs 13.0% (74240 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1252 lines 923 contigs and 329 short contigs 14.0% (79947 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1341 lines 923 contigs and 418 short contigs 15.0% (85637 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1431 lines 923 contigs and 508 short contigs 16.0% (91386 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1520 lines 923 contigs and 597 short contigs 17.0% (97085 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1609 lines 923 contigs and 686 short contigs 18.0% (102781 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1698 lines 923 contigs and 775 short contigs 19.0% (108477 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 20.0% (114219 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 21.0% (119925 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 22.0% (125613 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 23.0% (131313 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 24.0% (137057 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 25.0% (142736 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 26.0% (148490 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 27.0% (154181 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 28.0% (159870 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 29.0% (165622 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 30.0% (171305 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 31.0% (176986 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 32.0% (182748 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 33.0% (188454 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 34.0% (194138 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 35.0% (199875 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 36.0% (205571 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 37.0% (211258 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 38.0% (216980 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 39.0% (222689 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 40.0% (228384 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 41.0% (234088 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 42.0% (239778 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 43.0% (245529 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 44.0% (251225 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 45.0% (256911 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 46.0% (262619 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 47.0% (268333 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 48.0% (274042 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 49.0% (279742 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 50.0% (285506 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 51.0% (291219 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 52.0% (296893 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 53.0% (302635 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 54.0% (308305 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 55.0% (314026 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 56.0% (319729 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 57.0% (325418 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 58.0% (331133 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 59.0% (336861 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 60.0% (342575 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 61.0% (348280 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 62.0% (353988 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 63.0% (359684 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 64.0% (365396 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 65.0% (371125 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 66.0% (376851 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 67.0% (382547 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 68.0% (388254 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 69.0% (393940 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 70.0% (399671 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 71.0% (405347 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 72.0% (411063 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 73.0% (416810 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 74.0% (422526 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 75.0% (428230 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 76.0% (433920 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 77.0% (439594 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 78.0% (445348 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 79.0% (451043 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 80.0% (456777 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 81.0% (462455 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 82.0% (468162 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 83.0% (473902 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 84.0% (479575 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 85.0% (485300 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 86.0% (491027 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 87.0% (496695 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 88.0% (502415 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 89.0% (508156 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 90.0% (513819 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 91.0% (519521 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 92.0% (525269 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 93.0% (530990 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 94.0% (536685 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 95.0% (542383 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 96.0% (548081 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 97.0% (553788 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 98.0% (559536 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1722 lines 923 contigs and 799 short contigs 99.0% (565191 of 570893), ETA 0:00:00     [19.9Gb / 503.5Gb]                 
[00:00:00] Finished reading 9192 contigs and 1 coverages from 03bins/metabat2_2kb/1507990/temp/1507990.depth.txt [19.9Gb / 503.5Gb]. Ignored 7470 too small contigs.                                     
[00:00:00] Number of target contigs: 923 of large (>= 2000) and 799 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 923
[00:00:00] Allocated memory for TNF [19.9Gb / 503.5Gb]
[00:00:00] Calculating TNF 2.5% (23 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 4.4% (41 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 6.4% (59 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 8.2% (76 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 10.0% (92 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 11.7% (108 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 13.5% (125 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 15.6% (144 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 17.0% (157 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 18.4% (170 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 19.9% (184 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 21.5% (198 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 23.2% (214 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 24.4% (225 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 26.8% (247 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 28.0% (258 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 30.2% (279 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 32.2% (297 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 33.9% (313 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 35.4% (327 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 37.6% (347 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 39.4% (364 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 41.4% (382 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 42.8% (395 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 44.9% (414 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 46.8% (432 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 49.3% (455 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 51.1% (472 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 52.2% (482 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 53.7% (496 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 55.9% (516 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 57.9% (534 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 60.2% (556 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 62.2% (574 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 63.9% (590 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 65.7% (606 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 67.4% (622 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 69.1% (638 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 71.3% (658 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 73.3% (677 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 75.1% (693 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 77.1% (712 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 79.2% (731 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 81.0% (748 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 83.2% (768 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 84.9% (784 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 87.1% (804 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 89.2% (823 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 91.3% (843 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 93.1% (859 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 95.1% (878 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 97.2% (897 of 923), ETA 0:00:00    
[00:00:00] Calculating TNF 99.1% (915 of 923), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [19.9Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.8% (17 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 4.0% (37 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 6.9% (64 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 8.7% (80 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 10.4% (96 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 12.1% (112 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 13.9% (128 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 15.6% (144 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 17.3% (160 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 19.1% (176 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 20.8% (192 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 22.5% (208 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.3% (224 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.0% (240 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.7% (256 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.5% (272 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.2% (288 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.9% (304 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.7% (320 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.4% (336 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 38.1% (352 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.9% (368 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.6% (384 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.3% (400 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 45.1% (416 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.8% (432 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.5% (448 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.3% (464 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.0% (480 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.7% (496 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.7% (514 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 57.4% (530 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.3% (547 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.2% (565 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.3% (584 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 65.7% (606 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 67.5% (623 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 69.2% (639 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 71.0% (655 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 72.7% (671 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.4% (687 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 76.2% (703 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 77.9% (719 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 79.6% (735 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 81.4% (751 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.1% (767 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 84.8% (783 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 86.6% (799 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 88.3% (815 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 90.0% (831 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 91.8% (847 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 93.5% (863 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 95.2% (879 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 97.0% (895 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 98.7% (911 of 923), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 99.9% (922 of 923), ETA 0:00:00                   
[00:00:00] Finding cutoff p=999 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=999 2.7% (25 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 12.1% (112 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 22.4% (207 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 28.8% (266 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 34.9% (322 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 41.2% (380 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 48.0% (443 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 53.2% (491 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.9% (525 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 60.2% (556 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 63.5% (586 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 66.1% (610 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 68.7% (634 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 71.8% (663 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 74.4% (687 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 76.6% (707 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 78.1% (721 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.6% (735 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 80.8% (746 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 81.9% (756 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 83.1% (767 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 84.3% (778 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 85.6% (790 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 86.7% (800 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 87.8% (810 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.9% (821 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 90.0% (831 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 91.1% (841 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 92.1% (850 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 93.3% (861 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 94.3% (870 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 97.7% (902 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 99.1% (915 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.9; 0 / 923 (P = 0.00%) round 1]               
[00:00:00] Finding cutoff p=997 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=997 4.4% (41 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 17.1% (158 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 30.0% (277 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 44.5% (411 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 56.3% (520 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 67.3% (621 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 77.2% (713 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 85.9% (793 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 89.2% (823 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 91.0% (840 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 92.8% (857 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 94.5% (872 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.7; 0 / 923 (P = 0.00%) round 2]               
[00:00:00] Finding cutoff p=995 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=995 3.0% (28 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 18.1% (167 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 29.5% (272 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 44.7% (413 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 54.9% (507 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 67.2% (620 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 77.7% (717 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 87.9% (811 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 90.9% (839 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 92.8% (857 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 94.5% (872 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.5; 2 / 923 (P = 0.22%) round 3]               
[00:00:00] Finding cutoff p=994 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=994 2.9% (27 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 26.1% (241 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 41.1% (379 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 54.5% (503 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 65.9% (608 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 77.0% (711 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 86.6% (799 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 90.8% (838 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 93.0% (858 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 94.4% (871 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.4; 4 / 923 (P = 0.43%) round 4]               
[00:00:00] Finding cutoff p=992 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=992 4.1% (38 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 15.4% (142 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 28.6% (264 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 42.5% (392 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 55.0% (508 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 66.1% (610 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 75.8% (700 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 85.8% (792 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 89.8% (829 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 92.0% (849 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 93.4% (862 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 94.7% (874 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.2; 14 / 923 (P = 1.52%) round 5]               
[00:00:00] Finding cutoff p=990 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=990 3.3% (30 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 14.3% (132 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 27.7% (256 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 39.1% (361 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 53.4% (493 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 64.0% (591 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 74.1% (684 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 88.8% (820 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 91.8% (847 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 93.5% (863 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.0; 22 / 923 (P = 2.38%) round 6]               
[00:00:00] Finding cutoff p=986 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=986 2.6% (24 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 15.2% (140 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 29.8% (275 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 42.9% (396 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 55.8% (515 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 66.7% (616 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 76.8% (709 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 85.9% (793 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 89.7% (828 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 92.4% (853 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 94.3% (870 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.6; 74 / 923 (P = 8.02%) round 7]               
[00:00:00] Finding cutoff p=983 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=983 2.9% (27 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 14.7% (136 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 27.6% (255 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 38.6% (356 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 50.6% (467 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 62.5% (577 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 72.4% (668 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 82.3% (760 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 88.6% (818 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 91.9% (848 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 93.6% (864 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.3; 96 / 923 (P = 10.40%) round 8]               
[00:00:00] Finding cutoff p=980 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=980 2.8% (26 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 14.5% (134 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 27.2% (251 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 42.6% (393 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 54.8% (506 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 65.3% (603 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 75.3% (695 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 84.7% (782 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 89.6% (827 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 92.3% (852 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 94.1% (869 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 95.1% (878 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=980 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.0; 121 / 923 (P = 13.11%) round 9]               
[00:00:00] Finding cutoff p=975 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=975 1.8% (17 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 14.7% (136 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 27.8% (257 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 40.1% (370 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 52.0% (480 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 63.2% (583 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 71.3% (658 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 81.7% (754 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 87.1% (804 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 90.6% (836 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 93.4% (862 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 94.9% (876 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.5; 167 / 923 (P = 18.09%) round 10]               
[00:00:00] Finding cutoff p=970 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=970 2.6% (24 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 14.4% (133 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 24.9% (230 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 39.9% (368 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 50.8% (469 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 61.8% (570 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 71.3% (658 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 81.0% (748 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 86.9% (802 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 90.6% (836 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 92.8% (857 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 94.6% (873 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=970 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.0; 211 / 923 (P = 22.86%) round 11]               
[00:00:00] Finding cutoff p=965 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=965 2.7% (25 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 13.8% (127 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 26.0% (240 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 36.9% (341 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 48.3% (446 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 59.4% (548 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 69.0% (637 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 79.2% (731 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 85.2% (786 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 89.6% (827 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 92.2% (851 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 94.3% (870 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.5; 251 / 923 (P = 27.19%) round 12]               
[00:00:00] Finding cutoff p=961 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=961 3.4% (31 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 22.2% (205 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 31.3% (289 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 41.3% (381 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 52.4% (484 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 61.1% (564 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 70.6% (652 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 78.8% (727 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 85.5% (789 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 89.6% (827 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 92.2% (851 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 94.3% (870 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=961 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.1; 279 / 923 (P = 30.23%) round 13]               
[00:00:00] Finding cutoff p=956 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=956 2.4% (22 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 11.9% (110 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 23.1% (213 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 32.5% (300 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 42.4% (391 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 50.9% (470 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 61.1% (564 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 70.1% (647 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 78.2% (722 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 84.9% (784 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 89.2% (823 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 91.8% (847 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 94.3% (870 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.6; 309 / 923 (P = 33.48%) round 14]               
[00:00:00] Finding cutoff p=951 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=951 2.7% (25 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 13.1% (121 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 22.9% (211 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 37.9% (350 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 50.4% (465 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 60.0% (554 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 69.2% (639 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 76.4% (705 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 82.2% (759 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 86.5% (798 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 90.1% (832 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 93.2% (860 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 95.1% (878 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.1; 345 / 923 (P = 37.38%) round 15]               
[00:00:00] Finding cutoff p=948 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=948 3.8% (35 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 12.8% (118 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 24.2% (223 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 32.5% (300 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 42.1% (389 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 51.4% (474 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 61.4% (567 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 70.0% (646 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 77.5% (715 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 84.3% (778 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 88.5% (817 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 92.4% (853 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.8; 363 / 923 (P = 39.33%) round 16]               
[00:00:00] Finding cutoff p=944 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=944 3.1% (29 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 14.0% (129 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 21.7% (200 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 30.1% (278 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 38.5% (355 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 54.1% (499 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 63.5% (586 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 69.9% (645 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 75.9% (701 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 82.9% (765 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 87.9% (811 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 91.7% (846 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 93.8% (866 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=944 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.4; 383 / 923 (P = 41.50%) round 17]               
[00:00:00] Finding cutoff p=940 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=940 2.4% (22 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 13.2% (122 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 20.8% (192 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 30.8% (284 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 37.8% (349 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 47.5% (438 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 55.1% (509 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 63.7% (588 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 71.9% (664 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 77.6% (716 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 83.2% (768 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 88.4% (816 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 92.5% (854 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 95.0% (877 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 95.4% (881 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.0; 411 / 923 (P = 44.53%) round 18]               
[00:00:00] Finding cutoff p=935 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=935 3.6% (33 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 13.3% (123 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 22.6% (209 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 30.9% (285 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 37.4% (345 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 46.8% (432 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 61.9% (571 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 69.8% (644 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 76.2% (703 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 82.0% (757 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 85.6% (790 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 90.6% (836 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 93.2% (860 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 95.0% (877 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 95.4% (881 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.5; 433 / 923 (P = 46.91%) round 19]               
[00:00:00] Finding cutoff p=930 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=930 3.3% (30 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 12.4% (114 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 20.2% (186 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 27.8% (257 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 34.0% (314 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 42.4% (391 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 50.8% (469 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 58.6% (541 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 65.9% (608 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 72.9% (673 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 78.4% (724 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 82.9% (765 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 86.6% (799 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 92.5% (854 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 94.4% (871 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 95.4% (881 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.0; 468 / 923 (P = 50.70%) round 20]               
[00:00:00] Finding cutoff p=927 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=927 2.9% (27 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 12.6% (116 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 20.0% (185 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 29.0% (268 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 42.8% (395 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 50.6% (467 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 57.3% (529 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 63.6% (587 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 70.2% (648 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 76.3% (704 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 81.4% (751 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 86.0% (794 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 91.1% (841 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 93.8% (866 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 95.2% (879 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 95.6% (882 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.7; 490 / 923 (P = 53.09%) round 21]               
[00:00:00] Finding cutoff p=922 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=922 2.8% (26 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 12.0% (111 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 19.0% (175 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 26.5% (245 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 34.5% (318 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 43.0% (397 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 49.5% (457 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 56.0% (517 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 63.4% (585 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 71.6% (661 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 78.7% (726 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 82.8% (764 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 86.8% (801 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 91.3% (843 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 93.7% (865 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 95.7% (883 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=922 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.2; 507 / 923 (P = 54.93%) round 22]               
[00:00:00] Finding cutoff p=917 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=917 2.3% (21 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 21.5% (198 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 28.3% (261 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 35.8% (330 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 41.3% (381 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 49.2% (454 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 57.1% (527 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 63.8% (589 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 69.9% (645 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 75.7% (699 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 80.3% (741 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 84.4% (779 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 90.2% (833 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 93.5% (863 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 95.6% (882 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.7; 528 / 923 (P = 57.20%) round 23]               
[00:00:00] Finding cutoff p=912 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=912 2.2% (20 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 11.1% (102 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 16.7% (154 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 23.7% (219 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 31.2% (288 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 38.4% (354 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 44.6% (412 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 49.8% (460 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 57.0% (526 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 64.4% (594 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 70.0% (646 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 74.6% (689 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 79.3% (732 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 85.7% (791 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 89.2% (823 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 92.7% (856 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 94.5% (872 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 95.7% (883 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.2; 546 / 923 (P = 59.15%) round 24]               
[00:00:00] Finding cutoff p=909 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=909 2.9% (27 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 10.6% (98 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 18.6% (172 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 25.7% (237 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 31.4% (290 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 38.2% (353 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 43.4% (401 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 50.6% (467 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 55.5% (512 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 63.2% (583 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 68.8% (635 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 73.3% (677 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 77.9% (719 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 83.2% (768 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 88.4% (816 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 92.0% (849 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 94.7% (874 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 95.8% (884 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=909 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.9; 554 / 923 (P = 60.02%) round 25]               
[00:00:00] Finding cutoff p=905 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=905 1.8% (17 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 10.3% (95 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 18.1% (167 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 23.3% (215 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 30.2% (279 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 36.4% (336 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 41.7% (385 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 47.8% (441 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 56.6% (522 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 64.5% (595 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 69.3% (640 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 74.2% (685 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 78.3% (723 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 83.0% (766 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 89.5% (826 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 92.6% (855 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 94.0% (868 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 94.6% (873 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.5; 565 / 923 (P = 61.21%) round 26]               
[00:00:00] Finding cutoff p=901 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=901 2.2% (20 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 8.0% (74 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 15.4% (142 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 20.4% (188 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 26.8% (247 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 33.8% (312 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 41.3% (381 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 47.0% (434 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 54.6% (504 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 60.6% (559 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 65.3% (603 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 70.6% (652 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 76.1% (702 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 81.1% (749 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 85.5% (789 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 90.2% (833 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 93.2% (860 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 94.4% (871 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 95.6% (882 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=901 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.1; 577 / 923 (P = 62.51%) round 27]               
[00:00:00] Finding cutoff p=896 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=896 2.2% (20 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 8.2% (76 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 14.2% (131 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 21.8% (201 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 28.7% (265 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 35.0% (323 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 45.5% (420 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 50.5% (466 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 57.1% (527 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 65.0% (600 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 69.8% (644 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 74.4% (687 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 78.7% (726 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 82.4% (761 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 87.0% (803 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 91.0% (840 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 94.3% (870 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 95.6% (882 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=896 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.6; 584 / 923 (P = 63.27%) round 28]               
[00:00:00] Finding cutoff p=886 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=886 2.6% (24 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 10.1% (93 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 16.7% (154 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 23.3% (215 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 29.5% (272 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 36.4% (336 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 41.1% (379 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 47.8% (441 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 54.7% (505 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 63.4% (585 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 69.1% (638 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 73.0% (674 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 77.4% (714 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 82.9% (765 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 86.3% (797 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 90.0% (831 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 93.5% (863 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 95.3% (880 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=886 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 88.6; 596 / 923 (P = 64.57%) round 29]               
[00:00:00] Finding cutoff p=875 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=875 1.8% (17 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 8.0% (74 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 13.4% (124 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 19.4% (179 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 25.0% (231 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 32.1% (296 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 38.4% (354 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 44.1% (407 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 49.3% (455 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 55.1% (509 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 60.0% (554 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 65.1% (601 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 70.1% (647 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 74.9% (691 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 81.3% (750 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 86.2% (796 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 89.7% (828 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 91.8% (847 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 93.9% (867 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 95.9% (885 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=875 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 87.5; 616 / 923 (P = 66.74%) round 30]               
[00:00:00] Finding cutoff p=865 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=865 2.0% (18 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 8.1% (75 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 13.9% (128 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 20.8% (192 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 25.2% (233 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 33.4% (308 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 39.2% (362 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 45.2% (417 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 52.0% (480 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 55.8% (515 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 60.5% (558 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 67.1% (619 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 72.5% (669 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 77.5% (715 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 81.5% (752 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 89.5% (826 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 92.5% (854 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 94.5% (872 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 95.7% (883 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=865 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 86.5; 628 / 923 (P = 68.04%) round 31]               
[00:00:00] Finding cutoff p=855 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=855 2.3% (21 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 8.5% (78 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 13.9% (128 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 19.3% (178 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 26.8% (247 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 32.2% (297 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 37.2% (343 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 43.1% (398 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 48.5% (448 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 57.6% (532 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 62.6% (578 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 67.2% (620 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 73.1% (675 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 78.7% (726 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 83.2% (768 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 88.2% (814 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 92.0% (849 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 94.4% (871 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 95.6% (882 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=855 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.5; 643 / 923 (P = 69.66%) round 32]               
[00:00:00] Finding cutoff p=846 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=846 1.6% (15 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 8.2% (76 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 14.1% (130 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 19.8% (183 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 25.1% (232 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 33.6% (310 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 39.9% (368 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 49.1% (453 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 54.6% (504 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 60.1% (555 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 66.4% (613 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 71.8% (663 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 74.5% (688 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 78.8% (727 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 82.7% (763 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 85.8% (792 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 89.8% (829 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 92.3% (852 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 95.0% (877 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 96.0% (886 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.6; 651 / 923 (P = 70.53%) round 33]               
[00:00:00] Finding cutoff p=835 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=835 2.3% (21 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 7.8% (72 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 13.5% (125 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 17.1% (158 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 22.9% (211 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 28.6% (264 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 34.0% (314 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 42.0% (388 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 46.4% (428 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 52.9% (488 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 58.4% (539 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 62.8% (580 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 68.0% (628 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 73.7% (680 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 76.4% (705 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 80.7% (745 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 86.6% (799 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 89.2% (823 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 91.9% (848 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 93.9% (867 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 96.1% (887 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 83.5; 685 / 923 (P = 74.21%) round 34]               
[00:00:00] Finding cutoff p=825 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=825 2.2% (20 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 8.0% (74 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 12.2% (113 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 18.1% (167 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 23.2% (214 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 30.1% (278 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 34.5% (318 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 38.6% (356 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 43.4% (401 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 50.5% (466 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 55.3% (510 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 58.5% (540 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 64.7% (597 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 70.0% (646 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 73.2% (676 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 77.2% (713 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 81.4% (751 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 84.4% (779 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 87.4% (807 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 90.6% (836 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 94.1% (869 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 96.1% (887 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.5; 714 / 923 (P = 77.36%) round 35]               
[00:00:00] Finding cutoff p=816 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=816 2.4% (22 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 8.7% (80 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 11.9% (110 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 19.0% (175 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 24.4% (225 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 30.8% (284 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 35.0% (323 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 39.8% (367 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 45.0% (415 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 53.5% (494 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 58.1% (536 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 62.7% (579 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 68.5% (632 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 73.3% (677 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 76.7% (708 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 80.1% (739 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 83.6% (772 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 86.2% (796 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 89.1% (822 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 91.4% (844 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 93.4% (862 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 95.1% (878 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 96.3% (889 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=816 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 81.6; 726 / 923 (P = 78.66%) round 36]               
[00:00:00] Finding cutoff p=805 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=805 2.3% (21 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 8.5% (78 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 11.9% (110 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 17.2% (159 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 22.4% (207 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 29.4% (271 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 33.9% (313 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 39.1% (361 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 43.6% (402 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 48.1% (444 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 52.4% (484 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 56.0% (517 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 61.9% (571 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 67.3% (621 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 70.7% (653 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 74.4% (687 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 78.5% (725 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 81.6% (753 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 84.0% (775 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 87.9% (811 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 91.3% (843 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 92.8% (857 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 96.2% (888 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=805 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.5; 743 / 923 (P = 80.50%) round 37]               
[00:00:00] Finding cutoff p=796 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=796 2.1% (19 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 6.6% (61 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 10.4% (96 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 14.0% (129 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 20.2% (186 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 24.7% (228 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 28.4% (262 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 32.9% (304 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 37.7% (348 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 42.5% (392 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 46.3% (427 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 49.7% (459 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 54.8% (506 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 61.0% (563 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 64.7% (597 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 69.2% (639 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 74.6% (689 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 78.5% (725 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 81.7% (754 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 84.5% (780 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 88.9% (821 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 91.2% (842 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 93.2% (860 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 94.7% (874 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 96.4% (890 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=796 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 79.6; 758 / 923 (P = 82.12%) round 38]               
[00:00:00] Finding cutoff p=786 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=786 1.6% (15 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 6.5% (60 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 9.6% (89 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 13.7% (126 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 18.2% (168 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 26.3% (243 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 31.7% (293 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 37.2% (343 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 40.3% (372 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 46.5% (429 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 49.5% (457 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 53.1% (490 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 57.9% (534 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 62.0% (572 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 65.9% (608 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 69.4% (641 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 74.4% (687 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 77.4% (714 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 80.2% (740 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 82.3% (760 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 87.0% (803 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 88.6% (818 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 90.6% (836 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 92.7% (856 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 94.8% (875 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 96.3% (889 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 96.7% (893 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=786 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.6; 767 / 923 (P = 83.10%) round 39]               
[00:00:00] Finding cutoff p=777 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=777 1.7% (16 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 7.2% (66 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 10.3% (95 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 14.0% (129 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 18.0% (166 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 23.2% (214 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 27.0% (249 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 32.8% (303 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 35.8% (330 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 41.2% (380 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 44.1% (407 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 48.2% (445 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 53.1% (490 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 58.9% (544 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 62.1% (573 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 69.0% (637 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 73.9% (682 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 76.6% (707 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 80.3% (741 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 83.4% (770 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 86.8% (801 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 89.5% (826 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 91.7% (846 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 93.7% (865 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 95.8% (884 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 96.6% (892 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=777 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 77.7; 780 / 923 (P = 84.51%) round 40]               
[00:00:00] Finding cutoff p=768 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=768 2.2% (20 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 6.0% (55 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 8.6% (79 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 12.1% (112 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 17.1% (158 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 22.8% (210 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 27.1% (250 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 30.4% (281 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 36.1% (333 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 40.4% (373 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 43.6% (402 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 46.5% (429 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 50.5% (466 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 55.8% (515 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 60.1% (555 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 63.6% (587 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 68.6% (633 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 72.5% (669 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 75.6% (698 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 79.5% (734 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 84.2% (777 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 87.1% (804 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 89.8% (829 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 92.0% (849 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 94.1% (869 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 96.6% (892 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=768 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 76.8; 792 / 923 (P = 85.81%) round 41]               
[00:00:00] Finding cutoff p=759 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=759 2.1% (19 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 6.3% (58 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 10.4% (96 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 15.6% (144 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 20.2% (186 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 25.1% (232 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 29.6% (273 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 33.6% (310 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 36.1% (333 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 41.2% (380 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 44.6% (412 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 46.9% (433 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 51.8% (478 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 56.7% (523 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 60.3% (557 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 63.5% (586 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 68.8% (635 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 71.6% (661 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 74.2% (685 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 78.3% (723 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 83.9% (774 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 86.1% (795 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 89.1% (822 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 90.4% (834 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 93.6% (864 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 96.0% (886 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 96.7% (893 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=759 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 75.9; 807 / 923 (P = 87.43%) round 42]               
[00:00:00] Finding cutoff p=748 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=748 2.1% (19 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 5.9% (54 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 9.0% (83 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 12.4% (114 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 19.6% (181 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 24.1% (222 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 27.2% (251 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 31.3% (289 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 35.4% (327 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 40.0% (369 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 43.6% (402 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 46.8% (432 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 49.7% (459 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 55.4% (511 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 58.7% (542 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 61.8% (570 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 65.7% (606 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 69.1% (638 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 71.7% (662 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 75.0% (692 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 80.0% (738 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 81.5% (752 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 84.8% (783 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 87.3% (806 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 89.7% (828 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 93.4% (862 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 95.9% (885 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 96.9% (894 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=748 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 74.8; 818 / 923 (P = 88.62%) round 43]               
[00:00:00] Finding cutoff p=738 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=738 2.6% (24 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 7.0% (65 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 11.1% (102 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 13.8% (127 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 18.4% (170 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 23.0% (212 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 26.8% (247 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 30.6% (282 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 33.0% (305 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 38.6% (356 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 42.0% (388 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 45.7% (422 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 49.9% (461 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 57.1% (527 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 60.8% (561 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 64.0% (591 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 69.9% (645 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 72.3% (667 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 74.8% (690 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 77.1% (712 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 81.0% (748 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 83.9% (774 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 86.1% (795 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 88.3% (815 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 91.2% (842 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 93.3% (861 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 95.2% (879 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 96.5% (891 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=738 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 73.8; 824 / 923 (P = 89.27%) round 44]               
[00:00:00] Finding cutoff p=728 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=728 2.7% (25 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 6.1% (56 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 9.9% (91 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 12.7% (117 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 16.4% (151 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 21.0% (194 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 24.4% (225 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 28.3% (261 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 31.7% (293 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 36.1% (333 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 39.3% (363 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 43.1% (398 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 47.1% (435 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 52.5% (485 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 55.4% (511 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 58.5% (540 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 61.6% (569 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 64.2% (593 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 67.4% (622 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 70.9% (654 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 74.6% (689 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 77.8% (718 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 83.6% (772 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 85.9% (793 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 88.4% (816 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 91.7% (846 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 93.7% (865 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 95.2% (879 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 96.3% (889 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 97.0% (895 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=728 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 72.8; 831 / 923 (P = 90.03%) round 45]               
[00:00:00] Finding cutoff p=717 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=717 2.5% (23 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 6.4% (59 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 10.0% (92 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 13.4% (124 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 17.9% (165 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 21.9% (202 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 24.9% (230 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 28.7% (265 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 31.9% (294 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 37.1% (342 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 39.2% (362 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 43.6% (402 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 46.7% (431 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 50.5% (466 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 53.7% (496 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 55.7% (514 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 60.6% (559 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 63.5% (586 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 66.2% (611 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 69.3% (640 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 74.0% (683 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 76.8% (709 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 80.3% (741 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 82.7% (763 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 86.1% (795 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 89.4% (825 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 92.4% (853 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 94.4% (871 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 96.5% (891 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=717 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 71.7; 836 / 923 (P = 90.57%) round 46]               
[00:00:00] Finding cutoff p=706 [19.9Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=706 3.8% (35 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 7.5% (69 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 10.7% (99 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 14.1% (130 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 18.1% (167 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 22.2% (205 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 25.5% (235 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 30.6% (282 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 33.3% (307 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 36.6% (338 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 40.4% (373 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 42.7% (394 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 46.8% (432 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 50.9% (470 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 54.1% (499 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 57.3% (529 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 61.8% (570 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 65.2% (602 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 67.3% (621 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 70.9% (654 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 74.2% (685 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 77.5% (715 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 80.4% (742 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 82.3% (760 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 85.9% (793 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 88.5% (817 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 91.0% (840 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 92.8% (857 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 94.4% (871 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 95.7% (883 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 97.0% (895 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 97.5% (900 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 98.6% (910 of 923), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=706 99.7% (920 of 923), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 70.6; 843 / 923 (P = 91.33%) round 47]               
[00:00:00] Finished Preparing TNF Graph Building [pTNF = 69.70] [19.9Gb / 503.5Gb]                                            
[00:00:00] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=923 maxEdges=200
[00:00:00] Building TNF Graph 100.4% (927 of 923), ETA 0:00:00     [19.9Gb / 503.5Gb]                           
[00:00:00] Finished Building TNF Graph (34529 edges) [19.9Gb / 503.5Gb]                                          
[00:00:00] Cleaned up after Building TNF Graph (34529 edges) [19.9Gb / 503.5Gb]                                          
[00:00:00] Cleaned up TNF matrix of large contigs [19.9Gb / 503.5Gb]                                             
[00:00:00] Applying coverage correlations to TNF graph with 34529 edges
[00:00:00] Allocated memory for graph edges [19.9Gb / 503.5Gb]
[00:00:00] ... calculating abundance dist 1.0% (359 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 2.0% (692 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 3.0% (1042 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 4.0% (1391 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 5.0% (1737 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 6.1% (2103 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 7.0% (2434 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 8.1% (2780 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 9.1% (3127 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 10.0% (3463 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 11.0% (3807 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 12.0% (4155 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 13.1% (4524 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 14.1% (4855 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 15.1% (5199 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 16.1% (5543 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 17.1% (5892 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 18.1% (6241 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 19.0% (6574 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 20.1% (6930 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 21.1% (7274 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 22.0% (7613 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 23.1% (7962 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 24.1% (8319 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 25.1% (8662 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 26.1% (9000 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 27.1% (9346 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 28.1% (9691 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 29.1% (10046 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 30.1% (10391 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 31.1% (10728 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 32.1% (11081 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 33.1% (11422 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 34.1% (11768 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 35.1% (12119 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 36.1% (12463 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 37.1% (12821 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 38.1% (13151 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 39.1% (13497 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 40.1% (13856 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 41.1% (14187 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 42.1% (14537 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 43.1% (14882 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 44.1% (15237 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 45.1% (15584 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 46.1% (15916 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 47.1% (16272 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 48.1% (16608 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 49.1% (16965 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 50.1% (17301 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 51.1% (17649 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 52.2% (18010 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 53.1% (18344 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 54.1% (18696 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 55.1% (19030 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 56.1% (19380 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 57.1% (19732 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 58.2% (20080 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 59.2% (20432 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 60.1% (20767 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 61.2% (21118 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 62.2% (21462 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 63.2% (21823 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 64.2% (22169 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 65.1% (22492 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 66.2% (22846 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 67.2% (23192 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 68.2% (23542 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 69.2% (23879 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 70.2% (24236 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 71.2% (24573 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 72.2% (24925 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 73.2% (25270 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 74.2% (25604 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 75.2% (25970 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 76.2% (26305 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 77.2% (26657 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 78.2% (27000 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 79.2% (27334 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 80.2% (27683 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 81.2% (28033 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 82.2% (28384 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 83.2% (28719 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 84.2% (29075 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 85.2% (29422 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 86.2% (29769 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 87.2% (30104 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 88.2% (30454 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 89.2% (30805 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 90.2% (31161 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 91.2% (31496 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 92.2% (31841 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 93.2% (32181 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 94.2% (32526 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 95.2% (32870 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 96.2% (33222 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 97.2% (33568 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 98.2% (33908 of 34529), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 99.2% (34254 of 34529), ETA 0:00:00                              
[00:00:00] Calculating geometric means [19.9Gb / 503.5Gb]
[00:00:00] Traversing graph with 923 nodes and 34529 edges [19.9Gb / 503.5Gb]
[00:00:00] Building SCR Graph and Binning (89 vertices and 74 edges) [P = 9.50%; 19.9Gb / 503.5Gb]                           
[00:00:00] Building SCR Graph and Binning (176 vertices and 212 edges) [P = 19.00%; 19.9Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 1.0% (346 of 34529), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (265 vertices and 519 edges) [P = 28.50%; 19.9Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 2.0% (692 of 34529), ETA 0:00:00                               
[00:00:00] ... traversing graph 3.0% (1038 of 34529), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (351 vertices and 956 edges) [P = 38.00%; 19.9Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 4.0% (1384 of 34529), ETA 0:00:00                               
[00:00:00] ... traversing graph 5.0% (1730 of 34529), ETA 0:00:00                               
[00:00:00] ... traversing graph 6.0% (2076 of 34529), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (440 vertices and 1868 edges) [P = 47.50%; 19.9Gb / 503.5Gb]                           
[00:00:00] Building SCR Graph and Binning (454 vertices and 1997 edges) [P = 57.00%; 19.9Gb / 503.5Gb]                           
[00:00:00] Finished Traversing graph [19.9Gb / 503.5Gb]                                       
[00:00:00] Dissolved 0 small clusters leaving 0 leftover contigs to be re-merged into larger clusters
[00:00:00] Rescuing singleton large contigs                                   
[00:00:00] There are 533 bins already
[00:00:00] Outputting bins
[00:00:00] Writing cluster stats to: 03bins/metabat2_2kb/1507990/1507990.bin.BinInfo.txt
[00:00:03] 100.00% (5529983 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
533 bins (5529983 bases in total) formed.
[00:00:03] Finished
MetaBAT2 generated 533 bins for 1507990
MetaBAT2 binning completed for 1507990
