Decompressing scaffold file for 1507996...
Generating depth file for 1507996...
Running MetaBAT2 for 1507996 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 200000. with random seed=123
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [55.1Gb / 503.5Gb]
[00:00:00] Parsing assembly file [55.1Gb / 503.5Gb]
[00:00:00] ... processed 10 seqs, 10 long (>=2000), 0 short (>=1000) 1.0% (490145 of 47619868), ETA 0:00:01     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 24 seqs, 24 long (>=2000), 0 short (>=1000) 2.0% (969145 of 47619868), ETA 0:00:01     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 41 seqs, 41 long (>=2000), 0 short (>=1000) 3.0% (1451933 of 47619868), ETA 0:00:01     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 60 seqs, 60 long (>=2000), 0 short (>=1000) 4.0% (1928459 of 47619868), ETA 0:00:01     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 80 seqs, 80 long (>=2000), 0 short (>=1000) 5.0% (2387585 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 102 seqs, 102 long (>=2000), 0 short (>=1000) 6.0% (2858969 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 127 seqs, 127 long (>=2000), 0 short (>=1000) 7.0% (3347989 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 153 seqs, 153 long (>=2000), 0 short (>=1000) 8.0% (3818140 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 180 seqs, 180 long (>=2000), 0 short (>=1000) 9.0% (4286324 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 210 seqs, 210 long (>=2000), 0 short (>=1000) 10.0% (4775682 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 240 seqs, 240 long (>=2000), 0 short (>=1000) 11.0% (5238354 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 273 seqs, 273 long (>=2000), 0 short (>=1000) 12.0% (5728070 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 306 seqs, 306 long (>=2000), 0 short (>=1000) 13.0% (6194410 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 342 seqs, 342 long (>=2000), 0 short (>=1000) 14.0% (6676933 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 379 seqs, 379 long (>=2000), 0 short (>=1000) 15.0% (7151570 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 417 seqs, 417 long (>=2000), 0 short (>=1000) 16.0% (7621725 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 457 seqs, 457 long (>=2000), 0 short (>=1000) 17.0% (8097667 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 499 seqs, 499 long (>=2000), 0 short (>=1000) 18.0% (8575764 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 543 seqs, 543 long (>=2000), 0 short (>=1000) 19.0% (9056971 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 587 seqs, 587 long (>=2000), 0 short (>=1000) 20.0% (9525516 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 634 seqs, 634 long (>=2000), 0 short (>=1000) 21.0% (10008100 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 682 seqs, 682 long (>=2000), 0 short (>=1000) 22.0% (10483719 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 732 seqs, 732 long (>=2000), 0 short (>=1000) 23.0% (10961898 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 783 seqs, 783 long (>=2000), 0 short (>=1000) 24.0% (11432732 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 836 seqs, 836 long (>=2000), 0 short (>=1000) 25.0% (11907641 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 891 seqs, 891 long (>=2000), 0 short (>=1000) 26.0% (12386867 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 947 seqs, 947 long (>=2000), 0 short (>=1000) 27.0% (12858174 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1006 seqs, 1006 long (>=2000), 0 short (>=1000) 28.0% (13338270 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1067 seqs, 1067 long (>=2000), 0 short (>=1000) 29.0% (13816541 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1129 seqs, 1129 long (>=2000), 0 short (>=1000) 30.0% (14287904 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1194 seqs, 1194 long (>=2000), 0 short (>=1000) 31.0% (14768654 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1260 seqs, 1260 long (>=2000), 0 short (>=1000) 32.0% (15244860 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1328 seqs, 1328 long (>=2000), 0 short (>=1000) 33.0% (15719954 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1398 seqs, 1398 long (>=2000), 0 short (>=1000) 34.0% (16193273 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1471 seqs, 1471 long (>=2000), 0 short (>=1000) 35.0% (16669832 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1546 seqs, 1546 long (>=2000), 0 short (>=1000) 36.0% (17143215 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1625 seqs, 1625 long (>=2000), 0 short (>=1000) 37.0% (17624820 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1705 seqs, 1705 long (>=2000), 0 short (>=1000) 38.0% (18096390 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1789 seqs, 1789 long (>=2000), 0 short (>=1000) 39.0% (18576067 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1875 seqs, 1875 long (>=2000), 0 short (>=1000) 40.0% (19052052 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 1964 seqs, 1964 long (>=2000), 0 short (>=1000) 41.0% (19528890 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2056 seqs, 2056 long (>=2000), 0 short (>=1000) 42.0% (20001582 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2153 seqs, 2153 long (>=2000), 0 short (>=1000) 43.0% (20480123 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2253 seqs, 2253 long (>=2000), 0 short (>=1000) 44.0% (20956267 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2357 seqs, 2357 long (>=2000), 0 short (>=1000) 45.0% (21432938 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2465 seqs, 2465 long (>=2000), 0 short (>=1000) 46.0% (21908670 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2577 seqs, 2577 long (>=2000), 0 short (>=1000) 47.0% (22384906 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2693 seqs, 2693 long (>=2000), 0 short (>=1000) 48.0% (22860186 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2813 seqs, 2813 long (>=2000), 0 short (>=1000) 49.0% (23335160 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 2938 seqs, 2938 long (>=2000), 0 short (>=1000) 50.0% (23810926 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3068 seqs, 3068 long (>=2000), 0 short (>=1000) 51.0% (24286342 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3204 seqs, 3204 long (>=2000), 0 short (>=1000) 52.0% (24764708 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3345 seqs, 3345 long (>=2000), 0 short (>=1000) 53.0% (25238977 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3494 seqs, 3494 long (>=2000), 0 short (>=1000) 54.0% (25717663 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3649 seqs, 3649 long (>=2000), 0 short (>=1000) 55.0% (26193345 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3811 seqs, 3811 long (>=2000), 0 short (>=1000) 56.0% (26669258 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 3981 seqs, 3981 long (>=2000), 0 short (>=1000) 57.0% (27145312 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 4160 seqs, 4160 long (>=2000), 0 short (>=1000) 58.0% (27620612 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 4349 seqs, 4349 long (>=2000), 0 short (>=1000) 59.0% (28096381 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 4549 seqs, 4549 long (>=2000), 0 short (>=1000) 60.0% (28572752 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 4761 seqs, 4761 long (>=2000), 0 short (>=1000) 61.0% (29049358 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 4988 seqs, 4988 long (>=2000), 0 short (>=1000) 62.0% (29526086 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 5231 seqs, 5017 long (>=2000), 214 short (>=1000) 63.0% (30000555 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 5493 seqs, 5017 long (>=2000), 476 short (>=1000) 64.0% (30477147 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 5775 seqs, 5017 long (>=2000), 758 short (>=1000) 65.0% (30954432 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 6078 seqs, 5017 long (>=2000), 1061 short (>=1000) 66.0% (31429384 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 6404 seqs, 5017 long (>=2000), 1387 short (>=1000) 67.0% (31905882 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 6753 seqs, 5017 long (>=2000), 1736 short (>=1000) 68.0% (32382759 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 7126 seqs, 5017 long (>=2000), 2109 short (>=1000) 69.0% (32858311 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 7529 seqs, 5017 long (>=2000), 2512 short (>=1000) 70.0% (33334215 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 7964 seqs, 5017 long (>=2000), 2947 short (>=1000) 71.0% (33810479 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 8429 seqs, 5017 long (>=2000), 3348 short (>=1000) 72.0% (34286486 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 8925 seqs, 5017 long (>=2000), 3348 short (>=1000) 73.0% (34763232 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 9454 seqs, 5017 long (>=2000), 3348 short (>=1000) 74.0% (35239022 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 10021 seqs, 5017 long (>=2000), 3348 short (>=1000) 75.0% (35715075 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 10629 seqs, 5017 long (>=2000), 3348 short (>=1000) 76.0% (36191721 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 11281 seqs, 5017 long (>=2000), 3348 short (>=1000) 77.0% (36667493 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 11984 seqs, 5017 long (>=2000), 3348 short (>=1000) 78.0% (37144021 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 12741 seqs, 5017 long (>=2000), 3348 short (>=1000) 79.0% (37619906 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 13560 seqs, 5017 long (>=2000), 3348 short (>=1000) 80.0% (38095952 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 14449 seqs, 5017 long (>=2000), 3348 short (>=1000) 81.0% (38572292 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 15415 seqs, 5017 long (>=2000), 3348 short (>=1000) 82.0% (39048537 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 16468 seqs, 5017 long (>=2000), 3348 short (>=1000) 83.0% (39524613 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 17619 seqs, 5017 long (>=2000), 3348 short (>=1000) 84.0% (40000771 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 18879 seqs, 5017 long (>=2000), 3348 short (>=1000) 85.0% (40477218 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 20249 seqs, 5017 long (>=2000), 3348 short (>=1000) 86.0% (40953312 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 21748 seqs, 5017 long (>=2000), 3348 short (>=1000) 87.0% (41429600 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 23400 seqs, 5017 long (>=2000), 3348 short (>=1000) 88.0% (41905735 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 25213 seqs, 5017 long (>=2000), 3348 short (>=1000) 89.0% (42381929 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 27184 seqs, 5017 long (>=2000), 3348 short (>=1000) 90.0% (42857986 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 29342 seqs, 5017 long (>=2000), 3348 short (>=1000) 91.0% (43334317 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 31798 seqs, 5017 long (>=2000), 3348 short (>=1000) 92.0% (43810454 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 34641 seqs, 5017 long (>=2000), 3348 short (>=1000) 93.0% (44286611 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 38011 seqs, 5017 long (>=2000), 3348 short (>=1000) 94.0% (44762763 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] ... processed 43277 seqs, 5017 long (>=2000), 3348 short (>=1000) 95.0% (45238913 of 47619868), ETA 0:00:00     [55.1Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 5017, and small contigs >= 1000 bp are 3348                                                                  
[00:00:00] Allocating 5017 contigs by 1 samples abundances [55.1Gb / 503.5Gb]
[00:00:00] Allocating 5017 contigs by 1 samples variances [55.1Gb / 503.5Gb]
[00:00:00] Allocating 3348 small contigs by 1 samples abundances [55.1Gb / 503.5Gb]
[00:00:00] Reading 0.002554Gb abundance file [55.1Gb / 503.5Gb]
[00:00:00] ... processed 417 lines 417 contigs and 0 short contigs 1.0% (27436 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 841 lines 841 contigs and 0 short contigs 2.0% (54900 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 1267 lines 1267 contigs and 0 short contigs 3.0% (82282 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 1692 lines 1692 contigs and 0 short contigs 4.0% (109770 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 2116 lines 2116 contigs and 0 short contigs 5.0% (137146 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 2541 lines 2541 contigs and 0 short contigs 6.0% (164589 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 2966 lines 2966 contigs and 0 short contigs 7.0% (192028 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 3391 lines 3391 contigs and 0 short contigs 8.0% (219456 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 3816 lines 3816 contigs and 0 short contigs 9.0% (246895 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 4241 lines 4241 contigs and 0 short contigs 10.0% (274300 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 4667 lines 4667 contigs and 0 short contigs 11.0% (301723 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 5093 lines 5017 contigs and 76 short contigs 12.0% (329170 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 5519 lines 5017 contigs and 502 short contigs 13.0% (356557 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 5945 lines 5017 contigs and 928 short contigs 14.0% (383984 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 6372 lines 5017 contigs and 1355 short contigs 15.0% (411454 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 6798 lines 5017 contigs and 1781 short contigs 16.0% (438851 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 7225 lines 5017 contigs and 2208 short contigs 17.0% (466313 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 7651 lines 5017 contigs and 2634 short contigs 18.0% (493733 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8078 lines 5017 contigs and 3061 short contigs 19.0% (521155 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 20.0% (548543 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 21.0% (576015 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 22.0% (603411 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 23.0% (630837 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 24.0% (658289 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 25.0% (685678 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 26.0% (713145 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 27.0% (740535 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 28.0% (767977 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 29.0% (795415 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 30.0% (822840 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 31.0% (850260 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 32.0% (877664 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 33.0% (905146 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 34.0% (932567 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 35.0% (959979 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 36.0% (987408 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 37.0% (1014855 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 38.0% (1042239 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 39.0% (1069676 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 40.0% (1097105 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 41.0% (1124544 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 42.0% (1151941 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 43.0% (1179384 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 44.0% (1206829 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 45.0% (1234266 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 46.0% (1261675 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 47.0% (1289121 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 48.0% (1316516 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 49.0% (1343924 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 50.0% (1371393 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 51.0% (1398797 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 52.0% (1426243 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 53.0% (1453639 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 54.0% (1481096 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 55.0% (1508523 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 56.0% (1535932 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 57.0% (1563391 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 58.0% (1590828 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 59.0% (1618197 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 60.0% (1645660 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 61.0% (1673091 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 62.0% (1700497 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 63.0% (1727952 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 64.0% (1755381 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 65.0% (1782787 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 66.0% (1810218 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 67.0% (1837635 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 68.0% (1865075 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 69.0% (1892478 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 70.0% (1919916 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 71.0% (1947363 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 72.0% (1974796 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 73.0% (2002204 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 74.0% (2029645 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 75.0% (2057063 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 76.0% (2084481 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 77.0% (2111889 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 78.0% (2139321 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 79.0% (2166788 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 80.0% (2194185 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 81.0% (2221620 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 82.0% (2249036 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 83.0% (2276492 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 84.0% (2303920 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 85.0% (2331316 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 86.0% (2358735 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 87.0% (2386162 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 88.0% (2413619 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 89.0% (2441048 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 90.0% (2468490 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 91.0% (2495881 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 92.0% (2523287 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 93.0% (2550713 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 94.0% (2578199 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 95.0% (2605588 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 96.0% (2632994 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 97.0% (2660473 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 98.0% (2687853 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 99.0% (2715292 of 2742608), ETA 0:00:00     [55.1Gb / 503.5Gb]                 
[00:00:00] Finished reading 43939 contigs and 1 coverages from 03bins/metabat2_fixed/1507996/temp/1507996.depth.txt [55.1Gb / 503.5Gb]. Ignored 35574 too small contigs.                                     
[00:00:00] Number of target contigs: 5017 of large (>= 2000) and 3348 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 5017
[00:00:00] Allocated memory for TNF [55.1Gb / 503.5Gb]
[00:00:00] Calculating TNF 1.1% (57 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 2.1% (106 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 3.2% (163 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 4.3% (216 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 5.4% (271 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 6.4% (319 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 7.3% (366 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 8.3% (418 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 9.3% (465 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 10.3% (516 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 11.2% (564 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 12.3% (619 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 13.6% (681 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 14.5% (727 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 15.5% (777 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 16.3% (816 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 17.5% (879 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 18.5% (928 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 19.5% (977 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 20.4% (1023 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 21.4% (1073 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 22.4% (1123 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 23.5% (1177 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 24.5% (1231 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 25.6% (1285 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 26.6% (1337 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 27.6% (1387 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 28.7% (1441 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 29.7% (1492 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 30.8% (1544 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 31.7% (1588 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 32.6% (1635 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 33.5% (1683 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 34.7% (1743 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 35.7% (1792 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 36.7% (1842 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 37.7% (1892 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 38.7% (1942 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 39.8% (1995 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 41.0% (2056 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 41.9% (2104 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 42.8% (2147 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 43.8% (2199 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 44.7% (2244 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 45.9% (2302 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 46.8% (2347 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 48.0% (2407 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 49.0% (2458 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 49.9% (2505 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 51.2% (2567 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 52.0% (2611 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 53.1% (2665 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 54.1% (2714 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 55.1% (2763 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 56.1% (2817 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 57.1% (2865 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 58.0% (2909 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 59.1% (2965 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 60.3% (3025 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 61.3% (3073 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 62.3% (3127 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 63.0% (3163 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 64.1% (3214 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 65.1% (3268 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 66.4% (3329 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 67.4% (3380 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 68.4% (3430 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 69.1% (3468 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 70.3% (3525 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 71.6% (3590 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 72.2% (3624 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 73.3% (3675 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 74.5% (3739 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 75.5% (3788 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 76.5% (3836 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 77.3% (3877 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 78.5% (3940 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 79.6% (3993 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 80.5% (4037 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 81.4% (4086 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 82.5% (4139 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 83.5% (4188 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 84.4% (4236 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 85.4% (4286 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 86.5% (4338 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 87.6% (4395 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 88.7% (4452 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 89.8% (4503 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 90.8% (4553 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 91.5% (4593 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 92.6% (4645 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 93.6% (4697 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 94.7% (4749 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 95.7% (4800 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 96.8% (4854 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 97.8% (4909 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 98.7% (4953 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 99.8% (5006 of 5017), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [55.1Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.2% (62 of 5017), ETA 0:00:03                   
[00:00:00] ... processing TNF matrix 2.2% (112 of 5017), ETA 0:00:02                   
[00:00:00] ... processing TNF matrix 3.2% (160 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 4.1% (208 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 5.1% (256 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 6.4% (320 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 7.3% (368 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 8.3% (416 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 9.2% (464 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 10.2% (512 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 11.5% (576 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 12.4% (624 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 13.4% (672 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 14.4% (720 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 15.3% (768 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 16.3% (816 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 17.5% (880 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 18.5% (928 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 19.5% (977 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 20.5% (1026 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 21.4% (1075 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 22.4% (1125 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 23.4% (1174 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.7% (1239 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 25.7% (1288 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.7% (1338 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.6% (1387 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 28.6% (1437 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.6% (1487 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 30.6% (1535 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.6% (1583 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.8% (1647 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 33.8% (1695 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.7% (1743 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 35.7% (1791 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.7% (1839 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.6% (1887 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 38.9% (1952 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.9% (2000 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 40.8% (2049 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.8% (2097 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.8% (2145 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.7% (2194 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 44.7% (2245 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.0% (2309 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 47.0% (2357 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 47.9% (2405 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.9% (2453 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.9% (2502 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.9% (2552 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.1% (2616 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.1% (2664 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 54.1% (2712 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.1% (2763 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.1% (2813 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 57.0% (2862 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 58.0% (2910 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.0% (2958 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 60.3% (3023 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.2% (3072 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 62.2% (3120 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.1% (3168 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 64.1% (3216 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 65.1% (3264 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.3% (3328 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 67.3% (3377 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.3% (3426 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 69.3% (3475 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 70.2% (3523 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 71.2% (3572 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 72.5% (3636 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 73.4% (3684 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.4% (3732 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 75.4% (3783 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 76.4% (3831 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 77.3% (3879 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 78.3% (3927 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 79.6% (3992 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 80.5% (4040 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 81.5% (4089 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 82.5% (4138 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.4% (4186 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 84.4% (4234 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 85.4% (4285 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 86.7% (4350 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 87.7% (4399 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 88.6% (4447 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 89.6% (4495 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 90.6% (4545 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 91.5% (4593 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 92.5% (4641 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 93.8% (4707 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 94.8% (4755 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 95.7% (4803 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 96.7% (4851 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 97.6% (4899 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 98.6% (4947 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 99.9% (5010 of 5017), ETA 0:00:00                   
[00:00:00] Finding cutoff p=999 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=999 1.1% (55 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 2.3% (117 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 3.5% (176 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 4.6% (230 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 5.7% (286 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 7.0% (353 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 8.3% (416 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 9.5% (477 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 11.1% (556 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 12.7% (637 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 15.1% (758 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 16.2% (813 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 17.1% (857 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 17.9% (898 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 18.7% (938 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 19.6% (982 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 20.5% (1026 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 21.9% (1100 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 22.8% (1143 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 23.6% (1184 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 24.5% (1227 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 26.0% (1303 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 26.8% (1344 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 27.6% (1385 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 28.5% (1430 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 30.1% (1510 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 31.0% (1554 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 31.8% (1597 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 32.7% (1640 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 33.5% (1683 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 35.2% (1767 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 36.2% (1814 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 37.0% (1858 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 38.0% (1907 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 39.0% (1958 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 39.9% (2003 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 40.9% (2050 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 41.8% (2097 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 43.6% (2185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 44.4% (2229 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 45.8% (2299 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 47.2% (2370 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 48.1% (2411 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 49.5% (2485 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 50.3% (2524 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 51.1% (2564 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 52.0% (2607 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 53.5% (2682 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 54.2% (2717 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 55.0% (2758 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.4% (2828 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 57.2% (2869 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 58.4% (2932 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 59.2% (2968 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 60.2% (3019 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 61.3% (3074 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 62.2% (3121 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 63.3% (3176 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 64.3% (3228 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 65.4% (3280 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 66.4% (3333 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 67.4% (3381 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 68.2% (3424 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 69.3% (3477 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 70.4% (3531 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 71.3% (3576 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 72.3% (3625 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 73.5% (3689 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 74.4% (3733 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 75.4% (3784 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 76.3% (3826 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 77.4% (3883 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 78.3% (3930 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.5% (3988 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 80.3% (4030 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 81.5% (4087 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 82.4% (4135 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 83.4% (4185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 84.6% (4242 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 85.5% (4290 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 86.4% (4337 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 87.4% (4386 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.6% (4444 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 89.5% (4491 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 90.6% (4546 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 91.5% (4590 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 92.5% (4643 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 94.6% (4746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 96.7% (4849 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 98.6% (4948 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.9; 0 / 5017 (P = 0.00%) round 1]               
[00:00:00] Finding cutoff p=998 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=998 1.1% (56 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 2.5% (126 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 3.7% (185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 5.1% (257 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 6.7% (334 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 8.1% (407 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 9.7% (488 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 11.7% (589 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 15.2% (763 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 16.2% (813 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 17.2% (862 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 18.1% (906 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 19.1% (956 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 20.1% (1007 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 21.1% (1057 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 22.0% (1106 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 22.9% (1149 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 24.0% (1206 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 25.0% (1252 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 26.0% (1305 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 27.0% (1356 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 28.0% (1406 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 29.0% (1454 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 30.0% (1503 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 31.0% (1556 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 32.1% (1610 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 33.5% (1682 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 34.6% (1737 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 35.7% (1791 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 36.8% (1844 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 37.7% (1893 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 38.8% (1945 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 39.9% (2000 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 41.0% (2057 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 41.9% (2100 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 42.7% (2144 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 44.1% (2212 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 44.9% (2255 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 45.8% (2297 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 47.3% (2374 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 48.1% (2413 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 49.0% (2456 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 50.3% (2522 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 51.1% (2563 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 52.0% (2607 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 52.9% (2653 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 54.2% (2721 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 55.0% (2758 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 56.3% (2824 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 57.1% (2863 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 58.4% (2931 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 59.1% (2967 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 60.5% (3034 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 61.2% (3071 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 62.4% (3132 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 63.2% (3173 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 64.2% (3223 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 65.2% (3271 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 66.4% (3332 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 67.3% (3378 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 68.2% (3420 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 69.3% (3478 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 70.2% (3524 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 71.2% (3574 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 72.3% (3628 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 73.2% (3673 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 74.6% (3741 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 75.5% (3786 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 76.4% (3831 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 77.4% (3885 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 78.4% (3934 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 79.3% (3980 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 80.5% (4038 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 81.4% (4083 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 82.5% (4137 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 83.4% (4186 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 84.5% (4241 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 85.5% (4290 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 86.5% (4339 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 87.5% (4391 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 88.5% (4441 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 89.6% (4497 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 90.6% (4544 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 91.6% (4597 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 92.6% (4647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 93.5% (4692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 94.6% (4746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 95.6% (4795 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 98.6% (4948 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.8; 2 / 5017 (P = 0.04%) round 2]               
[00:00:00] Finding cutoff p=995 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=995 2.0% (101 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 3.7% (184 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 5.4% (269 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 7.1% (356 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 8.9% (445 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 11.4% (571 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 14.1% (706 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 15.1% (756 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 16.0% (803 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 16.9% (849 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 17.9% (900 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 18.9% (947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 19.9% (997 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 20.9% (1047 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 22.4% (1125 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 23.4% (1174 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 24.4% (1226 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 25.5% (1281 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 26.6% (1335 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 27.7% (1389 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 28.8% (1443 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 29.8% (1496 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 31.0% (1555 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 32.1% (1608 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 33.1% (1662 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 34.2% (1718 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 35.4% (1778 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 36.4% (1828 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 37.5% (1882 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 38.7% (1941 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 39.7% (1993 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 41.2% (2066 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 42.0% (2107 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 42.8% (2148 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 44.3% (2222 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 45.1% (2265 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 46.0% (2309 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 46.9% (2354 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 47.9% (2404 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 48.8% (2448 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 50.2% (2521 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 51.0% (2561 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 51.9% (2604 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 52.9% (2654 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 54.3% (2722 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 55.1% (2764 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 56.0% (2808 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 57.5% (2887 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 58.3% (2925 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 59.1% (2963 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 60.4% (3029 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 61.1% (3066 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 62.3% (3124 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 63.4% (3182 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 64.1% (3214 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 65.5% (3285 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 66.1% (3317 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 67.5% (3384 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 68.3% (3428 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 69.2% (3471 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 70.3% (3528 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 71.4% (3583 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 72.2% (3621 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 73.3% (3678 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 74.3% (3729 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 75.4% (3783 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 76.2% (3825 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 77.3% (3879 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 78.5% (3936 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 79.4% (3985 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 80.5% (4038 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 81.4% (4085 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 82.4% (4136 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 83.4% (4183 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 84.5% (4240 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 85.4% (4287 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 86.5% (4339 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 87.4% (4386 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 88.5% (4438 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 89.5% (4489 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 90.6% (4544 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 91.5% (4592 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 92.6% (4647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 93.6% (4695 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 94.6% (4746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 95.6% (4796 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 97.6% (4898 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.5; 59 / 5017 (P = 1.18%) round 3]               
[00:00:00] Finding cutoff p=994 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=994 1.1% (53 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 2.6% (131 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 4.0% (203 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 5.5% (278 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 7.9% (395 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 9.5% (477 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 11.6% (582 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 14.1% (707 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 15.7% (790 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 16.8% (842 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 17.8% (895 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 18.8% (943 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 19.8% (992 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 20.8% (1044 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 21.9% (1097 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 22.9% (1150 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 24.0% (1204 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 25.1% (1259 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 26.2% (1315 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 27.2% (1364 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 28.3% (1418 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 29.4% (1476 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 30.5% (1530 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 31.6% (1586 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 32.7% (1643 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 33.9% (1699 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 35.0% (1756 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 36.2% (1814 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 37.3% (1872 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 38.5% (1930 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 39.4% (1978 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 40.3% (2020 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 41.1% (2064 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 42.1% (2111 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 42.9% (2152 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 43.8% (2196 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 45.2% (2268 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 46.2% (2316 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 47.0% (2358 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 47.8% (2400 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 49.3% (2473 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 50.2% (2521 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 51.1% (2563 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 52.0% (2609 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 52.9% (2656 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 54.4% (2727 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 55.3% (2772 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 56.2% (2822 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 57.1% (2866 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 58.4% (2932 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 59.3% (2973 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 61.3% (3077 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 62.1% (3116 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 63.2% (3170 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 64.3% (3224 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 65.4% (3279 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 66.4% (3333 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 67.1% (3367 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 68.2% (3421 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 69.2% (3473 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 70.2% (3521 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 71.4% (3583 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 72.4% (3630 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 73.3% (3677 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 74.6% (3741 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 75.4% (3782 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 76.3% (3829 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 77.3% (3877 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 78.4% (3931 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 79.5% (3989 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 80.5% (4037 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 81.4% (4083 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 82.4% (4133 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 83.4% (4183 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 84.4% (4234 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 85.4% (4285 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 86.4% (4336 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 87.4% (4387 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 88.5% (4439 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 89.6% (4493 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 90.6% (4543 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 91.6% (4597 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 92.6% (4647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 93.6% (4697 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 94.6% (4746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 95.6% (4795 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.4; 89 / 5017 (P = 1.77%) round 4]               
[00:00:00] Finding cutoff p=991 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=991 1.7% (87 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 3.2% (161 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 4.6% (231 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 5.9% (298 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 7.5% (377 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 9.0% (453 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 10.5% (527 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 12.1% (608 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 13.8% (692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 15.8% (795 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 18.2% (914 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 19.3% (968 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 20.4% (1024 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 21.5% (1079 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 22.6% (1134 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 23.8% (1196 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 24.9% (1251 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 26.2% (1315 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 27.2% (1367 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 28.3% (1419 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 29.3% (1472 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 30.7% (1538 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 31.8% (1596 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 33.0% (1656 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 34.1% (1713 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 35.3% (1773 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 36.6% (1835 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 37.8% (1895 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 38.9% (1953 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 39.9% (2004 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 40.9% (2052 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 41.9% (2100 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 42.9% (2151 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 43.8% (2197 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 45.3% (2271 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 46.2% (2320 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 47.2% (2368 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 48.3% (2423 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 49.4% (2477 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 50.3% (2525 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 51.3% (2575 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 52.2% (2621 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 53.0% (2657 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 54.2% (2721 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 55.1% (2764 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 56.4% (2829 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 57.2% (2869 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 57.9% (2907 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 59.2% (2972 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 60.1% (3016 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 61.4% (3080 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 62.3% (3124 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 63.1% (3166 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 64.5% (3235 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 65.2% (3271 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 66.3% (3328 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 67.4% (3382 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 69.3% (3479 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 70.3% (3525 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 71.2% (3570 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 72.4% (3633 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 73.4% (3684 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 74.4% (3731 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 75.4% (3781 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 76.2% (3825 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 77.4% (3881 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 78.5% (3936 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 79.4% (3986 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 80.5% (4038 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 81.3% (4080 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 82.5% (4141 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 83.4% (4182 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 84.4% (4234 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 85.4% (4286 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 86.6% (4346 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 87.5% (4390 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 88.5% (4442 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 89.5% (4488 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 90.5% (4539 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 91.6% (4594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 92.6% (4645 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 93.5% (4693 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 94.6% (4747 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 95.6% (4798 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.1; 243 / 5017 (P = 4.84%) round 5]               
[00:00:00] Finding cutoff p=990 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=990 1.7% (85 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 3.0% (153 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 4.5% (225 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 5.9% (298 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 7.4% (372 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 8.7% (438 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 10.2% (510 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 12.1% (609 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 13.7% (689 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 15.3% (770 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 16.9% (848 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 18.7% (940 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 20.7% (1040 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 21.8% (1093 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 22.9% (1150 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 24.1% (1211 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 25.2% (1265 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 26.4% (1326 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 27.7% (1389 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 28.9% (1450 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 30.1% (1509 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 31.2% (1565 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 32.5% (1629 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 33.7% (1689 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 34.8% (1748 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 36.7% (1842 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 37.9% (1902 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 39.1% (1962 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 40.2% (2018 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 41.2% (2069 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 42.2% (2116 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 43.1% (2164 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 44.1% (2215 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 45.1% (2264 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 46.0% (2310 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 47.0% (2358 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 48.0% (2408 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 49.0% (2458 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 50.0% (2509 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 50.9% (2552 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 52.2% (2621 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 53.1% (2666 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 54.0% (2707 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 55.2% (2770 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 56.0% (2812 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 57.3% (2875 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 58.2% (2919 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 59.0% (2959 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 60.3% (3025 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 61.2% (3069 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 62.0% (3112 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 63.5% (3184 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 64.3% (3226 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 65.4% (3282 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 66.2% (3319 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 67.1% (3368 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 68.4% (3432 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 69.3% (3479 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 70.3% (3527 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 71.3% (3575 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 72.3% (3625 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 73.5% (3689 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 74.4% (3733 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 75.5% (3787 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 76.4% (3831 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 77.5% (3888 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 79.4% (3981 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 80.5% (4039 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 81.6% (4092 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 82.4% (4136 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 83.4% (4185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 84.5% (4241 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 85.6% (4293 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 86.6% (4344 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 87.6% (4393 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 88.5% (4438 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 89.6% (4493 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 90.5% (4540 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 91.5% (4592 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 94.6% (4748 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 95.6% (4795 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 96.6% (4845 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.0; 295 / 5017 (P = 5.88%) round 6]               
[00:00:00] Finding cutoff p=987 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=987 1.6% (78 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 2.9% (147 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 4.3% (216 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 5.6% (282 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 7.0% (351 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 8.3% (417 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 9.9% (495 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 11.3% (566 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 12.8% (642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 14.6% (731 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 16.2% (812 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 17.9% (896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 19.9% (999 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 21.6% (1082 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 22.7% (1139 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 24.0% (1203 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 25.1% (1259 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 26.9% (1350 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 28.1% (1410 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 29.2% (1464 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 30.4% (1524 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 31.5% (1580 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 32.7% (1642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 34.0% (1704 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 35.1% (1762 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 36.3% (1819 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 37.5% (1880 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 38.7% (1944 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 39.8% (1999 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 40.8% (2046 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 41.7% (2093 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 42.7% (2143 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 44.2% (2217 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 45.2% (2266 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 46.1% (2313 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 47.0% (2359 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 47.9% (2404 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 48.9% (2454 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 49.9% (2503 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 51.3% (2572 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 52.1% (2612 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 52.9% (2652 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 54.2% (2719 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 55.0% (2759 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 56.2% (2821 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 57.1% (2863 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 58.0% (2908 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 59.2% (2972 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 61.3% (3073 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 62.1% (3117 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 63.2% (3173 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 64.3% (3225 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 65.4% (3282 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 66.1% (3316 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 67.2% (3373 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 68.2% (3423 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 69.1% (3468 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 70.4% (3530 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 71.4% (3580 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 72.3% (3627 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 73.2% (3674 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 74.4% (3735 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 75.3% (3780 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 76.5% (3836 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 77.3% (3877 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 78.3% (3929 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 79.4% (3984 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 80.5% (4038 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 81.4% (4083 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 82.3% (4131 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 83.6% (4193 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 84.6% (4243 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 85.5% (4291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 86.5% (4342 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 87.6% (4394 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 88.6% (4446 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 89.6% (4497 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 90.6% (4543 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 91.6% (4594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 92.6% (4647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 94.6% (4747 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 95.6% (4798 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 96.6% (4848 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.7; 495 / 5017 (P = 9.87%) round 7]               
[00:00:00] Finding cutoff p=982 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=982 1.6% (79 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 2.9% (148 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 4.4% (219 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 5.8% (291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 7.3% (366 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 8.6% (432 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 10.1% (507 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 11.6% (583 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 13.1% (659 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 16.5% (826 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 18.2% (912 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 20.6% (1033 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 21.8% (1092 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 22.9% (1147 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 24.1% (1210 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 25.6% (1286 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 26.8% (1344 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 27.9% (1401 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 29.0% (1455 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 30.2% (1515 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 31.3% (1569 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 32.4% (1627 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 33.7% (1690 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 34.8% (1748 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 36.0% (1808 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 37.0% (1858 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 37.9% (1903 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 38.8% (1946 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 39.6% (1989 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 41.0% (2059 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 42.0% (2108 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 43.0% (2157 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 43.9% (2203 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 44.9% (2252 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 45.9% (2304 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 46.9% (2351 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 48.3% (2422 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 49.2% (2468 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 50.2% (2518 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 51.1% (2563 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 52.2% (2620 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 53.0% (2660 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 54.3% (2722 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 55.1% (2764 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 55.9% (2805 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 57.1% (2865 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 58.3% (2927 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 59.2% (2968 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 60.4% (3030 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 61.2% (3069 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 62.3% (3126 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 63.4% (3180 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 64.1% (3216 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 65.2% (3270 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 66.3% (3324 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 67.4% (3380 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 68.1% (3418 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 69.2% (3471 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 70.5% (3538 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 71.5% (3587 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 72.4% (3632 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 73.3% (3676 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 74.2% (3724 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 75.5% (3786 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 76.4% (3833 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 77.4% (3881 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 78.5% (3939 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 79.3% (3980 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 80.4% (4034 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 81.6% (4093 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 82.5% (4141 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 83.5% (4190 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 84.6% (4245 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 85.5% (4288 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 86.5% (4340 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 87.6% (4393 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 88.4% (4437 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 89.5% (4491 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 90.6% (4544 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 91.6% (4596 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 92.6% (4646 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 93.5% (4692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 94.6% (4746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 95.6% (4796 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 96.6% (4847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=982 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.2; 833 / 5017 (P = 16.60%) round 8]               
[00:00:00] Finding cutoff p=978 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=978 1.6% (81 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 3.6% (180 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 4.9% (245 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 6.3% (315 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 7.6% (379 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 8.9% (447 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 10.6% (530 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 12.0% (600 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 13.4% (674 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 15.1% (757 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 16.6% (832 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 18.4% (925 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 20.6% (1031 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 21.6% (1086 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 22.8% (1146 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 24.4% (1223 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 25.4% (1273 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 26.5% (1329 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 27.5% (1379 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 28.6% (1433 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 29.6% (1485 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 30.8% (1545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 31.9% (1600 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 33.0% (1655 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 34.3% (1719 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 35.4% (1774 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 36.5% (1831 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 37.5% (1881 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 38.3% (1922 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 39.3% (1970 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 40.1% (2013 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 41.0% (2057 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 41.9% (2104 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 42.8% (2149 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 43.7% (2194 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 45.1% (2263 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 46.0% (2307 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 46.9% (2352 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 47.8% (2400 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 49.3% (2475 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 50.3% (2522 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 51.2% (2569 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 52.6% (2638 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 53.4% (2679 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 54.2% (2718 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 54.9% (2756 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 56.1% (2814 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 56.9% (2856 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 58.1% (2916 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 59.4% (2981 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 60.2% (3021 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 61.5% (3083 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 62.2% (3122 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 63.4% (3182 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 64.2% (3221 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 65.3% (3275 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 66.3% (3326 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 67.3% (3378 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 68.3% (3429 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 69.3% (3478 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 70.1% (3519 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 71.2% (3571 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 72.3% (3625 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 73.3% (3675 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 74.5% (3740 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 75.5% (3786 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 76.4% (3832 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 77.3% (3879 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 78.3% (3928 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 79.4% (3983 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 80.5% (4037 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 81.3% (4080 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 82.4% (4132 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 83.4% (4185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 84.4% (4233 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 85.5% (4291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 86.5% (4342 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 87.5% (4390 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 88.4% (4437 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 89.5% (4492 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 90.6% (4547 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 91.6% (4598 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 92.5% (4641 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 93.5% (4692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 94.6% (4745 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 95.6% (4796 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.8; 1058 / 5017 (P = 21.09%) round 9]               
[00:00:00] Finding cutoff p=973 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=973 1.5% (76 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 2.9% (145 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 4.1% (208 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 5.5% (275 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 6.8% (343 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 8.2% (409 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 9.4% (474 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 10.8% (542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 12.2% (611 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 13.7% (687 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 15.2% (762 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 16.8% (841 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 18.6% (931 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 20.5% (1030 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 22.2% (1112 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 23.7% (1187 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 24.7% (1237 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 25.8% (1294 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 27.0% (1355 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 28.1% (1408 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 29.1% (1461 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 30.2% (1515 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 31.5% (1579 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 32.5% (1631 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 33.7% (1693 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 34.9% (1750 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 36.1% (1811 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 37.2% (1867 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 38.4% (1925 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 39.4% (1975 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 40.3% (2023 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 41.2% (2069 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 42.7% (2144 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 43.7% (2193 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 45.2% (2266 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 46.1% (2313 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 47.0% (2358 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 47.9% (2404 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 48.9% (2454 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 49.9% (2502 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 51.3% (2573 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 52.1% (2613 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 52.9% (2652 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 54.1% (2712 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 55.2% (2770 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 56.0% (2811 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 57.2% (2872 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 58.0% (2911 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 59.2% (2971 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 60.0% (3010 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 61.2% (3070 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 62.0% (3111 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 63.2% (3169 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 64.2% (3222 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 65.3% (3277 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 66.4% (3329 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 67.4% (3381 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 68.4% (3434 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 69.7% (3495 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 70.4% (3531 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 71.4% (3580 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 72.5% (3636 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 73.5% (3690 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 74.5% (3736 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 75.4% (3782 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 76.3% (3828 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 78.5% (3937 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 79.4% (3985 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 80.5% (4040 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 81.4% (4082 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 82.5% (4139 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 83.5% (4187 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 84.7% (4249 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 85.5% (4291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 86.6% (4344 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 87.6% (4393 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 88.6% (4444 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 89.5% (4490 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 90.5% (4540 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 91.5% (4592 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 92.5% (4641 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 94.6% (4745 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 95.6% (4796 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 96.6% (4847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=973 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.3; 1354 / 5017 (P = 26.99%) round 10]               
[00:00:00] Finding cutoff p=969 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=969 1.5% (77 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 2.9% (144 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 4.1% (204 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 5.5% (275 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 6.8% (341 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 8.1% (405 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 9.4% (472 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 10.9% (547 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 12.5% (629 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 14.0% (702 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 15.4% (772 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 17.1% (857 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 19.0% (952 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 20.8% (1043 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 22.5% (1130 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 24.1% (1210 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 25.0% (1256 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 26.2% (1315 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 27.3% (1369 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 28.4% (1423 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 29.4% (1477 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 30.6% (1533 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 31.8% (1593 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 34.1% (1713 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 35.5% (1779 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 36.6% (1835 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 37.7% (1889 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 39.0% (1958 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 39.9% (2002 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 40.9% (2050 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 42.0% (2105 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 42.9% (2151 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 43.8% (2195 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 44.7% (2244 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 46.2% (2317 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 47.0% (2360 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 48.0% (2409 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 48.9% (2453 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 49.8% (2500 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 50.9% (2552 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 52.1% (2612 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 53.3% (2673 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 54.1% (2712 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 55.2% (2771 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 56.0% (2808 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 57.2% (2870 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 58.0% (2912 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 59.3% (2973 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 61.3% (3076 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 62.1% (3117 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 63.2% (3172 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 64.3% (3226 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 65.3% (3278 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 66.4% (3329 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 67.4% (3380 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 68.4% (3432 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 69.7% (3496 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 70.4% (3530 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 71.4% (3582 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 72.5% (3636 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 73.2% (3673 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 74.5% (3737 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 75.4% (3783 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 76.8% (3852 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 77.4% (3882 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 78.3% (3929 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 79.4% (3986 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 80.5% (4041 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 81.5% (4091 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 82.6% (4143 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 83.5% (4189 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 84.5% (4237 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 85.4% (4285 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 86.5% (4340 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 87.6% (4393 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 88.6% (4443 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 89.5% (4488 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 90.6% (4544 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 91.5% (4593 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 96.6% (4847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=969 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.9; 1584 / 5017 (P = 31.57%) round 11]               
[00:00:00] Finding cutoff p=966 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=966 1.6% (78 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 2.8% (142 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 4.0% (202 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 5.5% (274 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 6.8% (340 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 8.0% (400 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 9.3% (466 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 10.7% (536 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 12.0% (604 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 13.5% (678 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 14.9% (746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 16.5% (827 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 18.1% (908 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 19.9% (998 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 22.0% (1105 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 23.7% (1187 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 25.4% (1274 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 26.5% (1328 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 27.6% (1384 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 28.7% (1439 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 29.7% (1489 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 30.7% (1542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 31.9% (1602 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 32.9% (1652 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 34.0% (1708 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 35.2% (1766 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 36.4% (1824 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 37.5% (1883 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 38.5% (1933 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 39.4% (1976 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 40.3% (2023 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 41.2% (2067 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 42.2% (2115 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 43.0% (2157 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 43.9% (2202 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 44.9% (2251 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 45.8% (2297 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 47.3% (2371 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 48.2% (2417 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 49.1% (2464 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 50.2% (2519 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 51.1% (2562 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 52.2% (2621 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 53.0% (2661 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 54.2% (2720 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 55.0% (2759 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 56.1% (2816 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 57.4% (2879 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 58.2% (2918 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 59.4% (2978 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 60.1% (3015 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 61.4% (3081 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 62.2% (3120 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 63.3% (3174 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 64.4% (3230 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 65.4% (3279 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 66.4% (3333 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 67.1% (3367 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 68.1% (3419 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 69.2% (3472 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 70.5% (3537 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 71.4% (3581 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 72.3% (3626 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 73.2% (3672 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 74.4% (3733 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 75.3% (3776 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 76.5% (3838 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 77.4% (3885 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 78.3% (3928 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 79.4% (3982 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 80.6% (4043 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 81.3% (4080 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 82.4% (4133 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 83.5% (4188 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 84.6% (4245 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 85.5% (4290 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 86.4% (4335 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 87.4% (4386 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 88.5% (4438 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 89.5% (4491 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 90.6% (4545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 91.6% (4596 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 93.6% (4695 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 94.6% (4748 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 95.6% (4798 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 96.6% (4845 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=966 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.6; 1758 / 5017 (P = 35.04%) round 12]               
[00:00:00] Finding cutoff p=963 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=963 1.5% (75 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 2.8% (138 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 4.1% (204 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 5.4% (273 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 6.7% (338 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 8.6% (433 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 10.0% (502 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 11.5% (575 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 12.9% (647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 14.4% (720 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 15.9% (798 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 17.4% (872 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 19.2% (965 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 21.1% (1061 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 22.6% (1134 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 24.3% (1221 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 25.4% (1276 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 26.5% (1329 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 27.7% (1388 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 28.7% (1439 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 29.8% (1495 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 30.8% (1547 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 31.9% (1602 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 32.8% (1647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 33.9% (1702 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 35.1% (1761 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 36.2% (1817 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 37.4% (1878 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 38.3% (1924 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 39.2% (1966 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 40.2% (2017 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 41.1% (2060 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 42.0% (2107 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 42.9% (2152 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 43.7% (2193 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 45.2% (2268 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 46.1% (2314 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 47.1% (2361 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 48.1% (2412 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 49.0% (2458 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 49.9% (2505 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 50.8% (2551 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 52.1% (2612 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 53.3% (2674 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 54.1% (2712 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 55.3% (2773 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 56.0% (2811 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 57.3% (2876 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 58.1% (2915 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 59.3% (2975 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 61.3% (3073 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 62.3% (3128 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 63.0% (3163 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 64.1% (3218 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 65.1% (3268 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 66.2% (3321 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 67.3% (3374 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 68.2% (3423 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 69.5% (3489 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 70.2% (3522 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 71.4% (3581 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 72.2% (3624 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 73.2% (3673 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 74.3% (3730 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 75.2% (3775 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 76.5% (3837 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 77.4% (3883 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 78.3% (3928 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 79.5% (3990 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 80.4% (4032 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 81.4% (4086 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 82.5% (4141 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 83.5% (4189 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 84.4% (4235 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 85.5% (4290 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 86.5% (4341 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 87.5% (4388 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 88.5% (4439 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 89.6% (4494 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 90.6% (4545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 91.6% (4594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 92.5% (4641 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 93.7% (4699 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 94.6% (4746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 96.6% (4847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.3; 1914 / 5017 (P = 38.15%) round 13]               
[00:00:00] Finding cutoff p=959 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=959 1.5% (77 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 2.8% (141 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 4.1% (206 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 5.4% (269 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 6.6% (331 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 7.8% (391 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 9.1% (455 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 10.3% (519 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 11.7% (588 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 13.2% (664 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 14.6% (731 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 16.1% (808 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 17.7% (887 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 19.4% (971 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 22.0% (1102 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 23.7% (1187 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 24.7% (1237 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 25.7% (1291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 26.7% (1340 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 27.9% (1398 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 29.0% (1456 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 30.1% (1510 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 31.3% (1570 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 32.3% (1621 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 33.4% (1676 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 34.4% (1728 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 35.6% (1784 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 36.8% (1848 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 37.9% (1903 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 38.8% (1947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 39.8% (1999 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 41.3% (2071 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 42.2% (2115 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 43.0% (2156 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 44.0% (2205 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 44.8% (2250 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 45.8% (2297 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 47.8% (2398 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 48.9% (2455 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 49.8% (2499 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 51.3% (2575 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 52.1% (2615 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 53.2% (2671 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 53.9% (2703 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 55.2% (2767 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 56.0% (2812 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 57.1% (2867 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 58.4% (2928 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 59.2% (2970 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 60.4% (3028 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 61.1% (3063 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 62.3% (3124 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 63.5% (3184 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 64.2% (3220 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 65.3% (3275 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 66.3% (3327 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 67.3% (3378 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 68.3% (3429 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 69.7% (3499 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 70.4% (3530 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 71.4% (3584 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 72.5% (3635 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 73.6% (3692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 74.2% (3723 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 75.5% (3786 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 76.3% (3829 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 77.5% (3886 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 78.3% (3928 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 79.4% (3981 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 80.5% (4041 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 81.5% (4087 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 82.5% (4141 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 83.5% (4190 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 84.5% (4240 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 85.5% (4291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 86.5% (4340 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 87.6% (4393 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 88.6% (4444 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 89.7% (4499 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 90.5% (4542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 91.5% (4592 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 92.6% (4644 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 93.5% (4692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 94.5% (4743 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 95.6% (4797 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.9; 2120 / 5017 (P = 42.26%) round 14]               
[00:00:00] Finding cutoff p=955 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=955 1.5% (77 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 2.8% (138 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 3.9% (197 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 5.2% (260 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 6.4% (323 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 7.5% (374 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 8.7% (435 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 10.0% (500 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 11.3% (565 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 12.5% (626 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 13.8% (694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 15.3% (766 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 17.0% (851 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 18.5% (930 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 21.4% (1073 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 23.6% (1186 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 24.6% (1236 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 25.7% (1287 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 26.6% (1337 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 27.8% (1394 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 28.8% (1443 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 29.8% (1494 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 31.1% (1562 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 31.9% (1601 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 33.0% (1654 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 34.0% (1708 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 35.1% (1760 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 36.1% (1810 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 37.9% (1899 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 38.8% (1949 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 39.8% (1998 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 41.1% (2064 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 42.0% (2108 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 42.9% (2152 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 43.7% (2194 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 45.2% (2266 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 46.2% (2318 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 47.4% (2378 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 48.3% (2424 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 49.3% (2473 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 50.2% (2517 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 51.1% (2564 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 52.0% (2607 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 53.2% (2669 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 54.1% (2713 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 55.3% (2775 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 56.0% (2810 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 57.2% (2869 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 58.4% (2928 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 59.1% (2966 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 60.4% (3030 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 61.3% (3075 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 62.1% (3115 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 63.4% (3181 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 64.1% (3215 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 65.4% (3282 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 66.2% (3321 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 67.2% (3369 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 69.4% (3484 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 70.5% (3539 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 71.2% (3571 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 72.5% (3635 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 73.4% (3680 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 74.3% (3728 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 75.5% (3786 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 76.3% (3829 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 77.3% (3877 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 78.4% (3935 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 79.4% (3982 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 80.7% (4047 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 81.5% (4089 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 82.4% (4134 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 83.4% (4182 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 84.6% (4242 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 85.4% (4285 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 86.5% (4342 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 87.5% (4389 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 88.5% (4440 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 89.7% (4498 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 90.5% (4539 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 91.6% (4594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 92.6% (4645 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 94.6% (4748 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 96.6% (4847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=955 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.5; 2327 / 5017 (P = 46.38%) round 15]               
[00:00:00] Finding cutoff p=950 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=950 1.6% (81 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 3.1% (155 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 4.3% (218 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 5.8% (292 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 7.1% (354 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 8.1% (408 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 9.3% (468 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 10.5% (527 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 11.9% (596 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 13.2% (662 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 15.2% (762 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 16.6% (833 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 18.2% (911 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 19.7% (989 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 22.0% (1104 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 23.1% (1158 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 23.6% (1186 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 24.6% (1233 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 25.5% (1281 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 26.8% (1343 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 28.1% (1409 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 28.8% (1445 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 29.9% (1499 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 30.8% (1545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 31.7% (1591 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 32.7% (1639 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 33.8% (1698 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 34.8% (1747 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 36.1% (1809 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 37.1% (1860 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 38.4% (1928 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 39.5% (1983 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 40.7% (2040 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 41.8% (2096 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 43.1% (2161 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 44.0% (2208 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 45.3% (2272 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 46.2% (2317 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 47.2% (2367 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 48.0% (2406 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 48.9% (2452 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 50.0% (2508 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 51.1% (2565 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 51.9% (2605 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 53.1% (2664 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 53.9% (2705 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 55.0% (2757 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 56.1% (2816 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 57.2% (2871 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 58.1% (2914 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 59.4% (2978 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 60.2% (3021 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 61.0% (3061 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 62.2% (3121 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 63.6% (3191 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 64.3% (3225 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 65.4% (3283 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 66.2% (3321 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 67.8% (3403 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 69.0% (3460 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 69.8% (3500 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 70.3% (3529 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 71.2% (3572 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 72.2% (3621 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 73.3% (3677 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 74.2% (3725 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 75.3% (3779 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 76.4% (3831 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 77.4% (3881 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 78.4% (3935 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 79.4% (3985 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 80.4% (4035 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 81.4% (4085 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 82.5% (4138 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 83.5% (4190 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 84.4% (4234 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 85.5% (4289 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 86.4% (4335 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 87.5% (4388 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 88.5% (4442 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 89.7% (4499 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 90.6% (4543 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 91.5% (4591 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 93.5% (4692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 94.6% (4746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 95.6% (4796 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.0; 2513 / 5017 (P = 50.09%) round 16]               
[00:00:00] Finding cutoff p=947 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=947 1.6% (78 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 2.9% (145 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 4.1% (205 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 5.3% (268 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 6.5% (326 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 7.7% (384 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 8.8% (444 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 10.8% (544 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 11.9% (599 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 13.3% (666 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 14.5% (729 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 16.0% (802 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 17.7% (889 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 19.3% (967 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 22.2% (1112 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 24.0% (1203 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 25.0% (1252 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 26.0% (1304 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 27.0% (1355 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 27.9% (1402 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 28.9% (1452 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 29.9% (1501 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 31.2% (1563 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 31.8% (1597 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 32.8% (1647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 33.8% (1695 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 34.9% (1749 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 35.9% (1803 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 37.2% (1867 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 38.2% (1916 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 39.2% (1969 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 40.0% (2009 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 41.1% (2062 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 41.9% (2101 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 43.1% (2161 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 44.0% (2209 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 44.9% (2252 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 45.9% (2304 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 47.2% (2367 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 48.0% (2408 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 49.0% (2458 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 50.3% (2523 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 51.2% (2568 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 51.8% (2601 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 53.1% (2664 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 54.3% (2726 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 55.1% (2762 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 56.1% (2815 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 57.5% (2886 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 58.2% (2919 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 59.3% (2975 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 60.1% (3013 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 61.3% (3073 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 62.1% (3116 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 63.2% (3172 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 64.2% (3223 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 65.2% (3273 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 66.2% (3320 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 67.1% (3367 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 68.5% (3436 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 69.5% (3487 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 70.2% (3520 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 71.4% (3583 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 72.3% (3627 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 73.2% (3672 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 74.3% (3727 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 75.5% (3786 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 76.3% (3826 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 77.5% (3888 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 78.6% (3943 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 79.6% (3992 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 81.5% (4090 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 82.6% (4144 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 83.6% (4195 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 84.5% (4239 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 85.5% (4289 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 86.5% (4342 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 87.4% (4387 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 88.5% (4440 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 89.5% (4489 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 90.6% (4543 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 91.5% (4592 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 92.7% (4651 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 93.6% (4696 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 95.6% (4797 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.7; 2628 / 5017 (P = 52.38%) round 17]               
[00:00:00] Finding cutoff p=943 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=943 1.5% (77 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 2.9% (145 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 4.0% (200 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 5.3% (266 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 6.5% (325 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 7.7% (384 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 8.8% (442 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 10.0% (501 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 11.0% (554 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 12.4% (621 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 13.7% (687 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 15.1% (757 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 16.6% (832 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 18.0% (901 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 21.0% (1053 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 23.6% (1184 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 24.5% (1231 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 25.4% (1276 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 26.9% (1351 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 27.9% (1401 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 28.8% (1445 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 30.2% (1515 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 31.0% (1556 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 32.0% (1606 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 32.9% (1651 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 33.9% (1701 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 35.0% (1755 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 36.2% (1818 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 37.3% (1871 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 38.6% (1939 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 40.0% (2006 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 40.8% (2046 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 42.0% (2108 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 42.9% (2152 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 43.8% (2198 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 44.9% (2255 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 46.2% (2319 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 48.0% (2407 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 48.8% (2449 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 50.1% (2514 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 51.0% (2558 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 51.8% (2601 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 53.3% (2676 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 54.0% (2708 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 55.1% (2765 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 56.5% (2835 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 57.2% (2871 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 58.0% (2912 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 59.1% (2965 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 60.3% (3024 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 61.6% (3091 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 62.4% (3133 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 63.3% (3177 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 64.2% (3223 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 65.4% (3279 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 66.3% (3324 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 67.2% (3370 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 68.6% (3443 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 69.2% (3470 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 70.2% (3523 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 71.2% (3571 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 72.4% (3631 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 73.5% (3687 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 74.2% (3724 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 75.3% (3779 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 76.3% (3826 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 78.6% (3942 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 79.6% (3993 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 80.5% (4040 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 81.5% (4087 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 83.0% (4165 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 83.4% (4185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 84.5% (4237 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 85.4% (4287 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 86.4% (4337 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 87.6% (4394 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 88.6% (4445 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 89.5% (4492 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 90.6% (4543 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 91.5% (4590 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 92.6% (4645 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 93.6% (4696 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 95.6% (4796 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.3; 2775 / 5017 (P = 55.31%) round 18]               
[00:00:00] Finding cutoff p=938 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=938 1.5% (77 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 2.9% (144 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 4.0% (199 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 5.2% (262 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 6.4% (319 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 7.5% (377 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 8.5% (428 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 9.6% (484 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 10.7% (536 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 11.9% (597 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 13.2% (664 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 14.5% (729 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 16.0% (805 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 17.4% (872 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 20.0% (1004 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 23.2% (1166 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 24.0% (1202 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 25.0% (1254 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 25.8% (1293 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 26.8% (1344 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 27.8% (1393 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 28.5% (1430 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 30.0% (1503 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 30.7% (1540 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 31.9% (1600 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 32.9% (1652 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 34.0% (1707 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 35.0% (1758 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 36.5% (1829 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 38.0% (1907 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 38.7% (1942 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 39.7% (1994 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 41.0% (2056 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 41.8% (2095 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 43.0% (2156 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 44.1% (2212 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 45.5% (2282 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 46.3% (2322 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 47.4% (2376 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 48.1% (2415 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 48.9% (2454 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 50.2% (2517 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 51.1% (2562 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 52.2% (2620 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 52.9% (2655 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 54.1% (2715 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 55.1% (2763 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 56.1% (2813 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 57.1% (2867 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 58.3% (2927 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 59.1% (2963 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 60.4% (3030 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 61.2% (3069 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 62.4% (3129 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 63.1% (3164 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 64.2% (3223 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 65.3% (3274 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 66.1% (3318 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 67.2% (3369 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 68.2% (3422 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 69.3% (3477 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 70.5% (3539 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 71.4% (3584 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 72.4% (3632 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 73.4% (3682 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 74.4% (3735 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 75.6% (3792 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 76.4% (3833 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 77.5% (3889 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 78.6% (3941 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 79.4% (3986 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 80.5% (4041 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 81.5% (4089 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 82.5% (4141 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 83.5% (4187 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 84.4% (4236 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 85.4% (4286 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 86.4% (4335 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 87.7% (4400 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 88.5% (4441 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 89.5% (4490 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 90.6% (4545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 91.6% (4594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 92.6% (4644 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 93.6% (4698 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 94.6% (4748 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 95.6% (4797 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 96.6% (4845 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.8; 2940 / 5017 (P = 58.60%) round 19]               
[00:00:00] Finding cutoff p=934 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=934 1.5% (74 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 2.8% (138 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 3.7% (185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 5.1% (258 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 6.1% (306 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 7.8% (389 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 8.7% (434 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 9.6% (480 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 10.6% (531 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 11.7% (586 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 13.1% (657 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 14.6% (733 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 15.7% (787 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 18.3% (918 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 21.1% (1061 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 22.7% (1140 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 23.6% (1185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 24.8% (1246 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 25.8% (1292 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 26.6% (1334 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 27.5% (1378 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 28.9% (1448 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 29.6% (1487 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 30.5% (1530 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 31.7% (1588 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 33.3% (1670 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 34.2% (1714 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 35.4% (1778 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 36.4% (1826 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 37.6% (1888 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 39.1% (1961 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 39.9% (2003 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 41.1% (2063 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 42.2% (2117 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 43.6% (2185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 44.4% (2227 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 45.3% (2272 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 46.1% (2315 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 46.9% (2355 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 48.2% (2416 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 48.9% (2453 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 49.9% (2505 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 51.5% (2584 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 52.3% (2625 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 53.2% (2667 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 54.1% (2715 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 55.1% (2764 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 55.9% (2806 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 57.2% (2868 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 58.1% (2915 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 59.3% (2977 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 60.1% (3013 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 61.1% (3064 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 62.0% (3111 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 63.0% (3162 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 64.3% (3225 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 65.3% (3277 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 66.3% (3328 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 67.5% (3385 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 68.3% (3429 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 69.5% (3488 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 70.2% (3523 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 71.3% (3578 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 72.2% (3621 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 73.3% (3676 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 74.3% (3727 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 75.5% (3789 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 76.7% (3848 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 77.9% (3909 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 78.5% (3936 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 79.3% (3980 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 80.4% (4032 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 81.9% (4109 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 82.9% (4157 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 83.5% (4189 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 84.5% (4241 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 85.5% (4292 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 86.5% (4340 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 87.5% (4392 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 88.6% (4443 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 89.6% (4496 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 90.5% (4539 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 91.6% (4594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 92.6% (4644 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 93.7% (4703 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 96.6% (4847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 97.6% (4898 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.4; 3088 / 5017 (P = 61.55%) round 20]               
[00:00:00] Finding cutoff p=930 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=930 1.5% (77 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 2.7% (135 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 3.6% (180 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 5.1% (254 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 6.0% (299 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 7.0% (350 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 8.5% (424 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 9.6% (480 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 10.6% (530 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 11.6% (581 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 12.7% (638 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 14.3% (719 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 15.4% (773 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 16.7% (839 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 19.6% (982 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 22.6% (1134 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 23.6% (1182 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 24.5% (1231 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 25.9% (1301 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 26.9% (1351 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 27.7% (1391 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 29.2% (1463 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 30.0% (1507 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 30.7% (1542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 31.5% (1582 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 32.8% (1646 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 34.0% (1707 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 34.8% (1748 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 36.2% (1818 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 37.2% (1865 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 38.6% (1935 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 39.4% (1975 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 40.2% (2018 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 40.9% (2054 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 41.7% (2091 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 43.3% (2171 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 44.8% (2249 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 46.3% (2322 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 47.0% (2359 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 47.8% (2397 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 48.9% (2452 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 50.2% (2521 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 51.0% (2560 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 52.2% (2619 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 53.1% (2662 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 54.0% (2709 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 55.1% (2766 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 56.2% (2818 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 57.3% (2874 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 58.1% (2913 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 59.0% (2960 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 60.3% (3027 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 61.0% (3060 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 62.6% (3143 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 63.3% (3175 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 64.1% (3214 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 65.3% (3274 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 66.4% (3329 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 67.4% (3379 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 68.2% (3424 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 69.6% (3492 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 70.2% (3522 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 71.2% (3570 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 72.4% (3633 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 73.5% (3687 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 74.4% (3733 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 76.7% (3847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 77.8% (3903 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 79.4% (3984 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 80.7% (4050 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 81.8% (4102 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 82.8% (4155 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 83.4% (4185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 84.6% (4242 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 85.5% (4291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 86.5% (4339 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 87.5% (4392 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 88.4% (4437 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 89.6% (4497 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 90.5% (4542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 91.5% (4593 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 96.6% (4847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.0; 3218 / 5017 (P = 64.14%) round 21]               
[00:00:00] Finding cutoff p=926 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=926 1.6% (78 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 2.8% (140 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 3.9% (196 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 5.2% (262 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 6.1% (308 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 7.3% (365 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 8.6% (431 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 9.7% (488 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 10.6% (533 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 11.6% (582 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 13.2% (663 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 14.8% (741 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 15.6% (784 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 18.5% (928 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 21.3% (1069 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 22.6% (1136 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 23.5% (1181 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 24.5% (1227 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 25.7% (1287 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 26.5% (1332 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 27.9% (1400 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 28.7% (1442 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 29.6% (1487 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 30.8% (1544 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 31.6% (1584 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 33.0% (1658 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 34.4% (1727 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 35.4% (1774 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 36.6% (1835 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 37.5% (1879 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 38.0% (1905 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 38.8% (1949 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 39.7% (1994 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 41.5% (2084 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 43.3% (2171 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 44.1% (2211 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 45.4% (2280 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 46.2% (2319 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 47.0% (2358 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 48.2% (2417 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 49.0% (2456 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 49.9% (2504 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 51.6% (2588 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 52.4% (2627 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 53.2% (2667 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 54.2% (2717 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 55.0% (2757 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 55.9% (2806 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 57.1% (2865 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 58.3% (2924 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 59.2% (2969 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 60.0% (3010 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 61.1% (3064 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 62.1% (3117 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 63.2% (3170 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 64.2% (3221 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 65.3% (3278 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 66.3% (3328 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 67.2% (3371 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 68.6% (3442 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 69.4% (3480 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 70.2% (3524 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 71.5% (3588 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 72.3% (3629 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 73.2% (3674 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 74.4% (3735 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 75.7% (3797 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 76.8% (3855 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 77.5% (3887 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 78.4% (3935 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 79.4% (3981 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 81.0% (4063 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 81.5% (4087 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 82.4% (4134 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 83.4% (4186 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 84.4% (4236 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 85.5% (4288 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 86.4% (4335 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 87.7% (4399 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 88.5% (4442 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 89.6% (4496 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 90.5% (4539 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 91.7% (4599 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 92.6% (4647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 93.6% (4697 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 95.6% (4796 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 96.7% (4853 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=926 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.6; 3321 / 5017 (P = 66.19%) round 22]               
[00:00:00] Finding cutoff p=923 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=923 1.5% (76 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 2.7% (134 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 3.6% (180 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 4.9% (248 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 5.9% (298 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 6.9% (347 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 7.7% (388 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 8.8% (439 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 9.9% (496 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 10.7% (537 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 11.5% (579 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 12.9% (645 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 14.5% (726 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 15.4% (772 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 18.3% (920 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 21.1% (1059 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 22.6% (1133 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 23.5% (1178 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 24.8% (1243 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 25.5% (1280 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 26.5% (1329 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 28.1% (1410 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 28.8% (1444 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 29.6% (1484 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 30.8% (1546 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 31.9% (1600 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 32.9% (1652 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 33.8% (1698 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 35.0% (1758 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 35.8% (1798 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 37.4% (1876 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 38.1% (1913 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 38.8% (1949 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 39.7% (1993 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 41.4% (2078 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 43.0% (2156 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 44.5% (2231 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 45.2% (2268 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 45.9% (2303 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 47.0% (2358 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 48.2% (2418 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 48.9% (2452 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 50.0% (2508 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 51.1% (2566 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 51.9% (2602 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 53.1% (2665 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 53.9% (2703 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 55.1% (2762 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 56.1% (2813 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 57.2% (2868 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 58.5% (2933 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 59.2% (2971 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 60.3% (3023 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 61.0% (3060 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 62.9% (3154 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 63.6% (3189 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 64.1% (3218 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 65.5% (3284 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 66.5% (3337 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 68.1% (3418 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 69.4% (3484 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 70.3% (3527 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 71.3% (3579 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 72.2% (3622 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 73.5% (3685 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 74.3% (3729 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 75.5% (3786 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 76.4% (3833 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 77.6% (3894 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 78.3% (3929 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 79.6% (3993 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 80.4% (4034 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 81.6% (4095 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 82.6% (4145 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 83.4% (4184 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 84.4% (4234 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 85.5% (4292 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 86.4% (4337 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 87.4% (4387 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 88.5% (4441 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 89.6% (4494 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 90.6% (4545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 91.5% (4591 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 94.6% (4745 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 95.6% (4795 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=923 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.3; 3399 / 5017 (P = 67.75%) round 23]               
[00:00:00] Finding cutoff p=919 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=919 1.5% (75 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 2.8% (140 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 3.6% (181 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 5.0% (250 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 5.8% (292 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 6.8% (343 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 7.6% (382 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 8.5% (424 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 9.5% (475 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 10.3% (518 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 12.1% (607 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 13.8% (690 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 14.8% (744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 17.9% (898 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 20.5% (1027 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 21.7% (1088 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 23.1% (1161 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 24.1% (1208 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 24.9% (1251 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 25.7% (1291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 26.5% (1331 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 27.9% (1399 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 28.5% (1431 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 29.5% (1481 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 30.6% (1534 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 32.2% (1615 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 33.1% (1659 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 34.3% (1721 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 35.2% (1767 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 36.7% (1840 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 37.8% (1894 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 39.0% (1956 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 39.8% (1995 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 40.8% (2049 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 42.3% (2121 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 43.1% (2164 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 44.2% (2216 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 44.9% (2252 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 45.9% (2303 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 47.1% (2364 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 48.3% (2421 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 49.0% (2460 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 50.0% (2509 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 51.1% (2566 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 51.9% (2606 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 53.1% (2665 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 53.9% (2703 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 55.0% (2758 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 55.9% (2807 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 57.3% (2874 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 58.1% (2917 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 59.1% (2963 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 60.2% (3021 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 61.5% (3085 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 62.9% (3154 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 63.4% (3181 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 64.7% (3247 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 65.3% (3274 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 66.2% (3322 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 67.1% (3368 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 68.4% (3434 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 69.1% (3468 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 70.2% (3520 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 71.2% (3572 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 72.4% (3631 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 73.2% (3674 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 74.3% (3729 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 75.3% (3780 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 76.5% (3838 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 77.4% (3883 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 78.6% (3944 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 79.4% (3982 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 80.7% (4047 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 81.8% (4103 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 82.6% (4142 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 83.4% (4183 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 84.5% (4238 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 85.4% (4287 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 86.4% (4337 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 87.6% (4395 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 88.5% (4438 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 89.5% (4489 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 90.6% (4545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 91.6% (4594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 92.6% (4644 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 93.6% (4695 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 96.7% (4849 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 97.6% (4898 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.9; 3508 / 5017 (P = 69.92%) round 24]               
[00:00:00] Finding cutoff p=915 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=915 1.5% (76 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 3.5% (174 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 4.2% (210 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 5.8% (289 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 6.7% (337 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 7.6% (379 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 8.3% (418 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 9.2% (460 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 10.2% (514 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 11.4% (572 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 12.8% (643 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 14.4% (720 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 17.4% (873 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 20.2% (1014 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 21.5% (1077 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 22.8% (1142 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 23.4% (1176 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 24.7% (1237 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 25.4% (1275 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 27.0% (1356 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 27.7% (1388 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 28.7% (1440 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 29.5% (1482 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 31.0% (1553 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 31.7% (1589 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 32.8% (1646 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 33.7% (1692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 35.0% (1756 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 35.8% (1796 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 36.9% (1851 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 37.6% (1887 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 39.3% (1972 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 41.0% (2059 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 41.8% (2095 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 42.8% (2148 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 44.1% (2210 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 44.8% (2246 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 45.7% (2295 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 47.1% (2363 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 47.9% (2401 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 49.0% (2460 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 49.9% (2505 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 51.1% (2562 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 51.9% (2604 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 53.2% (2668 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 54.1% (2715 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 55.3% (2774 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 56.0% (2808 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 57.2% (2872 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 58.1% (2913 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 59.3% (2976 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 60.1% (3014 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 61.1% (3063 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 62.5% (3138 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 63.1% (3166 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 64.2% (3219 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 65.1% (3265 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 66.2% (3320 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 67.4% (3383 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 68.3% (3428 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 69.3% (3475 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 70.2% (3522 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 71.4% (3580 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 72.8% (3653 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 73.2% (3674 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 74.4% (3731 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 75.3% (3776 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 76.6% (3845 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 77.5% (3887 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 78.8% (3955 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 79.4% (3983 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 80.5% (4038 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 81.4% (4086 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 82.5% (4141 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 83.4% (4183 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 84.4% (4234 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 85.9% (4308 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 86.6% (4347 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 87.5% (4390 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 88.6% (4443 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 89.5% (4490 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 90.7% (4552 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 91.5% (4593 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 92.6% (4644 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 93.6% (4695 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 94.6% (4748 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 95.7% (4799 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 96.6% (4845 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.5; 3602 / 5017 (P = 71.80%) round 25]               
[00:00:00] Finding cutoff p=911 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=911 1.4% (71 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 2.5% (127 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 3.4% (170 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 4.3% (216 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 5.6% (280 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 6.5% (325 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 7.4% (371 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 8.6% (433 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 9.4% (472 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 10.5% (529 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 11.8% (594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 13.0% (650 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 15.4% (774 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 18.1% (909 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 19.2% (964 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 20.2% (1013 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 21.4% (1072 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 22.8% (1145 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 23.6% (1185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 24.4% (1226 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 25.9% (1299 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 26.5% (1329 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 27.7% (1390 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 28.7% (1441 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 29.5% (1482 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 31.3% (1569 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 31.7% (1591 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 33.0% (1655 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 33.9% (1699 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 34.9% (1753 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 35.8% (1794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 36.9% (1852 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 38.1% (1912 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 39.7% (1993 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 41.0% (2059 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 42.0% (2106 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 42.9% (2154 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 43.8% (2195 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 44.9% (2254 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 45.9% (2303 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 47.0% (2358 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 48.0% (2407 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 49.1% (2462 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 50.2% (2518 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 51.2% (2568 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 51.8% (2601 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 53.1% (2663 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 54.1% (2716 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 55.1% (2764 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 55.9% (2805 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 57.0% (2861 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 58.1% (2913 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 59.0% (2960 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 60.4% (3030 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 61.7% (3095 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 62.3% (3127 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 63.2% (3171 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 64.4% (3231 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 65.1% (3266 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 66.3% (3326 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 67.2% (3372 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 68.3% (3429 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 69.4% (3481 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 70.2% (3520 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 71.3% (3575 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 72.5% (3638 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 73.3% (3676 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 74.3% (3727 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 76.2% (3825 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 77.7% (3899 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 78.4% (3932 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 79.6% (3992 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 80.5% (4040 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 81.3% (4081 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 82.4% (4135 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 83.5% (4190 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 84.6% (4245 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 85.5% (4291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 86.4% (4335 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 87.6% (4393 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 88.6% (4444 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 89.5% (4492 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 90.6% (4545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 91.5% (4590 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 92.5% (4641 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 93.5% (4693 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 96.7% (4850 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 97.6% (4899 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.1; 3680 / 5017 (P = 73.35%) round 26]               
[00:00:00] Finding cutoff p=908 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=908 1.5% (74 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 2.6% (128 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 3.4% (169 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 4.6% (229 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 5.4% (269 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 6.2% (309 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 7.5% (376 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 8.8% (439 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 9.6% (483 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 10.9% (546 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 12.0% (602 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 12.8% (644 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 15.2% (764 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 18.0% (904 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 19.2% (964 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 20.2% (1014 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 21.6% (1084 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 22.6% (1136 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 24.0% (1206 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 24.8% (1244 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 26.4% (1322 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 27.1% (1361 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 27.8% (1395 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 28.6% (1437 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 30.3% (1521 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 30.9% (1549 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 32.2% (1616 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 32.9% (1652 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 34.5% (1729 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 35.2% (1767 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 35.9% (1803 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 36.8% (1847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 38.0% (1904 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 39.2% (1966 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 40.7% (2043 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 42.2% (2118 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 43.0% (2155 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 43.8% (2195 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 44.8% (2247 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 45.8% (2297 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 46.9% (2355 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 47.8% (2398 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 48.8% (2448 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 50.1% (2513 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 51.5% (2584 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 52.3% (2622 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 53.5% (2683 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 54.2% (2720 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 55.1% (2762 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 56.1% (2813 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 57.3% (2875 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 58.5% (2933 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 59.0% (2960 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 60.0% (3009 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 61.4% (3079 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 62.4% (3129 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 63.1% (3166 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 64.1% (3214 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 65.4% (3279 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 66.2% (3320 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 67.2% (3371 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 68.3% (3426 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 69.2% (3470 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 70.4% (3532 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 72.0% (3611 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 72.5% (3636 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 73.6% (3693 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 74.3% (3727 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 75.7% (3797 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 76.7% (3846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 78.0% (3911 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 78.5% (3940 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 79.4% (3985 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 80.5% (4039 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 81.4% (4083 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 82.3% (4131 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 83.4% (4184 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 84.5% (4241 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 85.4% (4286 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 86.5% (4338 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 87.6% (4394 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 88.5% (4439 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 89.5% (4490 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 90.5% (4542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 91.5% (4592 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 92.6% (4648 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 93.5% (4692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 95.6% (4797 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 96.6% (4848 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 98.6% (4948 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.8; 3740 / 5017 (P = 74.55%) round 27]               
[00:00:00] Finding cutoff p=905 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=905 1.5% (75 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 2.6% (130 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 3.4% (170 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 4.6% (230 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 5.5% (274 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 6.8% (339 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 7.4% (373 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 8.3% (414 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 9.7% (485 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 10.5% (527 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 11.5% (579 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 13.0% (651 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 13.9% (699 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 16.4% (822 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 19.4% (971 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 20.6% (1032 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 21.4% (1073 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 22.9% (1149 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 23.5% (1177 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 24.5% (1227 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 26.1% (1307 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 26.7% (1341 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 27.5% (1381 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 28.5% (1428 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 29.8% (1497 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 30.6% (1536 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 31.9% (1598 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 32.6% (1634 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 34.0% (1704 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 34.8% (1746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 35.9% (1799 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 37.1% (1863 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 38.3% (1921 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 39.9% (2002 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 40.7% (2044 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 42.0% (2107 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 43.1% (2160 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 44.0% (2207 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 44.9% (2251 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 46.5% (2333 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 47.0% (2359 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 47.8% (2398 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 48.9% (2455 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 50.2% (2517 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 50.9% (2554 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 51.9% (2603 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 53.9% (2705 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 55.2% (2770 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 56.1% (2813 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 57.2% (2871 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 58.2% (2922 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 59.0% (2959 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 60.8% (3048 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 61.2% (3072 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 62.1% (3114 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 63.2% (3171 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 64.3% (3227 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 65.2% (3271 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 66.4% (3330 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 67.2% (3369 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 68.2% (3424 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 69.3% (3479 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 70.9% (3556 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 71.5% (3586 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 72.7% (3646 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 73.4% (3683 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 74.9% (3757 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 75.2% (3775 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 77.1% (3867 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 77.7% (3897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 78.7% (3947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 79.3% (3980 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 81.5% (4090 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 82.4% (4133 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 83.7% (4199 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 84.8% (4255 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 85.5% (4291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 86.4% (4335 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 87.5% (4388 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 88.5% (4441 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 89.6% (4493 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 90.5% (4542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 91.5% (4593 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 92.5% (4643 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 94.6% (4746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 95.7% (4800 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 98.6% (4948 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=905 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.5; 3799 / 5017 (P = 75.72%) round 28]               
[00:00:00] Finding cutoff p=902 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=902 1.4% (70 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 2.6% (129 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 3.5% (175 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 4.7% (234 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 5.5% (277 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 6.3% (314 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 7.2% (359 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 8.5% (426 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 9.3% (465 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 10.4% (520 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 11.4% (573 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 12.7% (639 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 13.8% (693 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 16.4% (825 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 19.3% (966 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 20.3% (1017 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 21.8% (1096 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 22.7% (1137 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 23.4% (1176 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 24.4% (1226 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 25.9% (1299 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 26.5% (1331 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 27.6% (1385 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 28.6% (1437 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 29.7% (1491 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 31.5% (1580 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 32.2% (1616 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 33.6% (1685 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 34.8% (1746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 35.8% (1794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 37.4% (1875 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 39.2% (1965 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 40.0% (2005 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 40.9% (2054 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 41.7% (2091 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 42.9% (2154 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 44.0% (2206 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 45.0% (2259 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 46.3% (2325 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 47.0% (2359 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 47.9% (2403 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 49.1% (2464 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 49.9% (2502 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 51.2% (2571 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 51.9% (2604 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 53.2% (2668 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 54.2% (2721 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 55.1% (2763 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 55.9% (2805 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 57.1% (2867 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 58.3% (2927 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 59.0% (2959 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 60.6% (3042 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 61.3% (3074 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 62.2% (3119 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 63.2% (3169 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 64.5% (3235 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 65.1% (3268 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 66.3% (3327 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 67.1% (3367 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 68.2% (3424 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 69.4% (3481 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 70.9% (3555 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 71.4% (3582 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 72.6% (3643 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 73.3% (3679 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 74.8% (3753 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 75.7% (3799 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 77.1% (3868 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 77.6% (3894 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 78.6% (3942 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 80.5% (4039 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 81.4% (4082 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 82.3% (4131 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 83.4% (4182 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 84.4% (4236 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 85.6% (4294 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 86.5% (4341 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 87.4% (4386 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 88.4% (4437 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 89.8% (4504 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 90.6% (4547 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 91.5% (4590 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 92.6% (4646 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 93.6% (4695 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 95.6% (4795 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 96.7% (4850 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 98.6% (4948 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=902 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.2; 3857 / 5017 (P = 76.88%) round 29]               
[00:00:00] Finding cutoff p=899 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=899 1.4% (69 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 2.6% (128 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 3.4% (170 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 4.5% (224 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 5.2% (261 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 6.5% (326 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 7.4% (371 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 8.3% (417 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 9.4% (473 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 10.6% (532 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 11.7% (589 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 12.6% (632 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 15.3% (768 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 18.0% (901 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 19.0% (954 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 20.0% (1004 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 21.2% (1063 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 22.7% (1140 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 23.6% (1184 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 25.7% (1287 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 26.7% (1342 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 27.5% (1379 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 28.6% (1437 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 29.9% (1501 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 30.7% (1538 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 31.9% (1598 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 32.7% (1639 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 34.0% (1705 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 34.9% (1750 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 35.9% (1803 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 36.8% (1845 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 38.5% (1931 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 40.1% (2014 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 40.7% (2044 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 41.7% (2094 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 42.8% (2149 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 43.7% (2194 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 45.0% (2260 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 46.4% (2328 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 47.1% (2364 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 48.0% (2409 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 48.9% (2453 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 50.0% (2507 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 51.2% (2570 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 51.9% (2605 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 53.1% (2665 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 54.0% (2711 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 55.2% (2770 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 56.4% (2829 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 57.2% (2871 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 58.0% (2911 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 59.1% (2964 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 60.1% (3017 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 61.4% (3080 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 62.2% (3120 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 63.5% (3187 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 64.3% (3225 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 65.2% (3272 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 66.3% (3324 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 67.6% (3393 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 68.2% (3424 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 69.2% (3472 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 70.2% (3523 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 71.4% (3584 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 72.3% (3627 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 73.2% (3673 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 74.2% (3725 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 75.3% (3779 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 76.7% (3848 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 77.3% (3877 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 78.4% (3932 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 79.3% (3979 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 81.4% (4082 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 82.4% (4133 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 83.4% (4186 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 84.4% (4235 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 85.5% (4289 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 86.7% (4350 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 87.5% (4388 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 88.5% (4442 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 89.5% (4492 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 90.5% (4542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 91.8% (4606 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 92.5% (4643 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 93.5% (4692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 95.6% (4795 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 96.6% (4847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 98.6% (4948 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.9; 3910 / 5017 (P = 77.94%) round 30]               
[00:00:00] Finding cutoff p=888 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=888 1.4% (71 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 2.6% (130 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 3.3% (166 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 4.3% (218 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 5.5% (276 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 6.2% (312 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 7.3% (366 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 8.3% (417 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 9.3% (465 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 10.6% (532 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 11.9% (598 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 13.4% (672 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 15.8% (791 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 18.6% (932 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 19.6% (984 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 20.8% (1044 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 21.9% (1100 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 22.6% (1136 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 23.4% (1174 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 25.2% (1266 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 25.9% (1300 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 26.6% (1336 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 27.6% (1385 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 29.3% (1468 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 29.9% (1500 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 30.9% (1551 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 31.6% (1583 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 33.0% (1655 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 33.7% (1693 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 34.9% (1751 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 35.6% (1788 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 36.6% (1836 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 37.8% (1897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 39.4% (1979 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 40.1% (2010 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 40.9% (2053 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 42.0% (2105 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 42.8% (2149 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 44.3% (2222 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 44.8% (2250 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 46.0% (2309 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 46.9% (2354 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 48.1% (2415 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 49.1% (2463 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 50.6% (2537 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 51.3% (2575 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 52.4% (2631 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 53.5% (2682 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 54.5% (2736 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 55.2% (2769 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 55.9% (2806 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 57.0% (2859 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 58.3% (2925 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 59.1% (2964 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 60.5% (3036 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 61.1% (3067 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 62.3% (3125 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 63.1% (3165 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 64.1% (3216 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 65.4% (3283 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 66.2% (3322 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 67.1% (3368 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 68.2% (3423 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 69.3% (3478 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 70.6% (3543 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 71.8% (3602 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 72.3% (3625 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 73.3% (3679 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 74.5% (3737 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 75.6% (3793 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 77.1% (3868 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 77.6% (3893 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 78.5% (3937 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 79.4% (3985 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 80.6% (4043 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 81.4% (4083 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 82.4% (4134 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 83.4% (4182 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 84.7% (4248 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 85.4% (4286 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 86.6% (4343 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 87.4% (4387 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 88.4% (4437 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 89.6% (4496 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 90.5% (4542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 91.5% (4592 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 93.6% (4697 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 94.6% (4746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 95.8% (4805 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 96.6% (4848 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 88.8; 3985 / 5017 (P = 79.43%) round 31]               
[00:00:00] Finding cutoff p=878 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=878 1.4% (69 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 2.6% (129 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 3.3% (165 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 4.4% (222 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 5.3% (268 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 6.2% (311 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 7.5% (374 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 8.5% (424 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 9.4% (472 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 10.8% (541 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 11.9% (598 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 12.8% (642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 15.2% (763 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 17.8% (891 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 18.7% (937 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 19.7% (986 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 20.8% (1046 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 22.5% (1129 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 23.4% (1175 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 25.0% (1253 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 25.6% (1286 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 26.9% (1348 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 28.0% (1403 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 28.9% (1448 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 29.6% (1485 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 30.9% (1550 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 32.3% (1620 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 33.0% (1656 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 33.6% (1688 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 34.7% (1741 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 36.5% (1830 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 37.8% (1897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 38.6% (1939 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 39.6% (1989 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 41.0% (2056 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 41.9% (2103 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 42.8% (2149 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 43.9% (2201 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 44.7% (2245 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 46.0% (2310 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 46.9% (2355 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 48.0% (2408 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 48.8% (2448 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 50.0% (2511 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 51.5% (2582 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 52.4% (2629 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 53.5% (2682 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 54.3% (2726 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 55.3% (2775 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 56.1% (2816 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 57.3% (2875 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 58.1% (2914 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 59.6% (2989 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 60.2% (3018 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 61.1% (3064 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 62.2% (3120 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 63.2% (3169 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 64.3% (3227 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 65.1% (3268 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 66.1% (3317 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 67.3% (3374 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 68.4% (3430 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 69.7% (3495 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 70.2% (3522 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 71.3% (3578 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 72.3% (3628 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 73.4% (3682 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 74.4% (3734 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 75.9% (3810 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 76.6% (3842 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 77.6% (3894 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 78.5% (3937 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 79.3% (3979 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 80.4% (4035 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 81.3% (4080 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 82.5% (4137 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 83.5% (4188 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 84.8% (4252 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 85.4% (4284 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 86.7% (4352 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 87.6% (4395 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 88.5% (4440 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 89.6% (4493 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 90.5% (4542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 91.6% (4594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 93.5% (4693 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 94.5% (4743 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 95.6% (4795 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 96.6% (4845 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 97.6% (4898 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 98.6% (4948 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 87.8; 4064 / 5017 (P = 81.00%) round 32]               
[00:00:00] Finding cutoff p=868 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=868 1.4% (70 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 2.8% (139 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 3.5% (178 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 4.7% (234 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 5.4% (269 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 6.2% (312 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 7.5% (374 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 8.5% (426 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 9.3% (466 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 10.6% (534 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 11.7% (587 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 12.4% (620 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 15.4% (773 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 18.2% (914 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 19.0% (955 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 20.1% (1009 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 21.1% (1057 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 22.5% (1127 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 23.5% (1178 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 24.9% (1248 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 25.8% (1295 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 26.7% (1340 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 27.4% (1377 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 28.7% (1439 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 30.1% (1510 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 31.0% (1554 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 32.3% (1618 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 33.0% (1654 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 33.8% (1694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 34.6% (1735 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 36.4% (1826 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 38.1% (1910 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 38.9% (1951 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 39.8% (1997 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 40.8% (2048 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 41.8% (2096 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 43.2% (2166 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 43.7% (2193 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 44.8% (2249 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 45.9% (2304 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 46.8% (2350 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 48.0% (2410 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 48.8% (2448 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 50.1% (2512 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 51.3% (2573 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 52.2% (2618 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 53.0% (2660 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 54.0% (2708 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 55.4% (2781 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 56.0% (2810 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 57.2% (2870 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 58.7% (2947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 59.2% (2968 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 60.0% (3011 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 61.4% (3081 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 62.4% (3130 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 63.1% (3168 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 64.2% (3223 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 65.2% (3271 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 66.6% (3340 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 67.3% (3375 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 68.1% (3418 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 69.4% (3484 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 70.6% (3543 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 71.3% (3577 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 72.4% (3632 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 73.4% (3683 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 74.3% (3729 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 75.7% (3798 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 76.3% (3826 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 77.4% (3883 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 78.4% (3932 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 79.4% (3983 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 80.3% (4030 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 81.3% (4080 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 82.8% (4153 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 83.6% (4194 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 84.5% (4238 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 85.4% (4287 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 86.5% (4338 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 87.4% (4386 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 88.8% (4456 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 89.6% (4494 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 90.6% (4545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 91.5% (4590 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 92.6% (4644 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 93.5% (4693 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 94.6% (4746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 95.6% (4796 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 97.6% (4899 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 98.6% (4948 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 86.8; 4157 / 5017 (P = 82.86%) round 33]               
[00:00:00] Finding cutoff p=858 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=858 1.5% (73 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 2.6% (130 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 3.3% (164 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 4.4% (220 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 5.6% (281 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 6.4% (319 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 7.2% (362 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 8.3% (417 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 10.1% (506 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 11.1% (557 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 11.7% (589 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 14.6% (730 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 17.3% (866 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 18.4% (922 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 20.1% (1009 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 20.9% (1049 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 22.3% (1121 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 23.1% (1158 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 24.5% (1229 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 25.7% (1289 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 26.8% (1343 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 27.6% (1385 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 28.6% (1436 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 29.9% (1502 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 30.6% (1533 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 31.9% (1600 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 32.7% (1643 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 34.0% (1705 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 34.6% (1736 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 36.1% (1813 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 38.0% (1908 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 38.7% (1941 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 40.0% (2009 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 40.7% (2040 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 41.7% (2092 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 42.8% (2146 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 44.4% (2226 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 45.0% (2258 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 46.0% (2308 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 46.9% (2351 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 48.0% (2406 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 49.2% (2470 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 50.0% (2507 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 51.2% (2569 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 52.1% (2616 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 53.3% (2672 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 53.9% (2704 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 55.5% (2785 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 56.1% (2816 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 57.1% (2866 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 58.8% (2950 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 59.3% (2975 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 60.1% (3015 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 61.1% (3067 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 62.0% (3112 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 63.2% (3169 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 64.3% (3224 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 65.2% (3272 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 66.2% (3320 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 67.3% (3376 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 68.4% (3431 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 69.7% (3499 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 71.0% (3564 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 71.5% (3589 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 72.5% (3635 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 73.4% (3683 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 74.5% (3737 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 76.2% (3824 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 76.9% (3860 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 77.8% (3904 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 78.4% (3935 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 79.4% (3981 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 80.6% (4042 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 81.3% (4081 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 82.4% (4136 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 83.4% (4184 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 84.8% (4254 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 85.4% (4284 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 86.4% (4337 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 87.6% (4393 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 88.5% (4441 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 89.6% (4494 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 90.5% (4539 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 91.7% (4603 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 92.6% (4644 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 93.6% (4695 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 95.6% (4795 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=858 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.8; 4245 / 5017 (P = 84.61%) round 34]               
[00:00:00] Finding cutoff p=849 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=849 1.4% (72 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 2.7% (135 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 3.5% (175 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 4.5% (228 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 5.3% (266 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 6.3% (316 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 7.2% (360 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 8.5% (425 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 9.8% (492 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 10.9% (548 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 11.8% (592 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 14.3% (716 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 16.7% (839 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 18.0% (901 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 19.1% (959 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 20.8% (1042 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 21.4% (1074 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 22.4% (1122 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 23.7% (1188 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 24.5% (1231 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 25.6% (1286 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 26.8% (1344 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 27.7% (1390 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 29.1% (1459 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 29.9% (1501 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 31.1% (1560 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 31.7% (1591 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 32.6% (1634 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 33.7% (1691 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 35.1% (1762 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 36.9% (1849 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 38.5% (1931 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 39.1% (1960 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 40.0% (2007 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 40.7% (2040 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 41.8% (2095 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 42.7% (2144 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 44.2% (2218 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 44.7% (2245 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 46.0% (2309 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 47.1% (2361 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 48.0% (2408 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 49.2% (2466 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 50.0% (2510 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 51.3% (2573 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 52.0% (2610 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 53.2% (2667 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 54.3% (2723 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 55.4% (2779 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 56.3% (2825 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 57.0% (2861 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 58.7% (2944 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 59.2% (2972 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 60.2% (3020 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 61.3% (3077 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 62.4% (3133 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 63.1% (3168 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 64.1% (3218 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 65.3% (3278 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 66.3% (3328 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 67.4% (3379 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 68.1% (3419 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 69.2% (3470 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 70.5% (3536 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 71.3% (3577 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 72.7% (3647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 73.2% (3672 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 75.2% (3772 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 75.8% (3802 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 76.8% (3852 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 77.4% (3882 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 78.3% (3928 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 79.5% (3991 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 80.4% (4033 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 81.4% (4083 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 82.4% (4132 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 83.5% (4190 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 84.4% (4234 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 85.5% (4288 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 86.5% (4339 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 87.5% (4391 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 88.4% (4437 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 89.5% (4491 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 90.6% (4543 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 91.6% (4598 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 94.5% (4743 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 95.6% (4797 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 96.6% (4845 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 98.6% (4948 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.9; 4324 / 5017 (P = 86.19%) round 35]               
[00:00:00] Finding cutoff p=839 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=839 1.4% (71 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 2.7% (137 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 3.2% (163 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 4.6% (229 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 5.1% (255 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 6.2% (311 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 7.4% (370 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 8.4% (419 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 9.8% (490 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 10.9% (548 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 11.5% (578 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 13.9% (699 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 16.1% (810 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 17.0% (854 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 17.9% (900 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 19.1% (956 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 20.0% (1001 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 20.9% (1047 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 22.3% (1121 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 23.9% (1198 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 24.7% (1240 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 25.8% (1294 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 26.8% (1347 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 27.7% (1389 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 29.2% (1463 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 29.7% (1492 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 30.9% (1548 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 31.6% (1583 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 32.6% (1636 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 33.9% (1700 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 34.6% (1734 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 35.7% (1789 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 37.2% (1864 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 37.9% (1902 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 38.9% (1950 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 40.0% (2007 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 41.0% (2059 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 42.4% (2129 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 43.0% (2158 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 44.1% (2212 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 44.9% (2251 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 45.8% (2298 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 46.8% (2346 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 48.3% (2422 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 49.1% (2464 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 50.3% (2524 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 51.2% (2569 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 52.3% (2624 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 53.1% (2664 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 54.1% (2713 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 54.9% (2756 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 56.1% (2815 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 57.2% (2869 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 58.4% (2928 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 59.4% (2979 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 61.2% (3070 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 62.1% (3117 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 63.1% (3167 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 64.2% (3223 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 65.1% (3266 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 66.3% (3326 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 67.4% (3379 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 68.9% (3456 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 69.2% (3472 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 70.3% (3529 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 71.2% (3570 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 72.5% (3635 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 73.4% (3684 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 75.3% (3778 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 76.8% (3852 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 77.4% (3883 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 79.6% (3996 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 80.5% (4038 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 81.6% (4092 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 82.4% (4135 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 83.5% (4191 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 84.5% (4237 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 85.5% (4290 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 86.4% (4336 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 87.4% (4387 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 88.6% (4446 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 89.5% (4491 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 90.5% (4542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 91.6% (4597 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 93.5% (4692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 96.6% (4847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 97.6% (4896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 98.6% (4949 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=839 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 83.9; 4414 / 5017 (P = 87.98%) round 36]               
[00:00:00] Finding cutoff p=828 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=828 1.4% (71 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 2.6% (131 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 3.2% (160 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 4.4% (222 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 5.1% (256 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 6.6% (330 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 7.4% (369 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 8.2% (413 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 9.3% (468 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 10.8% (540 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 11.6% (580 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 13.9% (699 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 15.9% (797 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 16.8% (842 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 18.3% (917 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 19.4% (971 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 20.5% (1029 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 22.0% (1106 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 22.6% (1135 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 24.1% (1208 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 24.5% (1230 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 25.7% (1291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 26.9% (1349 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 28.0% (1405 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 28.5% (1430 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 29.5% (1480 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 31.0% (1555 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 31.6% (1587 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 32.7% (1642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 33.6% (1684 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 35.0% (1755 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 36.7% (1839 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 38.3% (1921 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 38.7% (1944 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 39.9% (2001 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 40.7% (2041 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 41.8% (2095 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 42.9% (2154 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 44.1% (2213 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 44.8% (2250 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 46.1% (2312 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 47.5% (2382 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 48.0% (2406 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 49.2% (2466 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 50.2% (2521 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 51.1% (2566 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 52.0% (2608 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 53.7% (2694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 54.3% (2723 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 55.1% (2766 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 56.1% (2814 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 57.1% (2866 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 58.2% (2921 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 59.2% (2971 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 60.6% (3039 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 61.2% (3068 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 62.3% (3126 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 63.0% (3163 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 64.5% (3234 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 65.2% (3270 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 66.1% (3315 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 67.5% (3387 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 68.7% (3446 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 69.2% (3473 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 70.7% (3547 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 71.8% (3601 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 72.2% (3622 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 73.4% (3683 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 74.9% (3756 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 75.3% (3777 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 76.3% (3829 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 77.5% (3888 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 78.3% (3930 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 79.5% (3987 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 80.4% (4033 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 81.4% (4082 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 83.1% (4167 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 83.4% (4183 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 84.4% (4233 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 85.5% (4289 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 86.5% (4339 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 87.5% (4388 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 88.6% (4443 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 89.5% (4492 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 90.6% (4545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 91.5% (4591 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 93.5% (4693 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 94.5% (4743 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 96.6% (4848 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 97.7% (4900 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=828 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.8; 4496 / 5017 (P = 89.62%) round 37]               
[00:00:00] Finding cutoff p=818 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=818 1.1% (54 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 2.8% (140 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 3.5% (174 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 4.5% (224 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 5.3% (265 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 6.4% (322 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 7.4% (373 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 8.7% (438 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 10.0% (503 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 10.9% (545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 13.1% (658 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 15.4% (772 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 16.6% (833 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 17.8% (893 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 18.7% (940 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 19.5% (980 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 20.6% (1034 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 22.7% (1138 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 23.7% (1189 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 24.8% (1243 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 25.6% (1282 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 26.7% (1340 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 28.2% (1413 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 28.8% (1446 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 30.1% (1510 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 30.9% (1548 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 31.7% (1591 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 32.9% (1650 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 34.2% (1716 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 36.1% (1809 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 37.1% (1861 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 38.1% (1913 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 38.8% (1945 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 39.8% (1998 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 40.7% (2044 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 41.8% (2095 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 42.9% (2153 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 44.1% (2210 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 44.8% (2248 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 45.8% (2299 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 47.1% (2361 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 48.1% (2414 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 49.2% (2467 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 50.3% (2522 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 51.2% (2567 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 52.0% (2611 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 53.1% (2664 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 54.0% (2708 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 55.1% (2762 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 56.1% (2814 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 57.1% (2863 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 58.2% (2922 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 59.4% (2978 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 60.6% (3041 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 61.2% (3070 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 62.0% (3113 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 63.3% (3174 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 64.2% (3222 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 65.2% (3270 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 66.1% (3317 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 67.1% (3367 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 68.3% (3427 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 69.3% (3477 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 70.2% (3522 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 71.2% (3570 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 72.5% (3636 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 74.0% (3713 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 74.4% (3733 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 75.3% (3779 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 76.6% (3845 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 77.4% (3885 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 78.5% (3936 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 79.4% (3983 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 80.5% (4039 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 81.6% (4092 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 82.5% (4138 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 83.4% (4183 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 84.5% (4237 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 85.7% (4298 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 86.5% (4341 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 87.8% (4405 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 88.5% (4439 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 89.5% (4489 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 90.5% (4541 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 91.5% (4592 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 94.6% (4746 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 96.6% (4847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 97.6% (4898 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 98.6% (4949 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=818 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 81.8; 4563 / 5017 (P = 90.95%) round 38]               
[00:00:00] Finding cutoff p=809 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=809 1.2% (58 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 2.7% (134 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 3.2% (159 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 4.3% (215 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 5.1% (256 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 6.3% (314 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 7.3% (365 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 8.6% (433 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 10.1% (507 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 10.9% (545 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 12.8% (644 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 15.0% (754 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 15.8% (793 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 16.5% (829 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 17.7% (890 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 18.8% (944 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 19.7% (986 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 20.5% (1027 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 22.8% (1143 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 23.4% (1175 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 25.0% (1252 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 26.2% (1314 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 26.6% (1335 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 27.7% (1392 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 29.3% (1469 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 29.8% (1497 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 30.5% (1532 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 31.7% (1589 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 33.3% (1672 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 34.9% (1752 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 36.4% (1827 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 36.9% (1851 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 37.7% (1892 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 38.7% (1940 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 40.0% (2007 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 41.7% (2094 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 42.8% (2146 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 43.8% (2199 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 44.8% (2250 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 46.1% (2312 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 47.7% (2392 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 48.8% (2446 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 49.8% (2499 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 51.1% (2562 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 52.4% (2628 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 53.0% (2657 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 54.0% (2707 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 55.3% (2772 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 56.2% (2819 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 57.2% (2868 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 57.9% (2907 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 59.6% (2988 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 61.1% (3064 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 62.2% (3121 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 63.6% (3190 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 64.2% (3221 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 65.1% (3268 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 66.6% (3343 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 67.8% (3403 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 68.2% (3420 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 69.1% (3469 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 70.2% (3521 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 72.1% (3619 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 72.7% (3648 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 73.6% (3692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 74.3% (3730 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 75.4% (3782 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 76.3% (3830 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 78.3% (3929 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 79.4% (3986 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 80.7% (4049 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 81.8% (4103 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 82.4% (4136 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 83.6% (4196 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 84.6% (4245 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 85.6% (4296 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 86.5% (4340 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 87.4% (4386 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 88.5% (4438 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 89.6% (4497 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 90.5% (4540 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 91.6% (4594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 92.5% (4641 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 93.6% (4694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 94.6% (4744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 95.6% (4798 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 96.6% (4848 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 97.6% (4899 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=809 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.9; 4602 / 5017 (P = 91.73%) round 39]               
[00:00:00] Finding cutoff p=800 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=800 1.4% (71 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 2.5% (127 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 3.0% (153 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 4.2% (210 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 5.2% (262 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 6.4% (319 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 7.2% (362 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 8.5% (425 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 9.8% (493 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 10.5% (527 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 12.9% (646 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 15.0% (752 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 15.7% (789 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 16.4% (823 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 17.7% (886 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 18.6% (934 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 19.8% (994 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 21.2% (1065 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 22.5% (1128 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 23.9% (1198 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 25.0% (1252 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 26.0% (1306 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 26.6% (1336 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 27.7% (1391 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 29.5% (1478 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 30.1% (1508 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 30.6% (1536 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 31.9% (1600 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 32.6% (1637 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 33.9% (1699 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 35.4% (1776 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 36.2% (1817 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 37.2% (1866 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 37.7% (1892 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 38.7% (1940 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 39.8% (1998 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 41.1% (2062 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 41.7% (2094 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 42.7% (2142 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 44.0% (2207 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 45.0% (2259 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 46.0% (2308 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 47.0% (2356 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 48.5% (2434 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 49.4% (2479 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 50.4% (2531 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 51.2% (2571 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 52.0% (2609 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 53.2% (2671 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 53.9% (2703 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 54.9% (2756 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 56.0% (2808 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 57.0% (2858 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 58.0% (2911 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 59.2% (2971 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 60.4% (3031 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 61.0% (3060 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 62.0% (3112 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 63.1% (3167 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 64.5% (3238 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 65.1% (3267 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 66.3% (3326 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 67.4% (3379 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 68.5% (3439 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 69.3% (3476 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 70.5% (3536 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 71.6% (3590 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 73.2% (3672 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 74.6% (3744 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 75.5% (3787 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 76.5% (3838 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 77.4% (3885 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 78.5% (3940 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 79.7% (4001 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 80.9% (4059 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 82.0% (4114 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 82.3% (4131 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 83.4% (4184 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 84.5% (4237 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 85.5% (4291 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 86.5% (4341 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 87.5% (4392 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 88.6% (4443 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 89.6% (4495 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 90.5% (4542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 91.6% (4594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 92.5% (4642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 93.6% (4695 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 94.7% (4752 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 95.6% (4798 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 96.7% (4849 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 97.6% (4899 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.0; 4645 / 5017 (P = 92.59%) round 40]               
[00:00:00] Finding cutoff p=791 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=791 1.0% (51 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 2.5% (125 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 3.3% (166 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 4.5% (225 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 5.3% (267 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 6.6% (331 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 7.3% (366 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 8.9% (447 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 10.3% (515 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 12.9% (647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 15.0% (755 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 15.8% (791 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 16.6% (832 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 17.9% (896 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 18.8% (942 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 19.7% (989 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 21.0% (1055 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 22.4% (1123 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 23.6% (1186 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 24.6% (1233 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 26.0% (1305 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 26.5% (1329 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 27.7% (1392 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 29.4% (1474 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 30.0% (1507 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 30.5% (1532 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 31.8% (1594 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 33.4% (1674 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 35.0% (1757 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 35.7% (1792 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 36.8% (1845 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 38.0% (1906 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 38.8% (1948 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 40.2% (2019 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 40.8% (2047 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 41.9% (2102 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 42.7% (2143 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 43.9% (2204 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 44.9% (2251 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 45.8% (2296 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 47.5% (2381 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 48.4% (2426 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 49.3% (2474 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 50.3% (2523 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 51.0% (2561 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 52.1% (2615 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 53.7% (2694 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 54.2% (2720 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 55.1% (2765 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 56.1% (2814 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 57.2% (2868 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 58.3% (2924 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 59.5% (2985 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 61.1% (3064 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 62.2% (3121 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 63.3% (3177 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 64.1% (3216 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 66.0% (3313 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 66.3% (3328 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 67.5% (3385 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 68.3% (3429 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 69.4% (3482 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 70.3% (3527 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 71.8% (3600 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 72.5% (3639 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 73.5% (3688 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 74.2% (3723 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 75.5% (3789 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 76.5% (3838 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 77.4% (3884 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 78.4% (3931 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 79.3% (3980 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 80.5% (4041 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 81.3% (4081 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 82.7% (4147 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 83.5% (4189 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 84.6% (4242 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 85.5% (4290 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 86.7% (4352 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 87.8% (4403 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 88.5% (4441 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 89.6% (4495 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 90.5% (4541 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 91.5% (4591 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 92.6% (4647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 93.6% (4698 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 94.6% (4747 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 95.6% (4794 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 96.7% (4852 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 97.7% (4904 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 98.6% (4948 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=791 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 79.1; 4683 / 5017 (P = 93.34%) round 41]               
[00:00:00] Finding cutoff p=780 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=780 1.1% (53 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 2.5% (125 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 3.1% (156 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 4.3% (215 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 5.2% (259 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 6.1% (308 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 7.1% (358 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 8.6% (432 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 10.0% (501 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 10.6% (532 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 13.1% (656 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 15.5% (777 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 16.4% (821 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 17.5% (877 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 18.5% (928 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 19.7% (990 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 22.1% (1109 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 22.7% (1139 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 23.5% (1181 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 24.5% (1231 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 25.7% (1287 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 27.5% (1378 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 29.1% (1460 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 29.7% (1492 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 30.8% (1544 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 31.8% (1597 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 33.1% (1659 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 34.7% (1739 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 36.4% (1825 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 36.7% (1843 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 37.8% (1898 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 39.0% (1955 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 40.1% (2011 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 40.7% (2041 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 41.9% (2102 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 42.9% (2150 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 44.0% (2208 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 45.0% (2256 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 45.9% (2302 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 47.8% (2397 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 49.4% (2477 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 50.1% (2513 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 51.7% (2593 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 52.3% (2624 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 53.5% (2686 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 54.1% (2712 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 55.7% (2792 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 56.7% (2844 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 57.1% (2865 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 58.1% (2914 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 59.0% (2962 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 60.2% (3021 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 61.1% (3067 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 62.6% (3140 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 63.1% (3167 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 64.2% (3222 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 65.4% (3280 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 66.6% (3340 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 67.4% (3382 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 68.7% (3445 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 69.5% (3489 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 71.0% (3562 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 71.6% (3592 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 72.6% (3642 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 73.2% (3672 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 74.3% (3730 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 75.8% (3801 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 76.2% (3825 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 77.4% (3881 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 78.3% (3930 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 79.4% (3981 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 80.7% (4050 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 81.3% (4080 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 82.4% (4133 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 83.4% (4185 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 84.4% (4235 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 85.7% (4301 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 86.6% (4347 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 87.5% (4392 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 88.5% (4441 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 89.6% (4496 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 90.5% (4542 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 91.7% (4599 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 92.6% (4647 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 93.5% (4692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 94.6% (4745 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 95.6% (4798 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 96.7% (4849 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 97.7% (4901 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.0; 4717 / 5017 (P = 94.02%) round 42]               
[00:00:00] Finding cutoff p=770 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=770 1.3% (64 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 2.4% (119 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 4.0% (203 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 4.6% (230 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 5.3% (265 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 6.4% (320 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 7.2% (363 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 8.8% (442 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 10.0% (504 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 10.6% (533 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 13.1% (656 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 15.1% (757 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 15.6% (782 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 16.5% (830 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 18.0% (903 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 18.8% (941 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 19.7% (986 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 20.8% (1046 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 22.2% (1115 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 22.8% (1144 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 23.4% (1176 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 24.7% (1241 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 26.2% (1312 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 26.6% (1333 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 27.8% (1395 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 29.3% (1471 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 29.9% (1501 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 31.0% (1553 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 31.6% (1587 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 33.3% (1670 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 35.1% (1759 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 35.7% (1790 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 36.7% (1843 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 37.8% (1897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 38.8% (1947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 40.0% (2008 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 41.4% (2077 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 41.8% (2097 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 42.9% (2151 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 44.1% (2213 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 45.2% (2270 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 45.9% (2304 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 47.2% (2370 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 48.2% (2416 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 49.2% (2468 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 49.8% (2500 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 51.5% (2586 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 52.3% (2626 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 53.4% (2677 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 54.1% (2713 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 55.5% (2785 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 56.5% (2833 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 57.0% (2862 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 58.7% (2943 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 59.2% (2972 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 61.2% (3069 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 62.0% (3112 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 63.1% (3165 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 64.1% (3218 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 65.4% (3281 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 66.8% (3352 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 67.2% (3371 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 68.1% (3419 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 69.7% (3495 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 71.0% (3563 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 71.7% (3597 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 72.7% (3649 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 73.3% (3679 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 74.4% (3731 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 75.2% (3775 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 76.3% (3826 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 78.4% (3934 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 79.4% (3985 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 80.6% (4046 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 81.4% (4086 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 82.3% (4131 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 83.5% (4188 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 84.5% (4240 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 85.6% (4293 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 86.6% (4345 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 87.5% (4389 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 88.7% (4452 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 89.5% (4490 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 90.6% (4543 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 91.6% (4597 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 92.6% (4644 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 93.5% (4693 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 94.6% (4745 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 95.7% (4802 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 96.6% (4846 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 97.7% (4902 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 77.0; 4756 / 5017 (P = 94.80%) round 43]               
[00:00:00] Finding cutoff p=760 [55.3Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=760 1.3% (67 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 2.5% (124 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 4.0% (201 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 4.5% (225 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 5.5% (275 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 6.5% (326 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 7.4% (370 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 8.8% (441 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 9.9% (498 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 10.6% (531 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 13.0% (654 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 14.9% (749 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 15.6% (783 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 17.2% (861 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 18.1% (907 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 18.9% (947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 19.6% (984 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 21.7% (1090 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 22.4% (1126 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 23.7% (1191 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 24.7% (1241 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 26.6% (1334 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 28.4% (1424 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 29.0% (1456 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 29.7% (1491 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 30.5% (1530 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 32.2% (1616 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 34.1% (1709 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 34.7% (1740 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 35.8% (1798 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 36.8% (1848 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 38.0% (1904 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 39.1% (1963 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 40.6% (2039 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 41.2% (2066 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 41.7% (2094 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 42.9% (2154 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 43.8% (2198 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 45.1% (2264 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 46.4% (2330 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 47.4% (2377 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 48.4% (2429 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 49.1% (2465 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 49.9% (2503 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 51.0% (2560 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 52.6% (2638 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 53.6% (2689 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 54.1% (2714 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 55.5% (2783 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 56.5% (2834 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 57.0% (2861 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 58.6% (2941 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 59.2% (2968 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 60.1% (3014 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 61.1% (3065 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 62.6% (3140 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 63.2% (3169 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 64.2% (3219 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 65.5% (3288 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 66.9% (3355 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 67.2% (3371 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 68.1% (3419 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 69.1% (3468 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 71.1% (3566 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 71.7% (3599 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 72.8% (3651 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 73.5% (3690 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 74.3% (3730 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 75.4% (3783 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 76.4% (3834 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 77.3% (3878 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 78.5% (3937 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 79.5% (3990 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 80.7% (4047 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 81.5% (4090 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 82.4% (4135 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 83.5% (4190 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 84.4% (4236 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 85.4% (4285 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 86.5% (4342 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 87.4% (4387 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 88.7% (4452 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 89.5% (4489 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 90.5% (4541 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 91.5% (4592 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 92.5% (4641 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 93.5% (4692 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 94.6% (4745 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 95.7% (4799 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 96.6% (4847 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 97.6% (4897 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 98.6% (4947 of 5017), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 99.6% (4998 of 5017), ETA 0:00:00                              
[00:00:00] Finished Preparing TNF Graph Building [pTNF = 77.00] [55.1Gb / 503.5Gb]                                            
[00:00:00] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=5017 maxEdges=200
[00:00:00] Building TNF Graph 32.8% (1648 of 5017), ETA 0:00:00     [55.1Gb / 503.5Gb]                           
[00:00:00] Building TNF Graph 65.7% (3296 of 5017), ETA 0:00:00     [55.1Gb / 503.5Gb]                           
[00:00:00] Building TNF Graph 98.5% (4944 of 5017), ETA 0:00:00     [55.1Gb / 503.5Gb]                           
[00:00:00] Finished Building TNF Graph (207434 edges) [55.1Gb / 503.5Gb]                                          
[00:00:00] Cleaned up after Building TNF Graph (207434 edges) [55.1Gb / 503.5Gb]                                          
[00:00:00] Cleaned up TNF matrix of large contigs [55.1Gb / 503.5Gb]                                             
[00:00:00] Applying coverage correlations to TNF graph with 207434 edges
[00:00:00] Allocated memory for graph edges [55.1Gb / 503.5Gb]
[00:00:00] ... calculating abundance dist 1.0% (2090 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 2.0% (4153 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 3.0% (6237 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 4.0% (8314 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 5.0% (10388 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 6.0% (12451 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 7.0% (14531 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 8.0% (16606 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 9.0% (18689 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 10.0% (20761 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 11.0% (22838 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 12.0% (24912 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 13.0% (26978 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 14.0% (29066 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 15.0% (31128 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 16.0% (33206 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 17.0% (35279 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 18.0% (37361 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 19.0% (39435 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 20.0% (41504 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 21.0% (43588 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 22.0% (45655 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 23.0% (47725 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 24.0% (49810 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 25.0% (51881 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 26.0% (53963 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 27.0% (56031 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 28.0% (58105 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 29.0% (60176 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 30.0% (62263 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 31.0% (64329 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 32.0% (66412 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 33.0% (68484 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 34.0% (70564 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 35.0% (72634 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 36.0% (74707 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 37.0% (76775 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 38.0% (78857 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 39.0% (80936 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 40.0% (83004 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 41.0% (85078 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 42.0% (87155 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 43.0% (89231 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 44.0% (91315 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 45.0% (93377 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 46.0% (95455 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 47.0% (97535 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 48.0% (99613 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 49.0% (101675 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 50.0% (103754 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 51.0% (105836 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 52.0% (107900 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 53.0% (109979 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 54.0% (112063 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 55.0% (114128 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 56.0% (116216 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 57.0% (118276 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 58.0% (120354 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 59.0% (122431 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 60.0% (124514 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 61.0% (126575 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 62.0% (128650 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 63.0% (130725 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 64.0% (132801 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 65.0% (134889 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 66.0% (136963 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 67.0% (139029 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 68.0% (141108 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 69.0% (143182 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 70.0% (145256 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 71.0% (147325 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 72.0% (149407 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 73.0% (151477 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 74.0% (153559 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 75.0% (155627 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 76.0% (157709 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 77.0% (159788 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 78.0% (161858 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 79.0% (163930 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 80.0% (166005 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 81.0% (168087 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 82.0% (170153 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 83.0% (172225 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 84.0% (174313 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 85.0% (176386 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 86.0% (178459 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 87.0% (180531 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 88.0% (182604 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 89.0% (184675 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 90.0% (186760 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 91.0% (188827 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 92.0% (190908 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 93.0% (192976 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 94.0% (195060 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 95.0% (197129 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 96.0% (199204 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 97.0% (201279 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 98.0% (203351 of 207434), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 99.0% (205426 of 207434), ETA 0:00:00                              
[00:00:00] Calculating geometric means [55.1Gb / 503.5Gb]
[00:00:00] Traversing graph with 5017 nodes and 207434 edges [55.1Gb / 503.5Gb]
[00:00:00] Building SCR Graph and Binning (477 vertices and 936 edges) [P = 9.50%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 1.0% (2075 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 2.0% (4150 of 207434), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (954 vertices and 3486 edges) [P = 19.00%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 3.0% (6225 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 4.0% (8300 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 5.0% (10375 of 207434), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1430 vertices and 6259 edges) [P = 28.50%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 6.0% (12450 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 7.0% (14525 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 8.0% (16600 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 9.0% (18675 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 10.0% (20750 of 207434), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1907 vertices and 8280 edges) [P = 38.00%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 11.0% (22825 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 12.0% (24900 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 13.0% (26975 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 14.0% (29050 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 15.0% (31125 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 16.0% (33200 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 17.0% (35275 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 18.0% (37350 of 207434), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (2384 vertices and 9719 edges) [P = 47.50%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 19.0% (39425 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 20.0% (41500 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 21.0% (43575 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 22.0% (45650 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 23.0% (47725 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 24.0% (49800 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 25.0% (51875 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 26.0% (53950 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 27.0% (56025 of 207434), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (2860 vertices and 10998 edges) [P = 57.00%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 28.0% (58100 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 29.0% (60175 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 30.0% (62250 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 31.0% (64325 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 32.0% (66400 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 33.0% (68475 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 34.0% (70550 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 35.0% (72625 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 36.0% (74700 of 207434), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (3337 vertices and 12245 edges) [P = 66.50%; 55.1Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 37.0% (76775 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 38.0% (78850 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 39.0% (80925 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 40.0% (83000 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 41.0% (85075 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 42.0% (87150 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 43.0% (89225 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 44.0% (91300 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 45.0% (93375 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 46.0% (95450 of 207434), ETA 0:00:00                               
[00:00:00] ... traversing graph 47.0% (97525 of 207434), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (3813 vertices and 14238 edges) [P = 76.00%; 55.1Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 48.0% (99600 of 207434), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (3842 vertices and 14413 edges) [P = 85.50%; 55.1Gb / 503.5Gb]                           
[00:00:01] Finished Traversing graph [55.1Gb / 503.5Gb]                                       
[00:00:01] Dissolved 1832 small clusters leaving 1343 leftover contigs to be re-merged into larger clusters
[00:00:01] Rescuing singleton large contigs                                   
[00:00:01] There are 1 bins already
[00:00:01] Outputting bins
[00:00:01] Writing cluster stats to: 03bins/metabat2_fixed/1507996/1507996.bin.BinInfo.txt
[00:00:01] 78.27% (23149105 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
1 bins (23149105 bases in total) formed.
[00:00:01] Finished
MetaBAT2 generated 1 bins for 1507996
MetaBAT2 binning completed for 1507996
