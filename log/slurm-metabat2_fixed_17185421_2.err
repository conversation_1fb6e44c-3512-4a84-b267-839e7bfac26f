Output depth matrix to 03bins/metabat2_fixed/1507992/temp/1507992.depth.txt
jgi_summarize_bam_contig_depths 2.17.66-ga512006-dirty 20250124_080221
Running with 16 threads to save memory you can reduce the number of threads with the OMP_NUM_THREADS variable
Output matrix to 03bins/metabat2_fixed/1507992/temp/1507992.depth.txt
Opening all bam files and validating headers
Processing bam files with largest_contig=0
Thread 0 opening and reading the header for file: 02mapping/1507992/1507992.sorted.bam
Thread 0 opened the file: 02mapping/1507992/1507992.sorted.bam
Thread 0 processing bam 0: 1507992.sorted.bam
Thread 0 finished reading bam 0: 1507992.sorted.bam
Thread 0 finished: 1507992.sorted.bam with 10623208 reads and 10209847 readsWellMapped (96.1089%)
Creating depth matrix file: 03bins/metabat2_fixed/1507992/temp/1507992.depth.txt
Closing last bam file
Finished
