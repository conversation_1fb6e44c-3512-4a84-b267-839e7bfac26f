Decompressing scaffold file for 1507996...
Generating depth file for 1507996...
Running MetaBAT2 for 1507996 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 123. with random seed=1745943202
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [35.5Gb / 503.5Gb]
[00:00:00] Parsing assembly file [35.5Gb / 503.5Gb]
[00:00:00] ... processed 10 seqs, 10 long (>=2000), 0 short (>=1000) 1.0% (490145 of 47619868), ETA 0:00:02     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 24 seqs, 24 long (>=2000), 0 short (>=1000) 2.0% (969145 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 41 seqs, 41 long (>=2000), 0 short (>=1000) 3.0% (1451933 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 60 seqs, 60 long (>=2000), 0 short (>=1000) 4.0% (1928459 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 80 seqs, 80 long (>=2000), 0 short (>=1000) 5.0% (2387585 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 102 seqs, 102 long (>=2000), 0 short (>=1000) 6.0% (2858969 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 127 seqs, 127 long (>=2000), 0 short (>=1000) 7.0% (3347989 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 153 seqs, 153 long (>=2000), 0 short (>=1000) 8.0% (3818140 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 180 seqs, 180 long (>=2000), 0 short (>=1000) 9.0% (4286324 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 210 seqs, 210 long (>=2000), 0 short (>=1000) 10.0% (4775682 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 240 seqs, 240 long (>=2000), 0 short (>=1000) 11.0% (5238354 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 273 seqs, 273 long (>=2000), 0 short (>=1000) 12.0% (5728070 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 306 seqs, 306 long (>=2000), 0 short (>=1000) 13.0% (6194410 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 342 seqs, 342 long (>=2000), 0 short (>=1000) 14.0% (6676933 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 379 seqs, 379 long (>=2000), 0 short (>=1000) 15.0% (7151570 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 417 seqs, 417 long (>=2000), 0 short (>=1000) 16.0% (7621725 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 457 seqs, 457 long (>=2000), 0 short (>=1000) 17.0% (8097667 of 47619868), ETA 0:00:01     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 499 seqs, 499 long (>=2000), 0 short (>=1000) 18.0% (8575764 of 47619868), ETA 0:00:00     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 543 seqs, 543 long (>=2000), 0 short (>=1000) 19.0% (9056971 of 47619868), ETA 0:00:00     [35.5Gb / 503.5Gb]     
[00:00:00] ... processed 587 seqs, 587 long (>=2000), 0 short (>=1000) 20.0% (9525516 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 634 seqs, 634 long (>=2000), 0 short (>=1000) 21.0% (10008100 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 682 seqs, 682 long (>=2000), 0 short (>=1000) 22.0% (10483719 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 732 seqs, 732 long (>=2000), 0 short (>=1000) 23.0% (10961898 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 783 seqs, 783 long (>=2000), 0 short (>=1000) 24.0% (11432732 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 836 seqs, 836 long (>=2000), 0 short (>=1000) 25.0% (11907641 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 891 seqs, 891 long (>=2000), 0 short (>=1000) 26.0% (12386867 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 947 seqs, 947 long (>=2000), 0 short (>=1000) 27.0% (12858174 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1006 seqs, 1006 long (>=2000), 0 short (>=1000) 28.0% (13338270 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1067 seqs, 1067 long (>=2000), 0 short (>=1000) 29.0% (13816541 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1129 seqs, 1129 long (>=2000), 0 short (>=1000) 30.0% (14287904 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1194 seqs, 1194 long (>=2000), 0 short (>=1000) 31.0% (14768654 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1260 seqs, 1260 long (>=2000), 0 short (>=1000) 32.0% (15244860 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1328 seqs, 1328 long (>=2000), 0 short (>=1000) 33.0% (15719954 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1398 seqs, 1398 long (>=2000), 0 short (>=1000) 34.0% (16193273 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1471 seqs, 1471 long (>=2000), 0 short (>=1000) 35.0% (16669832 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1546 seqs, 1546 long (>=2000), 0 short (>=1000) 36.0% (17143215 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1625 seqs, 1625 long (>=2000), 0 short (>=1000) 37.0% (17624820 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1705 seqs, 1705 long (>=2000), 0 short (>=1000) 38.0% (18096390 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1789 seqs, 1789 long (>=2000), 0 short (>=1000) 39.0% (18576067 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1875 seqs, 1875 long (>=2000), 0 short (>=1000) 40.0% (19052052 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 1964 seqs, 1964 long (>=2000), 0 short (>=1000) 41.0% (19528890 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 2056 seqs, 2056 long (>=2000), 0 short (>=1000) 42.0% (20001582 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 2153 seqs, 2153 long (>=2000), 0 short (>=1000) 43.0% (20480123 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 2253 seqs, 2253 long (>=2000), 0 short (>=1000) 44.0% (20956267 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 2357 seqs, 2357 long (>=2000), 0 short (>=1000) 45.0% (21432938 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 2465 seqs, 2465 long (>=2000), 0 short (>=1000) 46.0% (21908670 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 2577 seqs, 2577 long (>=2000), 0 short (>=1000) 47.0% (22384906 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 2693 seqs, 2693 long (>=2000), 0 short (>=1000) 48.0% (22860186 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 2813 seqs, 2813 long (>=2000), 0 short (>=1000) 49.0% (23335160 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 2938 seqs, 2938 long (>=2000), 0 short (>=1000) 50.0% (23810926 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 3068 seqs, 3068 long (>=2000), 0 short (>=1000) 51.0% (24286342 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 3204 seqs, 3204 long (>=2000), 0 short (>=1000) 52.0% (24764708 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 3345 seqs, 3345 long (>=2000), 0 short (>=1000) 53.0% (25238977 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 3494 seqs, 3494 long (>=2000), 0 short (>=1000) 54.0% (25717663 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 3649 seqs, 3649 long (>=2000), 0 short (>=1000) 55.0% (26193345 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 3811 seqs, 3811 long (>=2000), 0 short (>=1000) 56.0% (26669258 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 3981 seqs, 3981 long (>=2000), 0 short (>=1000) 57.0% (27145312 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 4160 seqs, 4160 long (>=2000), 0 short (>=1000) 58.0% (27620612 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 4349 seqs, 4349 long (>=2000), 0 short (>=1000) 59.0% (28096381 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 4549 seqs, 4549 long (>=2000), 0 short (>=1000) 60.0% (28572752 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 4761 seqs, 4761 long (>=2000), 0 short (>=1000) 61.0% (29049358 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 4988 seqs, 4988 long (>=2000), 0 short (>=1000) 62.0% (29526086 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 5231 seqs, 5017 long (>=2000), 214 short (>=1000) 63.0% (30000555 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 5493 seqs, 5017 long (>=2000), 476 short (>=1000) 64.0% (30477147 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 5775 seqs, 5017 long (>=2000), 758 short (>=1000) 65.0% (30954432 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 6078 seqs, 5017 long (>=2000), 1061 short (>=1000) 66.0% (31429384 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 6404 seqs, 5017 long (>=2000), 1387 short (>=1000) 67.0% (31905882 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 6753 seqs, 5017 long (>=2000), 1736 short (>=1000) 68.0% (32382759 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 7126 seqs, 5017 long (>=2000), 2109 short (>=1000) 69.0% (32858311 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 7529 seqs, 5017 long (>=2000), 2512 short (>=1000) 70.0% (33334215 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 7964 seqs, 5017 long (>=2000), 2947 short (>=1000) 71.0% (33810479 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 8429 seqs, 5017 long (>=2000), 3348 short (>=1000) 72.0% (34286486 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 8925 seqs, 5017 long (>=2000), 3348 short (>=1000) 73.0% (34763232 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 9454 seqs, 5017 long (>=2000), 3348 short (>=1000) 74.0% (35239022 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 10021 seqs, 5017 long (>=2000), 3348 short (>=1000) 75.0% (35715075 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 10629 seqs, 5017 long (>=2000), 3348 short (>=1000) 76.0% (36191721 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 11281 seqs, 5017 long (>=2000), 3348 short (>=1000) 77.0% (36667493 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 11984 seqs, 5017 long (>=2000), 3348 short (>=1000) 78.0% (37144021 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 12741 seqs, 5017 long (>=2000), 3348 short (>=1000) 79.0% (37619906 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 13560 seqs, 5017 long (>=2000), 3348 short (>=1000) 80.0% (38095952 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 14449 seqs, 5017 long (>=2000), 3348 short (>=1000) 81.0% (38572292 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 15415 seqs, 5017 long (>=2000), 3348 short (>=1000) 82.0% (39048537 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 16468 seqs, 5017 long (>=2000), 3348 short (>=1000) 83.0% (39524613 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 17619 seqs, 5017 long (>=2000), 3348 short (>=1000) 84.0% (40000771 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 18879 seqs, 5017 long (>=2000), 3348 short (>=1000) 85.0% (40477218 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 20249 seqs, 5017 long (>=2000), 3348 short (>=1000) 86.0% (40953312 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 21748 seqs, 5017 long (>=2000), 3348 short (>=1000) 87.0% (41429600 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 23400 seqs, 5017 long (>=2000), 3348 short (>=1000) 88.0% (41905735 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 25213 seqs, 5017 long (>=2000), 3348 short (>=1000) 89.0% (42381929 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 27184 seqs, 5017 long (>=2000), 3348 short (>=1000) 90.0% (42857986 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 29342 seqs, 5017 long (>=2000), 3348 short (>=1000) 91.0% (43334317 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 31798 seqs, 5017 long (>=2000), 3348 short (>=1000) 92.0% (43810454 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 34641 seqs, 5017 long (>=2000), 3348 short (>=1000) 93.0% (44286611 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 38011 seqs, 5017 long (>=2000), 3348 short (>=1000) 94.0% (44762763 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] ... processed 43277 seqs, 5017 long (>=2000), 3348 short (>=1000) 95.0% (45238913 of 47619868), ETA 0:00:00     [35.6Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 5017, and small contigs >= 1000 bp are 3348                                                                  
[00:00:00] Allocating 5017 contigs by 1 samples abundances [35.6Gb / 503.5Gb]
[00:00:00] Allocating 5017 contigs by 1 samples variances [35.6Gb / 503.5Gb]
[00:00:00] Allocating 3348 small contigs by 1 samples abundances [35.6Gb / 503.5Gb]
[00:00:00] Reading 0.002554Gb abundance file [35.6Gb / 503.5Gb]
[00:00:00] ... processed 417 lines 417 contigs and 0 short contigs 1.0% (27436 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 841 lines 841 contigs and 0 short contigs 2.0% (54900 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 1267 lines 1267 contigs and 0 short contigs 3.0% (82282 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 1692 lines 1692 contigs and 0 short contigs 4.0% (109770 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 2116 lines 2116 contigs and 0 short contigs 5.0% (137146 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 2541 lines 2541 contigs and 0 short contigs 6.0% (164589 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 2966 lines 2966 contigs and 0 short contigs 7.0% (192028 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 3391 lines 3391 contigs and 0 short contigs 8.0% (219456 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 3816 lines 3816 contigs and 0 short contigs 9.0% (246895 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 4241 lines 4241 contigs and 0 short contigs 10.0% (274300 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 4667 lines 4667 contigs and 0 short contigs 11.0% (301723 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 5093 lines 5017 contigs and 76 short contigs 12.0% (329170 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 5519 lines 5017 contigs and 502 short contigs 13.0% (356557 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 5945 lines 5017 contigs and 928 short contigs 14.0% (383984 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 6372 lines 5017 contigs and 1355 short contigs 15.0% (411454 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 6798 lines 5017 contigs and 1781 short contigs 16.0% (438851 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 7225 lines 5017 contigs and 2208 short contigs 17.0% (466313 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 7651 lines 5017 contigs and 2634 short contigs 18.0% (493733 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8078 lines 5017 contigs and 3061 short contigs 19.0% (521155 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 20.0% (548543 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 21.0% (576015 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 22.0% (603411 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 23.0% (630837 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 24.0% (658289 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 25.0% (685678 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 26.0% (713145 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 27.0% (740535 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 28.0% (767977 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 29.0% (795415 of 2742608), ETA 0:00:00     [35.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 30.0% (822840 of 2742608), ETA 0:00:00     [35.5Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 31.0% (850260 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 32.0% (877664 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 33.0% (905146 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 34.0% (932567 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 35.0% (959979 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 36.0% (987408 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 37.0% (1014855 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 38.0% (1042239 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 39.0% (1069676 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 40.0% (1097105 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 41.0% (1124544 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 42.0% (1151941 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 43.0% (1179384 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 44.0% (1206829 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 45.0% (1234266 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 46.0% (1261675 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 47.0% (1289121 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 48.0% (1316516 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 49.0% (1343924 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 50.0% (1371393 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 51.0% (1398797 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 52.0% (1426243 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 53.0% (1453639 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 54.0% (1481096 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 55.0% (1508523 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 56.0% (1535932 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 57.0% (1563391 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 58.0% (1590828 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 59.0% (1618197 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 60.0% (1645660 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 61.0% (1673091 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 62.0% (1700497 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 63.0% (1727952 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 64.0% (1755381 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 65.0% (1782787 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 66.0% (1810218 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 67.0% (1837635 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 68.0% (1865075 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 69.0% (1892478 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 70.0% (1919916 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 71.0% (1947363 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 72.0% (1974796 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 73.0% (2002204 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 74.0% (2029645 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 75.0% (2057063 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 76.0% (2084481 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 77.0% (2111889 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 78.0% (2139321 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 79.0% (2166788 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 80.0% (2194185 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 81.0% (2221620 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 82.0% (2249036 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 83.0% (2276492 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 84.0% (2303920 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 85.0% (2331316 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 86.0% (2358735 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 87.0% (2386162 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 88.0% (2413619 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 89.0% (2441048 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 90.0% (2468490 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 91.0% (2495881 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 92.0% (2523287 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 93.0% (2550713 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 94.0% (2578199 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 95.0% (2605588 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 96.0% (2632994 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 97.0% (2660473 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 98.0% (2687853 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] ... processed 8365 lines 5017 contigs and 3348 short contigs 99.0% (2715292 of 2742608), ETA 0:00:00     [35.4Gb / 503.5Gb]                 
[00:00:00] Finished reading 43939 contigs and 1 coverages from 03bins/metabat2_2kb/1507996/temp/1507996.depth.txt [35.4Gb / 503.5Gb]. Ignored 35574 too small contigs.                                     
[00:00:00] Number of target contigs: 5017 of large (>= 2000) and 3348 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 5017
[00:00:00] Allocated memory for TNF [35.4Gb / 503.5Gb]
[00:00:00] Calculating TNF 26.5% (1327 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 27.5% (1379 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 28.6% (1433 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 29.5% (1479 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 30.6% (1533 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 31.6% (1583 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 32.6% (1634 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 33.6% (1684 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 34.6% (1738 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 35.6% (1786 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 36.7% (1839 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 37.6% (1888 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 38.7% (1942 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 39.7% (1992 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 40.7% (2040 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 41.7% (2092 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 42.7% (2142 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 43.8% (2196 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 44.8% (2247 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 45.8% (2299 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 46.8% (2346 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 47.8% (2400 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 48.8% (2449 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 49.8% (2499 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 50.9% (2556 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 51.9% (2603 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 52.9% (2654 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 53.9% (2703 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 55.0% (2759 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 55.9% (2806 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 56.9% (2856 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 58.0% (2910 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 59.0% (2959 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 60.0% (3009 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 61.0% (3060 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 62.1% (3114 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 63.1% (3165 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 64.1% (3215 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 65.1% (3264 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 66.1% (3318 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 67.2% (3371 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 68.2% (3422 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 69.2% (3471 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 70.2% (3520 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 71.2% (3573 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 72.2% (3623 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 73.2% (3673 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 74.2% (3725 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 75.3% (3777 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 76.4% (3831 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 77.4% (3883 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 78.4% (3931 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 79.3% (3979 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 80.4% (4033 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 81.4% (4082 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 82.4% (4136 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 83.4% (4184 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 84.4% (4233 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 85.4% (4287 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 86.4% (4336 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 87.4% (4387 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 88.5% (4441 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 89.5% (4492 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 90.6% (4543 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 91.5% (4590 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 92.5% (4641 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 93.6% (4696 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 94.6% (4748 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 95.7% (4799 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 96.6% (4848 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 97.6% (4898 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 98.7% (4950 of 5017), ETA 0:00:00    
[00:00:00] Calculating TNF 99.7% (5002 of 5017), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [35.4Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.2% (62 of 5017), ETA 0:00:02                   
[00:00:00] ... processing TNF matrix 2.0% (102 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 5.8% (289 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 6.1% (307 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 10.2% (513 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 11.3% (566 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 14.8% (741 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 15.3% (768 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 19.9% (1000 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 20.4% (1021 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 21.4% (1073 of 5017), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 22.4% (1126 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 25.3% (1270 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 25.5% (1278 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.5% (1327 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.5% (1378 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 30.6% (1534 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.7% (1588 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.5% (1632 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.1% (1812 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.6% (1838 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.7% (1891 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 38.7% (1942 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.9% (2100 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.8% (2149 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.8% (2195 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 47.6% (2386 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 47.9% (2404 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.9% (2451 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.8% (2500 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.3% (2674 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.9% (2706 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.0% (2760 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.0% (2809 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.0% (2962 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 60.0% (3011 of 5017), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.1% (3066 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 64.8% (3253 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 65.2% (3269 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 66.2% (3320 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 67.2% (3372 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 70.2% (3523 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 71.2% (3572 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 72.3% (3627 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 75.4% (3781 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 76.3% (3827 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 77.3% (3878 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 80.1% (4020 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 80.4% (4035 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 81.3% (4081 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 85.0% (4262 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 85.4% (4287 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 89.5% (4492 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 90.5% (4540 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 92.1% (4622 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 92.5% (4643 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 93.6% (4696 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 95.7% (4800 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 96.6% (4847 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 98.5% (4940 of 5017), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 98.6% (4947 of 5017), ETA 0:00:00                   
[00:00:01] Finding cutoff p=999 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=999 38.3% (1922 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 38.7% (1944 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 39.7% (1993 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 40.7% (2043 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 41.8% (2097 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 42.7% (2144 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 43.8% (2198 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 44.9% (2255 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 45.9% (2304 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 46.8% (2347 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 47.8% (2400 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 48.9% (2453 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 49.9% (2501 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 50.8% (2551 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 52.0% (2607 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 52.9% (2652 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 53.9% (2703 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 55.0% (2757 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 55.9% (2806 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 56.9% (2857 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 58.0% (2908 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 59.0% (2958 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 60.0% (3010 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 61.0% (3061 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 62.0% (3112 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 63.1% (3164 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 64.1% (3217 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 65.2% (3270 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 66.2% (3319 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 67.1% (3368 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 68.1% (3418 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 69.1% (3468 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 70.1% (3519 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 71.2% (3570 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 72.2% (3621 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 73.2% (3672 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 74.2% (3725 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.9; 0 / 5017 (P = 0.00%) round 1]               
[00:00:01] Finding cutoff p=998 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=998 1.1% (56 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 2.1% (104 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 3.2% (163 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 4.2% (209 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 5.1% (255 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 6.1% (307 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 7.3% (365 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 8.1% (408 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 9.2% (460 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 10.3% (516 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 11.3% (569 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 12.4% (622 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 13.3% (668 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 14.2% (714 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 15.5% (776 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 16.4% (823 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 17.4% (871 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 18.6% (932 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 19.5% (976 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 20.5% (1026 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 21.4% (1075 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 22.4% (1123 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 23.7% (1188 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 24.5% (1230 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 25.5% (1281 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 26.5% (1327 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 27.5% (1382 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 28.5% (1430 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 29.6% (1486 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 30.6% (1535 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 31.6% (1585 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 32.5% (1633 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 33.6% (1688 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 34.6% (1734 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 35.6% (1786 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 36.7% (1840 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 37.7% (1891 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 38.6% (1939 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 39.7% (1992 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 40.7% (2041 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 41.7% (2093 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 79.7% (4000 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.8; 2 / 5017 (P = 0.04%) round 2]               
[00:00:01] Finding cutoff p=997 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=997 1.0% (52 of 5017), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=997 2.1% (107 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 3.0% (153 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 4.1% (205 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 5.1% (258 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 6.2% (312 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 7.1% (357 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 8.2% (411 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 9.2% (460 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 10.2% (514 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 11.2% (561 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 12.2% (614 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 13.3% (668 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 14.3% (715 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 15.3% (768 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 16.3% (818 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 17.4% (871 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 18.3% (919 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 19.3% (969 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 20.4% (1022 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 21.3% (1071 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 22.4% (1123 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 23.4% (1173 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 24.4% (1224 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.7; 7 / 5017 (P = 0.14%) round 3]               
[00:00:01] Finding cutoff p=996 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=996 50.0% (2511 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 50.9% (2553 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 51.9% (2606 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 52.9% (2652 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 53.9% (2703 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 54.9% (2755 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 56.0% (2809 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 56.9% (2857 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 58.0% (2911 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 59.0% (2959 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 60.0% (3009 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 61.1% (3066 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 62.1% (3116 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 63.1% (3165 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 64.1% (3216 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 65.1% (3264 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 66.1% (3315 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.6; 32 / 5017 (P = 0.64%) round 4]               
[00:00:01] Finding cutoff p=994 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=994 1.3% (67 of 5017), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=994 2.1% (107 of 5017), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=994 3.1% (156 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 4.2% (211 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 5.4% (269 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 6.2% (311 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 7.2% (359 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 8.4% (419 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 10.3% (517 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 11.2% (564 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 12.2% (612 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 13.4% (671 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 14.4% (720 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 15.3% (767 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 16.3% (816 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 17.4% (874 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 18.4% (924 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 19.4% (973 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 20.4% (1023 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 21.4% (1074 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 22.4% (1124 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 23.4% (1174 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 24.4% (1225 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 25.5% (1280 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 26.5% (1328 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 27.4% (1377 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 28.6% (1433 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 29.5% (1479 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 30.5% (1530 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 31.5% (1581 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 32.6% (1634 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 33.6% (1685 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 34.6% (1736 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 35.6% (1785 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 36.6% (1836 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 37.6% (1887 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.4; 89 / 5017 (P = 1.77%) round 5]               
[00:00:01] Finding cutoff p=993 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=993 43.8% (2196 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 44.8% (2249 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 46.0% (2306 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 46.8% (2350 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 47.8% (2398 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 49.0% (2460 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 50.0% (2511 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 50.8% (2550 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 51.9% (2606 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 52.9% (2655 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 54.0% (2707 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 55.0% (2758 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 55.9% (2806 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 57.0% (2860 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 58.0% (2908 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 59.0% (2962 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 60.0% (3010 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 61.1% (3063 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 62.0% (3112 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 63.1% (3166 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 64.1% (3215 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 65.1% (3264 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 66.2% (3319 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 67.1% (3368 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 69.2% (3470 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 70.2% (3520 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 71.2% (3571 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 72.2% (3621 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 73.2% (3672 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 74.2% (3723 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=993 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.3; 128 / 5017 (P = 2.55%) round 6]               
[00:00:01] Finding cutoff p=992 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=992 31.3% (1569 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 31.5% (1581 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 32.5% (1633 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 33.6% (1684 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 34.6% (1734 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 35.6% (1785 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 36.6% (1836 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 37.7% (1889 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 38.6% (1939 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 39.7% (1990 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 40.7% (2041 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 41.7% (2093 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 42.7% (2142 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 43.7% (2193 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.2; 192 / 5017 (P = 3.83%) round 7]               
[00:00:01] Finding cutoff p=990 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=990 1.4% (70 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 2.7% (133 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 3.9% (194 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 5.0% (253 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 6.3% (317 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 7.6% (380 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 8.9% (446 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 10.3% (519 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 12.0% (602 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 13.8% (694 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 15.6% (784 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 16.5% (828 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 17.4% (872 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 18.8% (941 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 19.7% (986 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 20.6% (1034 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 21.5% (1077 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 22.4% (1123 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 23.8% (1194 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 24.8% (1243 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 25.7% (1288 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 26.6% (1333 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 27.5% (1381 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 28.8% (1445 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 29.8% (1493 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 30.8% (1543 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 31.7% (1589 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 32.6% (1637 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 33.6% (1688 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 34.6% (1736 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 35.6% (1785 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 36.6% (1836 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 37.7% (1892 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 38.7% (1940 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 39.7% (1994 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 40.7% (2043 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 41.7% (2093 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 42.9% (2152 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 44.0% (2207 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 44.9% (2253 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 45.9% (2305 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 46.8% (2348 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 47.8% (2400 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 48.9% (2455 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 50.0% (2506 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 51.0% (2561 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 51.9% (2603 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 53.0% (2657 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 54.0% (2709 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 55.1% (2764 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 55.9% (2806 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 57.0% (2860 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 58.1% (2914 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 59.0% (2961 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 60.0% (3009 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 61.0% (3061 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 62.0% (3111 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 63.1% (3165 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 64.1% (3214 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 65.1% (3268 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 66.1% (3317 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 67.2% (3370 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 68.1% (3419 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 69.2% (3471 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 70.1% (3519 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 71.2% (3570 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 72.2% (3622 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 73.2% (3674 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 74.2% (3724 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.0; 295 / 5017 (P = 5.88%) round 8]               
[00:00:01] Finding cutoff p=986 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=986 75.0% (3763 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 76.2% (3825 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 80.4% (4032 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 81.4% (4084 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 82.4% (4132 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 83.4% (4186 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 84.5% (4237 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 85.5% (4290 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 86.5% (4341 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=986 87.4% (4386 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 98.6; 575 / 5017 (P = 11.46%) round 9]               
[00:00:01] Finding cutoff p=983 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=983 43.8% (2198 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 44.7% (2244 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 45.7% (2295 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 46.8% (2346 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 47.8% (2397 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 48.8% (2448 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 49.8% (2499 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 98.3; 773 / 5017 (P = 15.41%) round 10]               
[00:00:01] Finding cutoff p=979 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=979 75.0% (3763 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=979 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=979 76.2% (3825 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=979 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=979 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=979 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=979 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 97.9; 994 / 5017 (P = 19.81%) round 11]               
[00:00:01] Finding cutoff p=976 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=976 75.0% (3763 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=976 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=976 76.2% (3825 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=976 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=976 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=976 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=976 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 97.6; 1185 / 5017 (P = 23.62%) round 12]               
[00:00:01] Finding cutoff p=972 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=972 1.0% (52 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 2.2% (111 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 3.2% (162 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 4.1% (207 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 5.1% (255 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 6.4% (321 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 7.4% (372 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 8.3% (418 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 9.4% (470 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 10.4% (520 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 11.4% (571 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 12.4% (623 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 13.4% (671 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 14.4% (724 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 15.4% (775 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 16.5% (829 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 17.5% (879 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 18.6% (932 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 19.7% (987 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 20.3% (1020 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 21.4% (1075 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 22.8% (1143 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 23.5% (1177 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 24.7% (1238 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 25.6% (1284 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 26.6% (1334 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 27.5% (1380 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 28.5% (1431 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 29.6% (1483 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 30.6% (1535 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 31.6% (1585 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 32.6% (1635 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 33.6% (1684 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 34.6% (1734 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 35.7% (1790 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 36.6% (1836 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 37.7% (1889 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 38.7% (1941 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 39.7% (1993 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 40.7% (2040 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 41.7% (2094 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 42.8% (2146 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 43.8% (2197 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 44.7% (2244 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 45.7% (2295 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 46.8% (2348 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 47.9% (2402 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 48.9% (2455 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 49.9% (2501 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 50.9% (2552 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 51.9% (2604 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 52.9% (2653 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 54.0% (2707 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 54.9% (2754 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 55.9% (2807 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 57.0% (2860 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=972 58.0% (2912 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 97.2; 1418 / 5017 (P = 28.26%) round 13]               
[00:00:01] Finding cutoff p=967 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=967 1.1% (54 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 2.1% (105 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 3.3% (168 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 4.2% (210 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 5.2% (259 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 6.4% (321 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 7.3% (367 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 8.3% (415 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 9.3% (468 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 10.4% (520 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 11.4% (571 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 12.4% (623 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 13.6% (680 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 14.3% (717 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 15.3% (768 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 16.4% (821 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 17.3% (870 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 18.4% (925 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 19.7% (986 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 20.5% (1027 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 21.4% (1076 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 22.4% (1124 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 23.5% (1181 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 24.6% (1233 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 25.4% (1275 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 26.5% (1332 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 27.6% (1385 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 28.7% (1442 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 29.6% (1483 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 30.7% (1538 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 31.7% (1592 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 32.8% (1648 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 33.7% (1690 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 34.6% (1735 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 35.7% (1790 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 36.8% (1848 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 37.6% (1887 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 38.7% (1941 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 39.8% (1998 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 40.9% (2050 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 41.7% (2091 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 42.7% (2143 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 43.8% (2197 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 44.9% (2251 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 45.8% (2297 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 46.9% (2352 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 47.8% (2398 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 48.8% (2448 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 49.8% (2499 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 50.8% (2551 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 51.8% (2601 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 52.9% (2655 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 53.9% (2706 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 55.0% (2757 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 55.9% (2807 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 57.0% (2860 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 58.0% (2910 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 59.0% (2960 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 61.0% (3061 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=967 62.0% (3111 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 96.7; 1705 / 5017 (P = 33.98%) round 14]               
[00:00:01] Finding cutoff p=964 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=964 48.9% (2452 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 49.8% (2500 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 50.9% (2552 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 51.9% (2603 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 53.0% (2657 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 53.9% (2704 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 54.9% (2756 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 56.0% (2809 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 57.0% (2860 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 58.0% (2910 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 59.0% (2958 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 61.0% (3062 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 62.0% (3113 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 63.0% (3163 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 64.1% (3214 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 65.1% (3265 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 66.1% (3315 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 69.1% (3468 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 96.4; 1865 / 5017 (P = 37.17%) round 15]               
[00:00:01] Finding cutoff p=960 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=960 50.0% (2508 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 50.9% (2554 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 51.9% (2602 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 52.9% (2654 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 53.9% (2705 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 55.0% (2757 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 55.9% (2806 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 56.9% (2856 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 57.9% (2907 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 59.0% (2960 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 60.0% (3009 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 61.0% (3062 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 62.0% (3111 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 63.0% (3163 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 64.0% (3213 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 65.1% (3265 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 66.1% (3316 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 69.1% (3469 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 70.2% (3520 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 96.0; 2076 / 5017 (P = 41.38%) round 16]               
[00:00:01] Finding cutoff p=955 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=955 75.0% (3763 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=955 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=955 76.2% (3825 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=955 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=955 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=955 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=955 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 95.5; 2327 / 5017 (P = 46.38%) round 17]               
[00:00:01] Finding cutoff p=951 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=951 1.2% (60 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 2.2% (108 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 3.2% (160 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 4.3% (215 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 5.2% (260 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 6.2% (312 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 7.3% (366 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 8.1% (408 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 9.1% (459 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 10.3% (517 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 11.3% (567 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 12.4% (620 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 13.4% (672 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 14.4% (724 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 15.3% (767 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 16.5% (830 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 17.3% (870 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 18.9% (946 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 19.4% (971 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 20.4% (1025 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 21.4% (1073 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 22.8% (1142 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 23.6% (1183 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 24.6% (1232 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 25.5% (1279 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 26.6% (1334 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 27.4% (1377 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 28.7% (1438 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 29.7% (1488 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 30.6% (1534 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 31.6% (1585 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 32.6% (1635 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 33.6% (1688 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 34.7% (1739 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 35.6% (1786 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 36.6% (1836 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 37.7% (1890 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 38.7% (1942 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 39.6% (1989 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 40.7% (2042 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 41.7% (2094 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 42.7% (2144 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 43.7% (2193 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 44.8% (2246 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 45.8% (2296 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 46.8% (2348 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 95.1; 2472 / 5017 (P = 49.27%) round 18]               
[00:00:01] Finding cutoff p=946 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=946 1.3% (67 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 2.9% (146 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 4.2% (210 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 5.3% (264 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 6.4% (321 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 7.5% (378 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 8.8% (444 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 10.3% (519 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 12.6% (632 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 13.2% (664 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 14.3% (717 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 15.4% (771 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 16.7% (837 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 17.4% (874 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 18.5% (927 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 19.7% (986 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 20.4% (1025 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 21.5% (1081 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 22.4% (1122 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 23.5% (1180 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 24.5% (1227 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 25.6% (1283 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 26.7% (1340 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 27.5% (1380 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 29.1% (1462 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 29.6% (1487 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 30.7% (1539 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 31.6% (1585 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 32.6% (1638 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 33.7% (1690 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 34.7% (1742 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 35.7% (1793 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 36.6% (1838 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 37.8% (1894 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 38.7% (1942 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 39.9% (2000 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 40.8% (2045 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 41.8% (2095 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 42.9% (2154 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 43.8% (2196 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 45.0% (2257 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 45.8% (2296 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 46.9% (2353 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 48.1% (2413 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 48.8% (2449 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 49.9% (2504 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 51.0% (2558 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 52.0% (2608 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 52.9% (2653 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 53.9% (2706 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 54.9% (2755 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 55.9% (2807 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 57.0% (2861 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 58.0% (2909 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 59.0% (2958 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 61.0% (3060 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 62.0% (3112 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 63.0% (3162 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=946 64.0% (3213 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 94.6; 2667 / 5017 (P = 53.16%) round 19]               
[00:00:01] Finding cutoff p=942 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=942 6.8% (339 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 7.9% (396 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 9.0% (454 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 11.4% (570 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 13.1% (655 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 13.6% (683 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 14.4% (723 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 15.4% (771 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 16.6% (831 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 17.5% (879 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 18.5% (928 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 19.4% (975 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 20.8% (1044 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 21.4% (1075 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 22.5% (1131 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 23.5% (1177 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 24.6% (1233 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 25.5% (1281 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 26.4% (1326 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 27.5% (1380 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 28.9% (1452 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 29.5% (1480 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 30.6% (1534 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 31.5% (1582 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 32.6% (1636 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 33.7% (1690 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 34.8% (1747 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 35.6% (1787 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 36.8% (1844 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 37.8% (1895 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 38.7% (1944 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 39.8% (1997 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 40.8% (2047 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 41.7% (2093 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 42.7% (2144 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 43.7% (2193 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 44.8% (2249 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 45.8% (2299 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 46.8% (2349 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 47.8% (2397 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 48.9% (2455 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 49.9% (2501 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 50.9% (2554 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 51.9% (2603 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 52.9% (2654 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 53.9% (2705 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 54.9% (2755 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 55.9% (2805 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 57.0% (2858 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 58.0% (2908 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 59.0% (2959 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 60.0% (3012 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 61.1% (3063 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 62.0% (3112 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 63.1% (3166 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 64.1% (3215 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 65.1% (3264 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 66.3% (3325 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 68.2% (3420 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 69.3% (3475 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 70.1% (3519 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 71.2% (3570 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 72.2% (3621 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 94.2; 2811 / 5017 (P = 56.03%) round 20]               
[00:00:01] Finding cutoff p=938 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=938 47.5% (2381 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 48.0% (2408 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 48.9% (2452 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 49.8% (2500 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 50.9% (2555 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 51.9% (2603 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 52.9% (2652 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 53.9% (2705 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 54.9% (2754 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 55.9% (2806 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 56.9% (2857 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 57.9% (2907 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 59.0% (2960 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 60.0% (3011 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 61.0% (3061 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 62.1% (3115 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 63.0% (3163 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 64.1% (3214 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 65.1% (3264 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 66.1% (3315 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 69.2% (3470 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 70.2% (3520 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 71.2% (3570 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 72.2% (3623 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=938 73.3% (3675 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 93.8; 2940 / 5017 (P = 58.60%) round 21]               
[00:00:01] Finding cutoff p=934 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=934 62.3% (3127 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=934 63.0% (3162 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=934 64.0% (3213 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=934 65.1% (3264 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=934 66.1% (3315 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=934 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=934 68.1% (3418 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=934 69.1% (3468 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=934 70.1% (3519 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=934 71.2% (3573 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=934 72.2% (3621 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=934 73.3% (3675 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 93.4; 3088 / 5017 (P = 61.55%) round 22]               
[00:00:01] Finding cutoff p=931 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=931 62.5% (3136 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 63.0% (3163 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 64.1% (3216 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 65.1% (3266 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 66.1% (3316 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 67.1% (3367 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 69.1% (3469 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 70.1% (3519 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 71.2% (3572 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 72.2% (3622 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 73.2% (3672 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 74.2% (3725 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 75.2% (3775 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 76.3% (3827 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 77.3% (3877 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 78.3% (3929 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=931 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 93.1; 3183 / 5017 (P = 63.44%) round 23]               
[00:00:01] Finding cutoff p=927 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=927 1.2% (58 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 2.2% (108 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 3.2% (163 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 4.2% (212 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 5.2% (263 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 6.4% (321 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 7.4% (371 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 8.4% (422 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 9.2% (461 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 10.4% (523 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 11.4% (574 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 12.3% (617 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 13.3% (665 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 14.3% (716 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 15.3% (767 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 16.3% (818 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 17.7% (888 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 18.4% (922 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 19.6% (982 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 20.5% (1030 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 21.5% (1078 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 22.4% (1122 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 23.5% (1178 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 24.5% (1229 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 25.4% (1276 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 26.5% (1327 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 27.5% (1380 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 28.5% (1428 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 29.5% (1481 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 30.6% (1537 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 31.7% (1590 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 32.5% (1632 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 33.7% (1690 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 34.7% (1742 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 35.7% (1793 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 36.7% (1840 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 37.9% (1900 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 38.7% (1941 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 39.6% (1989 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 40.8% (2045 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 41.7% (2092 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 42.8% (2145 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 43.7% (2193 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 44.8% (2247 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 45.7% (2295 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 46.8% (2346 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 47.8% (2398 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 48.8% (2448 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 49.9% (2501 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 50.8% (2551 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=927 51.8% (2601 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 92.7; 3302 / 5017 (P = 65.82%) round 24]               
[00:00:01] Finding cutoff p=923 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=923 1.3% (65 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 2.3% (115 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 3.3% (164 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 4.2% (213 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 5.3% (264 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 6.3% (315 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 7.7% (385 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 8.2% (411 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 9.2% (461 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 10.2% (510 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 11.3% (565 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 12.3% (616 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 13.3% (665 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 14.5% (728 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 15.3% (770 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 16.4% (825 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 17.5% (880 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 19.1% (957 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 19.5% (979 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 20.5% (1030 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 21.5% (1079 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 22.4% (1124 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 23.5% (1178 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 24.4% (1224 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 25.5% (1278 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 26.7% (1339 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 27.8% (1393 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 28.6% (1433 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 29.7% (1488 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 30.6% (1537 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 31.8% (1593 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 32.7% (1639 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 33.6% (1688 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 34.6% (1735 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 35.6% (1785 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 36.6% (1838 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 37.8% (1896 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 38.7% (1941 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 39.6% (1989 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 41.0% (2057 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 41.9% (2100 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 42.8% (2148 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 43.7% (2194 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 44.7% (2244 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 45.8% (2297 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 46.8% (2346 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 47.8% (2400 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 48.9% (2453 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 49.8% (2499 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 50.8% (2551 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 51.8% (2601 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 52.9% (2654 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 53.9% (2705 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 54.9% (2754 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 55.9% (2805 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 92.3; 3399 / 5017 (P = 67.75%) round 25]               
[00:00:01] Finding cutoff p=920 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=920 1.1% (56 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 3.0% (152 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 4.3% (218 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 5.3% (264 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 6.1% (306 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 7.4% (371 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 8.6% (429 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 9.7% (485 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 10.8% (541 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 12.0% (601 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 13.3% (667 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 14.9% (746 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 16.0% (802 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 16.5% (829 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 17.5% (879 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 18.7% (938 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 19.9% (999 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 20.6% (1031 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 21.6% (1084 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 22.6% (1136 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 23.5% (1181 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 24.7% (1240 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 25.8% (1294 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 26.6% (1334 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 27.6% (1387 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 28.6% (1435 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 29.7% (1490 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 30.9% (1549 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 32.2% (1613 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 32.6% (1636 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 33.7% (1689 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 35.4% (1775 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 35.9% (1801 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 36.7% (1840 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 37.7% (1893 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 38.7% (1944 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 39.9% (2000 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 40.8% (2046 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 41.7% (2091 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 42.9% (2152 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 44.0% (2208 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 44.9% (2251 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 45.9% (2303 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 46.9% (2352 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 47.9% (2402 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 48.8% (2449 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 49.9% (2501 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 50.8% (2551 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 51.9% (2605 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 53.0% (2659 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 53.9% (2706 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 55.0% (2761 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 56.0% (2808 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 56.9% (2857 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 58.0% (2908 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 59.1% (2963 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 60.1% (3015 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 61.1% (3065 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 62.1% (3114 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 63.1% (3164 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 64.1% (3218 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 65.2% (3269 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 66.1% (3317 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 67.1% (3368 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 68.1% (3418 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 69.1% (3469 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 70.3% (3526 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 71.2% (3574 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 72.2% (3622 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 73.2% (3673 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 74.2% (3723 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 92.0; 3484 / 5017 (P = 69.44%) round 26]               
[00:00:01] Finding cutoff p=916 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=916 62.2% (3123 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 63.0% (3162 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 64.0% (3213 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 65.1% (3264 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 66.1% (3315 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 68.1% (3418 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 69.1% (3469 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 70.2% (3520 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 71.2% (3572 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 72.2% (3623 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 73.3% (3677 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 74.4% (3734 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 75.3% (3776 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 76.3% (3827 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 77.4% (3882 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=916 78.3% (3928 of 5017), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 91.6; 3578 / 5017 (P = 71.32%) round 27]               
[00:00:01] Finding cutoff p=913 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=913 64.0% (3213 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 65.1% (3268 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 66.1% (3318 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 67.1% (3368 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 68.1% (3418 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 69.2% (3470 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 70.2% (3520 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 71.2% (3570 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 72.2% (3621 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 73.2% (3672 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 74.2% (3723 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 76.3% (3826 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 77.3% (3877 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 78.3% (3928 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=913 80.3% (4031 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 91.3; 3643 / 5017 (P = 72.61%) round 28]               
[00:00:02] Finding cutoff p=910 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=910 1.4% (71 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 2.2% (111 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 3.3% (167 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 4.2% (211 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 5.4% (271 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 6.2% (313 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 63.4% (3180 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 64.1% (3216 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 65.1% (3267 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 66.2% (3320 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 68.1% (3419 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 69.2% (3472 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 70.1% (3519 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 71.2% (3571 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 72.2% (3622 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 73.2% (3673 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 74.3% (3726 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 75.3% (3777 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 76.3% (3829 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 77.3% (3877 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 78.3% (3928 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=910 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 91.0; 3704 / 5017 (P = 73.83%) round 29]               
[00:00:02] Finding cutoff p=906 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=906 1.3% (64 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 2.2% (111 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 3.1% (158 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 4.3% (216 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 5.1% (255 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 6.2% (311 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 7.3% (364 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 8.6% (430 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 9.3% (469 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 10.4% (522 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 11.3% (566 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 12.4% (622 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 13.4% (674 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 14.5% (729 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 15.3% (767 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 16.5% (829 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 17.9% (898 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 18.4% (923 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 19.4% (973 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 20.4% (1022 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 21.7% (1087 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 22.5% (1129 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 23.6% (1183 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 24.4% (1225 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 25.7% (1289 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 26.6% (1337 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 27.5% (1379 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 28.8% (1444 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 29.6% (1485 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 30.5% (1531 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 31.6% (1584 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 32.7% (1642 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 33.6% (1688 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 34.7% (1741 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 35.6% (1787 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 36.6% (1838 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 37.7% (1892 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 38.9% (1953 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 39.7% (1990 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 40.7% (2044 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 41.8% (2096 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 42.8% (2145 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 43.9% (2203 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 44.7% (2245 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 45.7% (2295 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 46.8% (2348 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 47.8% (2397 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 48.8% (2448 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 49.8% (2499 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 50.8% (2550 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 51.9% (2602 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 52.9% (2652 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 90.6; 3782 / 5017 (P = 75.38%) round 30]               
[00:00:02] Finding cutoff p=902 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=902 1.4% (68 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 2.2% (109 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 3.2% (160 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 4.3% (214 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 5.3% (265 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 6.1% (308 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 7.2% (359 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 8.6% (429 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 9.3% (467 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 10.4% (523 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 11.3% (567 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 12.4% (621 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 13.4% (671 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 14.4% (722 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 15.4% (775 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 16.4% (825 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 17.7% (890 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 18.5% (930 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 19.5% (979 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 21.4% (1073 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 22.6% (1132 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 23.5% (1180 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 24.5% (1229 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 25.4% (1276 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 26.7% (1340 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 27.5% (1380 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 28.8% (1444 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 29.6% (1484 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 30.6% (1533 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 31.5% (1581 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 32.8% (1644 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 33.6% (1688 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 34.7% (1740 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 35.6% (1788 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 36.6% (1837 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 37.7% (1892 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 38.8% (1948 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 39.8% (1999 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 40.7% (2044 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 41.8% (2096 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 42.7% (2144 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 43.9% (2200 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 44.8% (2250 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 45.8% (2296 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 46.8% (2350 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 47.9% (2401 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 48.9% (2453 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 49.9% (2501 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 50.9% (2552 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 51.8% (2601 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 52.9% (2653 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 53.9% (2703 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 54.9% (2754 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=902 55.9% (2805 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 90.2; 3857 / 5017 (P = 76.88%) round 31]               
[00:00:02] Finding cutoff p=898 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=898 29.9% (1500 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 30.9% (1548 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 31.7% (1589 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 32.6% (1634 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 33.6% (1685 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 34.7% (1739 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 35.6% (1785 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 36.7% (1840 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 37.7% (1891 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 38.7% (1940 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 39.7% (1992 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 41.1% (2062 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 41.9% (2101 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 43.3% (2171 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 43.9% (2202 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 45.0% (2256 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 45.8% (2297 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 46.9% (2351 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 47.9% (2405 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 49.0% (2456 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 49.9% (2503 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 50.8% (2551 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 51.9% (2604 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 53.0% (2657 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 53.9% (2705 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 55.0% (2757 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 56.0% (2811 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 57.3% (2873 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 58.0% (2910 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 59.0% (2959 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 60.1% (3015 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 61.0% (3061 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 62.0% (3112 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 63.1% (3165 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 64.2% (3223 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 65.1% (3265 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 66.2% (3321 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 68.2% (3421 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 69.1% (3469 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 70.3% (3527 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 71.2% (3572 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 72.3% (3625 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 73.2% (3674 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 74.2% (3723 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 75.3% (3777 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=898 76.3% (3826 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 89.8; 3914 / 5017 (P = 78.01%) round 32]               
[00:00:02] Finding cutoff p=889 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=889 61.1% (3063 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 62.1% (3118 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 63.1% (3165 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 64.2% (3220 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 65.1% (3265 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 66.1% (3315 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 67.2% (3369 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 68.2% (3420 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 69.3% (3479 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 70.1% (3519 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 71.2% (3570 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 72.2% (3621 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 73.3% (3675 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 74.2% (3725 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 75.2% (3775 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 76.3% (3826 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 77.3% (3879 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=889 78.3% (3930 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 88.9; 3975 / 5017 (P = 79.23%) round 33]               
[00:00:02] Finding cutoff p=879 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=879 67.4% (3380 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 69.1% (3468 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 70.1% (3519 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 71.2% (3570 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 72.2% (3621 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 73.3% (3676 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 74.3% (3729 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 75.3% (3778 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 76.3% (3828 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 77.3% (3877 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 78.3% (3930 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 79.4% (3984 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=879 80.3% (4031 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 87.9; 4060 / 5017 (P = 80.92%) round 34]               
[00:00:02] Finding cutoff p=868 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=868 2.2% (110 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 4.3% (216 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 5.1% (257 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 6.2% (312 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 7.8% (389 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 8.8% (442 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 9.9% (497 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 10.7% (538 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 12.1% (606 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 13.9% (697 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 14.5% (726 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 15.4% (772 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 16.5% (826 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 73.9% (3710 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 74.2% (3723 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 75.2% (3775 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 76.2% (3825 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=868 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 86.8; 4157 / 5017 (P = 82.86%) round 35]               
[00:00:02] Finding cutoff p=858 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=858 1.1% (56 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 2.9% (144 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 4.3% (214 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 5.2% (263 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 6.6% (330 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 7.6% (379 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 8.5% (425 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 9.6% (483 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 10.9% (545 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 12.0% (602 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 13.3% (667 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 14.9% (749 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 16.3% (817 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 17.6% (882 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 18.9% (946 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 20.3% (1018 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 20.9% (1050 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 21.7% (1087 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 22.5% (1129 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 23.7% (1187 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 25.3% (1268 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 26.0% (1303 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 27.0% (1356 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 27.8% (1393 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 28.5% (1429 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 29.6% (1486 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 30.7% (1538 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 31.9% (1602 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 33.1% (1663 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 33.6% (1688 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 34.6% (1735 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 35.6% (1786 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 36.9% (1853 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 37.8% (1896 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 38.8% (1946 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 39.7% (1993 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 40.8% (2046 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 41.8% (2096 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 42.7% (2144 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 43.9% (2204 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 44.8% (2248 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 45.9% (2303 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 46.9% (2354 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 47.9% (2405 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 48.9% (2454 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 50.1% (2514 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 50.8% (2551 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 51.9% (2606 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 53.0% (2658 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 53.9% (2704 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 55.3% (2772 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 56.0% (2808 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 57.0% (2859 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 58.0% (2909 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 59.1% (2964 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 60.0% (3011 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 61.0% (3062 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 62.0% (3112 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 63.0% (3163 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 64.1% (3215 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 65.1% (3266 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 66.1% (3316 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=858 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 85.8; 4245 / 5017 (P = 84.61%) round 36]               
[00:00:02] Finding cutoff p=847 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=847 1.1% (55 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 2.1% (106 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 3.1% (155 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 4.1% (205 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 5.2% (263 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 6.5% (328 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 7.2% (360 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 8.5% (425 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 9.3% (468 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 10.3% (515 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 11.4% (570 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 12.2% (612 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 13.8% (690 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 14.4% (724 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 15.5% (778 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 16.6% (835 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 17.3% (870 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 18.6% (933 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 19.4% (971 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 21.0% (1053 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 21.4% (1073 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 22.7% (1137 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 23.5% (1177 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 24.4% (1225 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 25.7% (1287 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 26.5% (1328 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 27.5% (1379 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 28.6% (1437 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 29.6% (1483 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 30.6% (1537 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 31.7% (1588 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 32.7% (1641 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 33.6% (1684 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 34.6% (1734 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 35.6% (1786 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 36.7% (1839 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 37.8% (1894 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 38.7% (1940 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 40.2% (2017 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 40.9% (2054 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 41.8% (2095 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 42.8% (2145 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 43.7% (2193 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 44.8% (2250 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 46.0% (2307 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 46.9% (2355 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 47.8% (2397 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 48.9% (2451 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 49.9% (2501 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 50.9% (2553 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 51.9% (2603 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 52.9% (2652 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 54.0% (2707 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 54.9% (2755 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 56.1% (2813 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 57.0% (2859 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 58.1% (2914 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 59.0% (2960 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 60.0% (3010 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 61.0% (3061 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 62.0% (3113 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 63.1% (3166 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 64.9% (3257 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 65.5% (3287 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 66.3% (3325 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 67.1% (3367 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 68.7% (3449 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 70.2% (3523 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 71.3% (3579 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 72.3% (3625 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 73.3% (3675 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=847 74.3% (3730 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 84.7; 4342 / 5017 (P = 86.55%) round 37]               
[00:00:02] Finding cutoff p=838 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=838 1.3% (67 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 2.0% (102 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 3.1% (154 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 4.2% (212 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 5.4% (272 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 6.2% (312 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 7.2% (359 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 8.2% (413 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 9.2% (460 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 10.3% (518 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 11.5% (577 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 12.3% (616 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 13.6% (682 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 14.4% (721 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 15.3% (770 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 16.4% (822 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 17.5% (879 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 18.5% (928 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 19.4% (973 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 20.5% (1030 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 21.9% (1097 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 22.7% (1139 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 23.4% (1176 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 24.7% (1237 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 25.6% (1282 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 26.5% (1330 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 27.6% (1384 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 28.5% (1432 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 29.5% (1482 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 30.7% (1541 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 31.7% (1589 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 32.6% (1637 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 33.5% (1683 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 34.7% (1743 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 35.6% (1788 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 36.7% (1840 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 37.6% (1887 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 38.7% (1940 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 39.6% (1989 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 40.8% (2046 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 41.8% (2099 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 42.8% (2148 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 43.8% (2195 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 44.8% (2248 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 45.8% (2300 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 46.8% (2346 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 47.8% (2397 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 48.8% (2449 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 49.9% (2504 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 51.0% (2559 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 51.9% (2604 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 52.9% (2654 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 54.0% (2707 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 54.9% (2754 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=838 55.9% (2806 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 83.8; 4421 / 5017 (P = 88.12%) round 38]               
[00:00:02] Finding cutoff p=827 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=827 26.0% (1303 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 26.7% (1340 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 27.5% (1381 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 28.5% (1428 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 29.8% (1495 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 30.5% (1530 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 31.6% (1586 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 32.6% (1637 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 33.7% (1692 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 34.7% (1741 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 35.6% (1788 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 36.6% (1838 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 37.8% (1897 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 38.6% (1938 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 39.7% (1990 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 40.8% (2045 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 41.7% (2092 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 42.8% (2147 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 43.7% (2193 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 44.7% (2245 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 45.8% (2299 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 46.8% (2346 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 47.8% (2397 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 48.8% (2448 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 49.9% (2504 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 50.8% (2551 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 51.9% (2602 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 53.0% (2657 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 54.0% (2707 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 54.9% (2754 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 56.0% (2811 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 57.0% (2860 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 58.0% (2908 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 59.0% (2959 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 60.0% (3009 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=827 61.0% (3061 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 82.7; 4505 / 5017 (P = 89.79%) round 39]               
[00:00:02] Finding cutoff p=817 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=817 32.3% (1621 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 32.5% (1633 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 33.5% (1683 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 34.6% (1737 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 35.6% (1787 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 36.6% (1837 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 37.6% (1887 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 38.7% (1943 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 39.6% (1989 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 40.8% (2046 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 41.7% (2092 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 42.7% (2143 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 43.7% (2193 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 44.8% (2247 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 45.7% (2295 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 46.8% (2349 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 47.8% (2398 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 48.8% (2449 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 49.8% (2500 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 51.1% (2565 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 52.0% (2609 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 53.0% (2661 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 53.9% (2703 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 54.9% (2754 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 56.1% (2813 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=817 56.9% (2856 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 81.7; 4568 / 5017 (P = 91.05%) round 40]               
[00:00:02] Finding cutoff p=807 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=807 56.4% (2830 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 57.0% (2860 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 58.0% (2908 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 59.0% (2959 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 60.0% (3011 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 61.1% (3067 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 62.0% (3111 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 63.0% (3162 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 64.1% (3218 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 65.2% (3271 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 66.1% (3318 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 67.1% (3366 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 68.1% (3419 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 69.3% (3475 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 70.1% (3519 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 71.3% (3575 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 72.4% (3631 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 73.3% (3675 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 74.2% (3723 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 76.3% (3827 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 77.3% (3878 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=807 78.3% (3929 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 80.7; 4608 / 5017 (P = 91.85%) round 41]               
[00:00:02] Finding cutoff p=797 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=797 57.4% (2881 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 58.0% (2908 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 59.0% (2958 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 60.0% (3010 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 61.0% (3060 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 62.0% (3113 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 63.0% (3163 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 64.0% (3213 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 65.1% (3265 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 66.1% (3316 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 67.2% (3369 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 69.1% (3469 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 70.2% (3522 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 71.2% (3573 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 72.3% (3629 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 73.2% (3674 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 74.3% (3729 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 75.3% (3778 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 76.5% (3836 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=797 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 79.7; 4658 / 5017 (P = 92.84%) round 42]               
[00:00:02] Finding cutoff p=787 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=787 68.5% (3438 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 69.2% (3471 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 70.2% (3520 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 71.3% (3577 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 72.2% (3621 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 73.2% (3673 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 74.2% (3724 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 76.2% (3825 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 77.3% (3880 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 78.3% (3928 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=787 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 78.7; 4695 / 5017 (P = 93.58%) round 43]               
[00:00:02] Finding cutoff p=777 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=777 65.1% (3265 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 66.1% (3315 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 67.3% (3374 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 68.1% (3417 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 69.2% (3471 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 70.2% (3523 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 71.2% (3572 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 72.2% (3623 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 73.2% (3672 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 74.2% (3723 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 75.2% (3775 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 76.3% (3826 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=777 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 77.7; 4728 / 5017 (P = 94.24%) round 44]               
[00:00:02] Finding cutoff p=767 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=767 2.4% (119 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 3.3% (166 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 4.1% (205 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 5.1% (257 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 6.1% (307 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 7.6% (382 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 8.2% (410 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 9.6% (482 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 10.3% (516 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 11.3% (567 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 12.6% (634 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 74.6% (3744 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 75.2% (3775 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 76.3% (3828 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 80.3% (4031 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 81.3% (4080 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=767 82.3% (4131 of 5017), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 76.7; 4761 / 5017 (P = 94.90%) round 45]               
[00:00:02] Finding cutoff p=758 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=758 74.7% (3746 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=758 75.2% (3774 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=758 76.2% (3825 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=758 77.3% (3876 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=758 78.3% (3927 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=758 79.3% (3978 of 5017), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=758 80.3% (4029 of 5017), ETA 0:00:00                              
[00:00:02] Finished Preparing TNF Graph Building [pTNF = 76.70] [35.4Gb / 503.5Gb]                                            
[00:00:02] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=5017 maxEdges=200
[00:00:02] Building TNF Graph 4.1% (206 of 5017), ETA 0:00:05     [35.5Gb / 503.5Gb]                           
[00:00:02] Building TNF Graph 39.0% (1957 of 5017), ETA 0:00:01     [35.5Gb / 503.5Gb]                           
[00:00:02] Building TNF Graph 73.9% (3708 of 5017), ETA 0:00:00     [35.5Gb / 503.5Gb]                           
[00:00:03] Finished Building TNF Graph (209252 edges) [35.5Gb / 503.5Gb]                                          
[00:00:03] Cleaned up after Building TNF Graph (209252 edges) [35.5Gb / 503.5Gb]                                          
[00:00:03] Cleaned up TNF matrix of large contigs [35.5Gb / 503.5Gb]                                             
[00:00:03] Applying coverage correlations to TNF graph with 209252 edges
[00:00:03] Allocated memory for graph edges [35.5Gb / 503.5Gb]
[00:00:03] ... calculating abundance dist 14.9% (31160 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 15.0% (31402 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 16.0% (33494 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 17.0% (35588 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 18.0% (37674 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 19.0% (39774 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 20.0% (41866 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 21.0% (43956 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 22.0% (46052 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 23.0% (48143 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 24.0% (50239 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 25.0% (52330 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 26.0% (54419 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 27.0% (56519 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 28.0% (58605 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 29.0% (60703 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 30.0% (62797 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 31.0% (64890 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 32.0% (66983 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 33.0% (69079 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 34.0% (71168 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 35.0% (73256 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 36.0% (75357 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 37.0% (77447 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 38.0% (79542 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 39.0% (81631 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 40.0% (83720 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 41.0% (85817 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 42.0% (87913 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 43.0% (90000 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 44.0% (92094 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 45.0% (94185 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 46.0% (96288 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 47.0% (98379 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 48.0% (100464 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 49.0% (102566 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 50.0% (104651 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 51.0% (106755 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 52.0% (108838 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 53.0% (110932 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 54.0% (113028 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 55.0% (115116 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 56.0% (117216 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 57.0% (119312 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 58.0% (121405 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 59.0% (123492 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 60.0% (125590 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 61.0% (127683 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 62.0% (129766 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 63.0% (131865 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 64.0% (133963 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 65.0% (136054 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 66.0% (138148 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 67.0% (140231 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 68.0% (142327 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 69.0% (144418 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 70.0% (146519 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 71.0% (148607 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 72.0% (150703 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 73.0% (152800 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 74.0% (154884 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 75.0% (156976 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 76.0% (159070 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 77.0% (161166 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 78.0% (163259 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 79.0% (165350 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 80.0% (167443 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 81.0% (169534 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 82.0% (171626 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 83.0% (173724 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 84.0% (175822 of 209252), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 85.0% (177907 of 209252), ETA 0:00:00                              
[00:00:03] Calculating geometric means [35.5Gb / 503.5Gb]
[00:00:03] Traversing graph with 5017 nodes and 209252 edges [35.5Gb / 503.5Gb]
[00:00:03] Building SCR Graph and Binning (477 vertices and 937 edges) [P = 9.50%; 35.5Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 1.0% (2093 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 2.0% (4186 of 209252), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (954 vertices and 3474 edges) [P = 19.00%; 35.5Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 3.0% (6279 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 4.0% (8372 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 5.0% (10465 of 209252), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (1430 vertices and 6048 edges) [P = 28.50%; 35.5Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 6.0% (12558 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 7.0% (14651 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 8.0% (16744 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 9.0% (18837 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 10.0% (20930 of 209252), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (1907 vertices and 8379 edges) [P = 38.00%; 35.5Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 11.0% (23023 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 12.0% (25116 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 13.0% (27209 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 14.0% (29302 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 15.0% (31395 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 16.0% (33488 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 17.0% (35581 of 209252), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (2384 vertices and 10486 edges) [P = 47.50%; 35.5Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 18.0% (37674 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 19.0% (39767 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 20.0% (41860 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 21.0% (43953 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 22.0% (46046 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 23.0% (48139 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 24.0% (50232 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 25.0% (52325 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 26.0% (54418 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 27.0% (56511 of 209252), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (2860 vertices and 11872 edges) [P = 57.00%; 35.5Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 28.0% (58604 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 29.0% (60697 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 30.0% (62790 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 31.0% (64883 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 32.0% (66976 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 33.0% (69069 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 34.0% (71162 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 35.0% (73255 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 36.0% (75348 of 209252), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (3337 vertices and 13107 edges) [P = 66.50%; 35.5Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 37.0% (77441 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 38.0% (79534 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 39.0% (81627 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 40.0% (83720 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 41.0% (85813 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 42.0% (87906 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 43.0% (89999 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 44.0% (92092 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 45.0% (94185 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 46.0% (96278 of 209252), ETA 0:00:00                               
[00:00:03] ... traversing graph 47.0% (98371 of 209252), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (3813 vertices and 15001 edges) [P = 76.00%; 35.5Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 48.0% (100464 of 209252), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (3861 vertices and 15259 edges) [P = 85.50%; 35.5Gb / 503.5Gb]                           
[00:00:03] Finished Traversing graph [35.5Gb / 503.5Gb]                                       
[00:00:03] Dissolved 0 small clusters leaving 0 leftover contigs to be re-merged into larger clusters
[00:00:03] Rescuing singleton large contigs                                   
[00:00:03] There are 1326 bins already
[00:00:03] Outputting bins
[00:00:03] Writing cluster stats to: 03bins/metabat2_2kb/1507996/1507996.bin.BinInfo.txt
[00:00:15] 100.00% (29577374 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
1326 bins (29577374 bases in total) formed.
[00:00:15] Finished
MetaBAT2 generated 1326 bins for 1507996
MetaBAT2 binning completed for 1507996
