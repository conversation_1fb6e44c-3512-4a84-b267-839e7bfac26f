Decompressing scaffold file for 1507993...
Generating depth file for 1507993...
Running MetaBAT2 for 1507993 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 123. with random seed=1745943194
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [8.9Gb / 503.5Gb]
[00:00:00] Parsing assembly file [8.9Gb / 503.5Gb]
[00:00:00] ... processed 8 seqs, 8 long (>=2000), 0 short (>=1000) 1.1% (366367 of 33755644), ETA 0:00:01     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 18 seqs, 18 long (>=2000), 0 short (>=1000) 2.0% (677256 of 33755644), ETA 0:00:01     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 31 seqs, 31 long (>=2000), 0 short (>=1000) 3.1% (1035823 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 44 seqs, 44 long (>=2000), 0 short (>=1000) 4.0% (1357284 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 59 seqs, 59 long (>=2000), 0 short (>=1000) 5.0% (1700644 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 74 seqs, 74 long (>=2000), 0 short (>=1000) 6.0% (2025555 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 91 seqs, 91 long (>=2000), 0 short (>=1000) 7.0% (2365821 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 109 seqs, 109 long (>=2000), 0 short (>=1000) 8.0% (2700673 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 129 seqs, 129 long (>=2000), 0 short (>=1000) 9.0% (3050061 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 149 seqs, 149 long (>=2000), 0 short (>=1000) 10.0% (3381427 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 170 seqs, 170 long (>=2000), 0 short (>=1000) 11.0% (3716895 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 192 seqs, 192 long (>=2000), 0 short (>=1000) 12.0% (4054893 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 215 seqs, 215 long (>=2000), 0 short (>=1000) 13.0% (4392463 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 239 seqs, 239 long (>=2000), 0 short (>=1000) 14.0% (4729628 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 265 seqs, 265 long (>=2000), 0 short (>=1000) 15.0% (5075429 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 291 seqs, 291 long (>=2000), 0 short (>=1000) 16.0% (5408390 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 318 seqs, 318 long (>=2000), 0 short (>=1000) 17.0% (5741757 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 347 seqs, 347 long (>=2000), 0 short (>=1000) 18.0% (6083921 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 377 seqs, 377 long (>=2000), 0 short (>=1000) 19.0% (6418914 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 409 seqs, 409 long (>=2000), 0 short (>=1000) 20.0% (6760381 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 441 seqs, 441 long (>=2000), 0 short (>=1000) 21.0% (7088871 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 476 seqs, 476 long (>=2000), 0 short (>=1000) 22.0% (7434292 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 511 seqs, 511 long (>=2000), 0 short (>=1000) 23.0% (7768474 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 548 seqs, 548 long (>=2000), 0 short (>=1000) 24.0% (8106891 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 586 seqs, 586 long (>=2000), 0 short (>=1000) 25.0% (8442449 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 626 seqs, 626 long (>=2000), 0 short (>=1000) 26.0% (8783352 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 667 seqs, 667 long (>=2000), 0 short (>=1000) 27.0% (9120336 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 709 seqs, 709 long (>=2000), 0 short (>=1000) 28.0% (9452239 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 754 seqs, 754 long (>=2000), 0 short (>=1000) 29.0% (9795969 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 799 seqs, 799 long (>=2000), 0 short (>=1000) 30.0% (10129622 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 846 seqs, 846 long (>=2000), 0 short (>=1000) 31.0% (10466137 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 895 seqs, 895 long (>=2000), 0 short (>=1000) 32.0% (10805347 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 946 seqs, 946 long (>=2000), 0 short (>=1000) 33.0% (11145397 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 998 seqs, 998 long (>=2000), 0 short (>=1000) 34.0% (11480305 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1053 seqs, 1053 long (>=2000), 0 short (>=1000) 35.0% (11819605 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1110 seqs, 1110 long (>=2000), 0 short (>=1000) 36.0% (12157850 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1169 seqs, 1169 long (>=2000), 0 short (>=1000) 37.0% (12492138 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1231 seqs, 1231 long (>=2000), 0 short (>=1000) 38.0% (12829168 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1296 seqs, 1296 long (>=2000), 0 short (>=1000) 39.0% (13169700 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1363 seqs, 1363 long (>=2000), 0 short (>=1000) 40.0% (13505137 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1433 seqs, 1433 long (>=2000), 0 short (>=1000) 41.0% (13841578 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1506 seqs, 1506 long (>=2000), 0 short (>=1000) 42.0% (14179265 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1582 seqs, 1582 long (>=2000), 0 short (>=1000) 43.0% (14516786 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1661 seqs, 1661 long (>=2000), 0 short (>=1000) 44.0% (14853208 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1744 seqs, 1744 long (>=2000), 0 short (>=1000) 45.0% (15193055 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1829 seqs, 1829 long (>=2000), 0 short (>=1000) 46.0% (15528070 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 1919 seqs, 1919 long (>=2000), 0 short (>=1000) 47.0% (15867619 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 2013 seqs, 2013 long (>=2000), 0 short (>=1000) 48.0% (16205290 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 2111 seqs, 2111 long (>=2000), 0 short (>=1000) 49.0% (16542980 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 2213 seqs, 2213 long (>=2000), 0 short (>=1000) 50.0% (16877978 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 2322 seqs, 2322 long (>=2000), 0 short (>=1000) 51.0% (17215930 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 2437 seqs, 2437 long (>=2000), 0 short (>=1000) 52.0% (17553255 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 2559 seqs, 2559 long (>=2000), 0 short (>=1000) 53.0% (17891747 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 2688 seqs, 2688 long (>=2000), 0 short (>=1000) 54.0% (18230550 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 2823 seqs, 2823 long (>=2000), 0 short (>=1000) 55.0% (18566082 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 2967 seqs, 2967 long (>=2000), 0 short (>=1000) 56.0% (18905287 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 3117 seqs, 3117 long (>=2000), 0 short (>=1000) 57.0% (19241046 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 3277 seqs, 3277 long (>=2000), 0 short (>=1000) 58.0% (19579186 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 3446 seqs, 3355 long (>=2000), 91 short (>=1000) 59.0% (19916364 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 3626 seqs, 3355 long (>=2000), 271 short (>=1000) 60.0% (20253436 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 3818 seqs, 3355 long (>=2000), 463 short (>=1000) 61.0% (20591494 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 4021 seqs, 3355 long (>=2000), 666 short (>=1000) 62.0% (20928763 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 4236 seqs, 3355 long (>=2000), 881 short (>=1000) 63.0% (21266962 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 4463 seqs, 3355 long (>=2000), 1108 short (>=1000) 64.0% (21604869 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 4703 seqs, 3355 long (>=2000), 1348 short (>=1000) 65.0% (21942316 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 4957 seqs, 3355 long (>=2000), 1602 short (>=1000) 66.0% (22279744 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 5226 seqs, 3355 long (>=2000), 1871 short (>=1000) 67.0% (22617328 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 5510 seqs, 3355 long (>=2000), 2155 short (>=1000) 68.0% (22954780 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 5811 seqs, 3355 long (>=2000), 2456 short (>=1000) 69.0% (23292246 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 6130 seqs, 3355 long (>=2000), 2775 short (>=1000) 70.0% (23629404 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 6467 seqs, 3355 long (>=2000), 2951 short (>=1000) 71.0% (23966724 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 6824 seqs, 3355 long (>=2000), 2951 short (>=1000) 72.0% (24304122 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 7202 seqs, 3355 long (>=2000), 2951 short (>=1000) 73.0% (24642475 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 7601 seqs, 3355 long (>=2000), 2951 short (>=1000) 74.0% (24979595 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 8026 seqs, 3355 long (>=2000), 2951 short (>=1000) 75.0% (25317227 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 8478 seqs, 3355 long (>=2000), 2951 short (>=1000) 76.0% (25654597 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 8960 seqs, 3355 long (>=2000), 2951 short (>=1000) 77.0% (25992172 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 9474 seqs, 3355 long (>=2000), 2951 short (>=1000) 78.0% (26329521 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 10021 seqs, 3355 long (>=2000), 2951 short (>=1000) 79.0% (26667380 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 10602 seqs, 3355 long (>=2000), 2951 short (>=1000) 80.0% (27004834 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 11224 seqs, 3355 long (>=2000), 2951 short (>=1000) 81.0% (27342520 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 11890 seqs, 3355 long (>=2000), 2951 short (>=1000) 82.0% (27680052 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 12607 seqs, 3355 long (>=2000), 2951 short (>=1000) 83.0% (28017274 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 13382 seqs, 3355 long (>=2000), 2951 short (>=1000) 84.0% (28354856 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 14220 seqs, 3355 long (>=2000), 2951 short (>=1000) 85.0% (28692549 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 15122 seqs, 3355 long (>=2000), 2951 short (>=1000) 86.0% (29029975 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 16095 seqs, 3355 long (>=2000), 2951 short (>=1000) 87.0% (29367701 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 17143 seqs, 3355 long (>=2000), 2951 short (>=1000) 88.0% (29705114 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 18272 seqs, 3355 long (>=2000), 2951 short (>=1000) 89.0% (30042664 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 19480 seqs, 3355 long (>=2000), 2951 short (>=1000) 90.0% (30380221 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 20762 seqs, 3355 long (>=2000), 2951 short (>=1000) 91.0% (30717905 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 22124 seqs, 3355 long (>=2000), 2951 short (>=1000) 92.0% (31055275 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 23583 seqs, 3355 long (>=2000), 2951 short (>=1000) 93.0% (31392884 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 25157 seqs, 3355 long (>=2000), 2951 short (>=1000) 94.0% (31730419 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] ... processed 27219 seqs, 3355 long (>=2000), 2951 short (>=1000) 95.0% (32067958 of 33755644), ETA 0:00:00     [8.9Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 3355, and small contigs >= 1000 bp are 2951                                                                  
[00:00:00] Allocating 3355 contigs by 1 samples abundances [8.9Gb / 503.5Gb]
[00:00:00] Allocating 3355 contigs by 1 samples variances [8.9Gb / 503.5Gb]
[00:00:00] Allocating 2951 small contigs by 1 samples abundances [8.9Gb / 503.5Gb]
[00:00:00] Reading 0.001668Gb abundance file [8.9Gb / 503.5Gb]
[00:00:00] ... processed 273 lines 273 contigs and 0 short contigs 1.0% (17943 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 549 lines 549 contigs and 0 short contigs 2.0% (35849 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 832 lines 832 contigs and 0 short contigs 3.0% (53798 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1112 lines 1112 contigs and 0 short contigs 4.0% (71669 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1390 lines 1390 contigs and 0 short contigs 5.0% (89592 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1668 lines 1668 contigs and 0 short contigs 6.0% (107472 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1947 lines 1947 contigs and 0 short contigs 7.0% (125396 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 2226 lines 2226 contigs and 0 short contigs 8.0% (143317 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 2505 lines 2505 contigs and 0 short contigs 9.0% (161224 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 2784 lines 2784 contigs and 0 short contigs 10.0% (179140 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 3064 lines 3064 contigs and 0 short contigs 11.0% (197065 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 3344 lines 3344 contigs and 0 short contigs 12.0% (214983 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 3624 lines 3355 contigs and 269 short contigs 13.0% (232915 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 3904 lines 3355 contigs and 549 short contigs 14.0% (250818 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 4184 lines 3355 contigs and 829 short contigs 15.0% (268712 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 4464 lines 3355 contigs and 1109 short contigs 16.0% (286602 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 4744 lines 3355 contigs and 1389 short contigs 17.0% (304535 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 5024 lines 3355 contigs and 1669 short contigs 18.0% (322435 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 5305 lines 3355 contigs and 1950 short contigs 19.0% (340363 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 5585 lines 3355 contigs and 2230 short contigs 20.0% (358286 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 5865 lines 3355 contigs and 2510 short contigs 21.0% (376153 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6145 lines 3355 contigs and 2790 short contigs 22.0% (394086 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 23.0% (412015 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 24.0% (429921 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 25.0% (447846 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 26.0% (465729 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 27.0% (483668 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 28.0% (501559 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 29.0% (519461 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 30.0% (537400 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 31.0% (555275 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 32.0% (573205 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 33.0% (591099 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 34.0% (609044 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 35.0% (626941 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 36.0% (644860 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 37.0% (662800 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 38.0% (680671 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 39.0% (698599 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 40.0% (716524 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 41.0% (734453 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 42.0% (752342 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 43.0% (770232 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 44.0% (788161 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 45.0% (806043 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 46.0% (824009 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 47.0% (841915 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 48.0% (859827 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 49.0% (877711 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 50.0% (895610 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 51.0% (913574 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 52.0% (931461 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 53.0% (949340 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 54.0% (967248 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 55.0% (985220 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 56.0% (1003073 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 57.0% (1021000 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 58.0% (1038924 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 59.0% (1056833 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 60.0% (1074780 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 61.0% (1092635 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 62.0% (1110567 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 63.0% (1128517 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 64.0% (1146382 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 65.0% (1164333 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 66.0% (1182223 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 67.0% (1200144 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 68.0% (1218060 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 69.0% (1235951 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 70.0% (1253878 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 71.0% (1271786 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 72.0% (1289727 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 73.0% (1307634 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 74.0% (1325530 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 75.0% (1343449 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 76.0% (1361314 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 77.0% (1379233 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 78.0% (1397197 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 79.0% (1415062 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 80.0% (1433005 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 81.0% (1450901 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 82.0% (1468805 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 83.0% (1486734 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 84.0% (1504634 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 85.0% (1522569 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 86.0% (1540432 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 87.0% (1558405 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 88.0% (1576302 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 89.0% (1594221 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 90.0% (1612104 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 91.0% (1630056 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 92.0% (1647920 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 93.0% (1665848 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 94.0% (1683765 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 95.0% (1701688 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 96.0% (1719579 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 97.0% (1737511 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 98.0% (1755396 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 3355 contigs and 2951 short contigs 99.0% (1773294 of 1791121), ETA 0:00:00     [8.9Gb / 503.5Gb]                 
[00:00:00] Finished reading 28715 contigs and 1 coverages from 03bins/metabat2_2kb/1507993/temp/1507993.depth.txt [8.9Gb / 503.5Gb]. Ignored 22409 too small contigs.                                     
[00:00:00] Number of target contigs: 3355 of large (>= 2000) and 2951 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 3355
[00:00:00] Allocated memory for TNF [8.9Gb / 503.5Gb]
[00:00:00] Calculating TNF 2.2% (73 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 3.2% (108 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 4.2% (140 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 5.1% (170 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 6.2% (207 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 7.2% (241 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 8.1% (272 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 9.3% (312 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 10.2% (342 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 11.2% (375 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 12.2% (410 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 13.2% (444 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 14.3% (480 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 15.2% (510 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 16.3% (546 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 17.3% (579 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 18.3% (613 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 19.3% (646 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 20.6% (690 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 21.6% (726 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 22.7% (760 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 23.5% (787 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 24.5% (822 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 25.5% (856 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 26.6% (894 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 27.7% (928 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 28.6% (961 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 29.7% (996 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 30.6% (1025 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 31.7% (1064 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 32.7% (1098 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 33.5% (1125 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 34.7% (1164 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 35.9% (1203 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 36.9% (1237 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 38.0% (1276 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 38.5% (1292 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 40.0% (1343 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 40.6% (1361 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 41.7% (1398 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 42.7% (1434 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 43.7% (1465 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 44.9% (1505 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 46.0% (1544 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 47.0% (1577 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 48.1% (1613 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 49.0% (1644 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 50.0% (1676 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 51.0% (1712 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 51.8% (1739 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 52.8% (1772 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 54.1% (1815 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 55.2% (1851 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 56.1% (1883 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 57.0% (1912 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 57.9% (1942 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 59.1% (1982 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 60.2% (2021 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 61.2% (2054 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 61.8% (2075 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 62.9% (2110 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 64.2% (2155 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 65.2% (2186 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 66.2% (2221 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 66.9% (2245 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 68.0% (2280 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 69.0% (2316 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 70.3% (2359 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 71.4% (2395 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 72.4% (2428 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 73.4% (2462 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 74.4% (2495 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 75.3% (2525 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 76.2% (2556 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 77.3% (2594 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 78.3% (2628 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 79.4% (2665 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 80.5% (2700 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 81.1% (2721 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 82.6% (2771 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 83.3% (2796 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 84.5% (2834 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 85.6% (2873 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 86.6% (2905 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 87.3% (2929 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 88.5% (2968 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 89.7% (3009 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 90.4% (3032 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 91.2% (3061 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 92.3% (3097 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 93.3% (3130 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 94.8% (3179 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 95.7% (3210 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 96.7% (3244 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 97.3% (3264 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 98.4% (3302 of 3355), ETA 0:00:00    
[00:00:00] Calculating TNF 99.5% (3338 of 3355), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [8.9Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.3% (42 of 3355), ETA 0:00:03                   
[00:00:00] ... processing TNF matrix 2.4% (80 of 3355), ETA 0:00:02                   
[00:00:00] ... processing TNF matrix 3.3% (111 of 3355), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 4.3% (143 of 3355), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 5.2% (175 of 3355), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 6.2% (207 of 3355), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 7.1% (239 of 3355), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 8.6% (287 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 9.5% (319 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 10.5% (351 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 11.4% (383 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 12.4% (415 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 13.3% (447 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 14.3% (479 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 15.2% (511 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 16.7% (559 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 17.6% (591 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 18.6% (624 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 19.6% (657 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 20.5% (689 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 21.6% (724 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 22.6% (757 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 23.5% (788 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.4% (820 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 25.4% (852 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.8% (899 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.7% (931 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 28.7% (963 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.7% (995 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 30.6% (1027 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.6% (1059 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.5% (1091 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 33.5% (1123 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.9% (1171 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 35.9% (1203 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.8% (1235 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.8% (1267 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 38.7% (1299 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.7% (1331 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 40.6% (1363 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.6% (1396 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.6% (1429 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 44.0% (1477 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 45.0% (1509 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.0% (1543 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 47.0% (1576 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 47.9% (1608 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.8% (1637 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.8% (1670 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.7% (1702 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 51.7% (1735 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.1% (1782 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 54.1% (1814 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.0% (1846 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.0% (1878 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 57.0% (1911 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 57.9% (1943 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 58.9% (1975 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.8% (2007 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.3% (2055 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 62.2% (2087 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.2% (2120 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 64.1% (2152 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 65.1% (2184 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.1% (2217 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 67.0% (2249 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.0% (2281 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.9% (2313 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 69.9% (2346 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 71.4% (2395 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 72.3% (2427 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 73.3% (2459 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.2% (2491 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 75.2% (2523 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 76.2% (2555 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 77.2% (2589 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 78.1% (2621 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 79.1% (2654 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 80.1% (2686 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 81.5% (2733 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 82.4% (2765 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.4% (2797 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 84.4% (2831 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 85.3% (2862 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 86.3% (2896 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 87.3% (2929 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 88.3% (2961 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 89.2% (2992 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 90.6% (3040 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 91.6% (3072 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 92.5% (3104 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 93.5% (3136 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 94.4% (3168 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 95.4% (3201 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 96.4% (3233 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 97.3% (3265 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 98.3% (3298 of 3355), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 99.4% (3336 of 3355), ETA 0:00:00                   
[00:00:00] Finding cutoff p=999 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=999 1.8% (60 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 3.5% (119 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 6.1% (205 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 7.5% (250 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 9.2% (309 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 10.8% (361 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 13.9% (468 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 15.2% (510 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 16.2% (544 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 17.4% (584 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 18.5% (620 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 19.6% (657 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 20.5% (689 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 21.6% (726 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 22.8% (766 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 23.8% (797 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 24.8% (833 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 25.9% (870 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 27.1% (910 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 28.4% (952 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 30.1% (1009 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 31.2% (1047 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 32.3% (1082 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 33.2% (1115 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 34.2% (1147 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 35.1% (1177 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 36.1% (1210 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 37.2% (1247 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 38.3% (1284 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 39.3% (1320 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 40.5% (1358 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 41.7% (1399 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 42.6% (1430 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 44.2% (1482 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 45.1% (1514 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 46.0% (1542 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 47.0% (1577 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 47.9% (1606 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 48.8% (1637 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 49.7% (1667 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 51.4% (1724 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 52.1% (1749 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 53.0% (1777 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 54.2% (1820 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 55.1% (1849 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.1% (1882 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.9% (1910 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 57.8% (1940 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 59.4% (1993 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 60.2% (2019 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 61.0% (2047 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 61.9% (2077 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 62.8% (2108 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 64.3% (2156 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 65.1% (2183 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 65.9% (2212 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 67.1% (2252 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 68.2% (2288 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 69.3% (2325 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 70.3% (2360 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 71.1% (2385 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 72.1% (2418 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 73.1% (2454 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 74.2% (2490 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 75.1% (2520 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 76.0% (2550 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 77.3% (2594 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 78.3% (2626 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.1% (2653 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 80.4% (2698 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 81.2% (2725 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 82.5% (2768 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 83.4% (2798 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 84.3% (2829 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 85.2% (2857 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 86.4% (2898 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 87.3% (2928 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.2% (2959 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 89.3% (2996 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 90.2% (3026 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 91.2% (3060 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 92.3% (3097 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 93.3% (3129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 94.4% (3166 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 95.3% (3197 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.9; 0 / 3355 (P = 0.00%) round 1]               
[00:00:00] Finding cutoff p=998 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=998 1.5% (52 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 4.6% (153 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 7.3% (246 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 10.1% (339 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 14.1% (474 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 15.9% (532 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 17.4% (583 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 19.1% (642 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 21.2% (710 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 23.2% (778 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 25.2% (847 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 26.9% (904 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 28.8% (966 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 30.8% (1032 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 32.7% (1096 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 34.5% (1156 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 36.6% (1227 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 38.5% (1291 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 40.4% (1357 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 42.1% (1412 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 43.8% (1470 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 45.5% (1528 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 47.1% (1581 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 48.9% (1641 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 50.5% (1694 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 52.5% (1763 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 53.9% (1807 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 55.4% (1860 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 56.8% (1907 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 58.3% (1957 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 59.9% (2011 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 61.4% (2060 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 62.9% (2111 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 64.1% (2152 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 66.2% (2220 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 67.3% (2258 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 68.5% (2298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 69.6% (2335 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 70.6% (2369 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 71.7% (2406 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 72.7% (2439 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 73.8% (2477 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 74.8% (2509 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 75.7% (2539 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 76.6% (2570 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 77.5% (2601 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 78.3% (2626 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 79.2% (2658 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 80.1% (2689 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 81.2% (2724 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 82.2% (2757 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 83.1% (2789 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 84.4% (2832 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 85.3% (2862 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 86.4% (2898 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 87.4% (2933 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 88.4% (2967 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 89.3% (2997 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 90.3% (3030 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 91.3% (3062 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 92.5% (3102 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 93.4% (3132 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 94.4% (3168 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 95.5% (3203 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.8; 0 / 3355 (P = 0.00%) round 2]               
[00:00:00] Finding cutoff p=995 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=995 1.1% (36 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 3.4% (113 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 5.5% (183 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 7.6% (256 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 10.1% (338 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 13.8% (464 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 16.2% (545 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 19.1% (640 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 21.9% (735 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 24.9% (837 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 27.4% (920 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 29.4% (987 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 31.5% (1058 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 33.7% (1129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 35.8% (1200 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 37.5% (1258 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 39.9% (1338 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 41.9% (1406 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 44.1% (1479 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 45.7% (1533 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 47.4% (1589 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 49.2% (1649 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 50.8% (1705 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 52.7% (1768 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 54.4% (1824 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 56.2% (1885 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 57.8% (1940 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 59.4% (1992 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 60.9% (2042 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 62.3% (2091 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 63.8% (2140 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 64.8% (2175 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 66.0% (2214 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 67.2% (2253 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 68.2% (2288 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 69.2% (2321 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 70.2% (2356 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 71.2% (2390 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 72.4% (2430 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 73.6% (2468 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 74.7% (2506 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 75.8% (2542 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 76.7% (2572 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 77.6% (2604 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 78.5% (2635 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 79.5% (2668 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 81.3% (2727 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 82.2% (2758 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 83.2% (2791 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 84.2% (2825 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 85.2% (2858 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 86.3% (2896 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 87.5% (2934 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 88.3% (2961 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 89.3% (2995 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 90.4% (3032 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 91.2% (3060 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 92.4% (3099 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 93.2% (3128 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 94.5% (3169 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 95.4% (3201 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 96.3% (3232 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.5; 22 / 3355 (P = 0.66%) round 3]               
[00:00:00] Finding cutoff p=994 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=994 1.4% (46 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 4.0% (133 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 6.2% (208 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 8.5% (286 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 11.1% (373 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 13.5% (452 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 15.9% (535 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 18.7% (628 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 21.4% (719 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 24.2% (811 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 27.5% (921 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 29.4% (985 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 31.5% (1056 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 33.6% (1128 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 35.6% (1196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 37.6% (1263 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 39.9% (1337 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 42.2% (1415 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 44.3% (1487 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 46.0% (1544 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 48.8% (1636 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 50.6% (1697 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 52.4% (1757 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 54.0% (1813 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 56.0% (1879 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 57.7% (1937 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 59.3% (1990 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 60.7% (2037 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 62.2% (2086 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 63.8% (2141 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 65.0% (2180 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 66.1% (2218 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 67.2% (2254 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 68.3% (2292 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 69.4% (2328 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 70.4% (2361 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 71.4% (2395 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 72.6% (2436 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 73.6% (2469 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 74.8% (2511 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 75.8% (2544 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 76.9% (2579 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 77.7% (2608 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 78.5% (2635 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 79.3% (2662 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 80.1% (2689 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 81.2% (2724 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 82.2% (2757 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 83.1% (2789 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 84.3% (2827 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 85.3% (2861 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 86.3% (2896 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 87.2% (2925 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 88.2% (2958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 89.2% (2994 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 90.3% (3030 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 91.3% (3063 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 92.4% (3101 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 93.3% (3129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 94.3% (3163 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 95.3% (3198 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.4; 41 / 3355 (P = 1.22%) round 4]               
[00:00:00] Finding cutoff p=991 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=991 1.1% (37 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 3.4% (114 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 5.7% (191 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 7.9% (265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 10.4% (350 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 12.8% (430 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 15.2% (509 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 18.0% (603 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 20.8% (699 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 23.6% (792 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 26.6% (893 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 29.5% (989 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 31.5% (1056 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 33.4% (1121 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 35.5% (1190 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 37.5% (1258 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 39.4% (1323 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 41.5% (1394 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 43.5% (1461 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 45.5% (1527 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 47.4% (1590 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 49.2% (1651 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 51.2% (1717 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 52.9% (1775 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 54.6% (1833 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 56.5% (1894 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 57.7% (1936 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 59.1% (1983 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 60.6% (2033 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 62.2% (2087 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 63.9% (2143 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 65.3% (2191 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 66.6% (2234 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 67.6% (2269 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 68.8% (2309 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 69.9% (2346 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 71.8% (2409 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 73.0% (2449 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 74.3% (2493 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 75.5% (2533 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 76.6% (2571 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 77.6% (2602 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 78.3% (2627 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 79.2% (2657 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 80.3% (2695 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 81.2% (2723 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 82.2% (2757 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 83.1% (2789 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 84.1% (2823 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 85.3% (2863 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 86.1% (2890 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 87.3% (2928 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 88.4% (2967 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 89.2% (2993 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 90.3% (3028 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 91.3% (3064 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 92.3% (3098 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 93.3% (3129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 94.3% (3165 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 95.3% (3197 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.1; 132 / 3355 (P = 3.93%) round 5]               
[00:00:00] Finding cutoff p=989 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=989 1.1% (36 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 3.7% (125 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 6.4% (214 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 8.6% (287 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 10.9% (367 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 13.6% (456 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 15.8% (531 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 18.5% (622 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 21.5% (722 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 24.4% (819 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 27.4% (918 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 31.4% (1054 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 33.6% (1126 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 35.6% (1193 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 37.8% (1268 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 39.9% (1339 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 42.1% (1411 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 44.1% (1480 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 46.1% (1546 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 47.7% (1601 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 49.6% (1664 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 51.2% (1719 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 52.7% (1769 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 54.3% (1823 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 55.7% (1870 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 57.1% (1915 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 58.5% (1962 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 59.9% (2011 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 62.1% (2083 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 63.4% (2126 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 64.6% (2167 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 65.6% (2201 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 66.6% (2236 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 67.9% (2277 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 68.9% (2313 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 70.0% (2350 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 71.1% (2386 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 72.3% (2425 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 73.5% (2467 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 74.6% (2502 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 75.6% (2536 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 76.5% (2568 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 77.6% (2602 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 78.5% (2635 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 79.6% (2672 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 80.5% (2702 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 81.4% (2731 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 82.3% (2762 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 83.4% (2797 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 84.1% (2822 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 85.2% (2859 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 86.3% (2897 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 87.2% (2927 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 88.3% (2961 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 89.2% (2992 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 90.3% (3028 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 91.3% (3064 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 92.2% (3094 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 93.3% (3130 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 94.2% (3162 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 95.3% (3196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=989 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.9; 211 / 3355 (P = 6.29%) round 6]               
[00:00:00] Finding cutoff p=984 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=984 1.0% (35 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 3.8% (126 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 6.2% (207 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 8.6% (288 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 10.9% (366 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 13.4% (451 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 15.7% (528 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 18.6% (624 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 21.3% (715 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 24.2% (812 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 27.0% (905 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 30.2% (1014 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 32.2% (1080 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 34.3% (1151 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 36.2% (1215 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 38.4% (1288 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 40.7% (1365 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 42.7% (1434 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 44.9% (1505 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 46.8% (1570 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 48.6% (1630 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 50.3% (1687 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 52.1% (1749 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 54.0% (1813 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 55.6% (1864 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 57.1% (1916 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 59.6% (1998 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 61.0% (2046 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 62.3% (2091 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 63.6% (2134 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 64.8% (2175 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 66.1% (2219 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 67.5% (2266 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 68.7% (2305 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 69.9% (2346 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 71.1% (2385 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 72.2% (2421 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 73.3% (2460 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 74.4% (2496 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 75.6% (2535 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 76.5% (2565 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 77.4% (2598 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 78.4% (2629 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 79.2% (2656 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 80.4% (2697 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 81.2% (2724 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 82.3% (2760 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 83.2% (2790 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 84.4% (2830 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 85.4% (2866 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 86.3% (2894 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 87.4% (2932 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 88.2% (2958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 89.2% (2992 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 90.3% (3029 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 91.2% (3061 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 92.4% (3099 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 93.3% (3129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 94.2% (3162 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 95.3% (3196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 96.3% (3232 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=984 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.4; 417 / 3355 (P = 12.43%) round 7]               
[00:00:00] Finding cutoff p=981 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=981 1.0% (35 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 5.3% (178 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 7.8% (262 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 10.2% (341 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 12.7% (425 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 15.1% (506 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 17.3% (579 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 19.7% (661 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 22.3% (747 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 24.9% (836 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 27.5% (924 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 30.6% (1027 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 32.8% (1102 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 35.0% (1173 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 37.0% (1241 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 39.1% (1313 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 41.3% (1385 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 43.5% (1459 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 45.8% (1535 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 47.6% (1598 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 49.4% (1657 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 51.2% (1718 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 53.0% (1778 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 54.8% (1837 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 56.4% (1892 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 57.9% (1944 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 59.3% (1991 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 60.7% (2037 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 62.0% (2081 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 63.4% (2127 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 64.6% (2167 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 66.0% (2213 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 67.3% (2257 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 68.3% (2293 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 69.7% (2337 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 70.8% (2376 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 72.0% (2415 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 73.1% (2452 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 74.3% (2492 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 75.3% (2527 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 76.2% (2558 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 77.2% (2591 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 78.7% (2642 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 79.6% (2672 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 80.5% (2700 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 81.3% (2726 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 82.4% (2765 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 83.3% (2796 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 84.4% (2830 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 85.3% (2862 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 86.3% (2894 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 87.4% (2931 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 88.2% (2958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 89.3% (2995 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 90.3% (3031 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 91.3% (3063 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 92.2% (3094 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 93.3% (3131 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 94.3% (3164 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 95.3% (3196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 96.3% (3232 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.1; 538 / 3355 (P = 16.04%) round 8]               
[00:00:00] Finding cutoff p=978 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=978 1.1% (37 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 3.5% (116 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 6.0% (200 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 8.3% (278 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 10.6% (357 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 13.3% (445 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 15.6% (524 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 18.0% (603 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 20.5% (688 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 23.2% (779 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 26.2% (879 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 29.2% (980 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 31.4% (1052 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 33.4% (1121 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 35.4% (1189 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 37.5% (1258 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 39.5% (1326 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 43.1% (1446 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 45.3% (1519 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 47.2% (1584 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 48.9% (1641 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 50.9% (1707 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 52.6% (1766 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 54.5% (1829 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 56.0% (1880 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 57.5% (1930 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 59.0% (1979 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 60.4% (2028 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 61.8% (2072 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 63.0% (2114 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 64.3% (2156 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 65.5% (2198 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 66.8% (2242 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 68.1% (2284 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 69.4% (2327 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 70.5% (2365 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 71.6% (2402 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 72.8% (2441 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 73.8% (2476 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 74.8% (2511 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 75.8% (2543 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 76.7% (2572 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 77.5% (2600 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 78.2% (2625 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 79.3% (2662 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 80.4% (2699 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 81.2% (2723 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 82.4% (2764 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 83.4% (2798 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 84.3% (2829 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 85.3% (2863 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 86.4% (2898 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 87.3% (2929 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 88.2% (2958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 89.3% (2996 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 90.4% (3032 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 91.2% (3061 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 92.3% (3095 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 93.2% (3128 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 94.3% (3165 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 95.3% (3196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.8; 675 / 3355 (P = 20.12%) round 9]               
[00:00:00] Finding cutoff p=975 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=975 2.0% (68 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 4.3% (144 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 6.7% (224 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 8.9% (300 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 11.2% (376 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 13.7% (458 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 15.8% (531 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 18.3% (615 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 20.8% (699 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 23.6% (793 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 26.2% (879 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 29.0% (974 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 31.5% (1057 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 33.7% (1131 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 35.9% (1206 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 38.1% (1279 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 40.3% (1353 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 42.6% (1429 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 44.4% (1489 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 46.3% (1555 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 48.0% (1609 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 49.7% (1668 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 51.5% (1727 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 53.4% (1792 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 55.0% (1845 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 56.4% (1893 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 58.0% (1945 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 59.3% (1991 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 60.6% (2032 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 61.8% (2074 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 63.2% (2120 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 64.4% (2161 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 66.6% (2233 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 68.0% (2281 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 69.1% (2318 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 70.1% (2351 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 71.2% (2390 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 72.4% (2429 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 73.5% (2465 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 74.5% (2498 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 75.4% (2530 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 76.3% (2560 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 77.3% (2594 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 78.2% (2622 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 79.4% (2665 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 80.3% (2695 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 81.3% (2729 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 82.1% (2754 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 83.3% (2796 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 84.4% (2831 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 85.4% (2864 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 86.3% (2896 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 87.2% (2926 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 88.2% (2959 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 89.3% (2997 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 90.2% (3027 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 91.2% (3061 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 92.3% (3095 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 93.2% (3128 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 94.3% (3165 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 95.4% (3199 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.5; 768 / 3355 (P = 22.89%) round 10]               
[00:00:00] Finding cutoff p=972 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=972 1.1% (36 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 3.5% (117 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 5.9% (199 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 8.3% (277 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 10.6% (354 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 12.8% (429 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 16.3% (547 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 18.4% (618 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 21.1% (707 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 23.5% (787 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 26.0% (871 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 28.9% (970 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 31.1% (1044 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 32.9% (1104 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 34.6% (1161 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 36.5% (1225 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 38.4% (1289 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 40.4% (1356 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 42.7% (1432 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 44.7% (1499 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 46.3% (1554 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 48.1% (1614 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 49.8% (1670 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 51.5% (1729 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 53.4% (1790 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 55.0% (1844 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 56.4% (1893 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 57.9% (1942 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 59.4% (1993 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 60.8% (2039 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 62.1% (2082 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 63.3% (2123 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 65.6% (2201 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 66.9% (2244 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 68.4% (2295 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 69.6% (2336 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 70.7% (2371 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 71.7% (2405 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 72.8% (2441 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 73.9% (2479 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 75.1% (2519 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 76.1% (2552 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 77.1% (2587 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 78.0% (2618 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 79.4% (2663 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 80.3% (2695 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 81.2% (2724 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 82.4% (2763 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 83.4% (2797 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 84.3% (2829 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 85.3% (2861 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 86.3% (2897 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 87.2% (2925 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 88.2% (2960 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 89.3% (2996 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 90.3% (3031 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 91.3% (3063 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 92.3% (3096 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 93.3% (3131 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 94.4% (3166 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 95.3% (3196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 96.4% (3233 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.2; 858 / 3355 (P = 25.57%) round 11]               
[00:00:00] Finding cutoff p=967 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=967 1.1% (36 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 3.6% (122 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 5.8% (193 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 7.9% (266 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 10.1% (338 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 12.2% (409 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 14.5% (486 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 17.0% (570 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 19.8% (664 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 22.4% (753 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 25.0% (838 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 27.7% (930 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 30.7% (1029 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 32.6% (1094 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 34.4% (1154 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 36.4% (1221 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 38.4% (1288 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 40.6% (1361 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 42.7% (1434 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 44.7% (1499 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 46.5% (1560 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 49.3% (1654 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 51.1% (1716 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 52.8% (1772 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 54.5% (1829 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 55.9% (1876 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 57.3% (1923 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 58.7% (1970 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 60.1% (2018 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 61.4% (2061 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 62.5% (2098 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 63.8% (2142 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 65.1% (2183 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 66.4% (2228 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 68.0% (2281 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 69.2% (2320 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 70.3% (2357 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 71.3% (2392 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 72.4% (2430 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 73.5% (2466 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 74.5% (2501 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 75.6% (2538 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 76.6% (2570 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 77.5% (2600 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 78.3% (2627 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 79.1% (2654 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 80.1% (2686 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 81.1% (2720 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 82.2% (2757 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 83.2% (2791 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 84.2% (2824 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 85.1% (2856 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 86.3% (2897 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 87.4% (2931 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 88.2% (2959 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 89.2% (2994 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 90.3% (3028 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 91.4% (3067 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 92.3% (3096 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 93.3% (3130 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 94.3% (3165 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 95.3% (3197 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 96.3% (3232 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.7; 1044 / 3355 (P = 31.12%) round 12]               
[00:00:00] Finding cutoff p=962 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=962 1.0% (35 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 3.0% (102 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 5.1% (171 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 7.0% (236 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 9.0% (302 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 11.2% (375 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 13.6% (455 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 15.9% (533 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 18.6% (623 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 20.9% (702 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 23.2% (779 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 25.9% (869 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 28.6% (959 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 30.8% (1032 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 33.2% (1115 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 35.1% (1178 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 37.1% (1244 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 39.3% (1319 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 41.7% (1398 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 44.0% (1475 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 46.1% (1548 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 47.6% (1596 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 49.3% (1654 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 50.9% (1707 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 52.6% (1764 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 54.2% (1820 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 55.8% (1871 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 57.1% (1916 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 58.4% (1959 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 59.9% (2010 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 61.3% (2058 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 62.7% (2102 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 63.8% (2141 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 65.1% (2185 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 66.8% (2240 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 68.8% (2309 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 70.2% (2354 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 71.2% (2389 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 72.4% (2430 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 73.5% (2466 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 74.5% (2500 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 75.5% (2533 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 76.5% (2568 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 77.3% (2594 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 78.2% (2623 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 79.5% (2668 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 80.5% (2702 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 81.2% (2724 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 82.2% (2759 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 83.2% (2792 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 84.3% (2827 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 85.2% (2859 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 86.2% (2891 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 87.2% (2924 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 88.2% (2958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 89.2% (2994 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 90.3% (3028 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 91.3% (3062 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 92.3% (3097 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 93.3% (3130 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 94.3% (3163 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 95.3% (3198 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 96.4% (3234 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.2; 1217 / 3355 (P = 36.27%) round 13]               
[00:00:00] Finding cutoff p=958 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=958 1.1% (36 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 3.2% (108 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 5.4% (182 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 7.2% (240 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 9.2% (308 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 11.4% (383 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 13.4% (448 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 16.0% (536 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 20.2% (678 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 22.9% (769 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 25.4% (853 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 28.1% (942 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 30.6% (1028 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 32.5% (1091 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 34.3% (1150 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 36.1% (1212 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 37.7% (1265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 40.1% (1347 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 42.7% (1431 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 44.5% (1492 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 46.3% (1552 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 47.6% (1598 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 49.4% (1656 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 51.1% (1713 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 52.6% (1766 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 54.4% (1824 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 55.9% (1876 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 57.3% (1921 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 58.5% (1964 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 59.8% (2005 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 61.0% (2048 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 62.5% (2098 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 63.8% (2140 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 65.1% (2183 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 66.9% (2243 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 67.9% (2277 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 69.1% (2318 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 70.3% (2357 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 71.6% (2402 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 72.7% (2439 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 73.7% (2473 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 74.8% (2510 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 75.8% (2544 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 76.6% (2571 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 77.5% (2601 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 78.4% (2629 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 79.5% (2668 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 80.5% (2702 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 81.2% (2724 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 82.4% (2764 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 83.4% (2798 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 84.4% (2830 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 85.3% (2863 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 86.3% (2895 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 87.2% (2927 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 88.3% (2963 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 89.4% (2999 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 90.2% (3027 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 91.6% (3072 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 92.2% (3094 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 93.3% (3130 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 94.3% (3164 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 95.3% (3197 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=958 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.8; 1336 / 3355 (P = 39.82%) round 14]               
[00:00:00] Finding cutoff p=953 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=953 1.1% (37 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 3.0% (101 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 4.7% (159 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 6.1% (206 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 7.4% (249 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 9.3% (313 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 11.5% (385 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 13.4% (448 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 16.0% (537 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 18.2% (609 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 20.3% (682 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 22.7% (762 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 25.0% (838 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 27.3% (915 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 29.9% (1002 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 32.3% (1084 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 33.7% (1130 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 36.0% (1208 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 38.2% (1282 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 40.0% (1342 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 41.9% (1406 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 44.9% (1507 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 46.6% (1562 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 48.0% (1612 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 49.5% (1661 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 51.0% (1710 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 52.4% (1758 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 54.1% (1815 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 55.6% (1867 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 57.0% (1911 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 58.2% (1954 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 59.6% (2001 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 60.7% (2035 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 61.8% (2075 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 64.1% (2149 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 65.3% (2191 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 66.4% (2229 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 67.5% (2266 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 68.7% (2306 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 69.9% (2345 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 71.0% (2383 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 72.5% (2433 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 73.7% (2472 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 74.7% (2507 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 75.8% (2544 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 76.8% (2575 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 77.8% (2611 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 79.1% (2655 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 80.2% (2692 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 81.2% (2725 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 82.2% (2759 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 83.3% (2796 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 84.3% (2829 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 85.3% (2862 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 86.2% (2893 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 87.2% (2926 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 88.2% (2958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 89.4% (2999 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 90.2% (3027 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 91.4% (3067 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 92.3% (3097 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 93.3% (3130 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 94.3% (3163 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 95.4% (3201 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 96.3% (3232 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.3; 1484 / 3355 (P = 44.23%) round 15]               
[00:00:00] Finding cutoff p=948 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=948 1.1% (36 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 3.2% (108 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 5.2% (175 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 6.8% (228 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 8.6% (289 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 10.1% (338 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 12.5% (418 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 14.6% (491 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 17.3% (581 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 19.1% (640 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 21.4% (717 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 23.7% (794 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 25.8% (867 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 28.7% (963 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 30.7% (1029 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 32.4% (1088 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 34.3% (1150 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 36.6% (1227 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 38.9% (1304 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 40.5% (1359 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 42.4% (1422 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 44.1% (1478 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 45.6% (1530 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 47.1% (1580 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 48.6% (1631 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 50.1% (1680 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 51.7% (1733 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 53.1% (1782 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 54.5% (1829 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 56.0% (1879 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 57.6% (1934 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 59.2% (1987 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 60.0% (2012 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 62.1% (2085 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 64.4% (2160 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 65.8% (2206 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 66.9% (2246 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 68.6% (2300 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 69.7% (2338 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 70.9% (2379 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 72.3% (2424 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 73.4% (2463 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 74.3% (2494 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 75.4% (2531 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 76.5% (2565 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 77.6% (2604 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 78.7% (2640 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 79.5% (2668 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 80.3% (2694 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 81.3% (2726 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 82.3% (2760 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 83.1% (2788 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 84.3% (2829 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 85.3% (2861 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 86.2% (2892 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 87.2% (2926 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 88.3% (2961 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 89.4% (3001 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 90.4% (3032 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 91.4% (3066 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 92.3% (3095 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 93.5% (3137 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 94.3% (3165 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 95.4% (3199 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 96.4% (3234 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=948 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.8; 1632 / 3355 (P = 48.64%) round 16]               
[00:00:00] Finding cutoff p=945 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=945 1.8% (60 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 3.7% (123 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 4.9% (166 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 6.7% (225 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 9.6% (321 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 11.4% (381 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 13.6% (456 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 16.3% (548 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 18.3% (615 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 20.3% (681 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 22.0% (737 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 24.2% (813 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 26.5% (889 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 28.4% (953 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 31.1% (1042 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 32.8% (1100 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 34.7% (1163 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 36.8% (1236 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 38.6% (1295 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 40.5% (1359 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 42.4% (1424 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 44.1% (1479 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 46.0% (1542 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 47.7% (1600 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 48.9% (1642 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 50.3% (1686 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 51.6% (1732 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 53.0% (1777 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 54.5% (1827 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 55.7% (1870 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 56.9% (1908 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 58.2% (1952 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 59.4% (1992 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 61.2% (2052 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 62.4% (2093 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 63.6% (2134 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 64.8% (2175 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 66.7% (2237 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 67.7% (2272 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 68.7% (2306 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 70.2% (2355 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 71.3% (2392 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 72.5% (2433 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 73.6% (2470 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 74.6% (2502 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 77.6% (2603 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 78.7% (2642 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 79.5% (2668 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 80.3% (2693 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 81.1% (2720 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 82.1% (2755 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 83.3% (2795 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 84.1% (2822 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 85.3% (2861 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 86.2% (2892 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 87.2% (2924 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 88.2% (2958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 89.4% (2999 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 90.3% (3030 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 91.3% (3063 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 92.3% (3095 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 93.3% (3129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 94.4% (3166 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 95.3% (3196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.5; 1703 / 3355 (P = 50.76%) round 17]               
[00:00:00] Finding cutoff p=940 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=940 1.8% (61 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 3.7% (124 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 4.8% (161 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 6.3% (212 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 8.0% (268 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 9.9% (333 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 12.3% (413 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 14.8% (498 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 17.0% (571 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 19.3% (648 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 21.7% (728 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 23.7% (796 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 26.2% (878 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 28.2% (947 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 30.5% (1024 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 32.1% (1078 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 34.9% (1172 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 37.0% (1243 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 38.8% (1303 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 40.4% (1356 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 42.0% (1410 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 43.7% (1465 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 45.0% (1510 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 46.7% (1568 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 48.3% (1621 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 49.7% (1666 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 50.8% (1706 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 52.4% (1758 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 53.7% (1803 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 55.0% (1845 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 56.2% (1887 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 57.6% (1934 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 59.0% (1979 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 60.7% (2037 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 62.1% (2084 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 63.2% (2122 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 64.6% (2168 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 66.4% (2228 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 67.5% (2263 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 68.6% (2300 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 69.9% (2346 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 71.0% (2382 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 72.0% (2417 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 73.1% (2453 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 74.1% (2485 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 75.7% (2539 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 77.0% (2585 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 78.4% (2631 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 79.3% (2661 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 80.3% (2694 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 81.4% (2730 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 82.1% (2754 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 83.1% (2788 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 84.1% (2823 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 85.4% (2866 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 86.3% (2894 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 87.3% (2930 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 88.2% (2960 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 89.4% (2999 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 90.4% (3032 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 91.2% (3060 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 92.3% (3097 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 93.4% (3132 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 94.4% (3168 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 95.4% (3199 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=940 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.0; 1802 / 3355 (P = 53.71%) round 18]               
[00:00:00] Finding cutoff p=935 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=935 1.0% (35 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 2.7% (90 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 4.3% (145 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 5.3% (179 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 6.6% (220 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 8.3% (277 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 10.0% (335 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 12.4% (416 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 15.3% (512 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 17.2% (578 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 19.1% (640 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 21.3% (715 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 23.0% (773 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 25.3% (848 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 27.2% (911 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 29.4% (986 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 31.6% (1061 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 33.7% (1129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 36.0% (1207 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 37.4% (1255 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 39.3% (1318 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 40.7% (1364 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 42.4% (1423 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 44.0% (1475 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 45.3% (1521 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 46.9% (1572 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 48.4% (1623 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 50.4% (1691 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 51.4% (1726 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 52.8% (1773 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 53.8% (1806 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 55.2% (1851 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 56.3% (1890 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 57.5% (1930 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 59.6% (2000 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 61.0% (2045 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 62.1% (2085 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 63.1% (2116 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 64.8% (2173 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 66.0% (2214 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 67.0% (2248 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 68.5% (2299 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 69.7% (2337 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 70.8% (2374 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 71.7% (2407 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 72.7% (2440 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 74.0% (2483 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 75.5% (2534 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 76.7% (2574 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 77.6% (2603 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 78.2% (2624 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 79.1% (2654 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 80.4% (2697 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 81.2% (2723 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 82.3% (2762 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 83.3% (2794 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 84.4% (2831 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 85.4% (2864 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 86.2% (2893 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 87.2% (2927 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 88.2% (2959 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 89.2% (2994 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 90.3% (3031 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 91.3% (3064 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 92.3% (3097 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 93.3% (3129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 94.2% (3162 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 95.3% (3198 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 96.3% (3232 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 97.3% (3266 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=935 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.5; 1915 / 3355 (P = 57.08%) round 19]               
[00:00:00] Finding cutoff p=930 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=930 1.7% (57 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 3.3% (110 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 4.6% (153 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 5.8% (196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 6.9% (230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 8.6% (289 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 11.0% (368 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 13.4% (450 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 15.0% (504 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 17.0% (571 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 19.2% (644 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 20.8% (699 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 23.6% (793 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 25.8% (865 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 27.7% (931 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 29.8% (1000 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 32.2% (1079 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 33.9% (1137 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 35.3% (1184 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 36.8% (1234 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 38.4% (1287 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 40.1% (1344 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 41.5% (1391 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 43.0% (1441 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 44.5% (1493 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 45.8% (1535 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 47.2% (1583 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 48.6% (1630 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 49.8% (1670 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 50.9% (1709 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 52.2% (1750 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 53.2% (1784 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 54.2% (1820 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 56.2% (1885 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 57.5% (1929 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 59.5% (1997 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 60.8% (2039 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 62.2% (2088 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 63.3% (2123 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 64.4% (2162 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 65.8% (2209 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 67.1% (2250 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 68.1% (2285 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 69.2% (2320 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 70.3% (2357 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 71.9% (2412 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 73.0% (2448 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 74.0% (2484 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 75.4% (2528 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 76.6% (2570 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 77.5% (2600 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 78.5% (2635 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 80.0% (2684 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 80.7% (2707 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 81.3% (2728 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 82.3% (2762 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 83.5% (2800 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 84.1% (2822 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 85.2% (2860 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 86.3% (2897 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 87.4% (2931 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 88.3% (2961 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 89.3% (2995 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 90.2% (3027 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 91.4% (3066 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 92.3% (3097 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 93.3% (3130 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 94.2% (3162 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 95.4% (3199 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.0; 2027 / 3355 (P = 60.42%) round 20]               
[00:00:00] Finding cutoff p=927 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=927 1.1% (36 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 3.6% (122 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 4.9% (165 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 6.2% (208 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 7.4% (247 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 9.2% (309 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 10.5% (353 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 12.9% (433 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 15.8% (530 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 17.9% (601 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 19.7% (661 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 21.6% (726 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 23.6% (791 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 26.0% (872 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 28.3% (949 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 30.3% (1017 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 31.8% (1066 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 33.9% (1136 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 35.9% (1204 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 37.3% (1252 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 39.0% (1308 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 40.4% (1355 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 42.1% (1414 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 43.8% (1468 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 44.9% (1505 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 46.2% (1549 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 47.4% (1589 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 48.5% (1628 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 50.0% (1677 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 51.2% (1718 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 52.3% (1754 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 53.4% (1790 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 54.5% (1828 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 55.6% (1864 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 57.2% (1919 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 58.5% (1963 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 59.6% (1999 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 60.6% (2033 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 62.2% (2087 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 63.4% (2127 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 64.1% (2151 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 65.6% (2200 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 67.4% (2261 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 68.4% (2296 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 69.4% (2329 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 70.3% (2359 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 72.2% (2421 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 73.5% (2465 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 74.5% (2500 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 75.4% (2531 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 76.5% (2568 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 77.5% (2599 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 78.4% (2631 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 79.3% (2659 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 80.2% (2690 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 81.3% (2729 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 82.4% (2763 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 83.1% (2788 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 84.2% (2825 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 85.2% (2857 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 86.3% (2896 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 87.2% (2926 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 88.3% (2964 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 89.3% (2996 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 90.4% (3033 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 91.2% (3060 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 92.4% (3099 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 93.2% (3128 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 94.3% (3164 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 95.4% (3199 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.7; 2083 / 3355 (P = 62.09%) round 21]               
[00:00:00] Finding cutoff p=924 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=924 1.8% (60 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 3.2% (109 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 4.5% (152 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 5.5% (186 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 6.3% (210 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 7.2% (241 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 9.0% (303 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 11.5% (386 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 13.7% (461 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 15.9% (534 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 18.2% (610 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 19.8% (665 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 21.9% (736 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 24.1% (808 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 26.3% (881 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 28.3% (949 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 30.2% (1014 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 33.0% (1106 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 34.2% (1147 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 35.6% (1193 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 37.0% (1240 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 38.6% (1296 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 40.1% (1346 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 41.6% (1397 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 42.8% (1436 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 44.1% (1481 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 45.8% (1535 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 47.2% (1582 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 48.6% (1629 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 50.0% (1676 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 50.9% (1709 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 52.1% (1749 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 53.2% (1785 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 55.4% (1859 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 57.0% (1912 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 57.9% (1941 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 59.1% (1982 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 60.8% (2040 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 62.4% (2093 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 63.8% (2141 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 64.7% (2171 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 65.8% (2206 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 66.9% (2246 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 68.2% (2288 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 69.7% (2337 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 71.4% (2394 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 72.3% (2424 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 73.2% (2455 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 74.5% (2499 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 76.4% (2562 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 77.5% (2600 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 78.4% (2629 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 79.2% (2656 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 80.3% (2693 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 81.2% (2725 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 82.2% (2758 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 83.3% (2794 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 84.2% (2825 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 85.3% (2861 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 86.3% (2896 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 87.4% (2932 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 88.4% (2966 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 89.2% (2994 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 90.3% (3028 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 91.3% (3063 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 92.3% (3097 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 93.4% (3133 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 94.3% (3164 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 95.4% (3200 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.4; 2134 / 3355 (P = 63.61%) round 22]               
[00:00:00] Finding cutoff p=919 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=919 1.7% (58 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 3.1% (103 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 4.6% (153 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 5.6% (188 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 6.6% (220 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 7.8% (262 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 10.8% (363 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 14.1% (474 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 15.9% (533 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 18.6% (623 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 21.1% (708 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 22.7% (760 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 25.2% (844 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 27.9% (935 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 29.3% (982 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 31.4% (1053 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 33.4% (1121 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 35.3% (1184 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 36.7% (1232 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 38.2% (1280 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 39.8% (1336 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 41.4% (1390 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 43.0% (1442 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 44.2% (1482 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 45.5% (1527 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 46.9% (1573 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 47.7% (1599 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 49.0% (1645 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 49.9% (1674 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 50.9% (1708 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 52.1% (1747 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 53.1% (1783 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 54.1% (1815 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 56.0% (1878 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 57.0% (1913 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 58.2% (1954 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 59.9% (2010 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 61.1% (2050 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 62.3% (2089 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 63.5% (2131 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 64.5% (2163 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 65.6% (2200 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 66.6% (2235 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 67.9% (2277 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 69.8% (2342 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 71.4% (2395 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 72.1% (2419 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 73.4% (2463 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 74.3% (2492 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 75.3% (2527 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 76.3% (2559 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 77.6% (2604 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 78.3% (2627 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 79.4% (2665 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 80.1% (2689 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 81.2% (2724 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 82.2% (2758 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 83.2% (2791 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 84.3% (2827 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 85.3% (2862 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 86.4% (2898 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 87.2% (2926 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 88.4% (2965 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 89.2% (2994 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 90.2% (3026 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 91.3% (3063 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 92.4% (3101 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 93.4% (3133 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 94.4% (3167 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 95.3% (3197 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 96.3% (3232 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=919 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.9; 2218 / 3355 (P = 66.11%) round 23]               
[00:00:00] Finding cutoff p=916 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=916 1.1% (37 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 2.6% (88 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 4.1% (137 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 5.1% (172 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 6.3% (210 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 7.5% (250 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 9.0% (302 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 11.0% (369 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 13.2% (444 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 14.7% (494 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 16.9% (566 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 19.6% (658 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 21.2% (712 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 23.4% (785 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 25.4% (852 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 27.8% (932 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 29.4% (985 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 31.3% (1051 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 33.4% (1120 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 34.8% (1169 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 36.0% (1209 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 38.8% (1302 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 40.2% (1349 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 41.8% (1403 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 43.0% (1443 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 44.2% (1482 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 45.4% (1523 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 46.6% (1564 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 48.0% (1611 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 49.1% (1646 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 50.3% (1687 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 51.4% (1724 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 52.4% (1757 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 53.4% (1792 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 55.9% (1877 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 56.8% (1905 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 58.2% (1952 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 59.8% (2005 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 61.0% (2047 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 62.7% (2104 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 63.8% (2141 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 64.7% (2172 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 65.7% (2203 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 66.8% (2240 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 68.8% (2309 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 70.6% (2369 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 72.1% (2419 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 73.1% (2453 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 74.2% (2491 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 75.1% (2520 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 76.0% (2550 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 77.4% (2597 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 78.1% (2619 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 79.1% (2654 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 80.3% (2694 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 81.3% (2726 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 82.2% (2758 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 83.1% (2788 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 84.4% (2833 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 85.6% (2872 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 86.2% (2892 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 87.2% (2924 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 88.4% (2965 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 89.3% (2995 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 90.3% (3029 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 91.3% (3062 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 92.5% (3104 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 93.4% (3132 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 94.4% (3167 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 95.3% (3196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=916 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.6; 2261 / 3355 (P = 67.39%) round 24]               
[00:00:00] Finding cutoff p=911 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=911 1.6% (53 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 3.0% (100 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 4.3% (143 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 5.4% (181 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 6.3% (211 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 7.7% (258 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 9.6% (321 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 12.2% (409 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 13.9% (468 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 16.2% (544 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 18.5% (620 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 20.0% (671 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 22.7% (760 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 24.7% (828 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 26.7% (896 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 28.2% (947 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 30.6% (1025 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 32.5% (1091 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 33.7% (1129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 35.1% (1179 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 36.2% (1215 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 37.7% (1265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 39.2% (1316 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 40.1% (1347 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 41.7% (1400 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 42.8% (1435 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 43.8% (1468 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 45.7% (1534 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 47.0% (1576 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 48.0% (1609 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 49.0% (1643 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 50.0% (1676 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 50.8% (1706 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 53.1% (1783 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 54.5% (1830 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 55.2% (1852 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 56.2% (1886 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 57.8% (1938 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 58.9% (1976 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 59.9% (2008 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 61.1% (2050 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 62.1% (2082 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 63.0% (2113 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 64.1% (2150 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 65.1% (2184 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 66.9% (2244 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 68.6% (2300 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 69.5% (2332 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 70.6% (2368 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 71.4% (2396 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 72.3% (2426 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 73.2% (2455 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 74.3% (2492 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 75.3% (2525 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 76.1% (2554 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 77.2% (2590 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 78.0% (2618 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 79.1% (2653 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 80.1% (2689 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 81.3% (2729 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 82.1% (2755 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 83.4% (2798 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 84.4% (2833 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 85.2% (2858 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 86.3% (2894 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 87.2% (2924 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 88.5% (2969 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 89.2% (2994 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 90.4% (3032 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 91.2% (3060 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 92.3% (3097 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 93.5% (3136 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 94.3% (3164 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 95.3% (3197 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.1; 2312 / 3355 (P = 68.91%) round 25]               
[00:00:00] Finding cutoff p=906 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=906 1.6% (53 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 2.8% (93 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 4.2% (141 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 5.2% (173 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 6.4% (215 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 7.4% (247 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 9.2% (308 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 11.7% (391 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 13.0% (437 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 14.6% (491 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 17.7% (593 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 19.5% (653 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 21.4% (719 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 23.0% (772 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 25.5% (856 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 27.0% (906 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 29.5% (989 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 31.4% (1052 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 32.6% (1094 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 34.1% (1145 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 35.5% (1192 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 36.8% (1236 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 38.2% (1280 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 39.2% (1315 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 40.4% (1357 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 41.9% (1405 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 42.9% (1440 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 44.5% (1492 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 45.6% (1531 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 47.5% (1592 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 48.6% (1630 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 49.7% (1666 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 51.7% (1736 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 53.4% (1790 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 54.3% (1822 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 55.1% (1850 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 56.5% (1895 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 57.4% (1927 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 58.5% (1961 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 60.1% (2015 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 61.1% (2049 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 61.8% (2075 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 62.8% (2108 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 65.0% (2180 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 66.8% (2242 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 68.0% (2280 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 69.0% (2316 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 70.5% (2365 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 71.2% (2389 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 72.0% (2416 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 73.0% (2448 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 74.4% (2496 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 75.1% (2519 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 76.1% (2554 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 77.0% (2585 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 78.1% (2620 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 79.4% (2665 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 80.3% (2694 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 81.1% (2722 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 82.2% (2759 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 83.5% (2802 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 84.2% (2826 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 85.5% (2867 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 86.3% (2897 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 87.2% (2925 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 88.3% (2964 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 89.2% (2992 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 90.6% (3041 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 91.5% (3069 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 92.3% (3095 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 93.3% (3131 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 94.4% (3167 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 95.5% (3203 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 97.3% (3266 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.6; 2393 / 3355 (P = 71.33%) round 26]               
[00:00:00] Finding cutoff p=903 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=903 1.4% (47 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 2.6% (86 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 3.7% (124 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 4.5% (152 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 5.5% (183 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 6.5% (218 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 8.3% (279 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 10.8% (363 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 12.0% (404 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 13.9% (467 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 16.4% (550 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 18.1% (608 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 19.9% (666 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 21.5% (722 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 23.5% (788 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 25.4% (853 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 28.8% (967 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 31.1% (1042 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 32.1% (1076 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 33.1% (1112 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 34.2% (1148 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 35.6% (1193 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 37.0% (1241 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 38.0% (1275 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 39.3% (1319 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 40.7% (1365 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 41.8% (1402 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 42.9% (1440 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 44.5% (1492 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 45.5% (1527 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 46.7% (1567 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 47.7% (1600 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 49.2% (1650 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 51.1% (1716 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 52.7% (1767 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 53.4% (1791 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 54.2% (1817 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 55.7% (1869 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 56.8% (1907 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 57.8% (1938 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 59.5% (1996 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 60.5% (2029 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 61.3% (2056 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 62.3% (2091 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 62.9% (2110 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 64.9% (2179 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 66.7% (2238 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 67.9% (2278 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 69.1% (2319 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 70.1% (2351 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 71.2% (2390 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 72.3% (2424 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 73.1% (2453 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 74.2% (2489 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 75.1% (2519 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 76.2% (2555 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 77.1% (2587 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 78.2% (2624 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 79.4% (2665 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 80.1% (2686 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 81.6% (2739 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 82.1% (2756 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 83.2% (2792 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 84.3% (2828 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 85.2% (2857 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 86.2% (2892 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 87.2% (2925 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 88.2% (2959 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 89.2% (2993 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 90.7% (3042 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 91.2% (3060 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 92.4% (3099 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 93.4% (3132 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 94.3% (3165 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 95.4% (3200 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.3; 2433 / 3355 (P = 72.52%) round 27]               
[00:00:00] Finding cutoff p=898 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=898 1.3% (43 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 2.6% (87 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 3.7% (124 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 4.7% (159 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 5.7% (190 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 6.6% (221 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 8.5% (285 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 11.2% (377 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 12.7% (427 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 14.3% (480 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 16.7% (559 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 18.4% (616 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 20.1% (674 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 21.7% (728 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 23.6% (791 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 25.4% (851 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 28.6% (958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 30.7% (1030 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 31.7% (1064 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 33.2% (1114 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 33.8% (1135 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 35.2% (1181 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 36.8% (1233 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 38.0% (1275 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 39.3% (1317 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 40.6% (1361 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 41.6% (1397 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 42.9% (1440 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 44.3% (1486 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 45.5% (1526 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 46.4% (1556 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 47.6% (1598 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 50.2% (1685 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 52.1% (1748 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 53.2% (1785 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 54.1% (1816 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 55.8% (1871 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 56.8% (1905 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 58.7% (1968 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 59.3% (1990 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 60.3% (2023 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 61.4% (2059 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 62.2% (2086 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 63.8% (2142 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 65.7% (2204 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 66.6% (2233 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 67.5% (2264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 68.3% (2293 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 69.5% (2331 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 70.3% (2360 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 71.2% (2389 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 72.5% (2431 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 73.1% (2454 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 74.3% (2492 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 75.1% (2521 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 76.2% (2558 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 77.2% (2589 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 78.2% (2623 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 79.3% (2659 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 80.9% (2713 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 81.7% (2740 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 82.5% (2769 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 83.2% (2793 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 84.3% (2828 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 85.7% (2875 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 86.2% (2891 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 87.4% (2931 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 88.3% (2961 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 89.7% (3011 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 90.4% (3034 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 91.4% (3067 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 92.3% (3096 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 93.4% (3134 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 94.3% (3164 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 95.5% (3203 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.8; 2494 / 3355 (P = 74.34%) round 28]               
[00:00:00] Finding cutoff p=888 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=888 1.7% (57 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 2.7% (92 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 3.9% (132 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 4.9% (163 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 5.8% (195 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 6.6% (223 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 8.7% (293 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 11.4% (382 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 12.4% (417 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 14.3% (481 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 16.8% (563 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 18.2% (611 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 20.9% (700 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 23.1% (775 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 25.1% (843 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 27.2% (911 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 30.0% (1006 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 32.1% (1077 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 33.2% (1115 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 34.6% (1161 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 35.7% (1199 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 36.6% (1227 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 38.1% (1277 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 39.4% (1322 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 40.3% (1353 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 41.3% (1385 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 42.6% (1429 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 43.7% (1467 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 44.7% (1500 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 45.8% (1536 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 46.9% (1572 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 48.0% (1609 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 50.2% (1685 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 51.4% (1726 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 53.1% (1783 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 53.9% (1810 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 55.6% (1864 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 56.8% (1906 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 57.8% (1939 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 59.3% (1988 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 60.2% (2019 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 61.0% (2047 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 62.3% (2091 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 63.8% (2140 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 65.7% (2204 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 66.5% (2231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 67.5% (2266 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 68.5% (2297 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 69.4% (2329 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 70.2% (2355 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 71.1% (2387 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 72.0% (2416 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 73.2% (2457 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 74.2% (2490 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 75.2% (2524 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 76.6% (2570 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 77.3% (2595 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 78.3% (2626 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 79.0% (2652 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 80.3% (2693 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 81.7% (2742 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 82.4% (2764 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 83.2% (2791 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 84.4% (2831 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 85.2% (2859 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 86.2% (2891 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 87.5% (2936 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 88.5% (2968 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 89.7% (3009 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 90.5% (3035 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 91.2% (3061 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 92.5% (3105 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 93.4% (3133 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 94.2% (3162 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 95.3% (3198 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 97.3% (3266 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=888 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 88.8; 2545 / 3355 (P = 75.86%) round 29]               
[00:00:00] Finding cutoff p=879 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=879 1.5% (50 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 3.0% (100 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 4.1% (138 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 5.5% (184 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 6.4% (215 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 8.0% (269 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 10.8% (362 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 12.3% (411 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 13.7% (461 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 16.0% (538 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 17.8% (598 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 19.9% (666 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 21.8% (732 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 23.8% (797 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 25.9% (870 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 28.2% (947 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 30.6% (1027 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 31.8% (1067 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 32.7% (1096 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 33.9% (1138 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 35.0% (1174 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 36.5% (1226 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 37.8% (1269 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 38.8% (1303 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 39.9% (1337 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 41.2% (1381 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 42.3% (1420 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 43.5% (1461 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 44.6% (1495 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 45.5% (1528 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 46.6% (1562 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 47.6% (1597 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 49.5% (1662 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 50.8% (1706 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 51.8% (1738 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 53.9% (1808 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 55.2% (1853 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 56.8% (1906 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 57.8% (1938 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 59.1% (1982 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 60.2% (2019 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 61.2% (2053 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 62.1% (2083 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 63.7% (2137 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 65.3% (2192 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 66.0% (2214 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 67.1% (2251 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 68.1% (2284 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 69.2% (2320 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 70.1% (2352 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 71.2% (2388 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 72.0% (2414 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 73.1% (2452 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 74.3% (2493 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 75.0% (2517 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 76.3% (2559 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 77.2% (2589 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 78.2% (2622 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 79.2% (2656 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 80.8% (2712 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 81.5% (2733 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 82.4% (2763 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 83.1% (2788 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 84.1% (2822 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 85.4% (2864 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 86.6% (2905 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 87.2% (2927 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 88.2% (2959 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 89.7% (3011 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 90.6% (3041 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 91.3% (3064 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 92.4% (3100 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 93.4% (3135 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 94.3% (3163 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 95.4% (3199 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 87.9; 2598 / 3355 (P = 77.44%) round 30]               
[00:00:00] Finding cutoff p=870 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=870 1.6% (54 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 2.9% (97 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 4.2% (140 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 5.4% (182 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 6.6% (221 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 8.6% (289 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 11.1% (371 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 12.1% (405 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 14.0% (471 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 16.0% (537 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 17.8% (598 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 19.8% (665 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 21.2% (711 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 23.4% (784 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 24.7% (828 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 28.3% (950 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 30.3% (1017 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 31.4% (1052 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 32.8% (1099 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 33.8% (1135 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 35.3% (1183 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 36.8% (1235 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 37.6% (1263 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 39.0% (1310 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 40.1% (1344 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 41.3% (1386 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 42.5% (1426 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 43.8% (1468 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 45.2% (1515 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 46.3% (1552 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 47.2% (1582 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 48.1% (1614 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 50.0% (1679 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 51.3% (1722 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 52.3% (1756 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 53.1% (1782 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 55.5% (1861 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 56.8% (1904 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 58.8% (1972 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 59.8% (2007 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 60.8% (2040 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 62.8% (2106 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 64.4% (2159 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 65.0% (2182 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 66.1% (2216 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 67.4% (2260 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 68.3% (2290 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 69.5% (2332 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 70.3% (2357 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 71.1% (2386 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 72.1% (2420 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 73.1% (2453 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 74.2% (2488 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 75.4% (2531 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 76.5% (2568 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 77.6% (2602 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 78.4% (2629 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 79.2% (2658 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 80.7% (2709 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 81.5% (2733 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 82.3% (2760 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 83.2% (2792 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 84.1% (2822 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 85.4% (2866 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 86.4% (2898 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 87.3% (2928 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 88.2% (2960 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 89.5% (3003 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 90.4% (3034 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 91.4% (3067 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 92.5% (3102 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 93.3% (3131 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 94.3% (3164 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 95.4% (3201 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=870 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 87.0; 2675 / 3355 (P = 79.73%) round 31]               
[00:00:00] Finding cutoff p=859 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=859 1.5% (49 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 2.7% (91 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 4.0% (133 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 4.7% (159 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 6.1% (204 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 8.6% (287 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 10.9% (367 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 12.0% (401 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 13.5% (454 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 15.7% (527 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 17.3% (579 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 19.4% (651 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 21.0% (705 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 22.9% (769 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 24.4% (820 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 27.8% (933 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 30.0% (1005 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 30.8% (1032 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 31.5% (1058 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 32.6% (1093 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 33.9% (1138 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 35.3% (1184 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 36.5% (1225 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 37.7% (1265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 38.7% (1298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 39.8% (1335 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 40.8% (1370 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 41.8% (1401 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 43.0% (1441 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 43.8% (1468 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 44.9% (1505 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 46.0% (1543 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 48.1% (1613 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 49.2% (1649 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 50.1% (1680 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 51.2% (1718 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 52.8% (1771 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 54.2% (1819 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 55.5% (1861 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 57.0% (1913 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 58.0% (1947 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 59.0% (1978 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 60.7% (2037 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 62.6% (2101 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 63.3% (2125 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 64.0% (2147 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 64.9% (2176 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 66.3% (2224 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 67.2% (2256 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 68.4% (2295 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 69.2% (2322 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 70.2% (2354 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 71.1% (2385 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 72.4% (2429 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 73.1% (2454 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 74.1% (2486 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 75.3% (2525 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 76.2% (2555 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 77.1% (2587 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 78.5% (2632 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 79.6% (2672 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 80.7% (2708 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 81.3% (2728 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 82.1% (2754 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 83.2% (2793 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 84.3% (2828 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 85.1% (2856 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 86.2% (2891 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 87.4% (2931 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 88.5% (2968 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 89.3% (2995 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 90.4% (3032 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 91.4% (3065 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 92.3% (3096 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 93.4% (3132 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 94.3% (3164 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 95.3% (3198 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=859 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.9; 2756 / 3355 (P = 82.15%) round 32]               
[00:00:00] Finding cutoff p=849 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=849 1.5% (51 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 2.7% (90 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 3.8% (129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 4.9% (166 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 5.6% (189 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 6.9% (230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 9.0% (303 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 11.4% (383 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 12.3% (414 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 14.2% (477 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 16.2% (544 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 17.7% (595 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 20.6% (690 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 22.3% (747 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 25.1% (842 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 26.3% (883 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 28.7% (962 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 31.0% (1040 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 31.7% (1065 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 33.0% (1106 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 34.0% (1142 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 35.3% (1185 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 37.0% (1241 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 37.9% (1270 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 38.9% (1306 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 39.9% (1338 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 40.8% (1370 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 42.3% (1418 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 43.0% (1444 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 44.1% (1480 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 45.2% (1515 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 46.0% (1542 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 46.8% (1570 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 48.5% (1628 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 49.8% (1671 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 50.7% (1702 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 51.8% (1738 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 53.1% (1782 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 54.6% (1832 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 55.5% (1862 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 57.2% (1919 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 58.3% (1957 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 59.0% (1981 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 60.2% (2019 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 61.0% (2047 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 62.5% (2096 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 63.9% (2143 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 65.2% (2188 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 66.1% (2218 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 67.1% (2250 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 68.1% (2285 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 69.2% (2323 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 70.1% (2352 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 71.2% (2390 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 72.2% (2423 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 73.3% (2458 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 74.2% (2488 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 75.1% (2518 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 76.0% (2551 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 77.2% (2590 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 78.9% (2648 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 79.8% (2677 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 80.7% (2708 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 81.3% (2729 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 82.4% (2763 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 83.2% (2790 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 84.4% (2831 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 85.2% (2858 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 86.3% (2897 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 87.3% (2930 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 88.6% (2973 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 89.4% (3000 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 90.5% (3035 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 91.3% (3062 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 92.6% (3106 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 93.4% (3133 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 94.4% (3166 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 95.3% (3198 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 96.3% (3232 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=849 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.9; 2827 / 3355 (P = 84.26%) round 33]               
[00:00:00] Finding cutoff p=840 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=840 1.4% (46 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 2.3% (77 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 3.3% (112 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 4.3% (143 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 5.2% (176 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 6.3% (211 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 7.7% (257 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 10.5% (352 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 11.9% (400 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 13.7% (459 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 16.1% (539 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 17.4% (584 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 20.0% (671 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 21.8% (730 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 23.5% (789 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 25.4% (853 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 27.6% (927 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 29.9% (1004 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 30.6% (1027 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 31.8% (1068 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 33.1% (1109 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 34.0% (1140 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 35.5% (1192 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 36.8% (1236 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 38.0% (1276 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 39.2% (1315 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 40.6% (1361 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 41.6% (1395 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 42.9% (1439 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 44.1% (1480 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 45.1% (1514 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 47.2% (1583 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 48.4% (1624 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 49.2% (1649 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 50.0% (1679 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 51.5% (1729 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 53.4% (1792 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 54.4% (1826 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 55.8% (1872 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 56.8% (1907 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 58.0% (1946 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 59.0% (1978 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 60.6% (2034 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 61.8% (2072 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 62.5% (2097 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 63.5% (2130 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 64.1% (2152 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 65.1% (2185 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 65.9% (2211 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 66.9% (2246 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 68.5% (2299 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 69.6% (2334 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 70.3% (2360 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 71.1% (2384 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 72.2% (2421 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 73.4% (2463 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 74.1% (2485 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 75.1% (2520 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 76.2% (2558 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 77.2% (2591 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 78.8% (2644 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 79.3% (2662 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 80.2% (2691 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 81.5% (2733 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 82.4% (2763 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 83.1% (2789 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 84.2% (2825 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 85.2% (2859 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 86.3% (2894 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 87.4% (2933 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 88.5% (2969 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 89.2% (2994 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 90.3% (3030 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 91.4% (3067 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 92.3% (3096 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 93.3% (3130 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 94.3% (3164 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 95.3% (3197 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 97.3% (3266 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=840 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.0; 2892 / 3355 (P = 86.20%) round 34]               
[00:00:00] Finding cutoff p=831 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=831 1.3% (43 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 2.2% (75 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 3.5% (116 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 4.4% (147 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 5.1% (170 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 7.1% (239 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 10.2% (342 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 11.9% (399 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 13.8% (464 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 15.5% (519 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 17.3% (581 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 18.8% (631 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 20.6% (690 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 22.1% (740 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 25.2% (845 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 27.3% (915 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 28.2% (947 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 29.7% (996 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 30.6% (1025 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 31.7% (1062 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 32.8% (1100 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 33.7% (1129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 34.6% (1160 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 35.8% (1200 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 36.5% (1225 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 38.0% (1274 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 38.7% (1300 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 39.8% (1336 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 40.7% (1367 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 41.7% (1398 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 42.6% (1429 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 45.1% (1512 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 46.4% (1558 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 47.2% (1583 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 48.5% (1627 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 49.9% (1674 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 50.9% (1708 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 51.9% (1740 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 53.3% (1788 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 54.3% (1821 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 54.8% (1839 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 55.8% (1873 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 56.8% (1906 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 58.4% (1958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 59.9% (2009 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 61.3% (2055 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 62.0% (2079 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 63.2% (2120 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 64.2% (2154 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 65.1% (2183 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 66.2% (2222 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 67.2% (2255 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 68.0% (2280 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 69.1% (2318 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 70.0% (2350 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 71.4% (2394 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 72.1% (2420 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 73.3% (2458 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 74.1% (2487 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 75.6% (2537 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 76.3% (2559 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 77.2% (2589 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 78.3% (2626 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 79.9% (2679 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 80.7% (2709 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 81.2% (2723 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 82.2% (2758 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 83.2% (2791 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 84.2% (2826 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 85.8% (2880 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 86.9% (2914 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 87.6% (2938 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 88.4% (2967 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 89.2% (2993 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 90.3% (3030 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 91.2% (3061 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 92.5% (3104 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 93.3% (3129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 94.3% (3165 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 95.3% (3197 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 97.3% (3266 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 83.1; 2937 / 3355 (P = 87.54%) round 35]               
[00:00:00] Finding cutoff p=821 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=821 1.1% (38 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 2.4% (79 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 3.2% (108 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 4.1% (139 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 5.3% (178 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 6.9% (231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 8.7% (293 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 9.9% (333 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 11.7% (391 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 13.2% (443 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 14.2% (477 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 16.6% (556 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 18.1% (607 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 20.2% (678 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 21.9% (734 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 25.1% (841 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 27.2% (913 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 27.9% (935 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 28.9% (968 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 30.0% (1008 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 31.4% (1055 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 32.6% (1095 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 33.6% (1128 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 34.5% (1159 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 35.8% (1201 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 36.8% (1234 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 37.8% (1267 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 38.7% (1299 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 39.6% (1328 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 40.7% (1364 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 43.5% (1458 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 44.7% (1501 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 45.7% (1533 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 46.6% (1564 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 48.2% (1618 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 49.5% (1661 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 50.4% (1690 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 52.1% (1747 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 53.2% (1785 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 54.2% (1817 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 54.9% (1843 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 56.6% (1898 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 58.4% (1959 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 59.0% (1979 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 59.9% (2011 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 61.0% (2046 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 62.4% (2093 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 63.0% (2114 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 64.1% (2151 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 65.0% (2182 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 66.0% (2215 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 67.3% (2259 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 68.3% (2290 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 69.1% (2318 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 70.5% (2364 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 71.1% (2386 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 72.8% (2442 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 73.7% (2472 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 74.8% (2511 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 75.3% (2527 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 76.3% (2560 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 77.3% (2592 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 78.2% (2625 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 79.5% (2668 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 80.4% (2697 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 81.2% (2725 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 82.2% (2759 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 83.2% (2790 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 84.3% (2829 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 85.2% (2858 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 86.4% (2900 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 87.2% (2926 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 88.3% (2961 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 89.3% (2996 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 90.4% (3034 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 91.4% (3068 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 92.3% (3097 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 93.4% (3135 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 94.5% (3169 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 95.3% (3197 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 98.4% (3300 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=821 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.1; 2980 / 3355 (P = 88.82%) round 36]               
[00:00:00] Finding cutoff p=811 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=811 1.3% (45 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 2.2% (75 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 3.4% (114 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 4.2% (142 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 6.1% (205 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 7.9% (265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 8.9% (300 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 10.5% (351 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 12.9% (434 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 14.2% (477 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 16.1% (540 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 17.7% (593 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 19.4% (652 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 20.9% (700 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 23.7% (795 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 26.6% (892 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 28.0% (941 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 29.4% (986 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 30.6% (1028 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 32.2% (1081 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 32.9% (1105 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 34.0% (1140 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 34.8% (1167 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 36.0% (1207 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 37.3% (1252 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 38.2% (1282 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 39.6% (1329 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 40.6% (1362 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 41.9% (1405 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 44.0% (1477 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 45.3% (1521 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 46.1% (1546 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 46.9% (1573 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 48.3% (1620 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 49.5% (1661 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 50.4% (1690 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 51.9% (1741 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 52.9% (1776 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 53.9% (1808 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 54.9% (1842 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 56.2% (1887 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 57.6% (1933 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 58.5% (1963 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 59.4% (1993 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 60.1% (2016 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 61.1% (2051 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 62.5% (2096 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 63.1% (2117 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 64.0% (2148 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 65.0% (2180 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 66.4% (2229 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 67.0% (2248 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 68.2% (2287 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 68.9% (2312 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 70.2% (2356 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 71.1% (2387 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 72.0% (2414 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 73.2% (2456 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 74.1% (2486 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 75.3% (2527 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 76.2% (2555 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 77.3% (2595 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 78.1% (2619 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 79.6% (2669 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 80.2% (2692 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 81.3% (2728 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 82.1% (2754 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 83.2% (2793 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 84.6% (2839 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 85.6% (2873 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 86.4% (2900 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 87.4% (2933 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 88.2% (2958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 89.4% (3000 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 90.2% (3026 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 91.4% (3065 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 92.3% (3095 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 93.5% (3137 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 94.3% (3164 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 95.3% (3197 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 96.3% (3232 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 81.1; 3010 / 3355 (P = 89.72%) round 37]               
[00:00:00] Finding cutoff p=800 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=800 1.3% (43 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 2.0% (68 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 3.4% (113 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 4.3% (144 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 6.1% (204 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 8.1% (271 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 9.2% (309 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 11.0% (370 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 12.8% (428 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 14.1% (474 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 16.1% (540 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 17.9% (599 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 19.4% (650 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 20.6% (692 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 24.1% (809 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 25.9% (870 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 26.8% (900 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 28.2% (947 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 29.0% (973 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 30.3% (1017 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 31.7% (1063 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 32.4% (1088 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 33.9% (1139 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 34.8% (1167 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 35.9% (1203 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 37.1% (1246 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 38.0% (1274 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 38.9% (1306 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 39.6% (1327 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 40.6% (1361 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 43.5% (1459 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 44.3% (1487 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 45.2% (1518 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 46.0% (1544 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 47.2% (1585 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 48.3% (1620 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 49.0% (1644 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 50.6% (1698 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 51.3% (1722 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 52.2% (1751 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 53.2% (1786 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 54.2% (1817 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 55.7% (1870 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 57.5% (1928 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 58.2% (1952 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 59.0% (1980 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 60.3% (2024 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 61.0% (2046 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 61.9% (2078 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 63.4% (2128 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 64.2% (2153 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 64.9% (2177 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 66.3% (2224 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 67.0% (2247 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 68.5% (2298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 69.1% (2318 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 70.2% (2355 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 71.1% (2386 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 72.5% (2434 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 73.3% (2460 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 74.4% (2496 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 75.3% (2526 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 76.5% (2567 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 77.3% (2595 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 78.7% (2642 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 79.5% (2668 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 80.5% (2700 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 81.1% (2720 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 82.2% (2759 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 83.5% (2803 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 84.4% (2832 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 85.2% (2857 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 86.6% (2905 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 87.3% (2929 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 88.7% (2976 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 89.4% (2999 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 90.5% (3036 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 91.3% (3064 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 92.4% (3099 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 93.3% (3129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 94.2% (3162 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 95.3% (3196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 96.3% (3230 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 97.3% (3266 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.0; 3053 / 3355 (P = 91.00%) round 38]               
[00:00:00] Finding cutoff p=789 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=789 1.2% (39 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 2.6% (86 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 3.3% (110 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 4.1% (137 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 6.0% (200 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 8.0% (267 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 8.8% (294 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 10.5% (353 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 12.5% (418 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 13.4% (451 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 15.4% (516 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 16.9% (566 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 18.3% (615 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 20.1% (673 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 23.3% (783 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 26.0% (872 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 27.1% (910 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 28.2% (947 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 29.2% (978 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 30.2% (1014 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 31.6% (1059 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 32.7% (1096 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 33.7% (1130 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 34.8% (1168 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 35.7% (1197 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 36.8% (1235 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 37.8% (1267 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 38.8% (1301 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 40.8% (1369 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 42.1% (1414 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 43.1% (1447 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 44.3% (1485 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 45.8% (1537 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 47.1% (1581 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 47.6% (1598 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 48.9% (1641 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 49.7% (1667 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 51.0% (1710 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 52.0% (1743 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 53.9% (1810 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 55.5% (1863 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 56.1% (1882 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 56.9% (1909 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 58.3% (1957 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 58.8% (1972 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 59.9% (2011 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 61.3% (2056 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 62.2% (2086 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 63.0% (2112 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 64.1% (2152 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 65.1% (2185 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 66.3% (2224 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 67.3% (2259 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 69.0% (2314 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 69.9% (2346 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 71.4% (2395 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 72.3% (2427 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 73.3% (2459 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 74.5% (2499 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 75.1% (2518 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 76.1% (2553 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 77.1% (2587 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 78.2% (2625 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 79.3% (2661 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 80.1% (2686 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 81.4% (2732 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 82.6% (2772 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 83.3% (2796 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 84.6% (2838 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 85.3% (2862 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 86.6% (2906 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 87.5% (2936 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 88.6% (2972 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 89.5% (3003 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 90.8% (3048 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 91.5% (3071 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 92.3% (3098 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 93.3% (3131 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 94.2% (3162 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 95.3% (3198 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 96.5% (3238 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.9; 3092 / 3355 (P = 92.16%) round 39]               
[00:00:00] Finding cutoff p=778 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=778 1.2% (40 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 2.5% (85 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 3.1% (105 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 4.1% (136 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 5.7% (192 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 8.2% (274 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 10.1% (338 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 12.1% (407 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 13.1% (439 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 14.8% (495 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 16.6% (558 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 18.5% (619 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 20.8% (699 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 24.2% (811 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 26.2% (880 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 27.0% (907 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 28.0% (941 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 28.9% (968 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 30.1% (1009 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 31.3% (1050 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 32.2% (1080 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 32.9% (1103 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 33.8% (1135 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 34.8% (1169 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 36.1% (1210 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 37.0% (1241 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 37.7% (1266 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 38.6% (1294 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 39.6% (1327 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 41.7% (1399 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 42.9% (1438 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 43.6% (1463 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 44.6% (1498 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 46.4% (1558 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 47.5% (1595 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 48.3% (1619 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 49.7% (1669 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 51.2% (1718 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 52.0% (1746 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 53.8% (1805 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 55.2% (1852 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 55.8% (1872 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 56.8% (1907 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 58.1% (1948 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 59.3% (1991 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 60.1% (2016 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 61.2% (2052 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 62.1% (2083 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 62.8% (2108 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 64.1% (2149 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 64.9% (2179 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 66.1% (2218 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 67.1% (2251 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 68.0% (2281 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 69.5% (2332 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 70.5% (2365 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 71.7% (2407 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 72.2% (2421 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 73.0% (2448 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 74.3% (2494 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 75.1% (2521 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 76.0% (2550 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 77.1% (2588 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 78.7% (2641 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 79.5% (2667 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 80.1% (2686 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 81.4% (2730 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 82.4% (2763 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 83.2% (2792 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 84.1% (2822 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 85.2% (2859 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 86.8% (2912 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 87.6% (2938 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 88.2% (2959 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 89.3% (2997 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 90.3% (3029 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 91.6% (3073 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 92.3% (3098 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 93.3% (3130 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 94.4% (3167 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 95.3% (3196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 96.4% (3233 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 97.4% (3268 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=778 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 77.8; 3121 / 3355 (P = 93.03%) round 40]               
[00:00:00] Finding cutoff p=769 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=769 1.2% (39 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 2.2% (75 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 3.3% (111 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 4.1% (139 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 5.6% (187 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 7.6% (254 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 8.3% (280 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 10.2% (343 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 12.5% (418 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 13.4% (448 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 15.8% (531 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 18.1% (608 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 19.5% (654 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 21.0% (704 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 24.8% (833 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 26.9% (903 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 27.5% (923 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 28.6% (958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 30.1% (1011 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 31.4% (1053 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 32.2% (1080 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 33.0% (1107 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 33.9% (1138 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 34.9% (1171 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 35.9% (1206 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 36.6% (1227 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 37.5% (1259 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 38.7% (1298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 41.0% (1374 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 42.3% (1420 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 43.0% (1443 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 43.8% (1469 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 45.6% (1530 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 46.6% (1565 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 48.2% (1618 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 48.7% (1633 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 50.0% (1676 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 52.2% (1751 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 53.8% (1804 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 55.1% (1848 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 56.3% (1890 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 57.0% (1913 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 58.8% (1974 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 60.0% (2013 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 60.9% (2044 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 61.8% (2075 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 63.1% (2117 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 64.1% (2152 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 65.3% (2192 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 66.3% (2225 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 67.3% (2259 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 69.1% (2317 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 70.9% (2379 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 71.6% (2402 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 72.2% (2421 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 73.8% (2475 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 74.2% (2489 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 75.5% (2532 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 76.7% (2572 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 77.2% (2589 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 78.1% (2619 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 79.2% (2658 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 80.5% (2702 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 81.5% (2736 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 82.1% (2755 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 83.4% (2797 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 84.1% (2822 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 85.5% (2868 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 86.1% (2890 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 87.2% (2926 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 88.2% (2960 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 89.5% (3002 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 90.2% (3026 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 91.5% (3071 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 92.3% (3098 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 93.3% (3129 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 94.2% (3162 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 95.4% (3200 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 96.3% (3231 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 97.3% (3264 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 98.3% (3299 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=769 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 76.9; 3153 / 3355 (P = 93.98%) round 41]               
[00:00:00] Finding cutoff p=760 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=760 1.2% (41 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 2.3% (77 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 3.4% (114 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 4.1% (136 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 5.5% (184 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 7.6% (255 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 8.6% (289 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 10.6% (357 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 12.3% (411 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 13.6% (455 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 15.1% (505 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 16.9% (567 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 18.7% (626 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 20.0% (671 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 23.5% (787 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 26.0% (871 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 27.2% (913 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 28.0% (938 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 28.7% (962 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 29.5% (989 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 30.6% (1028 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 31.6% (1060 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 32.5% (1091 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 33.6% (1127 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 35.0% (1173 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 35.5% (1192 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 36.5% (1226 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 37.8% (1268 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 39.9% (1338 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 41.0% (1377 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 41.8% (1404 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 44.0% (1476 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 45.2% (1515 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 45.8% (1537 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 47.0% (1578 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 47.8% (1604 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 49.1% (1646 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 49.7% (1667 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 51.6% (1730 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 53.4% (1793 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 54.0% (1812 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 54.9% (1843 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 56.1% (1882 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 56.8% (1904 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 58.1% (1950 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 59.2% (1987 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 60.2% (2020 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 61.0% (2048 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 62.0% (2081 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 63.4% (2126 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 64.3% (2157 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 64.9% (2176 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 66.1% (2219 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 67.0% (2247 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 68.6% (2301 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 69.4% (2329 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 70.7% (2372 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 71.4% (2394 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 72.0% (2414 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 73.4% (2462 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 74.2% (2490 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 75.0% (2517 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 76.3% (2559 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 77.4% (2596 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 78.1% (2619 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 79.1% (2653 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 80.4% (2697 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 81.2% (2725 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 82.4% (2765 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 83.5% (2800 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 84.1% (2823 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 85.5% (2870 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 86.3% (2896 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 87.5% (2934 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 88.2% (2959 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 89.8% (3014 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 90.4% (3032 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 91.6% (3072 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 92.4% (3101 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 93.2% (3128 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 94.3% (3165 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 95.3% (3196 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 96.3% (3232 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=760 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 76.0; 3168 / 3355 (P = 94.43%) round 42]               
[00:00:00] Finding cutoff p=749 [9.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=749 1.1% (38 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 2.2% (73 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 3.1% (104 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 4.2% (140 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 5.7% (190 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 7.6% (256 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 8.2% (274 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 9.8% (330 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 11.8% (396 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 12.7% (425 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 14.3% (481 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 16.1% (540 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 17.6% (592 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 19.0% (638 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 22.6% (757 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 25.4% (853 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 26.4% (886 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 27.8% (932 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 29.0% (972 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 29.7% (996 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 30.7% (1029 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 31.7% (1062 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 32.8% (1101 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 33.6% (1126 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 34.6% (1160 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 35.7% (1198 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 37.8% (1269 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 39.0% (1309 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 39.8% (1335 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 40.8% (1370 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 42.3% (1420 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 43.3% (1453 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 44.0% (1476 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 45.7% (1532 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 46.8% (1570 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 48.0% (1609 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 49.9% (1674 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 51.7% (1734 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 52.8% (1770 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 54.2% (1819 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 55.4% (1859 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 56.4% (1891 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 57.6% (1932 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 58.7% (1970 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 59.5% (1996 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 60.1% (2016 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 61.5% (2062 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 62.2% (2088 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 62.9% (2111 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 64.1% (2152 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 65.2% (2189 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 67.0% (2249 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 67.9% (2279 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 69.3% (2324 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 70.4% (2362 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 71.1% (2385 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 72.2% (2423 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 73.1% (2453 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 74.1% (2485 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 75.2% (2522 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 76.0% (2550 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 77.1% (2588 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 78.3% (2626 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 79.4% (2664 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 80.5% (2701 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 81.1% (2721 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 82.1% (2754 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 83.1% (2788 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 84.7% (2841 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 85.5% (2867 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 86.6% (2906 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 87.2% (2924 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 88.2% (2958 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 89.6% (3005 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 90.3% (3028 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 91.5% (3071 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 92.3% (3098 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 93.3% (3131 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 94.3% (3163 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 95.3% (3198 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 96.4% (3234 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 97.3% (3265 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 98.3% (3298 of 3355), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=749 99.3% (3332 of 3355), ETA 0:00:00                              
[00:00:00] Finished Preparing TNF Graph Building [pTNF = 74.90] [8.9Gb / 503.5Gb]                                            
[00:00:00] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=3355 maxEdges=200
[00:00:00] Building TNF Graph 39.9% (1339 of 3355), ETA 0:00:00     [8.9Gb / 503.5Gb]                           
[00:00:00] Building TNF Graph 95.2% (3193 of 3355), ETA 0:00:00     [8.9Gb / 503.5Gb]                           
[00:00:00] Finished Building TNF Graph (144103 edges) [8.9Gb / 503.5Gb]                                          
[00:00:00] Cleaned up after Building TNF Graph (144103 edges) [8.9Gb / 503.5Gb]                                          
[00:00:00] Cleaned up TNF matrix of large contigs [8.9Gb / 503.5Gb]                                             
[00:00:00] Applying coverage correlations to TNF graph with 144103 edges
[00:00:00] Allocated memory for graph edges [8.9Gb / 503.5Gb]
[00:00:00] ... calculating abundance dist 1.0% (1444 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 2.0% (2896 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 3.0% (4327 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 4.0% (5777 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 5.0% (7214 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 6.0% (8659 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 7.0% (10095 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 8.0% (11542 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 9.0% (12992 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 10.0% (14434 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 11.0% (15872 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 12.0% (17316 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 13.0% (18762 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 14.0% (20188 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 15.0% (21642 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 16.0% (23072 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 17.0% (24520 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 18.0% (25959 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 19.0% (27398 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 20.0% (28843 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 21.0% (30294 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 22.0% (31733 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 23.0% (33172 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 24.0% (34614 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 25.0% (36054 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 26.0% (37499 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 27.0% (38943 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 28.0% (40390 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 29.0% (41831 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 30.0% (43273 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 31.0% (44716 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 32.0% (46150 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 33.0% (47601 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 34.0% (49030 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 35.0% (50477 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 36.0% (51925 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 37.0% (53360 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 38.0% (54804 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 39.0% (56241 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 40.0% (57695 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 41.0% (59135 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 42.0% (60571 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 43.0% (62014 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 44.0% (63463 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 45.0% (64900 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 46.0% (66340 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 47.0% (67789 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 48.0% (69226 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 49.0% (70669 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 50.0% (72111 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 51.0% (73558 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 52.0% (74992 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 53.0% (76436 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 54.0% (77879 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 55.0% (79316 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 56.0% (80758 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 57.0% (82205 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 58.0% (83645 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 59.0% (85091 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 60.0% (86530 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 61.1% (87977 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 62.0% (89414 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 63.0% (90853 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 64.0% (92291 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 65.1% (93741 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 66.1% (95186 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 67.0% (96620 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 68.1% (98068 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 69.1% (99510 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 70.1% (100946 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 71.1% (102394 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 72.1% (103836 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 73.1% (105277 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 74.1% (106722 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 75.1% (108165 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 76.1% (109596 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 77.1% (111046 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 78.1% (112487 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 79.1% (113928 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 80.1% (115360 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 81.1% (116808 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 82.1% (118244 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 83.1% (119702 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 84.1% (121128 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 85.1% (122582 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 86.1% (124019 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 87.1% (125465 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 88.1% (126909 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 89.1% (128350 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 90.1% (129781 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 91.1% (131224 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 92.1% (132668 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 93.1% (134109 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 94.1% (135555 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 95.1% (137004 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 96.1% (138445 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 97.1% (139882 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 98.1% (141330 of 144103), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 99.1% (142761 of 144103), ETA 0:00:00                              
[00:00:00] Calculating geometric means [8.9Gb / 503.5Gb]
[00:00:00] Traversing graph with 3355 nodes and 144103 edges [8.9Gb / 503.5Gb]
[00:00:00] Building SCR Graph and Binning (319 vertices and 463 edges) [P = 9.50%; 8.9Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 1.0% (1442 of 144103), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (638 vertices and 1552 edges) [P = 19.00%; 8.9Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 2.0% (2884 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 3.0% (4326 of 144103), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (957 vertices and 3093 edges) [P = 28.50%; 8.9Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 4.0% (5768 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 5.0% (7210 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 6.0% (8652 of 144103), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1275 vertices and 5358 edges) [P = 38.00%; 8.9Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 7.0% (10094 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 8.0% (11536 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 9.0% (12978 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 10.0% (14420 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 11.0% (15862 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 12.0% (17304 of 144103), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1594 vertices and 8101 edges) [P = 47.50%; 8.9Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 13.0% (18746 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 14.0% (20188 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 15.0% (21630 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 16.0% (23072 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 17.0% (24514 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 18.0% (25956 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 19.0% (27398 of 144103), ETA 0:00:00                               
[00:00:00] ... traversing graph 20.0% (28840 of 144103), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1912 vertices and 11963 edges) [P = 57.00%; 8.9Gb / 503.5Gb]                           
[00:00:00] Finished Traversing graph [8.9Gb / 503.5Gb]                                       
[00:00:00] Dissolved 0 small clusters leaving 0 leftover contigs to be re-merged into larger clusters
[00:00:00] Rescuing singleton large contigs                                   
[00:00:00] There are 1551 bins already
[00:00:00] Outputting bins
[00:00:00] Writing cluster stats to: 03bins/metabat2_2kb/1507993/1507993.bin.BinInfo.txt
[00:00:06] 100.00% (19732222 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
1551 bins (19732222 bases in total) formed.
[00:00:06] Finished
MetaBAT2 generated 1551 bins for 1507993
MetaBAT2 binning completed for 1507993
