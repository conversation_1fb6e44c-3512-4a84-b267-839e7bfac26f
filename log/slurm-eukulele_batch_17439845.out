Processing batch: batch1
  Copying 05genes/prodigal/1507990/1507990.bin.1/1507990.bin.1.proteins.faa to 06taxonomy/eukulele_batches/batch1/proteins/1507990.bin.1.proteins.faa
  Copying 05genes/prodigal/1507990/1507990.bin.2/1507990.bin.2.proteins.faa to 06taxonomy/eukulele_batches/batch1/proteins/1507990.bin.2.proteins.faa
  Copying 05genes/prodigal/1507990/1507990.bin.3/1507990.bin.3.proteins.faa to 06taxonomy/eukulele_batches/batch1/proteins/1507990.bin.3.proteins.faa
  Copying 05genes/prodigal/1507990/1507990.bin.4/1507990.bin.4.proteins.faa to 06taxonomy/eukulele_batches/batch1/proteins/1507990.bin.4.proteins.faa
  Copying 05genes/prodigal/1507990/1507990.bin.5/1507990.bin.5.proteins.faa to 06taxonomy/eukulele_batches/batch1/proteins/1507990.bin.5.proteins.faa
  Running EUKulele on batch batch1...
Running EUKulele with command line arguments, as no valid configuration file was provided.
The current EUKulele version is 2.0.9

Setting things up...
Specified reference directory, reference FASTA, and protein map/taxonomy table not found. Using database in location: ./marmmetsp.
Found database folder for ./marmmetsp in current directory; will not re-download.
Creating a diamond reference from database files...
Diamond database file already created; will not re-create database.
Aligning to reference database...
Aligning sample 1507990.bin.1.proteins...
Aligning sample 1507990.bin.2.proteins...
Aligning sample 1507990.bin.3.proteins...
Aligning sample 1507990.bin.4.proteins...
Aligning sample 1507990.bin.5.proteins...
Diamond process exited for sample 1507990.bin.5.proteins.
Diamond process exited for sample 1507990.bin.1.proteins.
Diamond process exited for sample 1507990.bin.2.proteins.
Diamond process exited for sample 1507990.bin.3.proteins.
Diamond process exited for sample 1507990.bin.4.proteins.
Performing taxonomic estimation steps...
Performing taxonomic visualization steps...
Performing taxonomic assignment steps...
Performing BUSCO steps...
Performing BUSCO steps...
Configuring BUSCO...
Running busco with 1 simultaneous jobs...
[] is what is in BUSCO directory
BUSCO initial run did not complete successfully.
Please check the BUSCO run log files in the log/ folder.
  Warning: EUKulele did not complete successfully for batch batch1
Completed batch: batch1

Processing batch: batch2
  Copying 05genes/prodigal/1507992/1507992.bin.1/1507992.bin.1.proteins.faa to 06taxonomy/eukulele_batches/batch2/proteins/1507992.bin.1.proteins.faa
  Copying 05genes/prodigal/1507992/1507992.bin.2/1507992.bin.2.proteins.faa to 06taxonomy/eukulele_batches/batch2/proteins/1507992.bin.2.proteins.faa
  Copying 05genes/prodigal/1507992/1507992.bin.5/1507992.bin.5.proteins.faa to 06taxonomy/eukulele_batches/batch2/proteins/1507992.bin.5.proteins.faa
  Running EUKulele on batch batch2...
Running EUKulele with command line arguments, as no valid configuration file was provided.
The current EUKulele version is 2.0.9

Setting things up...
Specified reference directory, reference FASTA, and protein map/taxonomy table not found. Using database in location: ./marmmetsp.
Found database folder for ./marmmetsp in current directory; will not re-download.
Creating a diamond reference from database files...
Diamond database file already created; will not re-create database.
Aligning to reference database...
Aligning sample 1507992.bin.1.proteins...
Aligning sample 1507992.bin.2.proteins...
Aligning sample 1507992.bin.5.proteins...
Diamond process exited for sample 1507992.bin.1.proteins.
Diamond process exited for sample 1507992.bin.2.proteins.
Diamond process exited for sample 1507992.bin.5.proteins.
Performing taxonomic estimation steps...
Performing taxonomic visualization steps...
Performing taxonomic assignment steps...
Performing BUSCO steps...
Performing BUSCO steps...
Configuring BUSCO...
BUSCO lineage database already found; not re-downloaded.
Running busco with 1 simultaneous jobs...
[] is what is in BUSCO directory
BUSCO initial run did not complete successfully.
Please check the BUSCO run log files in the log/ folder.
  Warning: EUKulele did not complete successfully for batch batch2
Completed batch: batch2

Processing batch: batch3
  Copying 05genes/prodigal/1507992/1507992.bin.3/1507992.bin.3.proteins.faa to 06taxonomy/eukulele_batches/batch3/proteins/1507992.bin.3.proteins.faa
  Copying 05genes/prodigal/1507992/1507992.bin.4/1507992.bin.4.proteins.faa to 06taxonomy/eukulele_batches/batch3/proteins/1507992.bin.4.proteins.faa
  Running EUKulele on batch batch3...
Running EUKulele with command line arguments, as no valid configuration file was provided.
The current EUKulele version is 2.0.9

Setting things up...
Specified reference directory, reference FASTA, and protein map/taxonomy table not found. Using database in location: ./marmmetsp.
Found database folder for ./marmmetsp in current directory; will not re-download.
Creating a diamond reference from database files...
Diamond database file already created; will not re-create database.
Aligning to reference database...
Aligning sample 1507992.bin.3.proteins...
Aligning sample 1507992.bin.4.proteins...
Diamond process exited for sample 1507992.bin.3.proteins.
Diamond process exited for sample 1507992.bin.4.proteins.
Performing taxonomic estimation steps...
Performing taxonomic visualization steps...
Performing taxonomic assignment steps...
Performing BUSCO steps...
Performing BUSCO steps...
Configuring BUSCO...
BUSCO lineage database already found; not re-downloaded.
Running busco with 1 simultaneous jobs...
[] is what is in BUSCO directory
BUSCO initial run did not complete successfully.
Please check the BUSCO run log files in the log/ folder.
  Warning: EUKulele did not complete successfully for batch batch3
Completed batch: batch3

Processing batch: batch4
  Copying 05genes/prodigal/1507993/1507993.bin.2/1507993.bin.2.proteins.faa to 06taxonomy/eukulele_batches/batch4/proteins/1507993.bin.2.proteins.faa
  Copying 05genes/prodigal/1507993/1507993.bin.3/1507993.bin.3.proteins.faa to 06taxonomy/eukulele_batches/batch4/proteins/1507993.bin.3.proteins.faa
  Copying 05genes/prodigal/1507993/1507993.bin.4/1507993.bin.4.proteins.faa to 06taxonomy/eukulele_batches/batch4/proteins/1507993.bin.4.proteins.faa
  Running EUKulele on batch batch4...
Running EUKulele with command line arguments, as no valid configuration file was provided.
The current EUKulele version is 2.0.9

Setting things up...
Specified reference directory, reference FASTA, and protein map/taxonomy table not found. Using database in location: ./marmmetsp.
Found database folder for ./marmmetsp in current directory; will not re-download.
Creating a diamond reference from database files...
Diamond database file already created; will not re-create database.
Aligning to reference database...
Aligning sample 1507993.bin.2.proteins...
Aligning sample 1507993.bin.3.proteins...
Aligning sample 1507993.bin.4.proteins...
Diamond process exited for sample 1507993.bin.2.proteins.
Diamond process exited for sample 1507993.bin.4.proteins.
Diamond process exited for sample 1507993.bin.3.proteins.
Performing taxonomic estimation steps...
Performing taxonomic visualization steps...
Performing taxonomic assignment steps...
Performing BUSCO steps...
Performing BUSCO steps...
Configuring BUSCO...
BUSCO lineage database already found; not re-downloaded.
Running busco with 1 simultaneous jobs...
[] is what is in BUSCO directory
BUSCO initial run did not complete successfully.
Please check the BUSCO run log files in the log/ folder.
  Warning: EUKulele did not complete successfully for batch batch4
Completed batch: batch4

Processing batch: batch5
  Copying 05genes/prodigal/1507993/1507993.bin.1/1507993.bin.1.proteins.faa to 06taxonomy/eukulele_batches/batch5/proteins/1507993.bin.1.proteins.faa
  Running EUKulele on batch batch5...
Running EUKulele with command line arguments, as no valid configuration file was provided.
The current EUKulele version is 2.0.9

Setting things up...
Specified reference directory, reference FASTA, and protein map/taxonomy table not found. Using database in location: ./marmmetsp.
Found database folder for ./marmmetsp in current directory; will not re-download.
Creating a diamond reference from database files...
Diamond database file already created; will not re-create database.
Aligning to reference database...
Aligning sample 1507993.bin.1.proteins...
Diamond process exited for sample 1507993.bin.1.proteins.
Performing taxonomic estimation steps...
Performing taxonomic visualization steps...
Performing taxonomic assignment steps...
Performing BUSCO steps...
Performing BUSCO steps...
Configuring BUSCO...
BUSCO lineage database already found; not re-downloaded.
Running busco with 1 simultaneous jobs...
[] is what is in BUSCO directory
BUSCO initial run did not complete successfully.
Please check the BUSCO run log files in the log/ folder.
  Warning: EUKulele did not complete successfully for batch batch5
Completed batch: batch5

Processing batch: batch6
  Copying 05genes/prodigal/1507994/1507994.bin.1/1507994.bin.1.proteins.faa to 06taxonomy/eukulele_batches/batch6/proteins/1507994.bin.1.proteins.faa
  Running EUKulele on batch batch6...
Running EUKulele with command line arguments, as no valid configuration file was provided.
The current EUKulele version is 2.0.9

Setting things up...
Specified reference directory, reference FASTA, and protein map/taxonomy table not found. Using database in location: ./marmmetsp.
Found database folder for ./marmmetsp in current directory; will not re-download.
Creating a diamond reference from database files...
Diamond database file already created; will not re-create database.
Aligning to reference database...
Aligning sample 1507994.bin.1.proteins...
Diamond process exited for sample 1507994.bin.1.proteins.
Performing taxonomic estimation steps...
Performing taxonomic visualization steps...
Performing taxonomic assignment steps...
Performing BUSCO steps...
Performing BUSCO steps...
Configuring BUSCO...
BUSCO lineage database already found; not re-downloaded.
Running busco with 1 simultaneous jobs...
[] is what is in BUSCO directory
BUSCO initial run did not complete successfully.
Please check the BUSCO run log files in the log/ folder.
  Warning: EUKulele did not complete successfully for batch batch6
Completed batch: batch6

Processing batch: batch7
  Copying 05genes/prodigal/1507995/1507995.bin.1/1507995.bin.1.proteins.faa to 06taxonomy/eukulele_batches/batch7/proteins/1507995.bin.1.proteins.faa
  Running EUKulele on batch batch7...
Running EUKulele with command line arguments, as no valid configuration file was provided.
The current EUKulele version is 2.0.9

Setting things up...
Specified reference directory, reference FASTA, and protein map/taxonomy table not found. Using database in location: ./marmmetsp.
Found database folder for ./marmmetsp in current directory; will not re-download.
Creating a diamond reference from database files...
Diamond database file already created; will not re-create database.
Aligning to reference database...
Aligning sample 1507995.bin.1.proteins...
Diamond process exited for sample 1507995.bin.1.proteins.
Performing taxonomic estimation steps...
Performing taxonomic visualization steps...
Performing taxonomic assignment steps...
Performing BUSCO steps...
Performing BUSCO steps...
Configuring BUSCO...
BUSCO lineage database already found; not re-downloaded.
Running busco with 1 simultaneous jobs...
[] is what is in BUSCO directory
BUSCO initial run did not complete successfully.
Please check the BUSCO run log files in the log/ folder.
  Warning: EUKulele did not complete successfully for batch batch7
Completed batch: batch7

Processing batch: batch8
  Copying 05genes/prodigal/1507996/1507996.bin.1/1507996.bin.1.proteins.faa to 06taxonomy/eukulele_batches/batch8/proteins/1507996.bin.1.proteins.faa
  Running EUKulele on batch batch8...
Running EUKulele with command line arguments, as no valid configuration file was provided.
The current EUKulele version is 2.0.9

Setting things up...
Specified reference directory, reference FASTA, and protein map/taxonomy table not found. Using database in location: ./marmmetsp.
Found database folder for ./marmmetsp in current directory; will not re-download.
Creating a diamond reference from database files...
Diamond database file already created; will not re-create database.
Aligning to reference database...
Aligning sample 1507996.bin.1.proteins...
Diamond process exited for sample 1507996.bin.1.proteins.
Performing taxonomic estimation steps...
Performing taxonomic visualization steps...
Performing taxonomic assignment steps...
Performing BUSCO steps...
Performing BUSCO steps...
Configuring BUSCO...
BUSCO lineage database already found; not re-downloaded.
Running busco with 1 simultaneous jobs...
[] is what is in BUSCO directory
BUSCO initial run did not complete successfully.
Please check the BUSCO run log files in the log/ folder.
  Warning: EUKulele did not complete successfully for batch batch8
Completed batch: batch8

Processing batch: batch9
  Copying 05genes/prodigal/1507999/1507999.bin.1/1507999.bin.1.proteins.faa to 06taxonomy/eukulele_batches/batch9/proteins/1507999.bin.1.proteins.faa
  Running EUKulele on batch batch9...
Running EUKulele with command line arguments, as no valid configuration file was provided.
The current EUKulele version is 2.0.9

Setting things up...
Specified reference directory, reference FASTA, and protein map/taxonomy table not found. Using database in location: ./marmmetsp.
Found database folder for ./marmmetsp in current directory; will not re-download.
Creating a diamond reference from database files...
Diamond database file already created; will not re-create database.
Aligning to reference database...
Aligning sample 1507999.bin.1.proteins...
Diamond process exited for sample 1507999.bin.1.proteins.
Performing taxonomic estimation steps...
Performing taxonomic visualization steps...
Performing taxonomic assignment steps...
Performing BUSCO steps...
Performing BUSCO steps...
Configuring BUSCO...
BUSCO lineage database already found; not re-downloaded.
Running busco with 1 simultaneous jobs...
[] is what is in BUSCO directory
BUSCO initial run did not complete successfully.
Please check the BUSCO run log files in the log/ folder.
  Warning: EUKulele did not complete successfully for batch batch9
Completed batch: batch9

All batches processed. Combined results available at:
  06taxonomy/eukulele_batches/eukulele_taxonomy_combined.tsv
  06taxonomy/eukulele_batches/eukulele_taxonomy_combined.md
