Decompressing scaffold file for 1507999...
Generating depth file for 1507999...
Running MetaBAT2 for 1507999 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 123. with random seed=1745943200
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [35.8Gb / 503.5Gb]
[00:00:00] Parsing assembly file [35.8Gb / 503.5Gb]
[00:00:00] ... processed 12 seqs, 12 long (>=2000), 0 short (>=1000) 1.0% (511114 of 50243910), ETA 0:00:02     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 28 seqs, 28 long (>=2000), 0 short (>=1000) 2.0% (1015016 of 50243910), ETA 0:00:02     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 46 seqs, 46 long (>=2000), 0 short (>=1000) 3.0% (1528452 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 66 seqs, 66 long (>=2000), 0 short (>=1000) 4.0% (2031825 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 88 seqs, 88 long (>=2000), 0 short (>=1000) 5.0% (2528833 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 111 seqs, 111 long (>=2000), 0 short (>=1000) 6.0% (3018283 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 137 seqs, 137 long (>=2000), 0 short (>=1000) 7.0% (3526853 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 164 seqs, 164 long (>=2000), 0 short (>=1000) 8.0% (4020931 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 194 seqs, 194 long (>=2000), 0 short (>=1000) 9.0% (4538285 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 224 seqs, 224 long (>=2000), 0 short (>=1000) 10.0% (5027731 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 257 seqs, 257 long (>=2000), 0 short (>=1000) 11.0% (5539442 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 291 seqs, 291 long (>=2000), 0 short (>=1000) 12.0% (6042574 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 326 seqs, 326 long (>=2000), 0 short (>=1000) 13.0% (6541568 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 362 seqs, 362 long (>=2000), 0 short (>=1000) 14.0% (7034160 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 401 seqs, 401 long (>=2000), 0 short (>=1000) 15.0% (7542116 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 441 seqs, 441 long (>=2000), 0 short (>=1000) 16.0% (8040830 of 50243910), ETA 0:00:01     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 483 seqs, 483 long (>=2000), 0 short (>=1000) 17.0% (8545122 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 526 seqs, 526 long (>=2000), 0 short (>=1000) 18.0% (9044112 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 571 seqs, 571 long (>=2000), 0 short (>=1000) 19.0% (9550032 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 617 seqs, 617 long (>=2000), 0 short (>=1000) 20.0% (10048919 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 666 seqs, 666 long (>=2000), 0 short (>=1000) 21.0% (10559764 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 716 seqs, 716 long (>=2000), 0 short (>=1000) 22.0% (11062206 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 767 seqs, 767 long (>=2000), 0 short (>=1000) 23.0% (11557248 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 822 seqs, 822 long (>=2000), 0 short (>=1000) 24.0% (12065788 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 878 seqs, 878 long (>=2000), 0 short (>=1000) 25.0% (12561747 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 937 seqs, 937 long (>=2000), 0 short (>=1000) 26.0% (13066271 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 998 seqs, 998 long (>=2000), 0 short (>=1000) 27.0% (13571913 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1060 seqs, 1060 long (>=2000), 0 short (>=1000) 28.0% (14068507 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1125 seqs, 1125 long (>=2000), 0 short (>=1000) 29.0% (14574221 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1192 seqs, 1192 long (>=2000), 0 short (>=1000) 30.0% (15078742 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1261 seqs, 1261 long (>=2000), 0 short (>=1000) 31.0% (15580781 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1332 seqs, 1332 long (>=2000), 0 short (>=1000) 32.0% (16080855 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1406 seqs, 1406 long (>=2000), 0 short (>=1000) 33.0% (16585155 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1482 seqs, 1482 long (>=2000), 0 short (>=1000) 34.0% (17084692 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1561 seqs, 1561 long (>=2000), 0 short (>=1000) 35.0% (17589091 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1642 seqs, 1642 long (>=2000), 0 short (>=1000) 36.0% (18088529 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1728 seqs, 1728 long (>=2000), 0 short (>=1000) 37.0% (18595078 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1816 seqs, 1816 long (>=2000), 0 short (>=1000) 38.0% (19092764 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 1909 seqs, 1909 long (>=2000), 0 short (>=1000) 39.0% (19599613 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 2005 seqs, 2005 long (>=2000), 0 short (>=1000) 40.0% (20102205 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 2104 seqs, 2104 long (>=2000), 0 short (>=1000) 41.0% (20602485 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 2207 seqs, 2207 long (>=2000), 0 short (>=1000) 42.0% (21104648 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 2314 seqs, 2314 long (>=2000), 0 short (>=1000) 43.0% (21606058 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 2426 seqs, 2426 long (>=2000), 0 short (>=1000) 44.0% (22110308 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 2542 seqs, 2542 long (>=2000), 0 short (>=1000) 45.0% (22613310 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 2662 seqs, 2662 long (>=2000), 0 short (>=1000) 46.0% (23112900 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 2788 seqs, 2788 long (>=2000), 0 short (>=1000) 47.0% (23616288 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 2919 seqs, 2919 long (>=2000), 0 short (>=1000) 48.0% (24118913 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 3056 seqs, 3056 long (>=2000), 0 short (>=1000) 49.0% (24620386 of 50243910), ETA 0:00:00     [35.8Gb / 503.5Gb]     
[00:00:00] ... processed 3199 seqs, 3199 long (>=2000), 0 short (>=1000) 50.0% (25122532 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 3349 seqs, 3349 long (>=2000), 0 short (>=1000) 51.0% (25627555 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 3505 seqs, 3505 long (>=2000), 0 short (>=1000) 52.0% (26129624 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 3668 seqs, 3668 long (>=2000), 0 short (>=1000) 53.0% (26629685 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 3841 seqs, 3841 long (>=2000), 0 short (>=1000) 54.0% (27133588 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 4023 seqs, 4023 long (>=2000), 0 short (>=1000) 55.0% (27636298 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 4215 seqs, 4215 long (>=2000), 0 short (>=1000) 56.0% (28137533 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 4419 seqs, 4419 long (>=2000), 0 short (>=1000) 57.0% (28640930 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 4636 seqs, 4636 long (>=2000), 0 short (>=1000) 58.0% (29142933 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 4868 seqs, 4868 long (>=2000), 0 short (>=1000) 59.0% (29645000 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 5117 seqs, 5022 long (>=2000), 95 short (>=1000) 60.0% (30147864 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 5385 seqs, 5022 long (>=2000), 363 short (>=1000) 61.0% (30650201 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 5674 seqs, 5022 long (>=2000), 652 short (>=1000) 62.0% (31151736 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 5987 seqs, 5022 long (>=2000), 965 short (>=1000) 63.0% (31654869 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 6324 seqs, 5022 long (>=2000), 1302 short (>=1000) 64.0% (32156858 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 6687 seqs, 5022 long (>=2000), 1665 short (>=1000) 65.0% (32658786 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 7078 seqs, 5022 long (>=2000), 2056 short (>=1000) 66.0% (33161515 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 7501 seqs, 5022 long (>=2000), 2479 short (>=1000) 67.0% (33664622 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 7957 seqs, 5022 long (>=2000), 2935 short (>=1000) 68.0% (34166972 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 8444 seqs, 5022 long (>=2000), 3422 short (>=1000) 69.0% (34668408 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 8963 seqs, 5022 long (>=2000), 3432 short (>=1000) 70.0% (35170931 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 9517 seqs, 5022 long (>=2000), 3432 short (>=1000) 71.0% (35673340 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 10108 seqs, 5022 long (>=2000), 3432 short (>=1000) 72.0% (36175800 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 10744 seqs, 5022 long (>=2000), 3432 short (>=1000) 73.0% (36678858 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 11427 seqs, 5022 long (>=2000), 3432 short (>=1000) 74.0% (37180732 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 12166 seqs, 5022 long (>=2000), 3432 short (>=1000) 75.0% (37683305 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 12964 seqs, 5022 long (>=2000), 3432 short (>=1000) 76.0% (38185964 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 13828 seqs, 5022 long (>=2000), 3432 short (>=1000) 77.0% (38688411 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 14763 seqs, 5022 long (>=2000), 3432 short (>=1000) 78.0% (39190552 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 15785 seqs, 5022 long (>=2000), 3432 short (>=1000) 79.0% (39692882 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 16902 seqs, 5022 long (>=2000), 3432 short (>=1000) 80.0% (40195565 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 18124 seqs, 5022 long (>=2000), 3432 short (>=1000) 81.0% (40697882 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 19458 seqs, 5022 long (>=2000), 3432 short (>=1000) 82.0% (41200137 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 20915 seqs, 5022 long (>=2000), 3432 short (>=1000) 83.0% (41702799 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 22512 seqs, 5022 long (>=2000), 3432 short (>=1000) 84.0% (42205126 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 24275 seqs, 5022 long (>=2000), 3432 short (>=1000) 85.0% (42707663 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 26208 seqs, 5022 long (>=2000), 3432 short (>=1000) 86.0% (43209840 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 28312 seqs, 5022 long (>=2000), 3432 short (>=1000) 87.0% (43712429 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 30610 seqs, 5022 long (>=2000), 3432 short (>=1000) 88.0% (44214902 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 33218 seqs, 5022 long (>=2000), 3432 short (>=1000) 89.0% (44717336 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 36176 seqs, 5022 long (>=2000), 3432 short (>=1000) 90.0% (45219613 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 39511 seqs, 5022 long (>=2000), 3432 short (>=1000) 91.0% (45722057 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 43657 seqs, 5022 long (>=2000), 3432 short (>=1000) 92.0% (46224517 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 48860 seqs, 5022 long (>=2000), 3432 short (>=1000) 93.0% (46726959 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] ... processed 56390 seqs, 5022 long (>=2000), 3432 short (>=1000) 94.0% (47229379 of 50243910), ETA 0:00:00     [35.9Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 5022, and small contigs >= 1000 bp are 3432                                                                  
[00:00:00] Allocating 5022 contigs by 1 samples abundances [35.9Gb / 503.5Gb]
[00:00:00] Allocating 5022 contigs by 1 samples variances [35.9Gb / 503.5Gb]
[00:00:00] Allocating 3432 small contigs by 1 samples abundances [35.9Gb / 503.5Gb]
[00:00:00] Reading 0.003364Gb abundance file [35.9Gb / 503.5Gb]
[00:00:00] ... processed 550 lines 550 contigs and 0 short contigs 1.0% (36136 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1111 lines 1111 contigs and 0 short contigs 2.0% (72264 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 1670 lines 1670 contigs and 0 short contigs 3.0% (108438 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 2229 lines 2229 contigs and 0 short contigs 4.0% (144540 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 2788 lines 2788 contigs and 0 short contigs 5.0% (180656 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 3347 lines 3347 contigs and 0 short contigs 6.0% (216759 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 3907 lines 3907 contigs and 0 short contigs 7.0% (252914 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 4467 lines 4467 contigs and 0 short contigs 8.0% (289016 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 5027 lines 5022 contigs and 5 short contigs 9.0% (325138 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 5588 lines 5022 contigs and 566 short contigs 10.0% (361284 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6149 lines 5022 contigs and 1127 short contigs 11.0% (397423 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 6710 lines 5022 contigs and 1688 short contigs 12.0% (433575 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 7270 lines 5022 contigs and 2248 short contigs 13.0% (469646 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 7832 lines 5022 contigs and 2810 short contigs 14.0% (505801 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8392 lines 5022 contigs and 3370 short contigs 15.0% (541899 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 16.0% (578055 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 17.0% (614193 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 18.0% (650268 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 19.0% (686427 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 20.0% (722549 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 21.0% (758653 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 22.0% (794795 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 23.0% (830920 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 24.0% (867062 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 25.0% (903155 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 26.0% (939329 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 27.0% (975426 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 28.0% (1011535 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 29.0% (1047684 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 30.0% (1083812 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 31.0% (1119965 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 32.0% (1156039 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 33.0% (1192184 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 34.0% (1228308 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 35.0% (1264442 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 36.0% (1300592 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 37.0% (1336689 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 38.0% (1372795 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 39.0% (1408938 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 40.0% (1445092 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 41.0% (1481168 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 42.0% (1517327 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 43.0% (1553453 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 44.0% (1589568 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 45.0% (1625690 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 46.0% (1661804 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 47.0% (1697969 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 48.0% (1734069 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 49.0% (1770197 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 50.0% (1806353 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 51.0% (1842432 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 52.0% (1878596 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 53.0% (1914717 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 54.0% (1950833 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 55.0% (1986930 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 56.0% (2023061 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 57.0% (2059220 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 58.0% (2095336 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 59.0% (2131444 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 60.0% (2167615 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 61.0% (2203704 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 62.0% (2239870 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 63.0% (2275949 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 64.0% (2312068 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 65.0% (2348222 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 66.0% (2384368 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 67.0% (2420461 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 68.0% (2456602 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 69.0% (2492750 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 70.0% (2528850 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 71.0% (2564956 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 72.0% (2601098 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 73.0% (2637206 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 74.0% (2673381 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 75.0% (2709487 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 76.0% (2745616 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 77.0% (2781743 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 78.0% (2817878 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 79.0% (2854005 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 80.0% (2890090 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 81.0% (2926232 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 82.0% (2962365 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 83.0% (2998495 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 84.0% (3034618 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 85.0% (3070763 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 86.0% (3106865 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 87.0% (3142970 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 88.0% (3179107 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 89.0% (3215223 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 90.0% (3251371 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 91.0% (3287522 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 92.0% (3323647 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 93.0% (3359762 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 94.0% (3395868 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 95.0% (3431998 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 96.0% (3468133 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 97.0% (3504279 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 98.0% (3540372 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] ... processed 8454 lines 5022 contigs and 3432 short contigs 99.0% (3576501 of 3612525), ETA 0:00:00     [35.9Gb / 503.5Gb]                 
[00:00:00] Finished reading 58452 contigs and 1 coverages from 03bins/metabat2_2kb/1507999/temp/1507999.depth.txt [35.9Gb / 503.5Gb]. Ignored 49998 too small contigs.                                     
[00:00:00] Number of target contigs: 5022 of large (>= 2000) and 3432 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 5022
[00:00:00] Allocated memory for TNF [35.9Gb / 503.5Gb]
[00:00:00] Calculating TNF 5.4% (269 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 6.2% (309 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 7.2% (361 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 8.2% (412 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 26.3% (1322 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 26.4% (1328 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 27.5% (1380 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 28.5% (1430 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 29.5% (1484 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 30.5% (1530 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 31.6% (1585 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 32.5% (1633 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 33.5% (1683 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 65.9% (3309 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 66.0% (3315 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 67.0% (3367 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 68.1% (3418 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 69.1% (3468 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 70.2% (3525 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 71.2% (3574 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 72.2% (3625 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 73.2% (3674 of 5022), ETA 0:00:00    
[00:00:00] Calculating TNF 99.8% (5013 of 5022), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [35.9Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.1% (55 of 5022), ETA 0:00:03                   
[00:00:00] ... processing TNF matrix 3.4% (173 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 4.1% (205 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 5.1% (257 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 6.6% (330 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 7.1% (357 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 9.2% (461 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 11.1% (558 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 11.2% (564 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 13.5% (678 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 14.9% (746 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 16.0% (806 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 16.3% (820 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 17.7% (890 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 18.6% (933 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 20.2% (1013 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 20.3% (1021 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 21.7% (1092 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 22.4% (1127 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 24.2% (1215 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 24.5% (1231 of 5022), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 26.2% (1317 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.5% (1333 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.5% (1382 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 28.5% (1432 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.5% (1480 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 30.5% (1532 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.6% (1586 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.5% (1634 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 33.5% (1684 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.6% (1740 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 35.7% (1793 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.6% (1838 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.6% (1889 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 38.7% (1942 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.6% (1989 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 40.7% (2046 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.7% (2094 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.9% (2153 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.7% (2194 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 44.7% (2247 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.2% (2319 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 47.6% (2388 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.0% (2411 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.9% (2458 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.8% (2503 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.8% (2551 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 51.9% (2605 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.8% (2653 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.8% (2704 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 54.9% (2757 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.9% (2806 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.9% (2856 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 58.0% (2912 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.0% (2965 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 60.0% (3015 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.0% (3065 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 62.0% (3113 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.0% (3166 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 64.1% (3221 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 65.1% (3271 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.0% (3316 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 67.0% (3367 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.1% (3421 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 69.2% (3474 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 70.2% (3526 of 5022), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 71.3% (3581 of 5022), ETA 0:00:00                   
[00:00:01] Finding cutoff p=999 [36.1Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=999 1.1% (54 of 5022), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=999 2.2% (108 of 5022), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=999 3.3% (165 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 4.1% (204 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 5.3% (264 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 6.2% (309 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 7.3% (369 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 8.2% (410 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 9.3% (468 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 10.5% (527 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 11.4% (571 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 12.2% (612 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 13.3% (670 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 14.5% (728 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 15.3% (767 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 16.4% (824 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 17.6% (883 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 18.4% (922 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 19.5% (978 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 20.5% (1029 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 21.9% (1102 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 22.6% (1134 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 23.4% (1176 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 24.4% (1227 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 25.4% (1277 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 27.1% (1360 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 27.6% (1386 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 28.6% (1437 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 29.6% (1486 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 31.5% (1580 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 31.7% (1593 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 32.5% (1634 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 33.6% (1687 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 34.6% (1736 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 35.5% (1785 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 36.6% (1840 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 37.7% (1891 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 38.6% (1940 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 39.6% (1990 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 40.6% (2041 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 41.7% (2093 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 42.7% (2144 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 44.7% (2244 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 45.7% (2297 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 46.7% (2347 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 47.7% (2397 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 48.8% (2449 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.9; 0 / 5022 (P = 0.00%) round 1]               
[00:00:01] Finding cutoff p=997 [35.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=997 1.3% (64 of 5022), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=997 3.6% (183 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 4.3% (215 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 5.3% (264 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 6.2% (310 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 7.4% (373 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 8.4% (421 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 9.3% (465 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 10.2% (511 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 11.5% (577 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 12.3% (620 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 13.2% (665 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 14.5% (726 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 15.4% (772 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 16.3% (818 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 17.5% (878 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 18.4% (925 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 19.5% (978 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 20.5% (1030 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 21.5% (1078 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 22.4% (1123 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 23.4% (1176 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 24.5% (1232 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 25.4% (1278 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 26.5% (1332 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 27.4% (1378 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 28.5% (1430 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 29.5% (1479 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 30.5% (1530 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 31.5% (1583 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 32.5% (1633 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 33.6% (1685 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 34.5% (1735 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 35.6% (1789 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 36.7% (1842 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 37.6% (1889 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 38.6% (1939 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 39.6% (1989 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 40.6% (2041 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 41.7% (2092 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 42.7% (2142 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.7; 0 / 5022 (P = 0.00%) round 2]               
[00:00:01] Finding cutoff p=996 [35.5Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=996 1.1% (54 of 5022), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=996 2.0% (102 of 5022), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=996 3.2% (160 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 4.1% (206 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 5.3% (266 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 6.1% (308 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 7.3% (365 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 8.2% (410 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 9.2% (464 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 10.2% (510 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 11.2% (564 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 12.4% (622 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 13.3% (667 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 14.4% (724 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 15.3% (767 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 16.3% (819 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 17.3% (870 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 18.3% (920 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 19.3% (969 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 20.5% (1027 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 21.4% (1077 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 22.4% (1125 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 23.4% (1174 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 24.4% (1224 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 25.5% (1283 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 26.4% (1326 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 27.4% (1377 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 28.5% (1430 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 29.5% (1479 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 30.5% (1534 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 31.5% (1583 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 32.6% (1638 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 33.6% (1685 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 34.5% (1735 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 35.7% (1791 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 36.6% (1839 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 37.6% (1887 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 38.6% (1940 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 39.7% (1993 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 40.6% (2040 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 41.7% (2093 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 42.7% (2143 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=996 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.6; 13 / 5022 (P = 0.26%) round 3]               
[00:00:01] Finding cutoff p=995 [35.5Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=995 50.2% (2521 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 50.8% (2550 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 51.8% (2602 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 52.8% (2654 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 53.8% (2703 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 54.9% (2757 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 55.9% (2805 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 56.9% (2857 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 58.0% (2911 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 58.9% (2959 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 59.9% (3010 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 61.0% (3061 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 61.9% (3111 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 63.0% (3164 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 64.0% (3215 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 65.1% (3267 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 66.0% (3317 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 67.1% (3368 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 68.1% (3419 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 69.1% (3469 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 70.1% (3521 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 71.2% (3574 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 72.2% (3625 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 73.2% (3675 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 74.3% (3729 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 75.3% (3780 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 76.2% (3825 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 77.3% (3880 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 78.2% (3929 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 79.3% (3982 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 80.2% (4029 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=995 81.3% (4081 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.5; 41 / 5022 (P = 0.82%) round 4]               
[00:00:01] Finding cutoff p=992 [35.5Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=992 87.6% (4399 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 88.4% (4437 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 89.4% (4488 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 90.4% (4539 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 91.4% (4590 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 92.4% (4641 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=992 93.4% (4692 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.2; 188 / 5022 (P = 3.74%) round 5]               
[00:00:01] Finding cutoff p=990 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=990 1.2% (61 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 2.1% (104 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 3.2% (160 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 4.2% (213 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 5.3% (267 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 6.1% (306 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 7.1% (359 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 8.3% (419 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 9.2% (461 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 10.2% (513 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 11.3% (567 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 12.4% (621 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 13.4% (674 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 14.5% (726 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 15.2% (765 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 16.3% (819 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 17.4% (874 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 18.5% (930 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 19.6% (986 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 20.4% (1024 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 21.4% (1074 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 22.3% (1122 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 23.4% (1174 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 24.5% (1232 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 25.4% (1277 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 26.5% (1331 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 27.5% (1379 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 28.6% (1434 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 29.5% (1479 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 30.6% (1536 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 31.5% (1583 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 32.5% (1633 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 33.6% (1687 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 34.6% (1738 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 35.5% (1785 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 36.6% (1836 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 37.7% (1894 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 38.6% (1938 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 39.6% (1990 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 40.6% (2040 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 41.7% (2093 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 42.7% (2142 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=990 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.0; 309 / 5022 (P = 6.15%) round 6]               
[00:00:01] Finding cutoff p=987 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=987 1.4% (71 of 5022), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=987 2.7% (134 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 3.5% (176 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 4.2% (211 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 5.5% (276 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 6.3% (317 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 7.1% (359 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 8.5% (426 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 9.4% (471 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 10.2% (514 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 11.7% (590 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 12.7% (640 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 13.8% (693 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 15.0% (755 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 17.2% (865 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 17.9% (900 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 18.5% (931 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 19.3% (971 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 20.3% (1020 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 21.5% (1079 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 22.5% (1131 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 23.5% (1179 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 24.5% (1229 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 25.5% (1282 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 26.5% (1332 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 27.6% (1384 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 28.6% (1436 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 29.6% (1486 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 30.6% (1538 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 31.6% (1589 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 32.7% (1640 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 33.5% (1683 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 34.5% (1734 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 35.6% (1786 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 36.6% (1839 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 37.7% (1892 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 38.6% (1938 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 39.7% (1994 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 40.7% (2044 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 41.7% (2093 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 42.7% (2146 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 43.7% (2196 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 44.7% (2245 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 45.7% (2295 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 46.7% (2346 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 47.7% (2398 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 48.7% (2448 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=987 49.8% (2499 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 98.7; 510 / 5022 (P = 10.16%) round 7]               
[00:00:01] Finding cutoff p=983 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=983 37.7% (1893 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 38.6% (1938 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 39.6% (1989 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 40.6% (2040 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 41.6% (2091 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 42.7% (2142 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=983 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 98.3; 789 / 5022 (P = 15.71%) round 8]               
[00:00:01] Finding cutoff p=980 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=980 1.1% (53 of 5022), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=980 2.3% (115 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 3.1% (154 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 4.2% (209 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 5.3% (265 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 6.5% (326 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 7.2% (362 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 8.3% (418 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 9.5% (477 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 10.3% (516 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 11.4% (574 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 12.2% (613 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 13.5% (677 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 14.3% (716 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 15.7% (789 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 16.8% (842 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 17.7% (891 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 18.3% (918 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 19.5% (977 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 20.5% (1027 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 21.3% (1072 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 22.6% (1137 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 23.4% (1176 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 24.5% (1230 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 25.4% (1276 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 26.6% (1334 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 27.6% (1388 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 28.4% (1428 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 29.6% (1485 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 30.5% (1532 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 31.6% (1586 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 32.6% (1637 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 33.6% (1685 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 34.6% (1737 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 35.6% (1787 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 36.7% (1841 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 37.6% (1889 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 38.7% (1942 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 39.6% (1989 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 40.6% (2040 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 41.7% (2092 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 42.7% (2142 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=980 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 98.0; 993 / 5022 (P = 19.77%) round 9]               
[00:00:01] Finding cutoff p=977 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=977 1.2% (59 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 2.1% (106 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 3.3% (166 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 4.2% (211 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 5.1% (258 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 6.4% (319 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 7.3% (366 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 8.3% (416 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 9.2% (461 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 10.4% (523 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 11.4% (572 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 12.4% (623 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 13.4% (671 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 14.3% (719 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 15.3% (766 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 16.3% (817 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 17.4% (873 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 18.4% (922 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 19.4% (976 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 20.5% (1028 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 21.5% (1082 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 22.6% (1137 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 23.7% (1188 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 24.5% (1228 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 25.5% (1279 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 26.4% (1328 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 27.5% (1380 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 28.5% (1430 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 29.5% (1481 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 30.5% (1532 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 31.6% (1585 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 32.6% (1639 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 33.7% (1691 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 34.5% (1734 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 35.7% (1792 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 36.6% (1840 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 37.7% (1891 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 38.6% (1939 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 39.6% (1989 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 40.6% (2041 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 41.7% (2092 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 42.7% (2142 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 97.7; 1160 / 5022 (P = 23.10%) round 10]               
[00:00:01] Finding cutoff p=974 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=974 71.9% (3611 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 72.2% (3626 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 73.1% (3672 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 74.2% (3724 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 75.1% (3774 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 76.2% (3828 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 77.2% (3876 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 78.3% (3930 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 79.2% (3978 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 80.3% (4031 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 81.2% (4080 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 82.3% (4135 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 83.3% (4184 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 84.3% (4235 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 85.3% (4285 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 86.3% (4336 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 87.4% (4387 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 88.4% (4440 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 89.4% (4490 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 90.5% (4543 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 91.4% (4591 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 92.4% (4641 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=974 93.4% (4692 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 97.4; 1330 / 5022 (P = 26.48%) round 11]               
[00:00:01] Finding cutoff p=970 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=970 68.9% (3461 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 69.1% (3468 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 70.1% (3521 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 71.1% (3571 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 72.1% (3621 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 73.1% (3672 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 74.2% (3724 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 75.2% (3775 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 76.2% (3825 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 77.2% (3876 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 78.2% (3927 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 79.2% (3978 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 80.2% (4029 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=970 81.2% (4080 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 97.0; 1571 / 5022 (P = 31.28%) round 12]               
[00:00:01] Finding cutoff p=965 [35.6Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=965 62.5% (3139 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 63.0% (3162 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 64.0% (3215 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 65.0% (3264 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 66.0% (3315 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 67.1% (3369 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 68.1% (3418 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 69.2% (3473 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 70.1% (3519 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 71.1% (3573 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 72.1% (3623 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 73.2% (3674 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 74.1% (3723 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 75.2% (3778 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 76.2% (3826 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 77.4% (3886 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 78.2% (3927 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 79.3% (3981 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 80.2% (4030 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 81.3% (4082 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 82.3% (4133 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 83.3% (4185 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 84.3% (4234 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 85.3% (4284 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 86.3% (4335 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 87.4% (4388 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=965 88.4% (4437 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 96.5; 1821 / 5022 (P = 36.26%) round 13]               
[00:00:01] Finding cutoff p=961 [35.5Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=961 72.3% (3631 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 73.2% (3674 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 74.2% (3724 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 75.2% (3776 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 76.2% (3826 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 77.2% (3876 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 78.2% (3929 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 79.3% (3981 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 80.3% (4033 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 81.3% (4084 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 82.3% (4134 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 83.3% (4182 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 84.3% (4234 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 85.3% (4285 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 86.3% (4335 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 87.4% (4389 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 88.4% (4438 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 89.4% (4489 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 90.4% (4541 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 91.4% (4591 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 92.4% (4641 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=961 93.4% (4693 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 96.1; 2020 / 5022 (P = 40.22%) round 14]               
[00:00:01] Finding cutoff p=958 [35.5Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=958 1.2% (60 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 2.2% (109 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 3.1% (154 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 4.3% (217 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 5.4% (273 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 6.2% (311 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 7.2% (362 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 8.3% (418 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 9.4% (472 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 10.3% (515 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 11.3% (565 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 12.2% (615 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 13.3% (666 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 14.3% (719 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 15.2% (765 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 16.3% (818 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 17.4% (874 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 18.3% (918 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 19.4% (972 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 20.4% (1025 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 21.5% (1082 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 22.4% (1123 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 23.4% (1174 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 24.5% (1231 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 25.4% (1277 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 26.6% (1336 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 27.6% (1385 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 28.6% (1436 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 29.5% (1483 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 30.6% (1539 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 31.5% (1581 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 32.6% (1635 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 33.5% (1684 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 34.6% (1737 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 35.6% (1786 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 36.6% (1836 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 37.6% (1889 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 38.6% (1938 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 39.6% (1989 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 40.6% (2041 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 41.6% (2091 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 42.7% (2143 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=958 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 95.8; 2175 / 5022 (P = 43.31%) round 15]               
[00:00:01] Finding cutoff p=953 [35.5Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=953 65.2% (3275 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 66.0% (3317 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 67.1% (3371 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 68.1% (3422 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 69.1% (3471 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 70.1% (3519 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 71.1% (3572 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 72.2% (3628 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 73.2% (3678 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 74.2% (3726 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 75.2% (3777 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 76.3% (3834 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 77.3% (3882 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 78.2% (3928 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 79.3% (3984 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 80.2% (4030 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 81.3% (4081 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 82.3% (4133 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 83.3% (4183 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 84.3% (4234 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 85.3% (4286 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 86.3% (4335 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=953 87.3% (4386 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 95.3; 2420 / 5022 (P = 48.19%) round 16]               
[00:00:01] Finding cutoff p=950 [35.5Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=950 1.2% (59 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 2.2% (112 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 3.3% (167 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 4.1% (208 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 5.2% (260 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 6.4% (322 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 7.3% (366 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 8.5% (428 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 9.3% (467 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 10.4% (524 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 11.4% (572 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 12.2% (612 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 13.4% (672 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 14.4% (723 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 15.3% (767 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 16.5% (831 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 17.3% (871 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 18.5% (928 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 19.3% (970 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 20.3% (1020 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 21.5% (1078 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 22.6% (1136 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 23.5% (1179 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 24.4% (1227 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 25.4% (1278 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 26.5% (1329 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 27.5% (1380 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 28.5% (1432 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 29.6% (1485 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 30.5% (1532 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 31.6% (1586 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 32.6% (1637 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 33.7% (1691 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 34.6% (1737 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 35.5% (1785 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 36.6% (1838 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 37.7% (1891 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 38.6% (1939 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 39.7% (1992 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 40.7% (2044 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 41.6% (2091 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 42.8% (2147 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 43.8% (2202 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 44.7% (2245 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 45.8% (2299 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 46.8% (2349 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 47.7% (2397 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 48.8% (2452 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 49.8% (2502 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 50.9% (2554 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 51.9% (2604 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 52.8% (2652 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 53.8% (2704 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 54.9% (2756 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=950 55.9% (2805 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 95.0; 2539 / 5022 (P = 50.56%) round 17]               
[00:00:01] Finding cutoff p=947 [35.5Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=947 1.2% (60 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 2.1% (105 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 3.2% (160 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 4.2% (210 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 5.3% (265 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 6.1% (306 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 7.3% (365 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 8.2% (414 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 9.1% (459 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 10.2% (514 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 11.3% (568 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 12.3% (616 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 13.3% (669 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 14.3% (719 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 15.4% (773 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 16.4% (823 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 17.4% (876 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 18.3% (919 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 19.7% (988 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 20.3% (1020 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 21.4% (1075 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 22.5% (1131 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 23.6% (1183 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 24.6% (1233 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 25.7% (1290 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 26.6% (1338 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 27.5% (1381 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 28.5% (1430 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 29.6% (1489 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 30.8% (1545 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 31.7% (1590 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 32.7% (1644 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 33.7% (1692 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 34.6% (1738 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 35.8% (1796 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 36.7% (1842 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 37.6% (1890 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 38.6% (1938 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 39.7% (1995 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 40.8% (2050 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 41.7% (2093 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 42.8% (2147 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 43.7% (2196 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 44.7% (2244 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 45.7% (2295 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 46.8% (2351 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 47.8% (2399 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 48.8% (2453 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 49.8% (2501 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 50.8% (2552 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 51.8% (2601 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 52.8% (2653 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 53.9% (2705 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 54.9% (2755 of 5022), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=947 55.9% (2805 of 5022), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 94.7; 2679 / 5022 (P = 53.35%) round 18]               
[00:00:01] Finding cutoff p=942 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=942 93.8% (4709 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=942 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=942 95.5% (4794 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=942 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=942 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=942 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=942 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 94.2; 2842 / 5022 (P = 56.59%) round 19]               
[00:00:02] Finding cutoff p=938 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=938 1.1% (56 of 5022), ETA 0:00:01                              
[00:00:02] ... finding cutoff p=938 3.3% (166 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 4.3% (215 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 5.2% (262 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 6.2% (311 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 7.1% (357 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 8.2% (411 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 9.5% (479 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 10.5% (527 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 11.7% (590 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 12.3% (618 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 13.3% (668 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 14.4% (724 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 15.3% (767 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 16.2% (816 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 17.7% (887 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 18.3% (918 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 19.6% (986 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 20.4% (1024 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 21.5% (1080 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 22.4% (1127 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 23.4% (1173 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 24.5% (1231 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 25.5% (1280 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 26.5% (1331 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 27.6% (1387 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 28.8% (1444 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 29.5% (1481 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 30.6% (1535 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 31.6% (1588 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 32.5% (1634 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 33.5% (1684 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 34.5% (1734 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 35.6% (1788 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 36.6% (1839 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 37.6% (1890 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 38.6% (1941 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 39.6% (1991 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 40.6% (2040 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 41.6% (2091 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 42.7% (2142 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=938 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 93.8; 2968 / 5022 (P = 59.10%) round 20]               
[00:00:02] Finding cutoff p=933 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=933 1.1% (54 of 5022), ETA 0:00:01                              
[00:00:02] ... finding cutoff p=933 2.1% (106 of 5022), ETA 0:00:01                              
[00:00:02] ... finding cutoff p=933 3.3% (168 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 4.2% (210 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 5.4% (271 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 6.3% (314 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 7.3% (365 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 8.2% (414 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 9.3% (466 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 10.7% (537 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 11.2% (562 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 12.3% (620 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 13.5% (679 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 14.5% (726 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 15.4% (772 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 16.5% (829 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 17.4% (872 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 18.4% (922 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 19.3% (970 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 20.3% (1020 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 21.4% (1077 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 22.5% (1132 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 23.4% (1177 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 24.5% (1229 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 25.6% (1288 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 26.5% (1333 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 27.4% (1377 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 28.5% (1432 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 29.5% (1479 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 30.5% (1533 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 31.5% (1581 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 32.6% (1635 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 33.5% (1684 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 34.5% (1735 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 35.6% (1790 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 36.6% (1840 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 37.6% (1889 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 38.6% (1939 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 39.7% (1992 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 40.6% (2040 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 41.7% (2093 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 42.7% (2142 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=933 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 93.3; 3142 / 5022 (P = 62.56%) round 21]               
[00:00:02] Finding cutoff p=930 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=930 1.3% (66 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 2.1% (107 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 3.2% (162 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 4.2% (213 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 5.1% (255 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 6.3% (315 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 7.4% (373 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 8.2% (412 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 9.3% (466 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 10.3% (518 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 11.4% (570 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 12.4% (623 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 13.2% (664 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 14.3% (719 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 15.5% (778 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 16.6% (833 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 17.4% (872 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 18.5% (927 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 19.3% (971 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 20.4% (1026 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 21.3% (1071 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 22.4% (1123 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 23.5% (1180 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 24.4% (1225 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 25.5% (1282 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 26.4% (1327 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 27.5% (1382 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 28.4% (1428 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 29.5% (1480 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 30.5% (1534 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 31.5% (1584 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 32.5% (1632 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 33.5% (1684 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 34.6% (1739 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 35.6% (1788 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 36.6% (1839 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 37.6% (1888 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 38.6% (1940 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 39.7% (1993 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 40.6% (2041 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 41.7% (2093 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 42.7% (2142 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=930 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 93.0; 3220 / 5022 (P = 64.12%) round 22]               
[00:00:02] Finding cutoff p=926 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=926 1.1% (56 of 5022), ETA 0:00:01                              
[00:00:02] ... finding cutoff p=926 2.1% (105 of 5022), ETA 0:00:01                              
[00:00:02] ... finding cutoff p=926 3.3% (165 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 4.3% (218 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 5.3% (268 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 6.2% (309 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 7.3% (366 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 8.3% (417 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 9.3% (469 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 10.8% (540 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 11.2% (564 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 12.4% (624 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 13.7% (687 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 14.6% (732 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 15.5% (777 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 16.3% (820 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 17.5% (879 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 18.5% (929 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 19.4% (974 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 20.4% (1025 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 21.5% (1078 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 22.5% (1131 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 23.5% (1178 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 24.6% (1234 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 25.4% (1275 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 26.5% (1331 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 27.5% (1383 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 28.5% (1432 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 29.5% (1482 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 30.5% (1532 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 31.6% (1587 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 32.6% (1639 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 33.6% (1685 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 34.7% (1743 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 35.6% (1786 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 36.6% (1837 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 37.6% (1887 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 38.6% (1939 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 39.7% (1992 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 40.6% (2040 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 41.7% (2092 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 42.7% (2142 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=926 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 92.6; 3335 / 5022 (P = 66.41%) round 23]               
[00:00:02] Finding cutoff p=923 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=923 1.1% (54 of 5022), ETA 0:00:01                              
[00:00:02] ... finding cutoff p=923 2.2% (108 of 5022), ETA 0:00:01                              
[00:00:02] ... finding cutoff p=923 3.1% (156 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 4.1% (207 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 5.5% (276 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 6.4% (322 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 7.3% (367 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 8.4% (424 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 9.3% (469 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 10.2% (510 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 11.3% (569 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 12.2% (614 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 13.4% (673 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 14.3% (718 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 15.3% (770 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 16.3% (818 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 17.5% (881 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 18.5% (931 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 19.9% (997 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 20.5% (1031 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 21.3% (1071 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 22.6% (1137 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 23.4% (1173 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 24.9% (1248 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 25.4% (1276 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 26.6% (1336 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 27.5% (1382 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 28.5% (1429 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 29.5% (1481 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 30.5% (1531 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 31.5% (1584 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 32.7% (1640 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 33.6% (1685 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 34.6% (1738 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 35.6% (1788 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 36.6% (1839 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 37.6% (1890 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 38.6% (1939 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 39.6% (1989 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 40.6% (2041 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 41.7% (2092 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 42.7% (2143 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=923 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 92.3; 3398 / 5022 (P = 67.66%) round 24]               
[00:00:02] Finding cutoff p=918 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=918 69.2% (3474 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 70.1% (3521 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 71.1% (3573 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 72.1% (3622 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 73.1% (3673 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 74.2% (3727 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 75.2% (3775 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 76.2% (3827 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 77.2% (3879 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 78.2% (3929 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 79.3% (3981 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 80.3% (4031 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 81.3% (4081 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 82.3% (4134 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 83.3% (4183 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 84.3% (4233 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 85.3% (4284 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 86.3% (4335 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=918 87.3% (4386 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 91.8; 3520 / 5022 (P = 70.09%) round 25]               
[00:00:02] Finding cutoff p=914 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=914 1.1% (54 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 2.5% (126 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 3.4% (171 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 4.4% (222 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 5.6% (282 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 6.3% (314 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 7.4% (371 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 8.4% (423 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 9.2% (460 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 10.2% (513 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 11.3% (569 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 12.5% (626 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 14.1% (710 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 14.8% (745 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 15.6% (783 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 16.7% (840 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 17.8% (896 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 18.8% (946 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 19.5% (981 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 20.5% (1028 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 21.8% (1093 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 22.6% (1133 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 23.8% (1194 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 24.7% (1238 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 25.8% (1295 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 26.4% (1327 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 27.5% (1382 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 28.5% (1431 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 29.6% (1487 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 30.5% (1530 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 31.6% (1586 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 32.6% (1637 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 33.6% (1688 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 35.0% (1759 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 35.6% (1790 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 37.0% (1857 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 37.7% (1891 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 38.7% (1942 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 39.7% (1996 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 40.7% (2045 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 41.6% (2091 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 42.7% (2143 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 43.7% (2197 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 44.7% (2246 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 45.7% (2295 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 46.8% (2350 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 47.7% (2397 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 48.9% (2454 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 49.8% (2500 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 50.8% (2550 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 51.8% (2602 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 52.8% (2654 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 53.8% (2703 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 54.9% (2755 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 55.9% (2805 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 56.9% (2856 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=914 57.9% (2909 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 91.4; 3604 / 5022 (P = 71.76%) round 26]               
[00:00:02] Finding cutoff p=911 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=911 1.2% (60 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 2.2% (111 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 3.1% (158 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 4.2% (211 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 5.1% (256 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 6.2% (309 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 7.2% (364 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 8.5% (426 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 9.6% (480 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 11.0% (550 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 12.0% (604 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 12.6% (633 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 13.3% (666 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 14.3% (719 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 15.6% (783 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 16.3% (817 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 17.5% (878 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 19.0% (956 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 20.1% (1010 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 20.9% (1050 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 21.9% (1098 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 22.6% (1134 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 23.4% (1175 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 24.6% (1237 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 25.5% (1279 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 26.6% (1337 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 27.6% (1384 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 28.5% (1432 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 29.7% (1490 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 30.7% (1541 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 31.6% (1589 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 32.6% (1636 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 33.8% (1697 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 34.8% (1746 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 35.6% (1788 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 36.7% (1843 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 37.6% (1888 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 38.6% (1939 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 39.7% (1993 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 40.8% (2047 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 41.8% (2098 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 42.8% (2147 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 44.7% (2247 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 45.7% (2295 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 46.8% (2352 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 47.7% (2397 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 48.7% (2448 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 49.8% (2499 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 50.8% (2550 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 51.8% (2601 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 52.8% (2653 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 53.9% (2705 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 54.8% (2754 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=911 55.9% (2805 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 91.1; 3659 / 5022 (P = 72.86%) round 27]               
[00:00:02] Finding cutoff p=907 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=907 67.0% (3366 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 68.1% (3422 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 69.1% (3471 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 70.2% (3525 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 71.1% (3573 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 72.2% (3624 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 73.2% (3674 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 74.2% (3725 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 75.2% (3776 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 76.2% (3826 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 77.2% (3877 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 78.2% (3927 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 79.2% (3978 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 80.2% (4030 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 81.3% (4082 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 82.3% (4134 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 83.4% (4187 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 84.3% (4235 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 85.3% (4286 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 86.4% (4337 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 87.4% (4387 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 88.4% (4438 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 89.4% (4489 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 90.4% (4541 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 91.4% (4590 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=907 92.4% (4642 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 90.7; 3717 / 5022 (P = 74.01%) round 28]               
[00:00:02] Finding cutoff p=904 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=904 61.5% (3087 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 62.0% (3114 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 63.0% (3164 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 64.2% (3225 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 65.1% (3271 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 66.2% (3325 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 67.0% (3366 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 68.1% (3419 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 69.2% (3475 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 70.2% (3523 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 71.1% (3571 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 72.1% (3622 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 73.3% (3680 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 74.1% (3723 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 75.1% (3774 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 76.2% (3829 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 77.3% (3882 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 78.2% (3929 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 79.5% (3990 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 80.3% (4034 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 81.2% (4080 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 82.3% (4131 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 83.3% (4183 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 84.4% (4238 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 85.4% (4289 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 86.3% (4335 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 87.4% (4388 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 88.4% (4438 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 89.4% (4488 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 90.4% (4540 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 91.4% (4590 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=904 92.5% (4645 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 90.4; 3763 / 5022 (P = 74.93%) round 29]               
[00:00:02] Finding cutoff p=899 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=899 1.0% (51 of 5022), ETA 0:00:01                              
[00:00:02] ... finding cutoff p=899 2.7% (137 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 4.1% (208 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 5.6% (281 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 7.3% (369 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 8.9% (449 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 11.2% (560 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 12.2% (613 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 13.5% (680 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 14.3% (719 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 15.5% (778 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 16.3% (818 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 17.3% (867 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 18.5% (927 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 19.5% (979 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 20.4% (1023 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 21.4% (1075 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 22.6% (1133 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 23.7% (1190 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 24.5% (1228 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 25.6% (1285 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 26.5% (1333 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 27.4% (1377 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 28.5% (1433 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 29.5% (1480 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 30.5% (1534 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 31.5% (1583 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 32.5% (1632 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 33.5% (1684 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 34.5% (1734 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 35.6% (1787 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 36.6% (1838 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 37.6% (1890 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 38.6% (1941 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 39.7% (1995 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 40.6% (2041 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 41.8% (2098 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 42.8% (2151 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 43.7% (2194 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 44.7% (2245 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 45.8% (2300 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 46.8% (2348 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 47.7% (2397 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 48.8% (2451 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 49.8% (2501 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 50.8% (2550 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 52.1% (2617 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=899 52.8% (2653 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 89.9; 3832 / 5022 (P = 76.30%) round 30]               
[00:00:02] Finding cutoff p=890 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=890 46.2% (2319 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 46.7% (2346 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 47.7% (2398 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 48.7% (2448 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 49.8% (2499 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 50.8% (2552 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 51.9% (2604 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 52.9% (2656 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 54.0% (2711 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 54.9% (2756 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 55.9% (2808 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 56.9% (2858 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 57.9% (2910 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 59.0% (2964 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 60.1% (3017 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 61.2% (3072 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 62.0% (3112 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 63.0% (3163 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 64.0% (3216 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 65.1% (3268 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 66.0% (3317 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 67.1% (3369 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 68.2% (3425 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 69.1% (3472 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 70.1% (3522 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 71.1% (3573 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 72.1% (3623 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 73.1% (3672 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 74.2% (3728 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 75.1% (3774 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 76.2% (3826 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 77.2% (3876 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=890 78.3% (3930 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 89.0; 3885 / 5022 (P = 77.36%) round 31]               
[00:00:02] Finding cutoff p=881 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=881 87.5% (4395 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=881 88.4% (4437 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=881 89.4% (4488 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=881 90.4% (4539 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=881 91.4% (4590 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=881 92.4% (4641 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=881 93.4% (4693 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=881 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=881 95.5% (4796 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=881 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 88.1; 3989 / 5022 (P = 79.43%) round 32]               
[00:00:02] Finding cutoff p=871 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=871 43.8% (2198 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 44.7% (2245 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 45.7% (2295 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 46.7% (2346 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 47.7% (2398 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 48.9% (2454 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 49.8% (2501 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 50.9% (2556 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 52.1% (2618 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 53.0% (2662 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 53.9% (2709 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 54.9% (2757 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 55.9% (2807 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 56.9% (2859 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 58.0% (2911 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 58.9% (2960 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 60.0% (3013 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 61.0% (3062 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 62.0% (3113 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 63.1% (3167 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 64.0% (3213 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 65.0% (3264 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 66.0% (3316 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 67.2% (3375 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 68.2% (3423 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 69.2% (3477 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 70.2% (3524 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 71.2% (3575 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 72.1% (3622 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 73.2% (3675 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 74.2% (3727 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 75.2% (3778 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 76.2% (3828 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 77.2% (3879 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 78.3% (3930 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 79.2% (3978 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 80.3% (4032 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 81.3% (4081 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 82.3% (4133 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 83.3% (4184 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 84.3% (4233 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=871 85.3% (4285 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 87.1; 4108 / 5022 (P = 81.80%) round 33]               
[00:00:02] Finding cutoff p=862 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=862 3.7% (186 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 4.9% (248 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 5.7% (285 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 6.5% (324 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 7.4% (373 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 8.5% (425 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 9.8% (491 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 11.0% (553 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 11.4% (575 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 12.3% (616 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 13.3% (667 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 14.7% (738 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 15.5% (777 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 16.8% (844 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 17.3% (867 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 18.5% (927 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 19.3% (970 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 20.3% (1021 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 21.4% (1077 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 22.5% (1129 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 23.6% (1183 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 24.5% (1231 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 25.5% (1279 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 26.4% (1327 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 27.7% (1393 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 28.6% (1437 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 29.5% (1483 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 30.6% (1537 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 31.5% (1583 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 32.6% (1635 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 33.5% (1683 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 34.5% (1735 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 35.6% (1788 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 36.6% (1837 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 37.6% (1887 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 38.6% (1939 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 39.6% (1991 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 40.7% (2042 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 41.6% (2091 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 42.7% (2143 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=862 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 86.2; 4197 / 5022 (P = 83.57%) round 34]               
[00:00:02] Finding cutoff p=851 [35.5Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=851 1.0% (51 of 5022), ETA 0:00:01                              
[00:00:02] ... finding cutoff p=851 2.3% (117 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 3.5% (176 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 4.1% (207 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 5.1% (257 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 6.4% (322 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 7.8% (393 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 8.9% (449 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 9.2% (460 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 10.4% (522 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 11.2% (564 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 12.2% (613 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 13.2% (665 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 14.4% (725 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 15.4% (771 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 16.3% (817 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 17.3% (869 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 18.5% (928 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 19.4% (973 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 20.3% (1021 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 21.5% (1081 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 22.4% (1127 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 23.4% (1173 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 24.6% (1233 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 25.5% (1279 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 26.5% (1332 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 27.5% (1380 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 28.5% (1430 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 29.5% (1482 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 30.6% (1535 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 31.6% (1585 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 32.5% (1632 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 33.6% (1686 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 34.6% (1737 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 35.6% (1788 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 36.7% (1841 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 37.6% (1887 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 38.6% (1941 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 39.6% (1991 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 40.6% (2040 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 41.6% (2091 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 42.7% (2143 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=851 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 85.1; 4295 / 5022 (P = 85.52%) round 35]               
[00:00:02] Finding cutoff p=841 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=841 1.0% (51 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 2.1% (103 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 3.1% (155 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 4.2% (212 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 5.3% (265 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 6.3% (315 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 7.5% (378 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 9.0% (450 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 10.0% (503 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 10.6% (532 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 11.3% (567 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 13.0% (653 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 13.6% (681 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 14.2% (714 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 15.4% (771 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 16.5% (829 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 17.7% (889 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 18.3% (919 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 19.3% (970 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 20.4% (1026 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 21.4% (1076 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 22.9% (1149 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 23.6% (1183 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 24.5% (1228 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 25.7% (1289 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 26.6% (1336 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 27.5% (1380 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 28.5% (1432 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 29.6% (1485 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 30.5% (1533 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 31.6% (1587 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 32.7% (1642 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 33.6% (1689 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 34.6% (1740 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 35.6% (1790 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 36.6% (1837 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 37.6% (1889 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 38.6% (1938 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 39.7% (1994 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 40.8% (2048 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 41.7% (2092 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 42.7% (2144 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 43.7% (2197 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 44.7% (2245 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 45.7% (2295 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 46.8% (2351 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 47.7% (2398 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 48.8% (2452 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 49.8% (2502 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 50.8% (2551 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 51.8% (2602 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 52.8% (2652 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=841 53.8% (2703 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 84.1; 4388 / 5022 (P = 87.38%) round 36]               
[00:00:02] Finding cutoff p=832 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=832 1.0% (51 of 5022), ETA 0:00:01                              
[00:00:02] ... finding cutoff p=832 2.1% (105 of 5022), ETA 0:00:01                              
[00:00:02] ... finding cutoff p=832 3.0% (153 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 4.1% (204 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 5.3% (265 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 6.3% (314 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 7.3% (365 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 8.8% (442 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 9.2% (464 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 10.3% (516 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 11.8% (593 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 12.2% (615 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 13.2% (665 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 14.4% (722 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 15.2% (765 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 16.4% (823 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 17.5% (878 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 18.5% (927 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 19.3% (970 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 20.5% (1030 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 21.4% (1077 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 22.4% (1127 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 23.5% (1180 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 24.5% (1228 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 25.4% (1275 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 26.5% (1329 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 27.5% (1382 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 28.7% (1439 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 29.5% (1481 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 30.7% (1543 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 31.7% (1590 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 32.7% (1644 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 34.0% (1708 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 34.9% (1754 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 36.4% (1828 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 36.6% (1836 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 37.6% (1888 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 38.9% (1953 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 39.7% (1992 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 40.7% (2043 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 41.7% (2092 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 42.7% (2143 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 43.7% (2194 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 44.7% (2244 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 45.7% (2296 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 46.8% (2349 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 47.8% (2402 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 48.8% (2449 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=832 49.8% (2501 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 83.2; 4447 / 5022 (P = 88.55%) round 37]               
[00:00:02] Finding cutoff p=821 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=821 1.2% (61 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 2.4% (120 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 3.4% (172 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 4.2% (212 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 5.2% (262 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 6.4% (323 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 7.4% (371 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 8.7% (439 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 9.5% (478 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 11.6% (581 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 13.8% (694 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 14.5% (727 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 16.2% (813 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 16.7% (839 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 17.6% (883 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 18.8% (945 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 19.9% (1000 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 20.4% (1025 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 21.5% (1080 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 22.4% (1126 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 23.6% (1186 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 24.6% (1236 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 25.5% (1279 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 26.4% (1328 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 27.4% (1378 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 28.5% (1433 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 29.5% (1480 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 30.5% (1534 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 31.5% (1582 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 32.6% (1638 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 33.6% (1687 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 34.6% (1736 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 35.6% (1788 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 36.7% (1841 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 37.6% (1890 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 38.6% (1938 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 39.6% (1989 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 40.6% (2041 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 41.6% (2091 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 42.7% (2144 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=821 43.7% (2195 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 82.1; 4527 / 5022 (P = 90.14%) round 38]               
[00:00:02] Finding cutoff p=810 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=810 43.8% (2201 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=810 44.7% (2244 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=810 45.7% (2295 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=810 46.7% (2346 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=810 47.7% (2397 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=810 48.7% (2448 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=810 49.8% (2499 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=810 50.8% (2551 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=810 51.8% (2601 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=810 52.8% (2653 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=810 53.8% (2704 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 81.0; 4594 / 5022 (P = 91.48%) round 39]               
[00:00:02] Finding cutoff p=801 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=801 1.3% (65 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 2.0% (102 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 3.3% (168 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 4.3% (217 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 5.2% (263 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 6.8% (340 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 7.4% (370 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 8.5% (426 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 9.4% (470 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 10.3% (517 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 11.3% (565 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 12.2% (612 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 13.5% (677 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 14.6% (735 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 15.5% (777 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 16.6% (833 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 17.4% (872 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 18.3% (921 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 19.7% (987 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 20.4% (1023 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 21.3% (1071 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 22.4% (1124 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 23.4% (1174 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 24.4% (1227 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 25.4% (1275 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 26.5% (1329 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 27.5% (1379 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 28.7% (1439 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 29.5% (1484 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 30.5% (1533 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 31.6% (1589 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 32.5% (1633 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 33.6% (1689 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 34.7% (1743 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 35.5% (1785 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 36.6% (1840 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 37.6% (1887 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 38.6% (1938 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 39.6% (1991 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 40.7% (2042 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 41.7% (2092 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 42.7% (2142 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=801 43.7% (2193 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 80.1; 4639 / 5022 (P = 92.37%) round 40]               
[00:00:02] Finding cutoff p=792 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=792 1.0% (51 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 2.1% (106 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 3.1% (155 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 4.3% (218 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 5.4% (272 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 6.3% (315 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 7.6% (383 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 8.6% (432 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 9.3% (467 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 10.6% (533 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 11.3% (567 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 12.8% (644 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 13.8% (694 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 14.2% (715 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 15.3% (770 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 16.4% (825 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 17.4% (874 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 18.9% (949 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 19.4% (973 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 20.6% (1033 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 21.7% (1088 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 22.4% (1124 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 23.4% (1176 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 24.4% (1227 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 25.6% (1288 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 26.5% (1332 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 27.8% (1394 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 28.6% (1434 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 29.5% (1481 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 30.5% (1530 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 31.5% (1584 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 32.7% (1641 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 33.5% (1683 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 34.8% (1748 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 35.7% (1794 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 36.6% (1838 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 37.7% (1893 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 38.6% (1941 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 39.7% (1992 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 40.9% (2052 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 41.9% (2102 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 42.7% (2146 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 43.8% (2202 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 44.7% (2245 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 45.8% (2301 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 46.7% (2347 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 47.8% (2402 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 48.7% (2448 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 49.8% (2500 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 50.9% (2554 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 51.8% (2602 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 52.8% (2652 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 53.8% (2703 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 54.8% (2754 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=792 55.9% (2805 of 5022), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 79.2; 4680 / 5022 (P = 93.19%) round 41]               
[00:00:02] Finding cutoff p=782 [35.6Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=782 1.2% (59 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 2.2% (112 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 3.1% (155 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 4.1% (205 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 5.2% (259 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 6.1% (308 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 7.1% (357 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 8.3% (416 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 9.2% (460 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 10.2% (510 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 11.6% (582 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 12.3% (619 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 13.5% (676 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 14.4% (723 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 15.3% (767 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 16.4% (822 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 17.3% (867 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 18.4% (924 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 19.3% (971 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 20.4% (1022 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 21.7% (1090 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 22.6% (1134 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 23.4% (1174 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 24.4% (1226 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 25.5% (1280 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 26.5% (1330 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 27.6% (1385 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 28.5% (1431 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 29.5% (1482 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 30.5% (1531 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 31.6% (1585 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 32.5% (1633 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 33.6% (1686 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 34.5% (1734 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 35.6% (1790 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 36.6% (1837 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 37.9% (1902 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 38.9% (1952 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 39.6% (1990 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 40.8% (2050 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 41.6% (2091 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 42.7% (2145 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 44.0% (2212 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 45.1% (2267 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 45.7% (2297 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 46.8% (2348 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 47.8% (2399 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 48.7% (2448 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 49.8% (2502 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 50.8% (2552 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 51.9% (2604 of 5022), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=782 52.9% (2656 of 5022), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 78.2; 4717 / 5022 (P = 93.93%) round 42]               
[00:00:03] Finding cutoff p=772 [35.6Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=772 93.8% (4709 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=772 94.4% (4743 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=772 95.5% (4794 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=772 96.5% (4845 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=772 97.5% (4896 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=772 98.5% (4947 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=772 99.5% (4998 of 5022), ETA 0:00:00                              
[00:00:03] Preparing TNF Graph Building [pTNF = 77.2; 4766 / 5022 (P = 94.90%) round 43]               
[00:00:03] Finding cutoff p=763 [35.6Gb / 503.5Gb]                                                     
[00:00:03] ... finding cutoff p=763 69.6% (3497 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 70.2% (3524 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 71.1% (3570 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 72.1% (3621 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 73.1% (3672 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 74.1% (3723 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 75.3% (3780 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 76.2% (3826 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 77.2% (3878 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 78.2% (3928 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 79.3% (3980 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 80.4% (4039 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 81.3% (4084 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 82.3% (4132 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 83.3% (4183 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 84.3% (4235 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 85.3% (4285 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 86.4% (4337 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 87.4% (4389 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 88.4% (4438 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 89.4% (4488 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 90.4% (4540 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 91.4% (4590 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 92.4% (4641 of 5022), ETA 0:00:00                              
[00:00:03] ... finding cutoff p=763 93.4% (4692 of 5022), ETA 0:00:00                              
[00:00:03] Finished Preparing TNF Graph Building [pTNF = 77.20] [35.4Gb / 503.5Gb]                                            
[00:00:03] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=5022 maxEdges=200
[00:00:03] Building TNF Graph 6.2% (309 of 5022), ETA 0:00:02     [35.4Gb / 503.5Gb]                           
[00:00:03] Building TNF Graph 41.0% (2060 of 5022), ETA 0:00:00     [35.6Gb / 503.5Gb]                           
[00:00:03] Building TNF Graph 88.2% (4429 of 5022), ETA 0:00:00     [35.6Gb / 503.5Gb]                           
[00:00:03] Finished Building TNF Graph (202562 edges) [35.6Gb / 503.5Gb]                                          
[00:00:03] Cleaned up after Building TNF Graph (202562 edges) [35.6Gb / 503.5Gb]                                          
[00:00:03] Cleaned up TNF matrix of large contigs [35.6Gb / 503.5Gb]                                             
[00:00:03] Applying coverage correlations to TNF graph with 202562 edges
[00:00:03] Allocated memory for graph edges [35.6Gb / 503.5Gb]
[00:00:03] ... calculating abundance dist 1.0% (2026 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 2.0% (4055 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 3.0% (6081 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 4.0% (8111 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 5.0% (10133 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 6.0% (12161 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 7.0% (14188 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 8.0% (16210 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 9.0% (18238 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 10.0% (20262 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 11.0% (22292 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 12.0% (24313 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 13.0% (26339 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 14.0% (28365 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 15.0% (30396 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 16.0% (32421 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 17.0% (34444 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 18.0% (36473 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 19.0% (38502 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 20.0% (40528 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 21.0% (42547 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 22.0% (44575 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 23.0% (46598 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 24.0% (48630 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 25.0% (50657 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 26.0% (52683 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 27.0% (54704 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 28.0% (56735 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 29.0% (58757 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 30.0% (60784 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 31.0% (62811 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 32.0% (64838 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 33.0% (66863 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 34.0% (68891 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 60.2% (121921 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 61.0% (123586 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 62.0% (125615 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 63.0% (127643 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 64.0% (129673 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 65.0% (131691 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 66.0% (133722 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 67.0% (135745 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 68.0% (137772 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 69.0% (139799 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 70.0% (141826 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 71.0% (143849 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 72.0% (145875 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 73.0% (147899 of 202562), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 74.0% (149925 of 202562), ETA 0:00:00                              
[00:00:03] Calculating geometric means [35.6Gb / 503.5Gb]
[00:00:03] Traversing graph with 5022 nodes and 202562 edges [35.6Gb / 503.5Gb]
[00:00:03] Building SCR Graph and Binning (478 vertices and 881 edges) [P = 9.50%; 35.6Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 1.0% (2026 of 202562), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (955 vertices and 3314 edges) [P = 19.00%; 35.6Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 2.0% (4052 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 3.0% (6078 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 4.0% (8104 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 5.0% (10130 of 202562), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (1432 vertices and 6549 edges) [P = 28.50%; 35.6Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 6.0% (12156 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 7.0% (14182 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 8.0% (16208 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 9.0% (18234 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 10.0% (20260 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 11.0% (22286 of 202562), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (1909 vertices and 8671 edges) [P = 38.00%; 35.6Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 12.0% (24312 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 13.0% (26338 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 14.0% (28364 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 15.0% (30390 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 16.0% (32416 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 17.0% (34442 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 18.0% (36468 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 19.0% (38494 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 20.0% (40520 of 202562), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (2386 vertices and 9782 edges) [P = 47.50%; 35.6Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 21.0% (42546 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 22.0% (44572 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 23.0% (46598 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 24.0% (48624 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 25.0% (50650 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 26.0% (52676 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 27.0% (54702 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 28.0% (56728 of 202562), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (2863 vertices and 10650 edges) [P = 57.00%; 35.6Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 29.0% (58754 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 30.0% (60780 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 31.0% (62806 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 32.0% (64832 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 33.0% (66858 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 34.0% (68884 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 35.0% (70910 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 36.0% (72936 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 37.0% (74962 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 38.0% (76988 of 202562), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (3340 vertices and 11763 edges) [P = 66.50%; 35.6Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 39.0% (79014 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 40.0% (81040 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 41.0% (83066 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 42.0% (85092 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 43.0% (87118 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 44.0% (89144 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 45.0% (91170 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 46.0% (93196 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 47.0% (95222 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 48.0% (97248 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 49.0% (99274 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 50.0% (101300 of 202562), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (3817 vertices and 13461 edges) [P = 76.00%; 35.6Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 51.0% (103326 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 52.0% (105352 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 53.0% (107378 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 54.0% (109404 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 55.0% (111430 of 202562), ETA 0:00:00                               
[00:00:03] ... traversing graph 56.0% (113456 of 202562), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (4020 vertices and 14438 edges) [P = 85.50%; 35.6Gb / 503.5Gb]                           
[00:00:03] Finished Traversing graph [35.6Gb / 503.5Gb]                                       
[00:00:03] Dissolved 0 small clusters leaving 0 leftover contigs to be re-merged into larger clusters
[00:00:03] Rescuing singleton large contigs                                   
[00:00:03] There are 1165 bins already
[00:00:03] Outputting bins
[00:00:03] Writing cluster stats to: 03bins/metabat2_2kb/1507999/1507999.bin.BinInfo.txt
[00:00:14] 100.00% (29953416 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
1165 bins (29953416 bases in total) formed.
[00:00:14] Finished
MetaBAT2 generated 1165 bins for 1507999
MetaBAT2 binning completed for 1507999
