INFO:    Converting OCI blobs to SIF format
INFO:    Starting build...
Copying blob sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
Copying blob sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying blob sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
Copying blob sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
Copying blob sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
Copying blob sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying blob sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
Copying blob sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
Copying config sha256:1a18e5b322db0c21b435c9bcf96d7aae8ee9294648210e1b1929be9505d910a2
time="2025-04-28T16:14:15-07:00" level=warning msg="Compressor for blob with digest sha256:1a18e5b322db0c21b435c9bcf96d7aae8ee9294648210e1b1929be9505d910a2 previously recorded as gzip, now uncompressed"
Writing manifest to image destination
2025/04/28 16:14:17  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:17  info unpack layer: sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
2025/04/28 16:14:17  warn rootless{dev/console} creating empty file in place of device 5:1
2025/04/28 16:14:17  info unpack layer: sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
2025/04/28 16:14:17  info unpack layer: sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
2025/04/28 16:14:17  info unpack layer: sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
2025/04/28 16:14:17  info unpack layer: sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
2025/04/28 16:14:17  info unpack layer: sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
2025/04/28 16:14:17  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:17  info unpack layer: sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
2025/04/28 16:14:17  info unpack layer: sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
INFO:    Creating SIF file...
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[bwa_index] Pack FASTA... 0.22 sec
[bwa_index] Construct BWT for the packed sequence...
[BWTIncCreate] textLength=64334774, availableWord=16526620
[BWTIncConstructFromPacked] 10 iterations done. 27260678 characters processed.
[BWTIncConstructFromPacked] 20 iterations done. 50360054 characters processed.
[bwt_gen] Finished constructing BWT in 27 iterations.
[bwa_index] 8.51 seconds elapse.
[bwa_index] Update BWT... 0.14 sec
[bwa_index] Pack forward-only FASTA... 0.10 sec
[bwa_index] Construct SA from BWT and Occ... 3.03 sec
[main] Version: 0.7.17-r1188
[main] CMD: /usr/local/bin/bwa index 02mapping/1507993/temp/scaffolds.fasta
[main] Real time: 12.814 sec; CPU: 12.016 sec
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 1072766 sequences (160000093 bp)...
[M::process] read 1070544 sequences (160000281 bp)...
[M::mem_process_seqs] Processed 1072766 reads in 123.647 CPU sec, 8.520 real sec
[M::mem_process_seqs] Processed 1070544 reads in 80.401 CPU sec, 5.652 real sec
[M::process] read 1070236 sequences (160000267 bp)...
[M::mem_process_seqs] Processed 1070236 reads in 75.024 CPU sec, 5.175 real sec
[M::process] read 1070482 sequences (160000253 bp)...
[M::mem_process_seqs] Processed 1070482 reads in 70.870 CPU sec, 4.807 real sec
[M::process] read 1071230 sequences (160000239 bp)...
[M::mem_process_seqs] Processed 1071230 reads in 66.233 CPU sec, 4.578 real sec
[M::process] read 1070458 sequences (160000036 bp)...
[M::mem_process_seqs] Processed 1070458 reads in 70.324 CPU sec, 4.732 real sec
[M::process] read 1071870 sequences (160000208 bp)...
[M::mem_process_seqs] Processed 1071870 reads in 98.720 CPU sec, 7.407 real sec
[M::process] read 578766 sequences (84866290 bp)...
[M::mem_process_seqs] Processed 578766 reads in 58.208 CPU sec, 4.044 real sec
[main] Version: 0.7.17-r1188
[main] CMD: bwa mem -t 16 02mapping/1507993/temp/scaffolds.fasta 00data/readsf/1507993.anqdpht.fastq.gz
[main] Real time: 60.242 sec; CPU: 652.734 sec
[bam_sort_core] merging from 0 files and 16 in-memory blocks...
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
