Processing sample: 1507994
Command line: /usr/local/bin/spades.py	-s	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507994.anqdpht.fastq.gz	--sc	-o	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994	-t	16	-m	128	

System information:
  SPAdes version: 3.15.5
  Python version: 3.10.6
  OS: Linux-4.18.0-553.5.1.el8_10.x86_64-x86_64-with-glibc2.28

Output dir: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994
Mode: read error correction and assembling
Debug mode is turned OFF

Dataset parameters:
  Single-cell mode
  Reads:
    Library number: 1, library type: single
      left reads: not specified
      right reads: not specified
      interlaced reads: not specified
      single reads: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507994.anqdpht.fastq.gz']
      merged reads: not specified
Read error correction parameters:
  Iterations: 1
  PHRED offset will be auto-detected
  Corrected reads will be compressed
Assembly parameters:
  k: [21, 33, 55]
  Repeat resolution is enabled
  Mismatch careful mode is turned OFF
  MismatchCorrector will be SKIPPED
  Coverage cutoff is turned OFF
Other parameters:
  Dir for temp files: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/tmp
  Threads: 16
  Memory limit (in Gb): 128


======= SPAdes pipeline started. Log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/spades.log

/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507994.anqdpht.fastq.gz: max reads length: 150

Reads length: 150


===== Before start started. 


===== Read error correction started. 


===== Read error correction started. 


== Running: /usr/local/bin/spades-hammer /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/corrected/configs/config.info

  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  75)   Starting BayesHammer, built from N/A, git revision N/A
  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  76)   Loading config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/corrected/configs/config.info
  0:00:00.001     1M / 33M   INFO    General                 (main.cpp                  :  78)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 33M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.001     1M / 33M   INFO    General                 (main.cpp                  :  86)   Trying to determine PHRED offset
  0:00:00.002     1M / 33M   INFO    General                 (main.cpp                  :  92)   Determined value is 33
  0:00:00.002     1M / 33M   INFO    General                 (hammer_tools.cpp          :  38)   Hamming graph threshold tau=1, k=21, subkmer positions = [ 0 10 ]
  0:00:00.002     1M / 33M   INFO    General                 (main.cpp                  : 113)   Size of aux. kmer data 24 bytes
     === ITERATION 0 begins ===
  0:00:00.006     1M / 33M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 16 files using 16 threads. This might take a while.
  0:00:00.009     1M / 33M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.009     1M / 33M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66666 Gb
  0:00:00.009     1M / 33M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 4194304
  0:00:00.048  9217M / 9217M INFO   K-mer Splitting          (kmer_data.cpp             :  97)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507994.anqdpht.fastq.gz
  0:00:12.368  9217M / 14G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 3682044 reads
  0:00:25.366  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 7305819 reads
  0:00:41.298  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 11000003 reads
  0:00:57.111  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 14730484 reads
  0:01:13.806  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 18439886 reads
  0:01:17.903  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 18938564 reads
  0:01:17.930  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 112)   Total 18938564 reads processed
  0:01:17.931     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:29.948     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 245769232 kmers in total.
  0:01:29.977     1M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:38.193   177M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 245769232 kmers, 177522032 bytes occupied (5.77849 bits per kmer).
  0:01:38.194   177M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 354)   Arranging kmers in hash map order
  0:01:46.984  3930M / 15G   INFO    General                 (main.cpp                  : 148)   Clustering Hamming graph.
  0:03:33.888  3930M / 15G   INFO    General                 (main.cpp                  : 155)   Extracting clusters:
  0:03:33.889  3930M / 15G   INFO    General                 (concurrent_dsu.cpp        :  18)   Connecting to root
  0:03:34.262  3930M / 15G   INFO    General                 (concurrent_dsu.cpp        :  34)   Calculating counts
  0:03:56.887  8282M / 15G   INFO    General                 (concurrent_dsu.cpp        :  63)   Writing down entries
  0:04:31.137  3930M / 15G   INFO    General                 (main.cpp                  : 167)   Clustering done. Total clusters: 127777697
  0:04:31.300  2053M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 371)   Collecting K-mer information, this takes a while.
  0:04:33.221  7682M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 377)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507994.anqdpht.fastq.gz
  0:06:09.454  7682M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 384)   Collection done, postprocessing.
  0:06:09.819  7682M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 397)   There are 245769232 kmers in total. Among them 135909024 (55.2994%) are singletons.
  0:06:09.819  7682M / 15G   INFO    General                 (main.cpp                  : 173)   Subclustering Hamming graph
  0:08:32.600  7682M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 650)   Subclustering done. Total 6959 non-read kmers were generated.
  0:08:32.606  7682M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 651)   Subclustering statistics:
  0:08:32.617  7682M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 652)     Total singleton hamming clusters: 85243936. Among them 45154052 (52.9704%) are good
  0:08:32.626  7682M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 653)     Total singleton subclusters: 5293099. Among them 5286653 (99.8782%) are good
  0:08:32.637  7682M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 654)     Total non-singleton subcluster centers: 44656110. Among them 32131940 (71.9542%) are good
  0:08:32.648  7682M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 655)     Average size of non-trivial subcluster: 3.59521 kmers
  0:08:32.659  7682M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 656)     Average number of sub-clusters per non-singleton cluster: 1.17434
  0:08:32.669  7682M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 657)     Total solid k-mers: 82572645
  0:08:32.680  7682M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 658)     Substitution probabilities: [4,4]((0.931618,0.0317518,0.0314586,0.00517131),(0.0243095,0.938439,0.00852697,0.0287243),(0.0278216,0.00831303,0.938826,0.0250393),(0.00498321,0.0315616,0.0315607,0.931895))
  0:08:32.834  7682M / 15G   INFO    General                 (main.cpp                  : 178)   Finished clustering.
  0:08:32.834  7682M / 15G   INFO    General                 (main.cpp                  : 197)   Starting solid k-mers expansion in 16 threads.
  0:09:14.615  7682M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 0 produced 10193255 new k-mers.
  0:09:56.980  7682M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 1 produced 713731 new k-mers.
  0:10:39.445  7682M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 2 produced 36790 new k-mers.
  0:11:21.493  7682M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 3 produced 2883 new k-mers.
  0:12:03.311  7682M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 4 produced 164 new k-mers.
  0:12:45.165  7682M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 5 produced 0 new k-mers.
  0:12:45.166  7682M / 15G   INFO    General                 (main.cpp                  : 222)   Solid k-mers finalized
  0:12:45.166  7682M / 15G   INFO    General                 (hammer_tools.cpp          : 222)   Starting read correction in 16 threads.
  0:12:45.166  7682M / 15G   INFO    General                 (hammer_tools.cpp          : 266)   Correcting single reads: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507994.anqdpht.fastq.gz
  0:12:47.324  8440M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 0 of 1600000 reads.
  0:12:49.653  8562M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 0
  0:12:50.998  8562M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 0
  0:12:53.133  8613M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 1 of 1600000 reads.
  0:12:54.833  8613M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 1
  0:12:56.007  8613M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 1
  0:12:58.163  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 2 of 1600000 reads.
  0:13:00.231  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 2
  0:13:01.379  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 2
  0:13:03.557  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 3 of 1600000 reads.
  0:13:06.827  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 3
  0:13:07.975  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 3
  0:13:10.221  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 4 of 1600000 reads.
  0:13:13.955  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 4
  0:13:15.110  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 4
  0:13:17.393  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 5 of 1600000 reads.
  0:13:22.909  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 5
  0:13:24.062  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 5
  0:13:26.363  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 6 of 1600000 reads.
  0:13:28.859  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 6
  0:13:30.045  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 6
  0:13:32.346  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 7 of 1600000 reads.
  0:13:35.096  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 7
  0:13:36.234  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 7
  0:13:38.518  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 8 of 1600000 reads.
  0:13:43.078  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 8
  0:13:44.233  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 8
  0:13:46.509  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 9 of 1600000 reads.
  0:13:48.857  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 9
  0:13:49.996  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 9
  0:13:52.285  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 10 of 1600000 reads.
  0:13:57.203  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 10
  0:13:58.372  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 10
  0:14:00.443  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 11 of 1338564 reads.
  0:14:14.946  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 11
  0:14:15.974  8619M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 11
  0:14:21.142  7682M / 15G   INFO    General                 (hammer_tools.cpp          : 276)   Correction done. Changed 4875233 bases in 2201782 reads.
  0:14:21.169  7682M / 15G   INFO    General                 (hammer_tools.cpp          : 277)   Failed to correct 0 bases out of 2772005858.
  0:14:21.685     1M / 15G   INFO    General                 (main.cpp                  : 255)   Saving corrected dataset description to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/corrected/corrected.yaml
  0:14:21.696     1M / 15G   INFO    General                 (main.cpp                  : 262)   All done. Exiting.

===== Read error correction finished. 


===== corrected reads compression started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/compress_all.py --input_file /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/corrected/corrected.yaml --ext_python_modules_home /usr/local/share/spades --max_threads 16 --output_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/corrected --gzip_output

== Compressing corrected reads (with gzip)
== Files to compress: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/corrected/1507994.anqdpht.fastq.00.0_0.cor.fastq']
== Files compression is finished
== Dataset yaml file is updated

===== corrected reads compression finished. 


===== Read error correction finished. 


===== Assembling started. 


===== K21 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K21/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K21/configs/mda_mode.info

  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K21/configs/config.info
  0:00:00.004     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K21/configs/mda_mode.info
  0:00:00.004     1M / 36M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.004     1M / 36M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.004     1M / 36M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.004     1M / 36M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/dataset.info) with K=21
  0:00:00.004     1M / 36M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.004     1M / 36M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.004     1M / 36M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.004     1M / 36M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.007     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.007     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.011     1M / 36M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.013     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.017     1M / 36M   INFO    General                 (read_converter.cpp        :  72)   Converting reads to binary format for library #0 (takes a while)
  0:00:00.017     1M / 36M   INFO    General                 (read_converter.cpp        :  73)   Converting paired reads
  0:00:00.022    23M / 36M   INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:00.022     1M / 36M   INFO    General                 (read_converter.cpp        :  86)   Converting single reads
  0:00:00.248    33M / 44M   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:00:00.262    35M / 50M   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:00:00.280    40M / 63M   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:00:00.391    35M / 86M   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:00:00.554    40M / 106M  INFO    General                 (binary_converter.cpp      :  96)   262144 reads processed
  0:00:01.006    34M / 155M  INFO    General                 (binary_converter.cpp      :  96)   524288 reads processed
  0:00:01.732    38M / 202M  INFO    General                 (binary_converter.cpp      :  96)   1048576 reads processed
  0:00:03.225    45M / 253M  INFO    General                 (binary_converter.cpp      :  96)   2097152 reads processed
  0:00:06.352    45M / 279M  INFO    General                 (binary_converter.cpp      :  96)   4194304 reads processed
  0:00:12.938    44M / 331M  INFO    General                 (binary_converter.cpp      :  96)   8388608 reads processed
  0:00:26.456    42M / 360M  INFO    General                 (binary_converter.cpp      :  96)   16777216 reads processed
  0:00:30.034    31M / 360M  INFO    General                 (binary_converter.cpp      : 111)   18918300 reads written
  0:00:30.040    21M / 360M  INFO    General                 (read_converter.cpp        :  92)   Converting merged reads
  0:00:30.222    31M / 360M  INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:30.640     1M / 360M  INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:30.826     1M / 360M  INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:30.826     1M / 360M  INFO    General                 (construction.cpp          : 159)   Average read length 146.406
  0:00:30.826     1M / 360M  INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:30.826     1M / 360M  INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:30.833     1M / 360M  INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:30.833     1M / 360M  INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:30.833     1M / 360M  INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:00:36.559  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 10824630 reads
  0:00:41.332  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 22654410 reads
  0:00:46.207  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 34380414 reads
  0:00:49.757  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 37661870 reads
  0:00:51.170  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 37836600 reads
  0:00:51.172     1M / 10G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 37836600 reads
  0:00:51.207     1M / 10G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:54.711     1M / 10G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 71412336 kmers in total.
  0:00:54.713     1M / 10G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:55.012     1M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:55.012     1M / 10G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:55.018     1M / 10G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:55.019     1M / 10G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:55.019     1M / 10G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:01:01.502  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 71412336 kmers
  0:01:01.502  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 71412336 kmers.
  0:01:01.503     1M / 10G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:04.920     1M / 10G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 69843529 kmers in total.
  0:01:04.927     1M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:06.282    52M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 69843529 kmers, 50564888 bytes occupied (5.79179 bits per kmer).
  0:01:06.282    52M / 10G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:09.297   121M / 10G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:11.618   121M / 10G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:11.660   120M / 10G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:01:11.660   120M / 10G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:01:11.661   120M / 10G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:01:13.988   160M / 10G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 554072
  0:01:14.010   160M / 10G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 560029
  0:01:14.029   120M / 10G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   8753639 22-mers were removed by early tip clipper
  0:01:14.029   120M / 10G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:14.154   120M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:16.942   276M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 4061674 sequences extracted
  0:01:18.136   275M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:18.719   276M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 8 loops collected
  0:01:18.809   275M / 10G   INFO    General                 (debruijn_graph_constructor: 487)   Total 8123364 edges to create
  0:01:18.809   589M / 10G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:21.890   713M / 10G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:21.940   713M / 10G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:21.967   745M / 10G   INFO    General                 (debruijn_graph_constructor: 503)   Total 2492875 vertices to create
  0:01:21.967   978M / 10G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:24.173   760M / 10G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:24.173   760M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:24.933   812M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 71412336 kmers, 51695648 bytes occupied (5.79123 bits per kmer).
  0:01:24.979  1088M / 10G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:01:43.317  1088M / 10G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:01:45.327  1088M / 10G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 8122302 edges
  0:01:46.596   692M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:01:47.525   693M / 10G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 16
  0:01:47.525   693M / 10G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 37.6978
  0:01:47.526   693M / 10G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 366
  0:01:47.553   692M / 10G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 37.6978
  0:01:47.553   692M / 10G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:01:47.553   692M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:01:47.554   692M / 10G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:01:47.555   692M / 10G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:01:47.555   692M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:01:47.647   692M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 0 times
  0:01:47.647   692M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:01:47.647   692M / 10G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:01:47.647   692M / 10G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:47.647   692M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:01:47.647   692M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:47.796   692M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 14526 times
  0:01:47.797   692M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:03.586   707M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 207189 times
  0:02:03.590   707M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:09.691   760M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 953066 times
  0:02:09.704   760M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:02:09.704   760M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:10.021   742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 7663 times
  0:02:10.022   742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:14.673   687M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 53185 times
  0:02:14.681   687M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:15.423   687M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 118318 times
  0:02:15.433   687M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:02:15.433   687M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:15.516   682M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 99 times
  0:02:15.516   682M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:16.901   672M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 12388 times
  0:02:16.903   672M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:17.135   673M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 39958 times
  0:02:17.140   673M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:02:17.140   673M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:17.169   671M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 23 times
  0:02:17.169   671M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:18.166   667M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 3617 times
  0:02:18.167   667M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:18.307   668M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 24309 times
  0:02:18.310   668M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:02:18.310   668M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:18.325   667M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 11 times
  0:02:18.325   667M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:19.005   666M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1780 times
  0:02:19.006   666M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:19.100   666M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 15498 times
  0:02:19.102   666M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:02:19.102   666M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:19.111   665M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 6 times
  0:02:19.112   665M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:19.828   664M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 870 times
  0:02:19.829   664M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:19.910   661M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 12529 times
  0:02:19.912   661M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:02:19.912   661M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:19.918   660M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:02:19.918   660M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:20.493   660M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 615 times
  0:02:20.493   660M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:20.549   660M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 9383 times
  0:02:20.550   660M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:02:20.550   660M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:20.554   660M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:02:20.554   660M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:20.942   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 415 times
  0:02:20.943   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:20.987   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 7376 times
  0:02:20.988   659M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:02:20.988   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:20.991   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:20.991   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:21.317   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 299 times
  0:02:21.317   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:21.352   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 5584 times
  0:02:21.352   659M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:02:21.353   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:21.355   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:21.355   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:21.582   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 221 times
  0:02:21.582   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:21.612   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 4965 times
  0:02:21.613   659M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:02:21.613   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:21.624   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 82 times
  0:02:21.624   659M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:22.177   658M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 502 times
  0:02:22.177   658M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:22.187   622M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1 times
  0:02:22.188   622M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:02:22.188   622M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:22.189   622M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:22.189   622M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:22.189   622M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:22.189   622M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:22.189   622M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:02:22.206   613M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:02:22.206   613M / 10G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:02:22.206   613M / 10G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:02:22.206   613M / 10G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:02:22.206   613M / 10G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:22.206   613M / 10G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:22.206   613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:22.350   613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 3358 times
  0:02:22.350   613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:22.670   613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 526 times
  0:02:22.670   613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:22.680   613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:22.680   613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:22.689   613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 22 times
  0:02:22.690   613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:23.101   615M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 26 times
  0:02:23.102   615M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:23.626   616M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1009 times
  0:02:23.627   616M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:23.709   616M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 34 times
  0:02:23.709   616M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:24.008   614M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 9 times
  0:02:24.009   614M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:24.018   614M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:24.018   614M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:24.027   613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:24.027   613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:24.428   616M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 3 times
  0:02:24.428   616M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:24.859   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 2 times
  0:02:24.860   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:24.969   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1 times
  0:02:24.969   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:25.273   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:02:25.273   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:25.273   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:25.273   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:25.273   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:25.273   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:25.273   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:25.273   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:25.273   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:02:25.274   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:25.390   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:02:25.390   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:25.692   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:02:25.692   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:25.692   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:25.692   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:25.692   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:25.692   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:25.692   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:25.692   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:25.692   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:02:25.692   618M / 10G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:02:25.739   618M / 10G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:02:25.840   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:02:25.852   618M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 2209 times
  0:02:25.869   613M / 10G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:02:25.869   613M / 10G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 59.547
  0:02:25.882   613M / 10G   INFO    General                 (simplification.cpp        : 497)     Total length = 38757841
  0:02:25.908   613M / 10G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 574
  0:02:25.908   613M / 10G   INFO    General                 (simplification.cpp        : 499)     Edges: 492519
  0:02:25.908   613M / 10G   INFO    General                 (simplification.cpp        : 500)     Vertices: 305006
  0:02:25.908   613M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:02:25.910   613M / 10G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K21/simplified_contigs
  0:02:25.924   616M / 10G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:02:25.931   616M / 10G   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:02:25.942   616M / 10G   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:02:25.960   616M / 10G   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:02:25.996   616M / 10G   INFO    General                 (binary_converter.cpp      : 111)   246362 reads written
  0:02:26.051   613M / 10G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:02:26.187     1M / 10G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 2 minutes 26 seconds

===== K21 finished. 


===== K33 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K33/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K33/configs/mda_mode.info

  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K33/configs/config.info
  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K33/configs/mda_mode.info
  0:00:00.000     1M / 36M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/dataset.info) with K=33
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 36M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.002     1M / 36M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.003     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.003     1M / 36M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K21/simplified_contigs
  0:00:00.062     1M / 36M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.062     1M / 36M   INFO    General                 (construction.cpp          : 159)   Average read length 146.406
  0:00:00.063     1M / 36M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.063     1M / 36M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.069     1M / 36M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.069     1M / 36M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.069     1M / 36M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:04.706  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 6069926 reads
  0:00:08.079  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 11886754 reads
  0:00:12.558  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 18252196 reads
  0:00:17.125  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 24796693 reads
  0:00:21.602  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 31258522 reads
  0:00:31.805  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 36256698 reads
  0:00:37.188  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 37869581 reads
  0:00:39.551  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 38329324 reads
  0:00:39.553     1M / 10G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 38329324 reads
  0:00:39.925     1M / 10G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:48.048     1M / 10G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 87101480 kmers in total.
  0:00:48.053     1M / 10G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:48.577     1M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:48.582     1M / 10G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:48.587     1M / 10G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:48.587     1M / 10G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:48.588     1M / 10G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:01:01.185  9602M / 10G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 87101480 kmers
  0:01:01.186  9602M / 10G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 87101480 kmers.
  0:01:01.187     2M / 10G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:07.346     2M / 10G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 85973628 kmers in total.
  0:01:07.352     2M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:10.583    73M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 85973628 kmers, 62213528 bytes occupied (5.78908 bits per kmer).
  0:01:10.583    73M / 10G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:15.479   157M / 10G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:18.661   157M / 10G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:18.767   157M / 10G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:01:18.767   157M / 10G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:01:18.767   157M / 10G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:01:21.685   236M / 10G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 773885
  0:01:21.740   236M / 10G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 783537
  0:01:21.790   157M / 10G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   14673413 34-mers were removed by early tip clipper
  0:01:21.801   157M / 10G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:21.978   157M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:25.437   312M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 3213054 sequences extracted
  0:01:27.073   312M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:27.737   312M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 14 loops collected
  0:01:27.854   312M / 10G   INFO    General                 (debruijn_graph_constructor: 487)   Total 6426136 edges to create
  0:01:27.855   561M / 10G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:30.268   661M / 10G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:30.306   661M / 10G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:30.326   677M / 10G   INFO    General                 (debruijn_graph_constructor: 503)   Total 2085216 vertices to create
  0:01:30.327   870M / 10G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:32.219   703M / 10G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:32.219   703M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:33.052   774M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 87101480 kmers, 63028832 bytes occupied (5.789 bits per kmer).
  0:01:33.083  1110M / 10G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:01:49.939  1110M / 10G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:01:52.492  1110M / 10G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 6425480 edges
  0:01:55.078   587M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:01:55.732   588M / 10G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 13
  0:01:55.732   588M / 10G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 29.375
  0:01:55.732   588M / 10G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 251
  0:01:55.743   587M / 10G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 29.375
  0:01:55.743   587M / 10G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:01:55.743   587M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:01:55.743   587M / 10G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:01:55.743   587M / 10G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:01:55.743   587M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:01:55.783   587M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 58 times
  0:01:55.783   587M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:01:55.783   587M / 10G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:01:55.783   587M / 10G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:55.783   587M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:01:55.783   587M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:55.955   586M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 32703 times
  0:01:55.956   586M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:03.397   590M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 183014 times
  0:02:03.401   590M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:08.065   627M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 681616 times
  0:02:08.079   627M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:02:08.079   627M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:08.324   607M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 9115 times
  0:02:08.325   607M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:11.839   570M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 45558 times
  0:02:11.846   570M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:12.688   562M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 113611 times
  0:02:12.698   562M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:02:12.698   562M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:12.752   558M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 208 times
  0:02:12.753   558M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:14.241   549M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 15451 times
  0:02:14.243   549M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:14.441   550M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 25679 times
  0:02:14.445   550M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:02:14.445   550M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:14.470   545M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 24 times
  0:02:14.470   545M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:15.034   544M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 3710 times
  0:02:15.035   544M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:15.135   544M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 11870 times
  0:02:15.138   544M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:02:15.138   544M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:15.149   543M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 7 times
  0:02:15.149   543M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:15.447   540M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1530 times
  0:02:15.447   540M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:15.505   541M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 7375 times
  0:02:15.507   541M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:02:15.507   541M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:15.513   540M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5 times
  0:02:15.513   540M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:15.689   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 796 times
  0:02:15.690   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:15.728   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 5062 times
  0:02:15.729   538M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:02:15.729   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:15.732   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5 times
  0:02:15.732   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:15.828   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 464 times
  0:02:15.828   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:15.858   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3707 times
  0:02:15.858   538M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:02:15.858   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:15.861   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:02:15.861   538M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:15.923   537M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 294 times
  0:02:15.923   537M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:15.946   537M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2754 times
  0:02:15.947   537M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:02:15.947   537M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:15.948   537M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 4 times
  0:02:15.948   537M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:15.986   537M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 183 times
  0:02:15.987   537M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:16.007   536M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2169 times
  0:02:16.008   536M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:02:16.008   536M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:16.009   536M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3 times
  0:02:16.009   536M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:16.045   536M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 161 times
  0:02:16.045   536M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:16.059   536M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1825 times
  0:02:16.060   536M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:02:16.060   536M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:16.068   535M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 94 times
  0:02:16.068   535M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:16.302   536M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 273 times
  0:02:16.302   536M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:16.309   500M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:02:16.309   500M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:02:16.309   500M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:16.309   500M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:16.310   500M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:16.310   500M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:16.310   500M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:16.310   500M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:02:16.322   497M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:02:16.323   497M / 10G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:02:16.323   497M / 10G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:02:16.323   497M / 10G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:02:16.323   497M / 10G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:16.323   497M / 10G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:16.323   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:16.400   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 2675 times
  0:02:16.400   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:16.662   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 993 times
  0:02:16.663   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:16.669   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:16.669   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:16.675   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 22 times
  0:02:16.675   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:16.891   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 17 times
  0:02:16.892   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:17.136   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 798 times
  0:02:17.136   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:17.174   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 15 times
  0:02:17.174   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:17.396   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 6 times
  0:02:17.397   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:17.403   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:17.403   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:17.409   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:17.409   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:17.671   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2 times
  0:02:17.671   497M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:17.937   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 3 times
  0:02:17.937   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:17.988   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1 times
  0:02:17.988   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:18.206   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:02:18.206   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:18.207   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:18.207   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:18.207   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:18.207   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:18.207   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:18.207   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:18.207   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:02:18.207   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:18.261   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:02:18.261   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:18.480   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:02:18.480   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:18.480   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:18.480   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:18.480   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:18.481   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:18.481   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:18.481   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:18.481   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:02:18.481   498M / 10G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:02:18.546   498M / 10G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:02:18.595   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:02:18.625   498M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 16621 times
  0:02:18.633   496M / 10G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:02:18.633   496M / 10G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 50.8078
  0:02:18.640   496M / 10G   INFO    General                 (simplification.cpp        : 497)     Total length = 40786667
  0:02:18.653   496M / 10G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 1673
  0:02:18.653   496M / 10G   INFO    General                 (simplification.cpp        : 499)     Edges: 223083
  0:02:18.653   496M / 10G   INFO    General                 (simplification.cpp        : 500)     Vertices: 161538
  0:02:18.653   496M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:02:18.664   496M / 10G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K33/simplified_contigs
  0:02:18.680   498M / 10G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:02:18.684   498M / 10G   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:02:18.695   498M / 10G   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:02:18.733   498M / 10G   INFO    General                 (binary_converter.cpp      : 111)   111566 reads written
  0:02:18.823   496M / 10G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:02:18.871     1M / 10G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 2 minutes 18 seconds

===== K33 finished. 


===== K55 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/configs/mda_mode.info

  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/configs/config.info
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/configs/mda_mode.info
  0:00:00.001     1M / 36M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/dataset.info) with K=55
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 36M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 242)   Will need read mapping, kmer mapper will be attached
  0:00:00.001     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.004     1M / 36M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.005     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.005     1M / 36M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K33/simplified_contigs
  0:00:00.091     1M / 36M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.091     1M / 36M   INFO    General                 (construction.cpp          : 159)   Average read length 146.406
  0:00:00.091     1M / 36M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.091     1M / 36M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.095     1M / 36M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.096     1M / 36M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.096     1M / 36M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:05.877  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 7914200 reads
  0:00:10.137  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 15760665 reads
  0:00:15.641  9601M / 9690M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 24157999 reads
  0:00:21.230  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 32393329 reads
  0:00:26.915  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 37213036 reads
  0:00:30.073  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 38059732 reads
  0:00:30.075     1M / 10G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 38059732 reads
  0:00:30.460     1M / 10G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:37.368     1M / 10G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 104864048 kmers in total.
  0:00:37.368     1M / 10G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:37.842     1M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:37.842     1M / 10G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:37.849     1M / 10G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:37.849     1M / 10G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:37.850     1M / 10G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:47.658  9602M / 10G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 49759295 kmers
  0:00:55.917  9602M / 10G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 99652631 kmers
  0:00:59.186  9602M / 10G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 104864160 kmers
  0:00:59.186  9602M / 10G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 104864160 kmers.
  0:00:59.187     2M / 10G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:07.030     2M / 10G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 104319266 kmers in total.
  0:01:07.032     2M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:10.184    76M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 104319266 kmers, 75464776 bytes occupied (5.78722 bits per kmer).
  0:01:10.184    76M / 10G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:14.837   176M / 10G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:18.679   176M / 10G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:18.803   176M / 10G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:18.965   176M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:23.657   381M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 4057835 sequences extracted
  0:01:25.805   380M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:26.762   381M / 10G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 0 loops collected
  0:01:26.927   380M / 10G   INFO    General                 (debruijn_graph_constructor: 487)   Total 8115670 edges to create
  0:01:26.927   694M / 10G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:29.926   818M / 10G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:29.978   818M / 10G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:30.010   850M / 10G   INFO    General                 (debruijn_graph_constructor: 503)   Total 3513053 vertices to create
  0:01:30.010  1175M / 10G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:33.125   955M / 10G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:33.126   955M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:33.947  1036M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 104864048 kmers, 75857840 bytes occupied (5.78714 bits per kmer).
  0:01:33.984  1440M / 10G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:01:49.083  1440M / 10G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:01:52.878  1440M / 10G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 8115450 edges
  0:01:54.378   808M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:01:54.958   809M / 10G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 9
  0:01:54.958   809M / 10G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 16.4927
  0:01:54.958   809M / 10G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 240
  0:01:54.974   808M / 10G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 16.4927
  0:01:54.974   808M / 10G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:01:54.974   808M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: early_gapcloser)
  0:01:54.974   808M / 10G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:01:54.974   808M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:01:54.974   808M / 10G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:01:54.974   808M / 10G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:01:54.975   808M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:01:55.033   808M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 27 times
  0:01:55.034   808M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:01:55.034   808M / 10G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:01:55.034   808M / 10G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:55.034   808M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:01:55.034   808M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:59.333   742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1155402 times
  0:01:59.344   742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:15.920  1461M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 117988 times
  0:02:15.922  1461M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:18.009  1483M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 271152 times
  0:02:18.017  1483M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:02:18.017  1483M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:18.254  1473M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 17263 times
  0:02:18.258  1473M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:25.463  1613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 26937 times
  0:02:25.464  1613M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:26.302  1614M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 103415 times
  0:02:26.308  1614M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:02:26.308  1614M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:26.369  1603M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1755 times
  0:02:26.371  1603M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:30.791  1704M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 17280 times
  0:02:30.793  1704M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:31.020  1703M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 24025 times
  0:02:31.023  1703M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:02:31.023  1703M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:31.049  1701M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 191 times
  0:02:31.050  1701M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:32.175  1724M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 4425 times
  0:02:32.176  1724M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:32.300  1724M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 12173 times
  0:02:32.301  1724M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:02:32.302  1724M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:32.315  1723M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 54 times
  0:02:32.315  1723M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:32.805  1733M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2037 times
  0:02:32.806  1733M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:32.863  1733M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 5801 times
  0:02:32.864  1733M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:02:32.865  1733M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:32.872  1733M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 28 times
  0:02:32.873  1733M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:33.089  1738M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 961 times
  0:02:33.090  1738M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:33.123  1738M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3526 times
  0:02:33.124  1738M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:02:33.124  1738M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:33.129  1738M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 21 times
  0:02:33.130  1738M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:33.241  1740M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 479 times
  0:02:33.242  1740M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:33.264  1740M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2451 times
  0:02:33.265  1740M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:02:33.265  1740M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:33.268  1740M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 9 times
  0:02:33.269  1740M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:33.337  1741M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 308 times
  0:02:33.337  1741M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:33.355  1741M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1845 times
  0:02:33.356  1741M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:02:33.356  1741M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:33.358  1741M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 8 times
  0:02:33.359  1741M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:33.402  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 196 times
  0:02:33.402  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:33.416  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1358 times
  0:02:33.416  1742M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:02:33.417  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:33.418  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:02:33.419  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:33.450  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 130 times
  0:02:33.450  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:33.462  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1174 times
  0:02:33.462  1742M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:02:33.463  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:33.490  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 898 times
  0:02:33.491  1742M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:33.592  1743M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 171 times
  0:02:33.593  1743M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:33.604  1726M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:02:33.605  1726M / 10G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:02:33.606  1726M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:33.607  1726M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:33.607  1726M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:33.608  1726M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:33.608  1726M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:33.609  1726M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:02:33.634  1724M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: late_gapcloser)
  0:02:33.635  1724M / 10G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:02:33.635  1724M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:02:33.636  1724M / 10G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:02:33.636  1724M / 10G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:02:33.637  1724M / 10G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:02:33.637  1724M / 10G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:33.638  1724M / 10G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:33.638  1724M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:02:33.639  1724M / 10G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:02:33.640  1724M / 10G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:02:33.844  1724M / 10G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:02:34.032  1724M / 10G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:02:39.856  1724M / 10G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:02:40.075  1724M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:02:40.077  1724M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:02:40.090  1724M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 14 times
  0:02:40.091  1724M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:40.131  1724M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1295 times
  0:02:40.132  1724M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:41.578  1769M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 2380 times
  0:02:41.579  1769M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.592  1769M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 7 times
  0:02:41.592  1769M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:41.605  1769M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 70 times
  0:02:41.606  1769M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.672  1770M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 28 times
  0:02:41.673  1770M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:41.918  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 687 times
  0:02:41.919  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:02:41.919  1778M / 10G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:02:41.920  1778M / 10G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:02:42.099  1778M / 10G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:02:42.258  1778M / 10G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:02:45.746  1778M / 10G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:02:45.943  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:02:45.944  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:02:45.956  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 2 times
  0:02:45.956  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:45.979  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 9 times
  0:02:45.980  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:46.379  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 13 times
  0:02:46.379  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:46.390  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:46.391  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:46.402  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:46.403  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:46.454  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1 times
  0:02:46.455  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:46.505  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 5 times
  0:02:46.505  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:02:46.506  1778M / 10G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:02:46.506  1778M / 10G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:02:46.666  1778M / 10G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:02:46.820  1778M / 10G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:02:49.838  1778M / 10G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:02:50.024  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:02:50.026  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:02:50.037  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:02:50.038  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:50.063  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1 times
  0:02:50.064  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:50.457  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 1 times
  0:02:50.458  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:50.458  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:50.459  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:50.460  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 1 times
  0:02:50.460  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:50.461  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2 times
  0:02:50.462  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:50.463  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1 times
  0:02:50.463  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:02:50.464  1778M / 10G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:02:50.464  1778M / 10G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:02:50.625  1778M / 10G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:02:50.776  1778M / 10G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:02:53.752  1778M / 10G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:02:53.938  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:02:53.939  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:02:53.951  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:02:53.951  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:53.977  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:02:53.978  1778M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:54.382  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 2 times
  0:02:54.382  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:54.383  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:54.384  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:54.384  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:54.385  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:54.385  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:54.386  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:54.387  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:02:54.387  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:02:54.388  1777M / 10G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:02:54.388  1777M / 10G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:02:54.545  1777M / 10G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:02:54.701  1777M / 10G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:02:57.679  1777M / 10G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:02:57.866  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 0 times
  0:02:57.867  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:02:57.879  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:02:57.880  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:57.906  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:02:57.906  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:58.304  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 1 times
  0:02:58.305  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:58.306  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:58.306  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:58.307  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:58.307  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:58.308  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:58.309  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:58.309  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:02:58.310  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:02:58.310  1777M / 10G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:02:58.311  1777M / 10G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:02:58.469  1777M / 10G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:02:58.625  1777M / 10G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:01.614  1777M / 10G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:01.814  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 0 times
  0:03:01.816  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:01.827  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:03:01.828  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:01.854  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:03:01.854  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:02.247  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:03:02.249  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:02.249  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:02.250  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:02.251  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:02.251  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:02.252  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:02.253  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:02.253  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:03:02.254  1777M / 10G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:03:02.305  1777M / 10G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:03:02.379  1777M / 10G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:03:02.471  1775M / 10G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 74855 times
  0:03:02.482  1774M / 10G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:03:02.483  1774M / 10G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 38.9717
  0:03:02.492  1774M / 10G   INFO    General                 (simplification.cpp        : 497)     Total length = 41998588
  0:03:02.505  1774M / 10G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 2781
  0:03:02.506  1774M / 10G   INFO    General                 (simplification.cpp        : 499)     Edges: 137153
  0:03:02.506  1774M / 10G   INFO    General                 (simplification.cpp        : 500)     Vertices: 113212
  0:03:02.507  1774M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Mismatch Correction (id: mismatch_correction)
  0:03:02.507  1774M / 10G   INFO    General                 (graph_pack.cpp            :  67)   Index refill
  0:03:02.508  1774M / 10G   INFO    General                 (edge_index.hpp            : 156)   Using small index (max_id = 8156251)
  0:03:02.591  1774M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:03:03.457  1804M / 10G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 41998069 kmers, 30334440 bytes occupied (5.77825 bits per kmer).
  0:03:03.572  2129M / 10G   INFO    General                 (edge_index_builders.hpp   : 252)   Collecting edge information from graph, this takes a while.
  0:03:04.170  2129M / 10G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 23112188 kmers to process
  0:03:08.119  2129M / 10G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:03:08.121  2129M / 10G   INFO    General                 (mismatch_correction.cpp   : 400)   Collect potential mismatches
  0:03:10.523  2145M / 10G   INFO    General                 (mismatch_correction.cpp   : 192)   Total 68596 edges (out of 137153) with 2301058 potential mismatch positions (33.5451 positions per edge)
  0:03:10.529  2145M / 10G   INFO    General                 (mismatch_correction.cpp   : 402)   Potential mismatches collected
  0:03:11.693  2147M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:03:11.816  2153M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:03:11.883  2156M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:03:12.038  2157M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:03:12.053  2161M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:03:12.228  2163M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:03:12.371  2165M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:03:13.499  2167M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:03:15.841  2181M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8400000 reads
  0:03:19.761  2196M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 16800000 reads
  0:03:29.626  2227M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 33624000 reads
  0:03:43.578  2715M / 10G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 37836600 reads processed
  0:03:46.271  2313M / 10G   INFO    General                 (mismatch_correction.cpp   : 395)   All edges processed
  0:03:46.292  2132M / 10G   INFO    General                 (mismatch_correction.cpp   : 450)   Corrected 832 nucleotides
  0:03:46.294  2132M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:03:46.294  2132M / 10G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/before_rr.fasta
  0:03:46.812  2132M / 10G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/assembly_graph_after_simplification.gfa
  0:03:47.151  2132M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Paired Information Counting (id: late_pair_info_count)
  0:03:47.290  2157M / 10G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 23148926 kmers to process
  0:03:51.036  2157M / 10G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:03:51.051  2157M / 10G   INFO    General                 (pair_info_count.cpp       : 339)   Min edge length for estimation: 2781
  0:03:51.052  2157M / 10G   INFO    General                 (pair_info_count.cpp       : 411)   Mapping single reads of library #0
  0:03:51.052  2157M / 10G   INFO    General                 (pair_info_count.cpp       :  48)   Selecting usual mapper
  0:03:52.251  2159M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:03:52.320  2160M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:03:52.322  2161M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:03:52.392  2165M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:03:52.437  2166M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:03:52.762  2167M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:03:52.823  2169M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:03:53.870  2170M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:03:56.213  2172M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8400000 reads
  0:04:01.766  2183M / 10G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 17624000 reads
  0:04:09.631  2214M / 10G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 18918300 reads processed
  0:04:09.641  2214M / 10G   INFO    General                 (pair_info_count.cpp       : 413)   Total paths obtained from single reads: 380280
  0:04:09.642  2214M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Distance Estimation (id: distance_estimation)
  0:04:09.643  2214M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Repeat Resolving (id: repeat_resolving)
  0:04:09.643  2214M / 10G   INFO    General                 (repeat_resolving.cpp      :  87)   Using Path-Extend repeat resolving
  0:04:09.645  2214M / 10G   INFO    General                 (launcher.cpp              : 600)   ExSPAnder repeat resolving tool started
  0:04:09.772  2491M / 10G   INFO    General                 (launcher.cpp              : 418)   Creating main extenders, unique edge length = 2000
  0:04:09.774  2501M / 10G   INFO    General                 (launcher.cpp              : 342)   filling path container
  0:04:10.687  3902M / 10G   INFO    General                 (extenders_logic.cpp       :  47)   resolvable_repeat_length_bound set to 10000
  0:04:12.235  3916M / 10G   INFO    General                 (extenders_logic.cpp       : 543)   Using 0 paired-end libraries
  0:04:12.238  3916M / 10G   INFO    General                 (extenders_logic.cpp       : 544)   Using 0 paired-end scaffolding libraries
  0:04:12.240  3916M / 10G   INFO    General                 (extenders_logic.cpp       : 545)   Using 1 single read library
  0:04:12.241  3916M / 10G   INFO    General                 (launcher.cpp              : 447)   Total number of extenders is 1
  0:04:12.243  3916M / 10G   INFO    General                 (path_extenders.cpp        :  36)   Processed 0 paths from 67914 (0%)
  0:04:13.343  3916M / 10G   INFO    General                 (path_extenders.cpp        :  34)   Processed 128 paths from 67914 (0%)
  0:04:14.672  3917M / 10G   INFO    General                 (path_extenders.cpp        :  34)   Processed 256 paths from 67914 (0%)
  0:04:17.197  3918M / 10G   INFO    General                 (path_extenders.cpp        :  34)   Processed 512 paths from 67914 (0%)
  0:04:17.689  3921M / 10G   INFO    General                 (path_extenders.cpp        :  34)   Processed 1024 paths from 67914 (1%)
  0:04:20.836  3925M / 10G   INFO    General                 (path_extenders.cpp        :  34)   Processed 2048 paths from 67914 (3%)
  0:04:24.466  3934M / 10G   INFO    General                 (path_extenders.cpp        :  34)   Processed 4096 paths from 67914 (6%)
  0:04:27.067  3946M / 10G   INFO    General                 (path_extenders.cpp        :  36)   Processed 6792 paths from 67914 (10%)
  0:04:29.121  3951M / 10G   INFO    General                 (path_extenders.cpp        :  34)   Processed 8192 paths from 67914 (12%)
  0:04:33.969  3973M / 10G   INFO    General                 (path_extenders.cpp        :  36)   Processed 13584 paths from 67914 (20%)
  0:04:35.217  3985M / 10G   INFO    General                 (path_extenders.cpp        :  34)   Processed 16384 paths from 67914 (24%)
  0:04:37.263  4002M / 10G   INFO    General                 (path_extenders.cpp        :  36)   Processed 20376 paths from 67914 (30%)
  0:04:40.229  4034M / 10G   INFO    General                 (path_extenders.cpp        :  36)   Processed 27168 paths from 67914 (40%)
  0:04:41.736  4061M / 10G   INFO    General                 (path_extenders.cpp        :  34)   Processed 32768 paths from 67914 (48%)
  0:04:42.101  4067M / 10G   INFO    General                 (path_extenders.cpp        :  36)   Processed 33960 paths from 67914 (50%)
  0:04:46.370  4101M / 10G   INFO    General                 (path_extenders.cpp        :  36)   Processed 40752 paths from 67914 (60%)
  0:04:56.725  4132M / 10G   INFO    General                 (path_extenders.cpp        :  36)   Processed 47544 paths from 67914 (70%)
  0:05:02.575  4150M / 10G   INFO    General                 (path_extenders.cpp        :  36)   Processed 54336 paths from 67914 (80%)
  0:05:03.654  4154M / 10G   INFO    General                 (path_extenders.cpp        :  36)   Processed 61128 paths from 67914 (90%)
  0:05:04.184  4156M / 10G   INFO    General                 (path_extenders.cpp        :  34)   Processed 65536 paths from 67914 (96%)
  0:05:04.376  4157M / 10G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:05:04.379  4157M / 10G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:05:04.504  4118M / 10G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:05:04.506  4118M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:05:04.507  4118M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:05:04.526  4118M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:05:04.528  4118M / 10G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:05:04.715  4122M / 10G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:05:04.920  4123M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:05:05.099  4313M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:05:05.326  4108M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:05:05.382  4102M / 10G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:05:05.388  4102M / 10G   INFO    General                 (launcher.cpp              : 454)   Closing gaps in paths
  0:05:05.492  4270M / 10G   INFO    General                 (launcher.cpp              : 484)   Gap closing completed
  0:05:05.522  4283M / 10G   INFO    General                 (launcher.cpp              : 302)   Traversing tandem repeats
  0:05:05.702  4284M / 10G   INFO    General                 (launcher.cpp              : 312)   Traversed 1338 loops
  0:05:05.705  4284M / 10G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:05:05.706  4284M / 10G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:05:05.732  4277M / 10G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:05:05.736  4277M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:05:05.738  4277M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:05:05.761  4277M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:05:05.764  4277M / 10G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:05:05.817  4277M / 10G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:05:05.869  4277M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:05:05.890  4298M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:05:05.924  4279M / 10G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:05:05.979  4281M / 10G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:05:05.982  4281M / 10G   INFO    General                 (launcher.cpp              : 664)   ExSPAnder repeat resolving tool finished
  0:05:06.229  2380M / 10G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:05:06.307  2380M / 10G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/before_rr.fasta
  0:05:06.799  2380M / 10G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/assembly_graph_with_scaffolds.gfa
  0:05:07.008  2380M / 10G   INFO    General                 (contig_output_stage.cpp   : 165)   Outputting FastG graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/assembly_graph.fastg
  0:05:07.852  2380M / 10G   INFO    General                 (contig_output_stage.cpp   : 196)   Breaking scaffolds
  0:05:08.126  2628M / 10G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/final_contigs.fasta
  0:05:08.489  2628M / 10G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/final_contigs.paths
  0:05:08.754  2433M / 10G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/scaffolds.fasta
  0:05:09.000  2433M / 10G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/scaffolds.paths
  0:05:09.106  2433M / 10G   INFO    General                 (contig_output_stage.cpp   : 111)   Populating GFA with scaffold paths
  0:05:09.351  2380M / 10G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:05:11.267     1M / 10G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 5 minutes 11 seconds

===== K55 finished. 


===== Copy files started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/copy_files.py /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/final_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/K55/final_contigs.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/contigs.paths


===== Copy files finished. 


===== Assembling finished. 


===== Breaking scaffolds started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/breaking_scaffolds_script.py --result_scaffolds_filename /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/scaffolds.fasta --misc_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/misc --threshold_for_breaking_scaffolds 3


===== Breaking scaffolds finished. 


===== Terminate started. 


===== Terminate finished. 

 * Corrected reads are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/corrected/
 * Assembled contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/contigs.fasta
 * Assembled scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/scaffolds.fasta
 * Paths in the assembly graph corresponding to the contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/contigs.paths
 * Paths in the assembly graph corresponding to the scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/scaffolds.paths
 * Assembly graph is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/assembly_graph.fastg
 * Assembly graph in GFA format is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/assembly_graph_with_scaffolds.gfa

======= SPAdes pipeline finished.

SPAdes log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507994/spades.log

Thank you for using SPAdes!
SPAdes assembly completed for 1507994
Assembly completed for sample: 1507994
