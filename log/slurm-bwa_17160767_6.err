INFO:    Converting OCI blobs to SIF format
INFO:    Starting build...
Copying blob sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
Copying blob sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
Copying blob sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying blob sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
Copying blob sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
Copying blob sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
Copying blob sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
Copying blob sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
Copying blob sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
Copying config sha256:1a18e5b322db0c21b435c9bcf96d7aae8ee9294648210e1b1929be9505d910a2
time="2025-04-28T16:14:16-07:00" level=warning msg="Compressor for blob with digest sha256:1a18e5b322db0c21b435c9bcf96d7aae8ee9294648210e1b1929be9505d910a2 previously recorded as gzip, now uncompressed"
Writing manifest to image destination
2025/04/28 16:14:16  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:16  info unpack layer: sha256:b0dc45cd432d14fb6df7d3239dc15d09c63906f8e7bfd373a4647b107fc3746c
2025/04/28 16:14:16  warn rootless{dev/console} creating empty file in place of device 5:1
2025/04/28 16:14:16  info unpack layer: sha256:9466b3513669459396338a74673ede0166f534ab64923f66ecca58176d1ffe5e
2025/04/28 16:14:16  info unpack layer: sha256:ddd482ea7b54727ff2b6748188290b75f6441ba4091d15a5e62d2a0ed47c81dd
2025/04/28 16:14:16  info unpack layer: sha256:4d69f833b9d8db2f02cc784f9bab7317317e4062129cafe65660c9eb2cd1c115
2025/04/28 16:14:16  info unpack layer: sha256:e7c454e5167ddab5101c53d104c90c0a037c3ee3930b9a74a14da40c852134cc
2025/04/28 16:14:16  info unpack layer: sha256:e38092b005c08dab4ae80615d29944107dc3d07307e37c4558914998e0e61827
2025/04/28 16:14:16  info unpack layer: sha256:4ca545ee6d5db5c1170386eeb39b2ffe3bd46e5d4a73a9acbebc805f19607eb3
2025/04/28 16:14:16  info unpack layer: sha256:f879b42dfe2b253544b0dc6834eff097a2a994cecda76b3c2950465fef9bfdfc
2025/04/28 16:14:16  info unpack layer: sha256:f815f3d4557eeba3f22dbc26c904d6aabdf0cfc53bdce3a5ebc600fb80f59e77
INFO:    Creating SIF file...
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[bwa_index] Pack FASTA... 0.37 sec
[bwa_index] Construct BWT for the packed sequence...
[BWTIncCreate] textLength=90465436, availableWord=18365468
[BWTIncConstructFromPacked] 10 iterations done. 30294060 characters processed.
[BWTIncConstructFromPacked] 20 iterations done. 55964236 characters processed.
[BWTIncConstructFromPacked] 30 iterations done. 78775932 characters processed.
[bwt_gen] Finished constructing BWT in 36 iterations.
[bwa_index] 12.33 seconds elapse.
[bwa_index] Update BWT... 0.20 sec
[bwa_index] Pack forward-only FASTA... 0.15 sec
[bwa_index] Construct SA from BWT and Occ... 6.28 sec
[main] Version: 0.7.17-r1188
[main] CMD: /usr/local/bin/bwa index 02mapping/1507996/temp/scaffolds.fasta
[main] Real time: 24.269 sec; CPU: 19.337 sec
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 1084804 sequences (160000141 bp)...
[M::process] read 1077570 sequences (160000270 bp)...
[M::mem_process_seqs] Processed 1084804 reads in 340.650 CPU sec, 22.777 real sec
[M::process] read 1086270 sequences (160000009 bp)...
[M::mem_process_seqs] Processed 1077570 reads in 245.152 CPU sec, 16.330 real sec
[M::mem_process_seqs] Processed 1086270 reads in 176.594 CPU sec, 12.097 real sec
[M::process] read 1083048 sequences (160000032 bp)...
[M::process] read 1083818 sequences (160000056 bp)...
[M::mem_process_seqs] Processed 1083048 reads in 186.975 CPU sec, 12.451 real sec
[M::mem_process_seqs] Processed 1083818 reads in 149.379 CPU sec, 10.208 real sec
[M::process] read 1102248 sequences (160000038 bp)...
[M::process] read 1081050 sequences (160000040 bp)...
[M::mem_process_seqs] Processed 1102248 reads in 158.328 CPU sec, 10.666 real sec
[M::mem_process_seqs] Processed 1081050 reads in 131.466 CPU sec, 9.377 real sec
[M::process] read 1081336 sequences (160000161 bp)...
[M::process] read 1080042 sequences (160000255 bp)...
[M::mem_process_seqs] Processed 1081336 reads in 110.008 CPU sec, 7.347 real sec
[M::mem_process_seqs] Processed 1080042 reads in 92.886 CPU sec, 6.569 real sec
[M::process] read 1078608 sequences (160000170 bp)...
[M::mem_process_seqs] Processed 1078608 reads in 93.300 CPU sec, 6.274 real sec
[M::process] read 1077472 sequences (160000036 bp)...
[M::mem_process_seqs] Processed 1077472 reads in 84.614 CPU sec, 5.960 real sec
[M::process] read 1079986 sequences (160000179 bp)...
[M::mem_process_seqs] Processed 1079986 reads in 81.489 CPU sec, 5.505 real sec
[M::process] read 1076892 sequences (160000124 bp)...
[M::mem_process_seqs] Processed 1076892 reads in 76.590 CPU sec, 5.149 real sec
[M::process] read 1079644 sequences (160000288 bp)...
[M::mem_process_seqs] Processed 1079644 reads in 79.448 CPU sec, 5.345 real sec
[M::process] read 1083856 sequences (160000229 bp)...
[M::mem_process_seqs] Processed 1083856 reads in 79.048 CPU sec, 5.312 real sec
[M::process] read 1082094 sequences (160000160 bp)...
[M::mem_process_seqs] Processed 1082094 reads in 83.441 CPU sec, 5.618 real sec
[M::process] read 1087104 sequences (160000181 bp)...
[M::mem_process_seqs] Processed 1087104 reads in 96.056 CPU sec, 6.427 real sec
[M::process] read 1087516 sequences (160000116 bp)...
[M::process] read 826988 sequences (114250877 bp)...
[M::mem_process_seqs] Processed 1087516 reads in 136.927 CPU sec, 9.148 real sec
[M::mem_process_seqs] Processed 826988 reads in 115.703 CPU sec, 7.908 real sec
[main] Version: 0.7.17-r1188
[main] CMD: bwa mem -t 16 02mapping/1507996/temp/scaffolds.fasta 00data/readsf/1507996.anqdpht.fastq.gz
[main] Real time: 193.917 sec; CPU: 2533.382 sec
[bam_sort_core] merging from 0 files and 16 in-memory blocks...
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
WARNING: Skipping mount /var/lib/apptainer/mnt/session/etc/resolv.conf [files]: /etc/resolv.conf doesn't exist in container
