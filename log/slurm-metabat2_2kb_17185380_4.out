Decompressing scaffold file for 1507994...
Generating depth file for 1507994...
Running MetaBAT2 for 1507994 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 123. with random seed=1745943201
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [32.6Gb / 503.5Gb]
[00:00:00] Parsing assembly file [32.6Gb / 503.5Gb]
[00:00:00] ... processed 13 seqs, 13 long (>=2000), 0 short (>=1000) 1.0% (488715 of 46693117), ETA 0:00:02     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 29 seqs, 29 long (>=2000), 0 short (>=1000) 2.0% (943416 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 48 seqs, 48 long (>=2000), 0 short (>=1000) 3.0% (1415680 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 69 seqs, 69 long (>=2000), 0 short (>=1000) 4.0% (1874622 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 92 seqs, 92 long (>=2000), 0 short (>=1000) 5.0% (2336946 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 117 seqs, 117 long (>=2000), 0 short (>=1000) 6.0% (2812664 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 143 seqs, 143 long (>=2000), 0 short (>=1000) 7.0% (3278908 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 171 seqs, 171 long (>=2000), 0 short (>=1000) 8.0% (3744915 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 200 seqs, 200 long (>=2000), 0 short (>=1000) 9.0% (4205320 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 231 seqs, 231 long (>=2000), 0 short (>=1000) 10.0% (4678058 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 263 seqs, 263 long (>=2000), 0 short (>=1000) 11.0% (5147896 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 296 seqs, 296 long (>=2000), 0 short (>=1000) 12.0% (5612139 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 330 seqs, 330 long (>=2000), 0 short (>=1000) 13.0% (6071635 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 366 seqs, 366 long (>=2000), 0 short (>=1000) 14.0% (6537456 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 404 seqs, 404 long (>=2000), 0 short (>=1000) 15.0% (7009135 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 443 seqs, 443 long (>=2000), 0 short (>=1000) 16.0% (7474549 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 484 seqs, 484 long (>=2000), 0 short (>=1000) 17.0% (7945810 of 46693117), ETA 0:00:01     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 526 seqs, 526 long (>=2000), 0 short (>=1000) 18.0% (8410319 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 570 seqs, 570 long (>=2000), 0 short (>=1000) 19.0% (8877707 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 615 seqs, 615 long (>=2000), 0 short (>=1000) 20.0% (9342853 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 662 seqs, 662 long (>=2000), 0 short (>=1000) 21.0% (9815381 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 709 seqs, 709 long (>=2000), 0 short (>=1000) 22.0% (10273725 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 759 seqs, 759 long (>=2000), 0 short (>=1000) 23.0% (10744030 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 810 seqs, 810 long (>=2000), 0 short (>=1000) 24.0% (11206925 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 863 seqs, 863 long (>=2000), 0 short (>=1000) 25.0% (11674038 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 918 seqs, 918 long (>=2000), 0 short (>=1000) 26.0% (12142027 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 975 seqs, 975 long (>=2000), 0 short (>=1000) 27.0% (12609747 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1034 seqs, 1034 long (>=2000), 0 short (>=1000) 28.0% (13078819 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1094 seqs, 1094 long (>=2000), 0 short (>=1000) 29.0% (13541517 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1157 seqs, 1157 long (>=2000), 0 short (>=1000) 30.0% (14012596 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1221 seqs, 1221 long (>=2000), 0 short (>=1000) 31.0% (14475713 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1288 seqs, 1288 long (>=2000), 0 short (>=1000) 32.0% (14946375 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1356 seqs, 1356 long (>=2000), 0 short (>=1000) 33.0% (15411567 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1426 seqs, 1426 long (>=2000), 0 short (>=1000) 34.0% (15877791 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1498 seqs, 1498 long (>=2000), 0 short (>=1000) 35.0% (16342666 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1573 seqs, 1573 long (>=2000), 0 short (>=1000) 36.0% (16810297 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1651 seqs, 1651 long (>=2000), 0 short (>=1000) 37.0% (17281709 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1731 seqs, 1731 long (>=2000), 0 short (>=1000) 38.0% (17748704 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1813 seqs, 1813 long (>=2000), 0 short (>=1000) 39.0% (18213471 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1898 seqs, 1898 long (>=2000), 0 short (>=1000) 40.0% (18681633 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 1985 seqs, 1985 long (>=2000), 0 short (>=1000) 41.0% (19145533 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 2076 seqs, 2076 long (>=2000), 0 short (>=1000) 42.0% (19613717 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 2170 seqs, 2170 long (>=2000), 0 short (>=1000) 43.0% (20081008 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 2267 seqs, 2267 long (>=2000), 0 short (>=1000) 44.0% (20546571 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 2368 seqs, 2368 long (>=2000), 0 short (>=1000) 45.0% (21013916 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 2473 seqs, 2473 long (>=2000), 0 short (>=1000) 46.0% (21483052 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 2581 seqs, 2581 long (>=2000), 0 short (>=1000) 47.0% (21948254 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 2694 seqs, 2694 long (>=2000), 0 short (>=1000) 48.0% (22416329 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 2810 seqs, 2810 long (>=2000), 0 short (>=1000) 49.0% (22879952 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 2931 seqs, 2931 long (>=2000), 0 short (>=1000) 50.0% (23346822 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 3057 seqs, 3057 long (>=2000), 0 short (>=1000) 51.0% (23815326 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 3187 seqs, 3187 long (>=2000), 0 short (>=1000) 52.0% (24281481 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 3323 seqs, 3323 long (>=2000), 0 short (>=1000) 53.0% (24749818 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 3464 seqs, 3464 long (>=2000), 0 short (>=1000) 54.0% (25215549 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 3612 seqs, 3612 long (>=2000), 0 short (>=1000) 55.0% (25683284 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 3767 seqs, 3767 long (>=2000), 0 short (>=1000) 56.0% (26150552 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 3929 seqs, 3929 long (>=2000), 0 short (>=1000) 57.0% (26617612 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 4099 seqs, 4099 long (>=2000), 0 short (>=1000) 58.0% (27083087 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 4279 seqs, 4279 long (>=2000), 0 short (>=1000) 59.0% (27549136 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 4471 seqs, 4471 long (>=2000), 0 short (>=1000) 60.0% (28017626 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 4673 seqs, 4673 long (>=2000), 0 short (>=1000) 61.0% (28484135 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 4888 seqs, 4888 long (>=2000), 0 short (>=1000) 62.0% (28951280 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 5117 seqs, 5074 long (>=2000), 43 short (>=1000) 63.0% (29418547 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 5361 seqs, 5074 long (>=2000), 287 short (>=1000) 64.0% (29884622 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 5623 seqs, 5074 long (>=2000), 549 short (>=1000) 65.0% (30351280 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 5906 seqs, 5074 long (>=2000), 832 short (>=1000) 66.0% (30818200 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 6209 seqs, 5074 long (>=2000), 1135 short (>=1000) 67.0% (31285366 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 6532 seqs, 5074 long (>=2000), 1458 short (>=1000) 68.0% (31751460 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 6879 seqs, 5074 long (>=2000), 1805 short (>=1000) 69.0% (32219127 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 7250 seqs, 5074 long (>=2000), 2176 short (>=1000) 70.0% (32686049 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 7648 seqs, 5074 long (>=2000), 2574 short (>=1000) 71.0% (33152569 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 8075 seqs, 5074 long (>=2000), 3001 short (>=1000) 72.0% (33619838 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 8531 seqs, 5074 long (>=2000), 3391 short (>=1000) 73.0% (34086109 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 9017 seqs, 5074 long (>=2000), 3391 short (>=1000) 74.0% (34553068 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 9538 seqs, 5074 long (>=2000), 3391 short (>=1000) 75.0% (35020727 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 10093 seqs, 5074 long (>=2000), 3391 short (>=1000) 76.0% (35486866 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 10689 seqs, 5074 long (>=2000), 3391 short (>=1000) 77.0% (35954483 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 11324 seqs, 5074 long (>=2000), 3391 short (>=1000) 78.0% (36420754 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 12007 seqs, 5074 long (>=2000), 3391 short (>=1000) 79.0% (36887906 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 12743 seqs, 5074 long (>=2000), 3391 short (>=1000) 80.0% (37354980 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 13545 seqs, 5074 long (>=2000), 3391 short (>=1000) 81.0% (37821899 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 14417 seqs, 5074 long (>=2000), 3391 short (>=1000) 82.0% (38288681 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 15371 seqs, 5074 long (>=2000), 3391 short (>=1000) 83.0% (38755750 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 16411 seqs, 5074 long (>=2000), 3391 short (>=1000) 84.0% (39222322 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 17549 seqs, 5074 long (>=2000), 3391 short (>=1000) 85.0% (39689406 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 18796 seqs, 5074 long (>=2000), 3391 short (>=1000) 86.0% (40156469 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 20158 seqs, 5074 long (>=2000), 3391 short (>=1000) 87.0% (40623184 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 21654 seqs, 5074 long (>=2000), 3391 short (>=1000) 88.0% (41090308 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 23302 seqs, 5074 long (>=2000), 3391 short (>=1000) 89.0% (41557090 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 25104 seqs, 5074 long (>=2000), 3391 short (>=1000) 90.0% (42023934 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 27068 seqs, 5074 long (>=2000), 3391 short (>=1000) 91.0% (42490823 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 29241 seqs, 5074 long (>=2000), 3391 short (>=1000) 92.0% (42957857 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 31780 seqs, 5074 long (>=2000), 3391 short (>=1000) 93.0% (43424811 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 34765 seqs, 5074 long (>=2000), 3391 short (>=1000) 94.0% (43891667 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] ... processed 38950 seqs, 5074 long (>=2000), 3391 short (>=1000) 95.0% (44358575 of 46693117), ETA 0:00:00     [32.6Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 5074, and small contigs >= 1000 bp are 3391                                                                  
[00:00:00] Allocating 5074 contigs by 1 samples abundances [32.6Gb / 503.5Gb]
[00:00:00] Allocating 5074 contigs by 1 samples variances [32.6Gb / 503.5Gb]
[00:00:00] Allocating 3391 small contigs by 1 samples abundances [32.6Gb / 503.5Gb]
[00:00:00] Reading 0.002377Gb abundance file [32.6Gb / 503.5Gb]
[00:00:00] ... processed 389 lines 389 contigs and 0 short contigs 1.0% (25581 of 2551906), ETA 0:00:01     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 781 lines 781 contigs and 0 short contigs 2.0% (51055 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 1179 lines 1179 contigs and 0 short contigs 3.0% (76575 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 1575 lines 1575 contigs and 0 short contigs 4.0% (102134 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 1970 lines 1970 contigs and 0 short contigs 5.0% (127632 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 2365 lines 2365 contigs and 0 short contigs 6.0% (153147 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 2760 lines 2760 contigs and 0 short contigs 7.0% (178649 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 3156 lines 3156 contigs and 0 short contigs 8.0% (204187 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 3551 lines 3551 contigs and 0 short contigs 9.0% (229680 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 3947 lines 3947 contigs and 0 short contigs 10.0% (255207 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 4343 lines 4343 contigs and 0 short contigs 11.0% (280746 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 4739 lines 4739 contigs and 0 short contigs 12.0% (306277 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 5135 lines 5074 contigs and 61 short contigs 13.0% (331764 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 5532 lines 5074 contigs and 458 short contigs 14.0% (357313 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 5929 lines 5074 contigs and 855 short contigs 15.0% (382857 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 6325 lines 5074 contigs and 1251 short contigs 16.0% (408337 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 6723 lines 5074 contigs and 1649 short contigs 17.0% (433883 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 7119 lines 5074 contigs and 2045 short contigs 18.0% (459369 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 7516 lines 5074 contigs and 2442 short contigs 19.0% (484904 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 7914 lines 5074 contigs and 2840 short contigs 20.0% (510427 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8311 lines 5074 contigs and 3237 short contigs 21.0% (535965 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 22.0% (561495 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 23.0% (587016 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 24.0% (612481 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 25.0% (638022 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 26.0% (663579 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 27.0% (689052 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 28.0% (714612 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 29.0% (740097 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 30.0% (765634 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 31.0% (791136 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 32.0% (816656 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 33.0% (842197 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 34.0% (867704 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 35.0% (893260 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 36.0% (918728 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 37.0% (944272 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 38.0% (969775 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 39.0% (995333 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 40.0% (1020819 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 41.0% (1046351 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 42.0% (1071860 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 43.0% (1097388 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 44.0% (1122924 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 45.0% (1148424 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 46.0% (1173939 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 47.0% (1199502 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 48.0% (1224968 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 49.0% (1250534 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 50.0% (1276051 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 51.0% (1301523 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 52.0% (1327086 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 53.0% (1352616 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 54.0% (1378124 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 55.0% (1403649 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 56.0% (1429170 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 57.0% (1454682 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 58.0% (1480193 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 59.0% (1505741 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 60.0% (1531210 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 61.0% (1556761 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 62.0% (1582252 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 63.0% (1607772 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 64.0% (1633315 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 65.0% (1658855 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 66.0% (1684333 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 67.0% (1709855 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 68.0% (1735385 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 69.0% (1760897 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 70.0% (1786431 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 71.0% (1811945 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 72.0% (1837465 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 73.0% (1862999 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 74.0% (1888492 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 75.0% (1914026 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 76.0% (1939544 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 77.0% (1965045 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 78.0% (1990601 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 79.0% (2016110 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 80.0% (2041651 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 81.0% (2067138 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 82.0% (2092691 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 83.0% (2118183 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 84.0% (2143695 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 85.0% (2169228 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 86.0% (2194741 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 87.0% (2220268 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 88.0% (2245769 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 89.0% (2271302 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 90.0% (2296828 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 91.0% (2322321 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 92.0% (2347890 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 93.0% (2373391 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 94.0% (2398901 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 95.0% (2424459 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 96.0% (2449953 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 97.0% (2475474 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 98.0% (2500996 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 99.0% (2526516 of 2551906), ETA 0:00:00     [32.6Gb / 503.5Gb]                 
[00:00:00] Finished reading 40836 contigs and 1 coverages from 03bins/metabat2_2kb/1507994/temp/1507994.depth.txt [32.6Gb / 503.5Gb]. Ignored 32371 too small contigs.                                     
[00:00:00] Number of target contigs: 5074 of large (>= 2000) and 3391 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 5074
[00:00:00] Allocated memory for TNF [32.6Gb / 503.5Gb]
[00:00:00] Calculating TNF 100.0% (5072 of 5074), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [32.6Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.1% (56 of 5074), ETA 0:00:02                   
[00:00:00] ... processing TNF matrix 2.0% (102 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 5.4% (275 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 6.1% (309 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 9.2% (466 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 10.1% (512 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 13.6% (689 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 14.2% (719 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 15.2% (771 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 18.1% (918 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 21.3% (1083 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 22.1% (1123 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 25.5% (1294 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.1% (1326 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.6% (1503 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 30.2% (1533 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.2% (1734 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 35.2% (1785 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.0% (1980 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.3% (1995 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 40.2% (2041 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.9% (2225 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 44.3% (2246 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 45.3% (2300 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.0% (2434 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.4% (2455 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.3% (2500 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.2% (2650 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.4% (2658 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.3% (2706 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.0% (2843 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.3% (2858 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.5% (3020 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 60.3% (3062 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 62.3% (3163 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 63.5% (3220 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 66.6% (3377 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 67.4% (3420 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 71.1% (3607 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 71.4% (3623 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 72.4% (3673 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 76.2% (3865 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 76.4% (3879 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 77.5% (3932 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 80.5% (4085 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 81.5% (4133 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 82.4% (4182 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 84.7% (4298 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 85.4% (4335 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 86.5% (4387 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 88.9% (4509 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 89.5% (4541 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 90.5% (4592 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 92.5% (4695 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 93.6% (4747 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 95.9% (4866 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 96.5% (4898 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 98.5% (4997 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 98.6% (5001 of 5074), ETA 0:00:00                   
[00:00:01] ... processing TNF matrix 99.7% (5057 of 5074), ETA 0:00:00                   
[00:00:01] Finding cutoff p=999 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=999 1.2% (63 of 5074), ETA 0:00:02                              
[00:00:01] ... finding cutoff p=999 3.4% (171 of 5074), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=999 4.3% (217 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 5.7% (290 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 6.8% (347 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 7.1% (359 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 8.1% (410 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 9.0% (459 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 10.1% (511 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 11.1% (561 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 12.1% (612 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 13.2% (668 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 14.2% (722 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 15.3% (774 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 16.1% (817 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 17.1% (868 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 18.2% (924 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 19.3% (980 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 20.2% (1027 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 21.2% (1074 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 22.1% (1123 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 23.1% (1174 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 24.2% (1226 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 25.2% (1277 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 26.1% (1326 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 27.2% (1379 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 28.1% (1428 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 29.2% (1481 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 30.2% (1531 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=999 31.2% (1581 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.9; 0 / 5074 (P = 0.00%) round 1]               
[00:00:01] Finding cutoff p=998 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=998 18.2% (924 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 19.6% (992 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 20.5% (1040 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 21.3% (1083 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 22.3% (1129 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 23.2% (1179 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 24.5% (1242 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 25.9% (1316 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 28.2% (1430 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 29.2% (1481 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 30.4% (1540 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 31.4% (1594 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 32.2% (1636 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 33.4% (1696 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 34.4% (1747 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 35.2% (1786 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 36.2% (1836 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 37.2% (1890 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 38.2% (1938 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 39.3% (1992 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 40.2% (2041 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 41.2% (2091 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 42.3% (2147 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 43.3% (2195 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 44.3% (2249 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 45.3% (2297 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 46.2% (2346 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 47.3% (2398 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 48.3% (2450 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 49.3% (2499 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 50.3% (2550 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 51.3% (2605 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 52.3% (2654 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 53.3% (2704 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 54.3% (2755 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 55.4% (2809 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 56.4% (2860 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 57.4% (2910 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 58.4% (2963 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 59.3% (3010 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 60.4% (3065 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 61.4% (3113 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 62.3% (3162 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 63.4% (3218 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=998 64.4% (3268 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.8; 0 / 5074 (P = 0.00%) round 2]               
[00:00:01] Finding cutoff p=997 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=997 12.5% (634 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 13.3% (673 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 14.2% (719 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 15.1% (767 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 16.2% (820 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 17.2% (872 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 18.2% (926 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 19.2% (976 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 20.1% (1022 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 21.2% (1078 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 22.2% (1125 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 23.2% (1178 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 24.2% (1229 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 25.1% (1276 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 26.2% (1329 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 27.2% (1380 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 28.2% (1430 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 29.2% (1481 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 30.2% (1532 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 31.2% (1584 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 32.2% (1635 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 33.2% (1685 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 34.2% (1736 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 35.3% (1789 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 36.2% (1836 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 37.2% (1887 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 38.2% (1938 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 39.2% (1989 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 40.2% (2042 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 41.2% (2093 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 42.3% (2145 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 43.3% (2195 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 44.3% (2247 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 45.2% (2295 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=997 46.3% (2347 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.7; 0 / 5074 (P = 0.00%) round 3]               
[00:00:01] Finding cutoff p=994 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=994 1.3% (67 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 2.1% (107 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 3.4% (170 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 4.2% (211 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 5.5% (278 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 6.4% (325 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 7.2% (364 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 8.1% (411 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 9.5% (484 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 10.3% (522 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 11.1% (563 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 12.5% (632 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 13.3% (673 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 14.1% (715 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 15.4% (782 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 16.2% (821 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 17.5% (887 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 18.2% (926 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 19.4% (985 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 20.3% (1028 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 21.7% (1101 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 23.2% (1179 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 24.7% (1253 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 25.6% (1297 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 26.4% (1341 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 27.3% (1383 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 28.4% (1443 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 29.6% (1504 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 30.4% (1540 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 31.3% (1589 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 32.2% (1635 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 33.2% (1683 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 34.3% (1742 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 35.3% (1791 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 36.2% (1836 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 37.5% (1904 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 38.5% (1956 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 39.5% (2005 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 40.4% (2050 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 41.3% (2097 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 42.3% (2146 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 43.3% (2197 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 64.7% (3285 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 65.4% (3316 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 66.4% (3367 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 67.3% (3417 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 68.4% (3470 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 69.4% (3521 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 70.4% (3572 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 71.4% (3622 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 72.4% (3673 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 73.4% (3723 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 74.4% (3775 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 75.4% (3825 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 76.4% (3877 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 77.4% (3928 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 78.4% (3979 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 79.4% (4030 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=994 80.4% (4080 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.4; 61 / 5074 (P = 1.20%) round 4]               
[00:00:01] Finding cutoff p=991 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=991 1.1% (55 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 2.9% (147 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 4.0% (205 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 5.1% (259 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 6.2% (317 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 7.4% (378 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 8.9% (453 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 11.0% (556 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 11.5% (582 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 12.3% (624 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 13.1% (666 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 14.3% (725 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 15.1% (767 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 16.3% (827 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 17.1% (869 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 18.3% (929 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 19.1% (971 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 20.3% (1032 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 21.3% (1080 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 22.4% (1139 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 23.3% (1183 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 24.2% (1227 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 25.3% (1285 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 26.2% (1330 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 27.4% (1390 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 28.2% (1431 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 29.4% (1493 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 30.3% (1537 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 31.2% (1582 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 32.3% (1641 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 33.3% (1692 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 34.2% (1737 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 35.2% (1785 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 36.3% (1842 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 37.2% (1889 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 38.5% (1951 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 39.4% (1999 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 40.4% (2048 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 41.3% (2095 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 42.4% (2149 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 43.3% (2195 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 44.4% (2252 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 45.4% (2302 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 46.4% (2355 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 47.4% (2406 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 48.4% (2456 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 49.3% (2499 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 50.3% (2552 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 51.3% (2604 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 52.3% (2656 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 53.4% (2707 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 54.3% (2757 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 55.3% (2807 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 56.3% (2857 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 57.3% (2907 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 58.3% (2959 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 59.3% (3009 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=991 60.3% (3060 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 99.1; 228 / 5074 (P = 4.49%) round 5]               
[00:00:01] Finding cutoff p=988 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=988 16.4% (833 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 17.2% (874 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 18.4% (934 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 19.2% (976 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 20.4% (1036 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 21.1% (1073 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 22.2% (1124 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 23.2% (1176 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 24.4% (1236 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 25.3% (1282 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 26.2% (1329 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 27.4% (1392 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 28.3% (1438 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 29.2% (1481 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 30.2% (1533 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 31.2% (1583 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 32.4% (1645 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 33.4% (1694 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 34.4% (1744 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 35.3% (1793 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 36.4% (1845 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 37.4% (1896 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 38.4% (1946 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 39.2% (1990 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 40.2% (2040 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 41.5% (2104 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 42.2% (2142 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 43.3% (2197 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 44.3% (2247 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 45.3% (2297 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 46.4% (2352 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 47.3% (2398 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 48.4% (2457 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 49.4% (2505 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 50.4% (2555 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 51.3% (2603 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 52.3% (2654 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 53.3% (2704 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 54.5% (2763 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 55.4% (2812 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 56.4% (2864 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 57.4% (2915 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 58.3% (2959 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 59.4% (3012 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 60.4% (3064 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 61.3% (3112 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 62.3% (3163 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 63.4% (3215 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 64.3% (3264 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 65.4% (3317 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 66.3% (3366 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 67.3% (3417 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 68.4% (3470 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 69.4% (3522 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 70.5% (3578 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 71.5% (3627 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 72.7% (3690 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 74.2% (3764 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=988 74.4% (3774 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 98.8; 408 / 5074 (P = 8.04%) round 6]               
[00:00:01] Finding cutoff p=985 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=985 1.4% (71 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 2.3% (118 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 3.1% (159 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 4.7% (236 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 5.6% (285 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 6.7% (340 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 8.2% (418 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 9.6% (488 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 10.2% (518 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 11.2% (567 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 12.2% (619 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 13.2% (669 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 14.2% (720 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 15.2% (771 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 16.2% (821 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 17.2% (873 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 18.2% (925 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 19.3% (977 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 20.1% (1020 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 21.1% (1073 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 22.2% (1125 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 23.2% (1178 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 24.2% (1230 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 25.3% (1282 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 26.3% (1333 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 27.3% (1383 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 28.2% (1432 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 29.3% (1485 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 30.3% (1535 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 31.3% (1587 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 32.2% (1632 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 33.2% (1684 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 34.2% (1737 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 35.3% (1792 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 36.2% (1838 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 37.3% (1892 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 38.2% (1938 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 39.2% (1989 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 40.3% (2046 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 41.2% (2093 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 42.4% (2151 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 43.3% (2199 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 44.3% (2247 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 45.4% (2302 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 46.5% (2357 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 47.3% (2401 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 49.4% (2505 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 50.3% (2553 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 51.3% (2604 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 52.4% (2657 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 53.3% (2704 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 54.4% (2758 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 55.3% (2807 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 56.3% (2856 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 57.3% (2907 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 58.4% (2961 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=985 59.3% (3009 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 98.5; 598 / 5074 (P = 11.79%) round 7]               
[00:00:01] Finding cutoff p=981 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=981 1.0% (51 of 5074), ETA 0:00:01                              
[00:00:01] ... finding cutoff p=981 2.1% (105 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 3.3% (168 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 4.3% (218 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 5.2% (264 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 6.1% (311 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 7.1% (359 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 8.4% (424 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 9.3% (472 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 10.2% (520 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 11.2% (568 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 12.2% (621 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 13.2% (672 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 14.2% (723 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 15.2% (772 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 16.2% (820 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 17.2% (874 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 18.2% (926 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 19.3% (979 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 20.3% (1032 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 21.4% (1084 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 22.4% (1138 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 23.1% (1173 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 24.2% (1226 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 25.3% (1283 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 26.3% (1336 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 27.4% (1388 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 28.4% (1442 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 29.2% (1481 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 30.2% (1531 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 31.2% (1581 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 32.3% (1639 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 33.4% (1693 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 34.3% (1742 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 35.3% (1792 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 36.2% (1837 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 37.2% (1888 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 38.2% (1940 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 39.3% (1993 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 40.3% (2043 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 41.3% (2097 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 42.4% (2150 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 43.3% (2195 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 44.3% (2248 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 45.3% (2298 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 46.2% (2346 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 47.3% (2398 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 48.3% (2451 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 49.3% (2502 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 50.3% (2550 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 51.3% (2602 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 52.3% (2656 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 53.3% (2703 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 54.3% (2754 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=981 55.3% (2805 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 98.1; 860 / 5074 (P = 16.95%) round 8]               
[00:00:01] Finding cutoff p=977 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=977 35.7% (1811 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 36.2% (1836 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 37.3% (1894 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 38.3% (1942 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 39.4% (2000 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 40.3% (2045 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 41.2% (2092 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 42.3% (2148 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 43.2% (2194 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 44.3% (2248 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 45.5% (2307 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 46.3% (2351 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 47.2% (2397 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 48.4% (2454 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 49.3% (2501 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 50.5% (2562 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 51.4% (2609 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 52.5% (2664 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 53.4% (2709 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 54.3% (2755 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 55.3% (2808 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 56.3% (2858 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 57.3% (2909 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 58.3% (2959 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 59.3% (3009 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 60.3% (3060 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 61.4% (3113 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 62.4% (3165 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 63.4% (3215 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 64.3% (3264 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 65.3% (3315 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 66.3% (3366 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 67.3% (3417 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=977 68.3% (3468 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 97.7; 1142 / 5074 (P = 22.51%) round 9]               
[00:00:01] Finding cutoff p=973 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=973 40.2% (2042 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 41.3% (2094 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 42.3% (2144 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 43.2% (2193 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 44.3% (2247 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 45.3% (2297 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 46.3% (2349 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 47.3% (2398 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 48.3% (2449 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 49.3% (2499 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 50.3% (2551 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 51.3% (2601 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 52.3% (2652 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 53.3% (2703 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 54.3% (2754 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 55.3% (2805 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 56.3% (2858 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 57.3% (2907 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 58.4% (2961 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 59.3% (3010 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=973 60.4% (3066 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 97.3; 1408 / 5074 (P = 27.75%) round 10]               
[00:00:01] Finding cutoff p=968 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=968 45.8% (2324 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 46.2% (2346 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 47.2% (2397 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 49.3% (2499 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 50.3% (2550 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 51.3% (2601 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 52.3% (2655 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 53.3% (2704 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 54.4% (2762 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 55.7% (2825 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 56.8% (2882 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 58.2% (2954 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=968 58.3% (2958 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 96.8; 1689 / 5074 (P = 33.29%) round 11]               
[00:00:01] Finding cutoff p=964 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=964 61.6% (3128 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 62.3% (3162 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 63.3% (3213 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 64.3% (3264 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 65.3% (3315 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 66.3% (3366 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=964 67.3% (3417 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 96.4; 1884 / 5074 (P = 37.13%) round 12]               
[00:00:01] Finding cutoff p=960 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=960 68.6% (3480 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 69.4% (3519 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 70.4% (3570 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 71.4% (3621 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 72.4% (3672 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 73.4% (3723 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=960 74.4% (3774 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 96.0; 2092 / 5074 (P = 41.23%) round 13]               
[00:00:01] Finding cutoff p=956 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=956 56.0% (2840 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 56.4% (2861 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 57.3% (2907 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 58.3% (2958 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 59.4% (3015 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 60.5% (3069 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 61.4% (3115 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 62.4% (3164 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 63.5% (3222 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 64.5% (3272 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 65.5% (3322 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 66.6% (3377 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 67.4% (3418 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 68.4% (3470 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 69.4% (3519 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 70.4% (3570 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 71.4% (3621 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 72.4% (3672 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 73.4% (3723 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=956 74.4% (3774 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 95.6; 2280 / 5074 (P = 44.93%) round 14]               
[00:00:01] Finding cutoff p=951 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=951 56.0% (2843 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 56.3% (2856 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 57.3% (2907 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 58.3% (2958 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 59.3% (3009 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 60.3% (3060 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 61.3% (3111 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 62.4% (3166 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 63.4% (3218 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 64.3% (3265 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 65.5% (3321 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=951 66.3% (3366 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 95.1; 2525 / 5074 (P = 49.76%) round 15]               
[00:00:01] Finding cutoff p=948 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=948 55.0% (2791 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 55.3% (2805 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 56.3% (2857 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 57.3% (2908 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 58.3% (2958 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 59.3% (3009 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 60.3% (3060 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 61.3% (3112 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 62.4% (3165 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 63.3% (3213 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 64.3% (3264 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 65.4% (3316 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 66.4% (3367 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 67.6% (3432 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 68.7% (3486 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 69.5% (3525 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 70.4% (3571 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 71.6% (3633 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 73.0% (3704 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=948 73.4% (3723 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 94.8; 2662 / 5074 (P = 52.46%) round 16]               
[00:00:01] Finding cutoff p=945 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=945 1.5% (77 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 3.0% (153 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 4.3% (217 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 5.3% (268 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 6.8% (345 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 8.1% (411 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 9.5% (480 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 11.0% (559 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 13.2% (669 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 14.2% (723 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 15.1% (766 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 16.3% (827 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 17.4% (883 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 18.2% (923 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 19.2% (974 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 20.3% (1031 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 21.6% (1095 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 22.3% (1131 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 23.5% (1190 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 24.1% (1225 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 25.3% (1282 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 26.4% (1340 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 27.7% (1406 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 28.5% (1446 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 29.3% (1487 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 30.5% (1546 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 31.3% (1588 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 32.2% (1635 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 33.5% (1698 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 34.3% (1742 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 35.5% (1799 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 36.4% (1845 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 37.6% (1908 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 38.6% (1958 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 39.6% (2007 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 40.4% (2048 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 41.4% (2102 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 42.2% (2143 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 43.3% (2196 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 44.3% (2249 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 45.3% (2296 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 46.4% (2353 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 47.3% (2401 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 48.4% (2454 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 49.3% (2499 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 50.3% (2551 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 51.3% (2605 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 52.3% (2654 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 53.4% (2709 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 54.4% (2759 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 55.3% (2807 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 56.3% (2857 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 57.3% (2909 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 58.3% (2959 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 59.3% (3010 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 60.3% (3062 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 61.4% (3113 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 62.4% (3164 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 63.3% (3213 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 64.3% (3264 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 65.3% (3315 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=945 66.4% (3367 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 94.5; 2783 / 5074 (P = 54.85%) round 17]               
[00:00:01] Finding cutoff p=942 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=942 46.4% (2355 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 47.3% (2400 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 48.3% (2452 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 49.3% (2502 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 50.3% (2552 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 51.3% (2604 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 52.3% (2652 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 53.3% (2706 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 54.4% (2758 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 55.3% (2805 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 56.4% (2864 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 57.4% (2913 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 58.3% (2960 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 59.3% (3010 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 60.3% (3060 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 61.3% (3111 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 62.3% (3162 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 63.3% (3213 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=942 64.3% (3264 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 94.2; 2907 / 5074 (P = 57.29%) round 18]               
[00:00:01] Finding cutoff p=937 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=937 46.1% (2337 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 46.5% (2359 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 47.4% (2406 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 48.4% (2458 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 49.3% (2499 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 50.4% (2558 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 51.4% (2608 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 52.4% (2660 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 53.3% (2705 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 54.3% (2755 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 55.3% (2808 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 56.4% (2861 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 57.4% (2913 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 58.5% (2969 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 59.3% (3011 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 60.4% (3064 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 61.4% (3117 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 62.4% (3168 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 63.4% (3218 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 64.3% (3264 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 65.3% (3315 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 66.3% (3366 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 67.3% (3417 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=937 68.3% (3468 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 93.7; 3041 / 5074 (P = 59.93%) round 19]               
[00:00:01] Finding cutoff p=932 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=932 23.2% (1178 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 24.2% (1229 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 25.1% (1275 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 26.2% (1329 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 27.3% (1385 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 28.2% (1433 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 29.2% (1484 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 30.2% (1532 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 31.2% (1582 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 32.3% (1638 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 33.2% (1683 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 34.2% (1736 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 35.2% (1788 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 36.2% (1837 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 37.2% (1887 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 38.2% (1940 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 39.2% (1991 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 40.2% (2042 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 41.2% (2091 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 42.3% (2144 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 43.4% (2204 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 44.2% (2245 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 45.3% (2296 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 46.3% (2351 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 47.2% (2397 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 49.3% (2500 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 50.3% (2551 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 51.5% (2612 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 52.4% (2658 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 53.3% (2706 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 54.3% (2755 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 55.3% (2805 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 56.3% (2856 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 57.4% (2912 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 58.3% (2959 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 59.5% (3019 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 60.4% (3065 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 61.4% (3115 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 62.3% (3162 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 63.4% (3216 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=932 64.4% (3268 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 93.2; 3200 / 5074 (P = 63.07%) round 20]               
[00:00:01] Finding cutoff p=928 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=928 32.6% (1652 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 33.2% (1687 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 34.2% (1734 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 35.3% (1789 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 36.2% (1837 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 37.2% (1888 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 38.2% (1939 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 39.2% (1990 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 40.2% (2040 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 41.2% (2093 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 42.2% (2142 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 43.3% (2195 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 44.2% (2245 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 45.3% (2297 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 46.3% (2348 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 47.3% (2398 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 49.3% (2499 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 50.4% (2556 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 51.4% (2607 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 52.3% (2654 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 53.3% (2703 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 54.5% (2763 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 55.4% (2810 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 56.5% (2868 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 57.4% (2915 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 58.5% (2967 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 59.3% (3010 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 60.3% (3060 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 61.4% (3114 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=928 62.4% (3164 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 92.8; 3301 / 5074 (P = 65.06%) round 21]               
[00:00:01] Finding cutoff p=923 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=923 32.9% (1670 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 33.5% (1699 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 34.2% (1737 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 35.4% (1794 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 36.3% (1843 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 38.2% (1940 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 40.1% (2034 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 40.2% (2041 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 41.2% (2091 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 42.2% (2143 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 43.2% (2193 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 44.2% (2245 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 45.2% (2295 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 46.2% (2346 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 47.2% (2397 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 49.3% (2500 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 50.3% (2551 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=923 51.3% (2603 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 92.3; 3438 / 5074 (P = 67.76%) round 22]               
[00:00:01] Finding cutoff p=920 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=920 38.2% (1936 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 38.2% (1940 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 39.2% (1990 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 40.2% (2040 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 41.2% (2093 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 42.2% (2142 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 43.2% (2193 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 44.2% (2244 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 45.3% (2298 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 46.5% (2360 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 47.8% (2427 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 48.5% (2463 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 49.5% (2513 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 51.5% (2613 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=920 52.3% (2652 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 92.0; 3517 / 5074 (P = 69.31%) round 23]               
[00:00:01] Finding cutoff p=915 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=915 62.5% (3171 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=915 63.3% (3213 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=915 64.3% (3264 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=915 65.3% (3315 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=915 66.3% (3366 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=915 67.3% (3417 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=915 68.3% (3468 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 91.5; 3635 / 5074 (P = 71.64%) round 24]               
[00:00:01] Finding cutoff p=910 [32.8Gb / 503.5Gb]                                                     
[00:00:01] ... finding cutoff p=910 1.2% (60 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 2.7% (136 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 3.8% (192 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 4.7% (238 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 6.4% (325 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 8.2% (418 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 9.1% (460 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 10.1% (515 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 11.1% (565 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 12.1% (613 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 13.1% (666 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 14.2% (720 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 15.1% (765 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 16.1% (819 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 17.4% (882 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 18.1% (919 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 19.1% (971 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 20.2% (1023 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 21.2% (1076 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 22.3% (1133 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 23.1% (1174 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 24.2% (1226 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 25.4% (1289 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 26.4% (1338 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 27.3% (1383 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 28.2% (1431 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 29.2% (1484 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 30.2% (1533 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 31.3% (1590 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 32.2% (1632 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 33.3% (1692 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 34.2% (1735 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 35.2% (1786 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 36.2% (1836 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 37.2% (1887 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 38.2% (1938 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 39.6% (2007 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 40.2% (2042 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 41.3% (2095 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 42.3% (2148 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 43.5% (2206 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 44.2% (2244 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 45.3% (2299 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 46.6% (2362 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 47.3% (2398 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 49.3% (2500 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 50.3% (2550 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 51.3% (2602 of 5074), ETA 0:00:00                              
[00:00:01] ... finding cutoff p=910 52.3% (2654 of 5074), ETA 0:00:00                              
[00:00:01] Preparing TNF Graph Building [pTNF = 91.0; 3739 / 5074 (P = 73.69%) round 25]               
[00:00:01] Finding cutoff p=906 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=906 72.7% (3690 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 73.4% (3723 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 74.4% (3774 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 75.4% (3826 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 76.4% (3877 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 77.4% (3928 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 78.4% (3980 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 79.4% (4030 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 80.4% (4080 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 81.5% (4134 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 82.4% (4183 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 83.5% (4237 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 84.7% (4297 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 85.5% (4340 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 86.5% (4391 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 87.6% (4444 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 88.5% (4491 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 89.6% (4544 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 90.5% (4591 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 91.5% (4642 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=906 92.5% (4692 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 90.6; 3824 / 5074 (P = 75.36%) round 26]               
[00:00:02] Finding cutoff p=901 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=901 1.4% (73 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 3.4% (170 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 4.7% (241 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 5.5% (280 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 7.0% (356 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 7.8% (395 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 8.7% (443 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 10.5% (531 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 12.1% (613 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 13.2% (671 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 14.4% (732 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 15.3% (775 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 16.3% (825 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 17.2% (874 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 18.2% (923 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 19.4% (986 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 20.4% (1034 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 21.3% (1082 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 22.2% (1125 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 23.6% (1196 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 24.4% (1238 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 25.2% (1278 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 26.1% (1326 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 27.3% (1385 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 28.2% (1431 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 29.5% (1496 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 30.4% (1544 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 31.3% (1586 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 32.2% (1636 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 33.3% (1691 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 34.2% (1734 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 35.5% (1802 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 36.4% (1845 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 37.3% (1895 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 38.3% (1944 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 39.3% (1995 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 40.2% (2040 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 41.3% (2094 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 42.3% (2145 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 43.2% (2193 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 44.2% (2245 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 45.3% (2296 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 46.2% (2346 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 47.3% (2400 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 49.3% (2500 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 50.3% (2550 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 51.3% (2602 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=901 52.3% (2652 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 90.1; 3912 / 5074 (P = 77.10%) round 27]               
[00:00:02] Finding cutoff p=897 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=897 1.2% (63 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 2.1% (109 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 3.0% (154 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 4.2% (214 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 5.0% (255 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 6.1% (309 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 7.2% (367 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 8.1% (410 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 9.2% (469 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 10.1% (515 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 11.1% (563 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 12.3% (625 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 13.4% (681 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 14.1% (716 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 15.2% (771 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 16.1% (818 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 17.2% (875 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 18.2% (922 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 19.6% (993 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 21.1% (1073 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 23.1% (1174 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 25.1% (1272 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 26.9% (1364 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 27.9% (1416 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 29.8% (1510 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 30.9% (1569 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 31.4% (1592 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 32.3% (1637 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 33.9% (1720 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 34.8% (1764 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 35.3% (1793 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 36.3% (1842 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 37.5% (1902 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 38.5% (1952 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 39.5% (2006 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 40.5% (2054 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 41.4% (2100 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 42.6% (2162 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 43.5% (2207 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 44.6% (2262 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 45.4% (2306 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 46.3% (2347 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 47.4% (2404 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 48.4% (2458 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 49.5% (2510 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 50.4% (2555 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 51.4% (2610 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 52.6% (2669 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 53.5% (2717 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 54.4% (2762 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 55.4% (2809 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 56.3% (2856 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 57.3% (2907 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 58.3% (2959 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 59.3% (3009 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 60.3% (3062 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 61.4% (3113 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=897 62.3% (3162 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 89.7; 3944 / 5074 (P = 77.73%) round 28]               
[00:00:02] Finding cutoff p=887 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=887 1.2% (61 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 2.0% (102 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 3.0% (153 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 4.1% (209 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 5.0% (255 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 6.1% (311 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 7.3% (368 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 8.1% (413 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 9.2% (465 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 10.1% (515 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 11.1% (563 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 12.4% (628 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 13.1% (664 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 14.1% (716 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 15.2% (770 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 16.3% (825 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 17.1% (867 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 18.2% (924 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 19.2% (974 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 20.6% (1043 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 21.3% (1083 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 22.2% (1125 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 23.2% (1177 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 24.2% (1230 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 25.2% (1277 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 26.2% (1331 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 27.2% (1382 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 28.2% (1430 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 29.3% (1485 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 30.2% (1534 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 31.2% (1582 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 32.2% (1635 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 33.2% (1684 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 34.3% (1742 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 35.2% (1788 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 36.3% (1843 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 37.3% (1895 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 38.2% (1940 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 39.2% (1990 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 40.2% (2040 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 41.2% (2092 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 42.3% (2144 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=887 43.2% (2193 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 88.7; 4013 / 5074 (P = 79.09%) round 29]               
[00:00:02] Finding cutoff p=876 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=876 1.2% (61 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 2.0% (102 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 3.0% (153 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 4.1% (206 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 5.1% (260 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 6.1% (307 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 7.2% (365 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 8.1% (413 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 9.3% (471 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 10.1% (511 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 11.1% (562 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 12.1% (615 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 13.2% (669 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 14.1% (715 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 15.1% (767 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 16.1% (819 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 17.1% (870 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 18.1% (918 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 19.1% (969 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 20.2% (1027 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 21.2% (1075 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 22.3% (1130 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 23.3% (1183 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 24.1% (1224 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 25.4% (1287 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 26.3% (1333 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 27.2% (1382 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 28.4% (1440 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 29.4% (1491 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 30.2% (1534 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 31.2% (1583 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 32.2% (1635 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 33.4% (1693 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 34.2% (1735 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 35.3% (1793 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 36.3% (1844 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 37.2% (1887 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 38.3% (1942 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 39.2% (1989 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 40.2% (2042 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 41.2% (2093 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 42.2% (2143 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=876 43.3% (2196 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 87.6; 4117 / 5074 (P = 81.14%) round 30]               
[00:00:02] Finding cutoff p=865 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=865 1.2% (63 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 2.8% (141 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 4.0% (205 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 5.1% (261 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 6.4% (325 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 7.4% (373 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 8.2% (418 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 10.8% (546 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 11.1% (563 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 12.1% (615 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 13.2% (670 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 14.1% (717 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 15.2% (770 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 16.2% (820 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 17.1% (867 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 18.3% (929 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 19.1% (970 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 20.2% (1027 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 21.3% (1082 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 22.5% (1143 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 23.3% (1183 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 24.3% (1232 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 25.1% (1276 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 26.2% (1329 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 27.3% (1384 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 28.2% (1430 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 29.2% (1480 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 30.2% (1533 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 31.3% (1590 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 32.2% (1633 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 33.3% (1691 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 34.2% (1736 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 35.3% (1793 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 36.4% (1849 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 37.2% (1889 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 38.4% (1948 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 39.2% (1991 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 40.2% (2042 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 41.3% (2096 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 42.4% (2151 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 43.3% (2199 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 44.2% (2245 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 45.4% (2306 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 46.3% (2347 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 47.3% (2399 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 48.3% (2450 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 49.3% (2503 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 50.3% (2554 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 51.3% (2602 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 52.3% (2652 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 53.3% (2704 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 54.3% (2754 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 55.3% (2806 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 56.3% (2858 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=865 57.3% (2907 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 86.5; 4249 / 5074 (P = 83.74%) round 31]               
[00:00:02] Finding cutoff p=856 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=856 1.2% (61 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 2.8% (143 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 4.0% (201 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 4.7% (236 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 6.0% (303 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 6.7% (342 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 7.6% (387 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 9.6% (488 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 10.9% (555 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 11.3% (571 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 12.1% (614 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 13.2% (669 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 14.2% (718 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 15.2% (770 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 16.2% (823 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 17.2% (875 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 18.5% (938 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 19.2% (974 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 20.4% (1034 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 21.3% (1082 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 22.2% (1124 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 23.1% (1174 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 24.2% (1226 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 25.4% (1288 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 26.5% (1345 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 27.1% (1377 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 28.2% (1429 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 29.1% (1479 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 30.4% (1542 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 31.4% (1593 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 32.2% (1636 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 33.6% (1704 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 34.4% (1745 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 35.2% (1787 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 36.3% (1843 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 37.4% (1896 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 38.2% (1938 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 39.4% (1998 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 40.3% (2045 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 41.3% (2097 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 42.4% (2149 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 43.5% (2207 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 44.2% (2245 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 45.3% (2299 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 46.6% (2362 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 47.3% (2398 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 49.3% (2500 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 50.4% (2556 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 51.3% (2603 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 52.3% (2652 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 53.3% (2705 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 54.3% (2755 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=856 55.3% (2805 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 85.6; 4322 / 5074 (P = 85.18%) round 32]               
[00:00:02] Finding cutoff p=845 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=845 1.3% (64 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 3.0% (150 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 4.1% (208 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 5.9% (297 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 6.6% (334 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 7.4% (375 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 9.1% (460 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 11.2% (566 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 12.3% (623 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 13.1% (665 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 14.2% (718 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 15.2% (772 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 16.3% (825 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 17.1% (870 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 18.2% (925 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 19.6% (996 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 20.2% (1023 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 21.1% (1071 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 22.3% (1130 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 23.4% (1187 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 24.5% (1241 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 25.6% (1300 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 26.4% (1341 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 27.2% (1382 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 28.3% (1435 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 29.3% (1486 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 30.2% (1534 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 31.2% (1581 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 32.2% (1633 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 33.3% (1689 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 34.3% (1740 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 35.5% (1799 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 36.3% (1841 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 37.2% (1890 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 38.3% (1941 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 39.4% (2000 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 40.2% (2042 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 41.4% (2100 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 42.4% (2150 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 43.4% (2201 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 44.3% (2247 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 45.3% (2300 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 46.3% (2348 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 47.3% (2400 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 48.5% (2462 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 49.4% (2506 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 50.3% (2550 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 51.3% (2603 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 52.3% (2654 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 53.3% (2703 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 54.3% (2755 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 55.3% (2806 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 56.3% (2856 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=845 57.3% (2907 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 84.5; 4413 / 5074 (P = 86.97%) round 33]               
[00:00:02] Finding cutoff p=835 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=835 1.1% (56 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 2.1% (107 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 3.2% (162 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 4.2% (212 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 5.1% (257 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 6.3% (321 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 7.2% (363 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 8.2% (414 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 9.6% (486 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 10.3% (522 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 11.7% (595 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 12.6% (639 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 13.5% (687 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 14.5% (737 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 17.2% (871 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 18.1% (919 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 19.5% (991 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 20.9% (1058 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 21.3% (1082 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 22.2% (1125 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 23.1% (1173 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 24.3% (1234 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 25.6% (1301 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 26.2% (1329 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 27.4% (1392 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 28.1% (1428 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 29.4% (1490 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 30.3% (1537 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 31.2% (1583 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 32.3% (1638 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 33.2% (1684 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 34.2% (1736 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 35.2% (1787 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 36.7% (1862 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 37.4% (1897 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 38.3% (1943 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 39.3% (1992 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 40.4% (2050 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 41.3% (2096 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 42.3% (2148 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 43.4% (2202 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 44.4% (2253 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 45.4% (2302 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 46.4% (2352 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 47.3% (2402 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 48.3% (2453 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 49.3% (2502 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 50.4% (2557 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 51.3% (2604 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 52.3% (2653 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 53.3% (2704 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 54.3% (2757 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 55.3% (2806 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 56.3% (2857 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 57.4% (2912 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 58.3% (2959 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 59.3% (3009 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 60.3% (3060 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 61.3% (3111 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=835 62.3% (3162 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 83.5; 4492 / 5074 (P = 88.53%) round 34]               
[00:00:02] Finding cutoff p=826 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=826 1.1% (58 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 2.4% (123 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 3.3% (167 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 4.1% (209 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 5.0% (255 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 6.1% (309 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 7.3% (370 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 8.1% (412 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 9.2% (466 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 10.3% (522 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 11.1% (563 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 12.2% (619 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 13.5% (683 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 14.2% (718 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 15.6% (792 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 16.5% (838 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 17.2% (872 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 18.1% (919 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 19.2% (976 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 20.3% (1028 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 21.3% (1082 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 22.2% (1125 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 23.4% (1187 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 24.2% (1227 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 25.3% (1282 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 26.3% (1334 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 27.2% (1379 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 28.2% (1429 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 29.3% (1486 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 30.2% (1533 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 31.2% (1582 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 32.2% (1634 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 33.3% (1688 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 34.3% (1738 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 35.2% (1786 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 36.2% (1838 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 37.2% (1888 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 38.6% (1960 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 39.3% (1993 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 40.4% (2051 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 41.5% (2106 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 42.6% (2161 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 43.3% (2198 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 44.3% (2248 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 45.3% (2300 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 46.6% (2367 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 47.6% (2414 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 48.6% (2468 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 49.8% (2525 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 50.4% (2556 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 51.4% (2608 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 52.3% (2655 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 53.3% (2704 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 54.3% (2754 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 55.3% (2805 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=826 56.5% (2865 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 82.6; 4548 / 5074 (P = 89.63%) round 35]               
[00:00:02] Finding cutoff p=815 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=815 1.3% (64 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 2.0% (104 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 3.1% (157 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 4.1% (210 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 5.1% (257 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 6.1% (312 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 7.1% (362 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 8.2% (414 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 9.2% (465 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 10.1% (512 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 11.2% (567 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 12.1% (616 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 13.1% (664 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 14.3% (725 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 15.5% (788 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 16.6% (842 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 17.4% (885 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 18.3% (928 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 19.2% (972 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 20.2% (1027 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 21.2% (1075 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 22.1% (1123 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 23.3% (1183 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 24.3% (1231 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 25.3% (1284 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 26.4% (1340 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 27.3% (1385 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 28.2% (1433 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 29.1% (1479 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 30.2% (1531 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 31.2% (1581 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 32.2% (1632 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 33.2% (1686 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 34.2% (1734 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 35.2% (1788 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 36.3% (1840 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 37.2% (1890 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 38.2% (1940 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 39.2% (1991 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 40.2% (2040 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 41.2% (2093 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 42.2% (2142 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=815 43.2% (2193 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 81.5; 4599 / 5074 (P = 90.64%) round 36]               
[00:00:02] Finding cutoff p=806 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=806 1.1% (55 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 2.3% (119 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 3.2% (162 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 4.0% (204 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 5.2% (264 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 6.1% (307 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 7.1% (360 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 8.2% (414 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 9.1% (464 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 10.1% (510 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 11.2% (567 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 12.5% (633 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 13.1% (665 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 14.5% (737 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 15.3% (777 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 16.2% (823 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 17.1% (867 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 18.2% (924 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 19.2% (975 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 20.2% (1023 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 21.2% (1076 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 22.2% (1128 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 23.2% (1179 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 24.2% (1227 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 25.2% (1278 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 26.2% (1331 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 27.2% (1379 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 28.2% (1431 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 29.1% (1479 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 30.2% (1533 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 31.2% (1581 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 32.3% (1639 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 33.2% (1687 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 34.4% (1743 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 35.2% (1785 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 36.2% (1838 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 37.3% (1893 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 38.2% (1938 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 39.3% (1992 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 40.2% (2042 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 41.2% (2091 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=806 75.0% (3804 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 80.6; 4644 / 5074 (P = 91.53%) round 37]               
[00:00:02] Finding cutoff p=796 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=796 1.5% (75 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 3.2% (164 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 4.6% (232 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 5.3% (270 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 6.9% (349 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 7.6% (386 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 8.4% (428 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 11.1% (561 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 12.4% (627 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 13.3% (677 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 14.3% (724 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 15.2% (770 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 16.3% (826 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 17.2% (875 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 18.2% (925 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 19.2% (972 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 20.2% (1024 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 21.2% (1076 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 22.3% (1132 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 23.3% (1181 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 24.3% (1231 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 25.2% (1279 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 26.1% (1326 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 27.3% (1384 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 28.2% (1433 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 29.2% (1483 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 30.4% (1543 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 31.2% (1584 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 32.4% (1643 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 33.4% (1695 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 34.3% (1742 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 35.4% (1796 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 36.3% (1840 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 37.3% (1893 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 38.2% (1939 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 39.3% (1993 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 40.3% (2045 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 41.3% (2096 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 42.2% (2142 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 43.2% (2193 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 72.0% (3655 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 72.4% (3672 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 73.4% (3723 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=796 74.4% (3774 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 79.6; 4706 / 5074 (P = 92.75%) round 38]               
[00:00:02] Finding cutoff p=785 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=785 1.5% (76 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 3.3% (169 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 4.6% (235 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 5.3% (267 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 6.7% (340 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 7.5% (380 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 8.2% (416 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 10.0% (509 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 11.9% (604 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 12.3% (626 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 13.4% (678 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 14.2% (721 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 15.1% (768 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 16.2% (823 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 17.1% (870 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 18.4% (932 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 19.2% (976 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 20.5% (1041 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 21.2% (1076 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 22.2% (1126 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 23.4% (1188 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 24.5% (1243 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 25.3% (1282 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 26.4% (1339 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 27.4% (1390 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 28.2% (1431 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 29.4% (1492 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 30.2% (1532 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 31.2% (1582 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 32.3% (1640 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 33.3% (1690 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 34.3% (1742 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 35.6% (1808 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 36.3% (1844 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 37.3% (1893 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 38.3% (1944 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 70.5% (3576 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 71.4% (3621 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 72.4% (3672 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 73.4% (3723 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=785 74.4% (3774 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 78.5; 4746 / 5074 (P = 93.54%) round 39]               
[00:00:02] Finding cutoff p=775 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=775 66.5% (3374 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=775 67.4% (3418 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=775 68.4% (3470 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=775 69.4% (3519 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=775 70.4% (3570 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=775 71.4% (3621 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=775 72.4% (3672 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=775 73.4% (3723 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=775 74.4% (3774 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 77.5; 4773 / 5074 (P = 94.07%) round 40]               
[00:00:02] Finding cutoff p=766 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=766 62.5% (3172 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 63.3% (3213 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 64.3% (3264 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 65.3% (3315 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 66.4% (3367 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 67.3% (3417 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 68.4% (3470 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 69.4% (3522 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 70.4% (3570 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 71.4% (3621 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 72.4% (3674 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 73.4% (3725 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 74.4% (3774 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 75.4% (3826 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 76.4% (3877 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=766 77.5% (3931 of 5074), ETA 0:00:00                              
[00:00:02] Preparing TNF Graph Building [pTNF = 76.6; 4806 / 5074 (P = 94.72%) round 41]               
[00:00:02] Finding cutoff p=756 [32.8Gb / 503.5Gb]                                                     
[00:00:02] ... finding cutoff p=756 66.1% (3353 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=756 66.3% (3366 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=756 67.3% (3417 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=756 68.3% (3468 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=756 69.4% (3519 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=756 70.4% (3570 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=756 71.4% (3621 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=756 72.4% (3673 of 5074), ETA 0:00:00                              
[00:00:02] ... finding cutoff p=756 73.4% (3723 of 5074), ETA 0:00:00                              
[00:00:02] Finished Preparing TNF Graph Building [pTNF = 75.60] [32.6Gb / 503.5Gb]                                            
[00:00:02] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=5074 maxEdges=200
[00:00:02] Building TNF Graph 8.1% (412 of 5074), ETA 0:00:03     [32.7Gb / 503.5Gb]                           
[00:00:02] Building TNF Graph 28.4% (1442 of 5074), ETA 0:00:01     [32.6Gb / 503.5Gb]                           
[00:00:03] Building TNF Graph 50.7% (2575 of 5074), ETA 0:00:01     [32.7Gb / 503.5Gb]                           
[00:00:03] Building TNF Graph 85.3% (4326 of 5074), ETA 0:00:00     [32.7Gb / 503.5Gb]                           
[00:00:03] Finished Building TNF Graph (222010 edges) [32.7Gb / 503.5Gb]                                          
[00:00:03] Cleaned up after Building TNF Graph (222010 edges) [32.7Gb / 503.5Gb]                                          
[00:00:03] Cleaned up TNF matrix of large contigs [32.7Gb / 503.5Gb]                                             
[00:00:03] Applying coverage correlations to TNF graph with 222010 edges
[00:00:03] Allocated memory for graph edges [32.7Gb / 503.5Gb]
[00:00:03] ... calculating abundance dist 1.0% (2223 of 222010), ETA 0:00:01                              
[00:00:03] ... calculating abundance dist 2.0% (4445 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 3.0% (6668 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 4.0% (8886 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 5.0% (11105 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 6.0% (13326 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 7.0% (15550 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 8.0% (17774 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 9.0% (19989 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 10.0% (22213 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 11.0% (24434 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 12.0% (26657 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 13.0% (28874 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 14.0% (31095 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 15.0% (33319 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 16.0% (35539 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 17.0% (37757 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 18.0% (39980 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 19.0% (42203 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 20.0% (44423 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 21.0% (46647 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 22.0% (48862 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 23.0% (51086 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 24.0% (53304 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 25.0% (55528 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 26.0% (57750 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 27.0% (59968 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 28.0% (62189 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 29.0% (64414 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 30.0% (66631 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 31.0% (68855 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 32.0% (71072 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 33.0% (73294 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 34.0% (75519 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 35.0% (77735 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 36.0% (79960 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 37.0% (82180 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 38.0% (84403 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 39.0% (86624 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 40.0% (88845 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 41.0% (91064 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 42.0% (93284 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 43.0% (95507 of 222010), ETA 0:00:00                              
[00:00:03] ... calculating abundance dist 44.0% (97725 of 222010), ETA 0:00:00                              
[00:00:03] Calculating geometric means [32.7Gb / 503.5Gb]
[00:00:03] Traversing graph with 5074 nodes and 222010 edges [32.7Gb / 503.5Gb]
[00:00:03] Building SCR Graph and Binning (483 vertices and 788 edges) [P = 9.50%; 32.7Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 1.0% (2221 of 222010), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (965 vertices and 2924 edges) [P = 19.00%; 32.7Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 2.0% (4442 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 3.0% (6663 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 4.0% (8884 of 222010), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (1447 vertices and 5267 edges) [P = 28.50%; 32.7Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 5.0% (11105 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 6.0% (13326 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 7.0% (15547 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 8.0% (17768 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 9.0% (19989 of 222010), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (1929 vertices and 8103 edges) [P = 38.00%; 32.7Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 10.0% (22210 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 11.0% (24431 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 12.0% (26652 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 13.0% (28873 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 14.0% (31094 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 15.0% (33315 of 222010), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (2411 vertices and 10691 edges) [P = 47.50%; 32.7Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 16.0% (35536 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 17.0% (37757 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 18.0% (39978 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 19.0% (42199 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 20.0% (44420 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 21.0% (46641 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 22.0% (48862 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 23.0% (51083 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 24.0% (53304 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 25.0% (55525 of 222010), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (2893 vertices and 12293 edges) [P = 57.00%; 32.7Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 26.0% (57746 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 27.0% (59967 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 28.0% (62188 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 29.0% (64409 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 30.0% (66630 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 31.0% (68851 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 32.0% (71072 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 33.0% (73293 of 222010), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (3375 vertices and 13350 edges) [P = 66.50%; 32.7Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 34.0% (75514 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 35.0% (77735 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 36.0% (79956 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 37.0% (82177 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 38.0% (84398 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 39.0% (86619 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 40.0% (88840 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 41.0% (91061 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 42.0% (93282 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 43.0% (95503 of 222010), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (3857 vertices and 14986 edges) [P = 76.00%; 32.7Gb / 503.5Gb]                           
[00:00:03] ... traversing graph 44.0% (97724 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 45.0% (99945 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 46.0% (102166 of 222010), ETA 0:00:00                               
[00:00:03] ... traversing graph 47.0% (104387 of 222010), ETA 0:00:00                               
[00:00:03] Building SCR Graph and Binning (4007 vertices and 15711 edges) [P = 85.50%; 32.7Gb / 503.5Gb]                           
[00:00:03] Finished Traversing graph [32.7Gb / 503.5Gb]                                       
[00:00:03] Dissolved 0 small clusters leaving 0 leftover contigs to be re-merged into larger clusters
[00:00:03] Rescuing singleton large contigs                                   
[00:00:03] There are 1221 bins already
[00:00:03] Outputting bins
[00:00:03] Writing cluster stats to: 03bins/metabat2_2kb/1507994/1507994.bin.BinInfo.txt
[00:00:14] 100.00% (29326126 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
1221 bins (29326126 bases in total) formed.
[00:00:14] Finished
MetaBAT2 generated 1221 bins for 1507994
MetaBAT2 binning completed for 1507994
