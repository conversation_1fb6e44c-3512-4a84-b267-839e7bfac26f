/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/EUKulele/manage_steps.py:28: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`
  MEM_AVAIL_GB = pd.read_csv("free.csv", sep = "\s+").free[0] / 10**3
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/EUKulele/busco_runner.py:19: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`
  MEM_AVAIL_GB = pd.read_csv("free.csv", sep = "\s+").free[0] / 10**3
usage: eukulele [subroutine] --mets_or_mags [dataset_type] --sample_dir [sample_directory] --reference_dir [reference_database_location] [all other options]
EUKulele: error: argument subroutine: invalid choice: '8' (choose from '', 'all', 'download', 'setup', 'alignment', 'busco', 'coregenes')
