Output depth matrix to 03bins/metabat2_2kb/1507990/temp/1507990.depth.txt
jgi_summarize_bam_contig_depths 2.17.66-ga512006-dirty 20250124_080221
Running with 16 threads to save memory you can reduce the number of threads with the OMP_NUM_THREADS variable
Output matrix to 03bins/metabat2_2kb/1507990/temp/1507990.depth.txt
Opening all bam files and validating headers
Processing bam files with largest_contig=0
Thread 0 opening and reading the header for file: 02mapping/1507990/1507990.sorted.bam
Thread 0 opened the file: 02mapping/1507990/1507990.sorted.bam
Thread 0 processing bam 0: 1507990.sorted.bam
Thread 0 finished reading bam 0: 1507990.sorted.bam
Thread 0 finished: 1507990.sorted.bam with 9802768 reads and 9472837 readsWellMapped (96.6343%)
Creating depth matrix file: 03bins/metabat2_2kb/1507990/temp/1507990.depth.txt
Closing last bam file
Finished
