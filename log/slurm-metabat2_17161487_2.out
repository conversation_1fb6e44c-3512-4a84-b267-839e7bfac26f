Pulling MetaBAT2 Docker image...
Processing binning for sample: 1507992
Decompressing scaffold file for 1507992...
Generating depth file for 1507992...
Running MetaBAT2 for 1507992...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 1500, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 200000. with random seed=1745882681
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [91.0Gb / 503.5Gb]
[00:00:00] Parsing assembly file [91.0Gb / 503.5Gb]
[00:00:00] ... processed 5 seqs, 5 long (>=1500), 0 short (>=1000) 1.0% (246975 of 23546120), ETA 0:00:02     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 14 seqs, 14 long (>=1500), 0 short (>=1000) 2.1% (494376 of 23546120), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 23 seqs, 23 long (>=1500), 0 short (>=1000) 3.0% (717882 of 23546120), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 33 seqs, 33 long (>=1500), 0 short (>=1000) 4.0% (947922 of 23546120), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 45 seqs, 45 long (>=1500), 0 short (>=1000) 5.1% (1196326 of 23546120), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 57 seqs, 57 long (>=1500), 0 short (>=1000) 6.0% (1424111 of 23546120), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 70 seqs, 70 long (>=1500), 0 short (>=1000) 7.0% (1651873 of 23546120), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 84 seqs, 84 long (>=1500), 0 short (>=1000) 8.0% (1885459 of 23546120), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 99 seqs, 99 long (>=1500), 0 short (>=1000) 9.0% (2122050 of 23546120), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 115 seqs, 115 long (>=1500), 0 short (>=1000) 10.0% (2362335 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 132 seqs, 132 long (>=1500), 0 short (>=1000) 11.0% (2600647 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 149 seqs, 149 long (>=1500), 0 short (>=1000) 12.0% (2831238 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 167 seqs, 167 long (>=1500), 0 short (>=1000) 13.0% (3066080 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 186 seqs, 186 long (>=1500), 0 short (>=1000) 14.0% (3302670 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 206 seqs, 206 long (>=1500), 0 short (>=1000) 15.0% (3539080 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 227 seqs, 227 long (>=1500), 0 short (>=1000) 16.0% (3776100 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 249 seqs, 249 long (>=1500), 0 short (>=1000) 17.0% (4009331 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 272 seqs, 272 long (>=1500), 0 short (>=1000) 18.0% (4241639 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 297 seqs, 297 long (>=1500), 0 short (>=1000) 19.0% (4482232 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 322 seqs, 322 long (>=1500), 0 short (>=1000) 20.0% (4712668 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 349 seqs, 349 long (>=1500), 0 short (>=1000) 21.0% (4952389 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 376 seqs, 376 long (>=1500), 0 short (>=1000) 22.0% (5183247 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 405 seqs, 405 long (>=1500), 0 short (>=1000) 23.0% (5421177 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 435 seqs, 435 long (>=1500), 0 short (>=1000) 24.0% (5657810 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 465 seqs, 465 long (>=1500), 0 short (>=1000) 25.0% (5886563 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 497 seqs, 497 long (>=1500), 0 short (>=1000) 26.0% (6122518 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 531 seqs, 531 long (>=1500), 0 short (>=1000) 27.0% (6364315 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 565 seqs, 565 long (>=1500), 0 short (>=1000) 28.0% (6596675 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 601 seqs, 601 long (>=1500), 0 short (>=1000) 29.0% (6830372 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 639 seqs, 639 long (>=1500), 0 short (>=1000) 30.0% (7067197 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 678 seqs, 678 long (>=1500), 0 short (>=1000) 31.0% (7300721 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 720 seqs, 720 long (>=1500), 0 short (>=1000) 32.0% (7539512 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 763 seqs, 763 long (>=1500), 0 short (>=1000) 33.0% (7773589 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 808 seqs, 808 long (>=1500), 0 short (>=1000) 34.0% (8009378 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 854 seqs, 854 long (>=1500), 0 short (>=1000) 35.0% (8241291 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 903 seqs, 903 long (>=1500), 0 short (>=1000) 36.0% (8476851 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 955 seqs, 955 long (>=1500), 0 short (>=1000) 37.0% (8716504 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1008 seqs, 1008 long (>=1500), 0 short (>=1000) 38.0% (8949239 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1064 seqs, 1064 long (>=1500), 0 short (>=1000) 39.0% (9184395 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1123 seqs, 1123 long (>=1500), 0 short (>=1000) 40.0% (9421149 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1184 seqs, 1184 long (>=1500), 0 short (>=1000) 41.0% (9655448 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1248 seqs, 1248 long (>=1500), 0 short (>=1000) 42.0% (9890628 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1315 seqs, 1315 long (>=1500), 0 short (>=1000) 43.0% (10126799 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1385 seqs, 1385 long (>=1500), 0 short (>=1000) 44.0% (10363402 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1457 seqs, 1457 long (>=1500), 0 short (>=1000) 45.0% (10596184 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1533 seqs, 1533 long (>=1500), 0 short (>=1000) 46.0% (10831562 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1613 seqs, 1613 long (>=1500), 0 short (>=1000) 47.0% (11067411 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1697 seqs, 1697 long (>=1500), 0 short (>=1000) 48.0% (11303002 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1785 seqs, 1785 long (>=1500), 0 short (>=1000) 49.0% (11538606 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1877 seqs, 1877 long (>=1500), 0 short (>=1000) 50.0% (11773712 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1974 seqs, 1974 long (>=1500), 0 short (>=1000) 51.0% (12009422 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2077 seqs, 2077 long (>=1500), 0 short (>=1000) 52.0% (12245547 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2186 seqs, 2186 long (>=1500), 0 short (>=1000) 53.0% (12480492 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2302 seqs, 2302 long (>=1500), 0 short (>=1000) 54.0% (12716818 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2425 seqs, 2425 long (>=1500), 0 short (>=1000) 55.0% (12952036 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2556 seqs, 2556 long (>=1500), 0 short (>=1000) 56.0% (13187202 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2695 seqs, 2695 long (>=1500), 0 short (>=1000) 57.0% (13422110 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2843 seqs, 2843 long (>=1500), 0 short (>=1000) 58.0% (13657375 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3000 seqs, 2918 long (>=1500), 82 short (>=1000) 59.0% (13892766 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3167 seqs, 2918 long (>=1500), 249 short (>=1000) 60.0% (14129072 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3343 seqs, 2918 long (>=1500), 425 short (>=1000) 61.0% (14364068 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3529 seqs, 2918 long (>=1500), 611 short (>=1000) 62.0% (14599504 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3726 seqs, 2918 long (>=1500), 808 short (>=1000) 63.0% (14834481 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3935 seqs, 2918 long (>=1500), 1017 short (>=1000) 64.0% (15070029 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4157 seqs, 2918 long (>=1500), 1239 short (>=1000) 65.0% (15305588 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4393 seqs, 2918 long (>=1500), 1334 short (>=1000) 66.0% (15540853 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4643 seqs, 2918 long (>=1500), 1334 short (>=1000) 67.0% (15776768 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4905 seqs, 2918 long (>=1500), 1334 short (>=1000) 68.0% (16012288 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 5182 seqs, 2918 long (>=1500), 1334 short (>=1000) 69.0% (16247654 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 5476 seqs, 2918 long (>=1500), 1334 short (>=1000) 70.0% (16483053 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 5787 seqs, 2918 long (>=1500), 1334 short (>=1000) 71.0% (16718251 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 6115 seqs, 2918 long (>=1500), 1334 short (>=1000) 72.0% (16953576 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 6462 seqs, 2918 long (>=1500), 1334 short (>=1000) 73.0% (17189382 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 6829 seqs, 2918 long (>=1500), 1334 short (>=1000) 74.0% (17424614 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 7218 seqs, 2918 long (>=1500), 1334 short (>=1000) 75.0% (17660170 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 7629 seqs, 2918 long (>=1500), 1334 short (>=1000) 76.0% (17895198 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 8067 seqs, 2918 long (>=1500), 1334 short (>=1000) 77.0% (18131047 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 8529 seqs, 2918 long (>=1500), 1334 short (>=1000) 78.0% (18366392 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 9018 seqs, 2918 long (>=1500), 1334 short (>=1000) 79.0% (18601931 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 9535 seqs, 2918 long (>=1500), 1334 short (>=1000) 80.0% (18837356 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 10083 seqs, 2918 long (>=1500), 1334 short (>=1000) 81.0% (19072659 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 10666 seqs, 2918 long (>=1500), 1334 short (>=1000) 82.0% (19308016 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 11285 seqs, 2918 long (>=1500), 1334 short (>=1000) 83.0% (19543470 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 11941 seqs, 2918 long (>=1500), 1334 short (>=1000) 84.0% (19779151 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 12635 seqs, 2918 long (>=1500), 1334 short (>=1000) 85.0% (20014460 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 13368 seqs, 2918 long (>=1500), 1334 short (>=1000) 86.0% (20249804 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 14144 seqs, 2918 long (>=1500), 1334 short (>=1000) 87.0% (20485395 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 14964 seqs, 2918 long (>=1500), 1334 short (>=1000) 88.0% (20720836 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 15824 seqs, 2918 long (>=1500), 1334 short (>=1000) 89.0% (20956194 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 16719 seqs, 2918 long (>=1500), 1334 short (>=1000) 90.0% (21191646 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 17651 seqs, 2918 long (>=1500), 1334 short (>=1000) 91.0% (21427153 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 18626 seqs, 2918 long (>=1500), 1334 short (>=1000) 92.0% (21662517 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 19653 seqs, 2918 long (>=1500), 1334 short (>=1000) 93.0% (21898042 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 20739 seqs, 2918 long (>=1500), 1334 short (>=1000) 94.0% (22133552 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 22374 seqs, 2918 long (>=1500), 1334 short (>=1000) 95.0% (22368898 of 23546120), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 1500 bp are 2918, and small contigs >= 1000 bp are 1334                                                                  
[00:00:00] Allocating 2918 contigs by 1 samples abundances [91.0Gb / 503.5Gb]
[00:00:00] Allocating 2918 contigs by 1 samples variances [91.0Gb / 503.5Gb]
[00:00:00] Allocating 1334 small contigs by 1 samples abundances [91.0Gb / 503.5Gb]
[00:00:00] Reading 0.001310Gb abundance file [91.0Gb / 503.5Gb]
[00:00:00] ... processed 213 lines 213 contigs and 0 short contigs 1.0% (14076 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 433 lines 433 contigs and 0 short contigs 2.0% (28204 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 654 lines 654 contigs and 0 short contigs 3.0% (42268 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 875 lines 875 contigs and 0 short contigs 4.0% (56303 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1095 lines 1095 contigs and 0 short contigs 5.0% (70361 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1314 lines 1314 contigs and 0 short contigs 6.0% (84453 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1533 lines 1533 contigs and 0 short contigs 7.0% (98546 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1753 lines 1753 contigs and 0 short contigs 8.0% (112631 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1972 lines 1972 contigs and 0 short contigs 9.0% (126649 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 2193 lines 2193 contigs and 0 short contigs 10.0% (140781 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 2413 lines 2413 contigs and 0 short contigs 11.0% (154835 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 2633 lines 2633 contigs and 0 short contigs 12.0% (168909 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 2853 lines 2853 contigs and 0 short contigs 13.0% (182958 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 3073 lines 2918 contigs and 155 short contigs 14.0% (197028 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 3294 lines 2918 contigs and 376 short contigs 15.0% (211138 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 3514 lines 2918 contigs and 596 short contigs 16.0% (225202 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 3734 lines 2918 contigs and 816 short contigs 17.0% (239249 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 3955 lines 2918 contigs and 1037 short contigs 18.0% (253338 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4175 lines 2918 contigs and 1257 short contigs 19.0% (267393 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 20.0% (281488 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 21.0% (295536 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 22.0% (309635 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 23.0% (323704 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 24.0% (337747 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 25.0% (351850 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 26.0% (365884 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 27.0% (379958 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 28.0% (394029 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 29.0% (408102 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 30.0% (422198 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 31.0% (436269 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 32.0% (450358 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 33.0% (464381 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 34.0% (478494 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 35.0% (492527 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 36.0% (506622 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 37.0% (520713 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 38.0% (534736 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 39.0% (548848 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 40.0% (562939 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 41.0% (576982 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 42.0% (591052 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 43.0% (605145 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 44.0% (619194 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 45.0% (633262 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 46.0% (647341 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 47.0% (661440 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 48.0% (675475 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 49.0% (689577 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 50.0% (703642 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 51.0% (717713 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 52.0% (731796 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 53.0% (745851 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 54.0% (759918 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 55.0% (773976 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 56.0% (788083 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 57.0% (802155 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 58.0% (816207 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 59.0% (830281 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 60.0% (844366 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 61.0% (858446 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 62.0% (872505 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 63.0% (886543 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 64.0% (900650 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 65.0% (914691 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 66.0% (928754 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 67.0% (942858 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 68.0% (956909 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 69.0% (970973 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 70.0% (985101 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 71.0% (999131 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 72.0% (1013232 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 73.0% (1027310 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 74.0% (1041346 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 75.0% (1055448 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 76.0% (1069512 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 77.0% (1083601 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 78.0% (1097620 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 79.0% (1111701 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 80.0% (1125761 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 81.0% (1139841 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 82.0% (1153933 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 83.0% (1167979 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 84.0% (1182100 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 85.0% (1196126 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 86.0% (1210223 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 87.0% (1224283 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 88.0% (1238374 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 89.0% (1252468 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 90.0% (1266541 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 91.0% (1280564 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 92.0% (1294670 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 93.0% (1308730 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 94.0% (1322804 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 95.0% (1336856 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 96.0% (1350941 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 97.0% (1365029 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 98.0% (1379080 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4252 lines 2918 contigs and 1334 short contigs 99.0% (1393179 of 1407113), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] Finished reading 22514 contigs and 1 coverages from 03bins/metabat2/1507992/temp/1507992.depth.txt [91.0Gb / 503.5Gb]. Ignored 18262 too small contigs.                                     
[00:00:00] Number of target contigs: 2918 of large (>= 1500) and 1334 of small ones (>=1000 & <1500). 
[00:00:00] Start contig TNF calculation. nobs = 2918
[00:00:00] Allocated memory for TNF [91.0Gb / 503.5Gb]
[00:00:00] Calculating TNF 1.2% (36 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 2.4% (70 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 3.3% (97 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 4.1% (121 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 5.7% (166 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 6.3% (185 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 7.3% (212 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 8.6% (252 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 9.4% (274 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 10.3% (301 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 11.8% (344 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 12.5% (364 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 13.4% (392 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 14.6% (426 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 15.6% (456 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 16.7% (488 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 17.9% (522 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 18.6% (544 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 20.0% (583 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 20.8% (606 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 21.9% (638 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 23.2% (677 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 24.2% (707 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 25.1% (732 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 25.9% (756 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 27.0% (787 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 27.9% (815 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 29.1% (850 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 30.2% (880 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 31.0% (906 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 32.5% (949 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 33.1% (966 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 34.2% (999 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 35.4% (1032 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 36.1% (1053 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 37.4% (1090 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 38.4% (1121 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 39.2% (1143 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 40.4% (1178 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 41.7% (1217 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 42.5% (1239 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 43.7% (1275 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 44.5% (1299 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 45.8% (1335 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 46.4% (1354 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 47.5% (1386 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 48.9% (1428 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 49.5% (1445 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 50.9% (1484 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 51.5% (1502 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 52.6% (1536 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 53.7% (1566 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 54.8% (1599 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 55.5% (1620 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 56.5% (1650 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 57.7% (1683 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 58.9% (1719 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 60.1% (1754 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 60.8% (1773 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 62.0% (1808 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 63.1% (1840 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 64.1% (1870 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 64.9% (1893 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 66.0% (1927 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 67.0% (1956 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 68.3% (1993 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 69.5% (2028 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 70.3% (2050 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 71.1% (2076 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 72.2% (2108 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 73.2% (2135 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 74.5% (2175 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 75.2% (2195 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 76.5% (2232 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 77.2% (2254 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 78.4% (2288 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 79.6% (2323 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 80.6% (2353 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 81.4% (2376 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 82.6% (2409 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 83.8% (2446 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 84.5% (2467 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 85.4% (2491 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 86.7% (2530 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 87.5% (2554 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 88.7% (2587 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 89.8% (2619 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 90.7% (2646 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 91.8% (2679 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 92.5% (2700 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 93.7% (2733 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 95.1% (2776 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 95.9% (2798 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 96.7% (2823 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 97.9% (2857 of 2918), ETA 0:00:00    
[00:00:00] Calculating TNF 98.7% (2880 of 2918), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [91.0Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.1% (31 of 2918), ETA 0:00:04                   
[00:00:00] ... processing TNF matrix 2.2% (64 of 2918), ETA 0:00:02                   
[00:00:00] ... processing TNF matrix 3.3% (96 of 2918), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 4.4% (128 of 2918), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 5.5% (160 of 2918), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 6.6% (192 of 2918), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 7.7% (224 of 2918), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 8.2% (240 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 9.3% (272 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 10.4% (304 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 11.5% (336 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 12.6% (368 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 13.7% (400 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 14.8% (432 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 15.9% (464 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 16.4% (480 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 17.5% (512 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 18.6% (544 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 19.7% (576 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 20.8% (608 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 21.9% (640 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 23.0% (672 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.1% (704 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.7% (720 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 25.8% (752 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.9% (784 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 28.0% (816 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.1% (848 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 30.2% (880 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.3% (912 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.4% (944 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.9% (960 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.0% (992 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 35.1% (1024 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.2% (1056 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.3% (1089 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 38.5% (1123 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.1% (1140 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 40.2% (1173 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.3% (1206 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.5% (1240 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.6% (1273 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 44.2% (1290 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 45.3% (1323 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.4% (1355 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 47.5% (1387 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.6% (1419 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.7% (1451 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.8% (1483 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 51.9% (1515 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.5% (1531 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.6% (1563 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 54.7% (1597 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.8% (1629 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.9% (1661 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 58.0% (1693 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.1% (1725 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.7% (1742 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 60.8% (1774 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.9% (1806 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.0% (1838 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 64.1% (1870 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 65.2% (1902 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.3% (1935 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.9% (1951 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.0% (1983 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 69.1% (2015 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 70.2% (2047 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 71.2% (2079 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 72.3% (2111 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 73.4% (2143 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.5% (2175 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 75.1% (2192 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 76.2% (2224 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 77.3% (2257 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 78.4% (2289 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 79.6% (2322 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 80.7% (2355 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 81.3% (2371 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 82.4% (2403 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.5% (2436 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 84.6% (2468 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 85.7% (2502 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 86.8% (2534 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 87.4% (2550 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 88.5% (2583 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 89.7% (2616 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 90.7% (2648 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 91.9% (2681 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 93.0% (2713 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 94.1% (2745 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 94.6% (2761 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 95.7% (2793 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 96.8% (2825 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 97.9% (2857 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 99.0% (2889 of 2918), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 99.8% (2913 of 2918), ETA 0:00:00                   
[00:00:00] Finding cutoff p=999 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=999 2.9% (85 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 5.2% (151 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 9.0% (264 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 9.9% (289 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 10.8% (314 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 11.7% (340 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 12.7% (372 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 13.6% (398 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 14.7% (428 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 15.8% (462 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 17.0% (495 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 17.6% (514 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 19.0% (553 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 19.7% (576 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 20.7% (603 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 22.1% (645 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 22.9% (668 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 23.7% (693 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 24.7% (721 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 26.3% (767 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 27.1% (792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 28.0% (818 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 28.9% (843 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 30.3% (883 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 31.1% (908 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 31.9% (930 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 33.4% (975 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 34.2% (999 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 35.1% (1025 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 36.0% (1050 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 37.5% (1095 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 38.3% (1117 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 39.2% (1143 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 40.3% (1177 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 41.1% (1200 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 42.8% (1249 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 43.5% (1268 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 44.3% (1294 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 45.6% (1330 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 46.9% (1368 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 47.6% (1388 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 48.4% (1413 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 49.9% (1457 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 51.2% (1495 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 51.8% (1512 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 52.5% (1533 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 53.5% (1560 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 54.8% (1598 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.0% (1634 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.6% (1651 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 58.0% (1693 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 59.1% (1724 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 59.8% (1744 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 61.1% (1782 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 61.8% (1803 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 62.9% (1836 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 64.0% (1867 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 65.1% (1899 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 66.3% (1934 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 66.9% (1953 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 67.9% (1982 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 69.2% (2020 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 70.2% (2047 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 71.2% (2079 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 72.2% (2107 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 73.2% (2135 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 74.1% (2162 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 75.2% (2195 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 76.3% (2227 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 77.2% (2254 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 78.2% (2281 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.5% (2319 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 80.5% (2349 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 81.3% (2373 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 82.3% (2401 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 83.6% (2439 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 84.4% (2463 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 85.4% (2492 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 86.4% (2520 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 87.5% (2554 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.6% (2584 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 89.5% (2611 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 90.5% (2640 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 91.6% (2674 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 92.6% (2703 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 93.6% (2731 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 94.7% (2762 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 95.7% (2793 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 97.7% (2852 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 98.7% (2881 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.9; 0 / 2918 (P = 0.00%) round 1]               
[00:00:00] Finding cutoff p=997 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=997 1.0% (30 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 3.1% (89 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 5.8% (169 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 8.2% (240 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 11.9% (346 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 12.9% (375 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 13.9% (405 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 15.0% (438 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 16.0% (466 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 17.2% (502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 18.7% (546 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 19.9% (581 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 20.9% (611 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 22.0% (643 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 23.5% (687 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 24.9% (726 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 26.9% (786 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 28.1% (820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 29.7% (868 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 31.4% (915 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 33.1% (967 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 35.2% (1026 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 37.0% (1080 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 39.5% (1152 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 41.6% (1213 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 43.6% (1272 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 45.7% (1333 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 47.5% (1385 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 49.6% (1448 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 51.5% (1502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 53.0% (1547 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 54.7% (1596 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 58.0% (1691 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 59.7% (1741 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 61.9% (1805 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 63.7% (1859 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 65.3% (1904 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 66.3% (1936 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 67.5% (1969 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 68.3% (1993 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 69.2% (2020 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 70.2% (2047 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 71.5% (2087 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 72.5% (2115 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 73.4% (2141 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 74.1% (2162 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 75.1% (2190 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 76.4% (2228 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 77.1% (2251 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 78.2% (2282 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 79.2% (2310 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 80.2% (2341 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 81.4% (2374 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 82.4% (2405 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 83.3% (2432 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 84.6% (2468 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 85.4% (2491 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 86.4% (2521 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 87.6% (2556 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 88.6% (2584 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 89.6% (2614 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 90.5% (2641 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 91.6% (2673 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 92.5% (2700 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 93.7% (2735 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 94.8% (2765 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=997 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.7; 0 / 2918 (P = 0.00%) round 2]               
[00:00:00] Finding cutoff p=994 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=994 1.3% (37 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 4.2% (124 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 6.9% (202 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 9.5% (276 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 12.1% (353 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 14.6% (426 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 17.0% (495 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 19.6% (571 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 22.1% (645 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 24.3% (710 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 26.9% (784 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 29.6% (863 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 31.9% (930 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 34.5% (1007 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 37.3% (1087 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 40.1% (1169 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 43.3% (1264 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 46.6% (1360 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 49.2% (1435 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 51.6% (1506 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 54.2% (1581 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 56.2% (1641 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 58.3% (1702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 60.2% (1757 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 62.3% (1818 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 64.4% (1878 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 66.0% (1925 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 67.3% (1964 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 68.5% (1999 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 69.7% (2035 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 70.9% (2070 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 72.3% (2110 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 73.7% (2151 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 74.7% (2180 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 75.6% (2207 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 76.5% (2233 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 77.4% (2259 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 78.4% (2287 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 79.2% (2311 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 80.6% (2351 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 81.4% (2375 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 82.9% (2419 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 83.8% (2446 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 84.6% (2468 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 85.6% (2499 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 86.5% (2524 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 87.6% (2555 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 88.5% (2582 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 89.5% (2612 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 90.5% (2641 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 91.7% (2675 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 92.8% (2707 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 93.6% (2730 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 94.7% (2762 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 95.6% (2790 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.4; 11 / 2918 (P = 0.38%) round 3]               
[00:00:00] Finding cutoff p=991 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=991 1.2% (34 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 3.9% (113 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 6.0% (175 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 8.4% (244 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 11.0% (320 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 13.1% (382 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 15.9% (464 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 18.7% (545 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 20.9% (611 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 23.6% (688 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 26.5% (773 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 29.6% (865 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 32.2% (939 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 35.1% (1025 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 38.3% (1118 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 41.4% (1208 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 44.1% (1288 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 47.4% (1382 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 50.7% (1480 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 53.1% (1550 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 55.9% (1630 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 58.3% (1701 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 61.4% (1791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 63.5% (1854 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 65.4% (1908 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 67.3% (1964 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 69.1% (2017 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 70.4% (2055 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 71.9% (2097 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 73.1% (2133 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 74.4% (2171 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 75.9% (2215 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 77.2% (2253 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 78.4% (2288 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 79.3% (2315 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 80.3% (2342 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 81.3% (2373 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 82.5% (2408 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 83.4% (2434 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 84.6% (2469 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 85.3% (2490 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 86.5% (2524 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 87.7% (2559 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 88.5% (2583 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 89.4% (2610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 90.5% (2640 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 91.6% (2672 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 92.6% (2702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 93.6% (2730 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 94.6% (2761 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.1; 44 / 2918 (P = 1.51%) round 4]               
[00:00:00] Finding cutoff p=990 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=990 1.1% (31 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 4.3% (125 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 6.7% (195 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 9.3% (272 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 12.0% (349 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 14.9% (435 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 19.0% (553 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 22.0% (642 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 24.6% (717 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 27.5% (801 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 30.2% (880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 33.3% (972 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 35.9% (1048 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 38.8% (1133 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 41.9% (1223 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 45.2% (1318 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 48.6% (1418 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 52.3% (1527 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 55.1% (1608 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 57.6% (1682 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 59.8% (1745 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 62.0% (1809 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 63.9% (1866 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 65.9% (1922 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 67.9% (1980 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 69.5% (2029 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 71.0% (2073 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 72.4% (2112 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 73.9% (2155 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 75.1% (2191 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 76.3% (2225 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 77.3% (2257 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 78.4% (2289 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 79.4% (2318 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 80.2% (2341 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 81.4% (2375 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 82.6% (2410 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 83.3% (2431 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 84.5% (2466 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 85.7% (2500 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 86.4% (2521 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 87.6% (2556 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 88.4% (2580 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 89.5% (2611 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 90.6% (2645 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 91.7% (2676 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 92.6% (2703 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 93.6% (2731 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.0; 58 / 2918 (P = 1.99%) round 5]               
[00:00:00] Finding cutoff p=987 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=987 1.1% (31 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 4.1% (120 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 6.9% (201 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 9.8% (285 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 12.2% (355 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 15.0% (437 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 18.0% (524 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 20.3% (593 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 23.0% (671 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 25.8% (752 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 28.1% (820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 30.9% (902 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 33.7% (983 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 36.8% (1073 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 39.4% (1149 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 42.7% (1245 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 45.8% (1335 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 48.7% (1420 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 52.4% (1530 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 55.2% (1612 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 57.6% (1682 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 60.2% (1757 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 62.3% (1818 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 64.6% (1886 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 67.0% (1955 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 68.7% (2004 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 70.6% (2060 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 71.9% (2099 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 73.5% (2145 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 75.2% (2193 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 76.3% (2226 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 77.4% (2259 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 79.0% (2305 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 79.9% (2332 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 80.7% (2354 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 81.5% (2377 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 82.2% (2400 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 83.4% (2435 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 84.6% (2468 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 85.5% (2494 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 86.7% (2529 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 87.4% (2551 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 88.6% (2586 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 89.6% (2615 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 90.6% (2643 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 91.6% (2674 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 92.6% (2702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 93.6% (2731 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 94.6% (2761 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 95.8% (2794 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=987 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.7; 116 / 2918 (P = 3.98%) round 6]               
[00:00:00] Finding cutoff p=983 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=983 1.1% (33 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 3.6% (106 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 6.1% (178 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 9.2% (268 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 12.2% (355 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 14.5% (423 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 16.9% (492 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 19.1% (558 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 22.0% (643 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 24.8% (723 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 27.2% (794 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 30.1% (878 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 33.0% (964 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 35.6% (1040 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 38.3% (1118 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 41.4% (1207 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 44.1% (1286 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 48.7% (1422 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 52.6% (1536 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 55.3% (1615 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 58.0% (1691 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 60.2% (1756 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 62.1% (1812 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 64.1% (1870 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 66.1% (1930 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 68.0% (1985 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 69.7% (2033 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 71.4% (2084 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 72.9% (2126 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 74.2% (2164 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 75.3% (2197 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 76.4% (2230 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 77.4% (2259 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 78.5% (2290 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 79.7% (2325 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 80.7% (2354 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 81.5% (2379 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 82.2% (2400 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 83.4% (2435 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 84.5% (2466 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 85.7% (2501 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 86.4% (2522 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 87.6% (2557 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 88.4% (2580 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 89.5% (2612 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 90.6% (2644 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 91.6% (2674 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 92.6% (2703 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 93.6% (2730 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.3; 211 / 2918 (P = 7.23%) round 7]               
[00:00:00] Finding cutoff p=979 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=979 1.3% (38 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 5.1% (149 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 7.7% (225 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 10.5% (306 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 13.4% (390 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 16.0% (467 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 18.5% (541 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 21.2% (618 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 24.1% (702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 27.2% (795 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 29.7% (868 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 32.4% (946 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 35.3% (1029 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 38.3% (1118 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 41.3% (1204 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 44.4% (1296 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 46.9% (1368 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 49.7% (1449 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 53.5% (1561 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 55.9% (1632 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 58.7% (1714 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 61.0% (1780 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 63.3% (1847 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 65.5% (1911 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 67.1% (1958 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 68.9% (2010 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 70.9% (2069 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 72.4% (2112 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 73.9% (2157 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 74.9% (2187 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 76.0% (2218 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 77.2% (2253 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 78.4% (2289 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 79.5% (2321 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 80.4% (2345 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 81.6% (2380 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 82.5% (2406 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 83.6% (2438 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 84.4% (2462 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 85.5% (2495 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 86.6% (2527 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 87.5% (2553 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 88.6% (2586 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 89.7% (2616 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 90.6% (2645 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 91.7% (2676 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 92.6% (2701 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 93.7% (2735 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.9; 308 / 2918 (P = 10.56%) round 8]               
[00:00:00] Finding cutoff p=975 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=975 1.0% (30 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 4.2% (122 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 6.7% (195 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 9.8% (286 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 12.6% (368 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 15.3% (445 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 17.8% (520 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 20.5% (599 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 23.0% (671 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 25.8% (752 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 28.2% (824 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 30.7% (896 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 33.6% (979 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 35.7% (1041 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 38.7% (1130 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 41.6% (1213 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 44.1% (1287 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 47.1% (1374 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 50.4% (1470 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 52.8% (1540 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 55.2% (1610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 57.5% (1679 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 59.8% (1746 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 61.6% (1798 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 63.8% (1862 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 65.6% (1915 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 67.4% (1968 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 70.0% (2042 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 71.8% (2096 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 73.4% (2141 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 74.5% (2173 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 75.7% (2208 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 76.9% (2243 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 78.0% (2276 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 79.2% (2311 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 80.3% (2342 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 81.6% (2381 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 82.4% (2405 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 83.6% (2439 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 84.4% (2463 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 85.6% (2497 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 86.7% (2529 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 87.5% (2553 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 88.6% (2584 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 89.4% (2610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 90.7% (2648 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 91.6% (2672 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 92.6% (2701 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 93.6% (2732 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 94.7% (2763 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 95.6% (2790 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.5; 411 / 2918 (P = 14.08%) round 9]               
[00:00:00] Finding cutoff p=971 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=971 1.3% (38 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 3.8% (111 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 6.2% (180 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 8.9% (260 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 11.8% (345 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 14.3% (418 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 17.2% (502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 19.8% (579 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 22.6% (659 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 25.4% (742 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 28.1% (820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 32.4% (944 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 35.4% (1033 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 37.6% (1098 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 40.7% (1187 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 43.5% (1268 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 46.3% (1352 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 49.6% (1447 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 52.3% (1525 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 55.0% (1605 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 57.6% (1682 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 60.0% (1751 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 62.4% (1821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 64.6% (1884 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 66.4% (1938 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 68.3% (1993 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 70.2% (2047 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 71.8% (2095 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 73.2% (2135 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 74.4% (2172 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 75.7% (2208 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 76.7% (2238 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 77.8% (2271 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 78.9% (2301 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 80.0% (2334 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 80.9% (2360 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 81.8% (2388 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 82.7% (2413 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 83.5% (2437 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 84.4% (2463 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 85.3% (2490 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 86.5% (2523 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 87.4% (2550 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 88.5% (2581 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 89.7% (2617 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 90.5% (2641 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 91.5% (2671 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 92.6% (2703 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 93.6% (2730 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 94.6% (2761 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 95.6% (2790 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=971 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.1; 509 / 2918 (P = 17.44%) round 10]               
[00:00:00] Finding cutoff p=967 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=967 1.2% (36 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 3.7% (108 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 5.9% (173 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 8.5% (248 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 11.0% (320 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 13.9% (406 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 16.2% (473 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 18.8% (549 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 21.5% (627 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 24.1% (702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 26.9% (786 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 29.4% (857 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 32.0% (934 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 34.9% (1019 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 37.6% (1096 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 40.6% (1186 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 43.7% (1275 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 46.6% (1361 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 49.5% (1444 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 51.9% (1515 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 54.5% (1589 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 57.0% (1662 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 59.1% (1725 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 61.2% (1787 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 63.4% (1850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 65.1% (1900 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 66.7% (1945 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 68.5% (1999 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 70.1% (2046 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 71.8% (2096 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 73.0% (2129 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 74.0% (2158 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 74.9% (2185 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 76.0% (2219 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 77.2% (2252 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 78.3% (2285 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 80.1% (2338 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 81.3% (2373 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 82.6% (2410 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 83.4% (2433 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 84.6% (2468 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 85.4% (2493 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 86.6% (2527 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 87.7% (2558 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 88.7% (2588 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 89.7% (2616 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 90.5% (2642 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 91.6% (2672 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 92.6% (2701 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 93.7% (2734 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=967 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.7; 601 / 2918 (P = 20.60%) round 11]               
[00:00:00] Finding cutoff p=963 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=963 1.0% (30 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 3.6% (106 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 6.0% (176 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 8.6% (252 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 11.3% (331 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 13.4% (391 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 15.6% (455 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 18.1% (528 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 20.9% (611 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 23.7% (691 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 26.1% (761 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 28.8% (841 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 31.6% (922 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 34.4% (1005 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 37.1% (1082 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 39.9% (1163 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 42.5% (1239 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 45.7% (1333 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 48.6% (1418 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 51.9% (1514 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 54.1% (1578 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 56.5% (1648 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 58.9% (1719 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 61.3% (1789 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 63.5% (1854 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 65.5% (1912 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 67.4% (1967 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 69.0% (2013 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 70.5% (2058 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 72.1% (2105 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 73.4% (2142 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 74.6% (2178 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 75.7% (2209 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 77.1% (2249 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 78.1% (2279 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 79.1% (2309 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 80.5% (2348 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 81.8% (2387 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 82.9% (2418 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 83.7% (2443 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 84.5% (2466 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 85.7% (2502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 86.5% (2525 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 87.4% (2550 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 88.5% (2582 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 89.7% (2618 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 90.5% (2642 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 91.6% (2673 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 92.6% (2702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 93.7% (2734 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 94.7% (2762 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=963 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.3; 691 / 2918 (P = 23.68%) round 12]               
[00:00:00] Finding cutoff p=959 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=959 1.9% (55 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 4.7% (136 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 8.5% (248 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 11.7% (341 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 14.7% (428 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 17.2% (501 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 19.7% (576 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 22.1% (644 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 24.7% (721 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 27.5% (803 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 30.0% (874 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 32.8% (958 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 35.6% (1039 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 38.9% (1134 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 42.2% (1231 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 45.1% (1317 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 48.5% (1416 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 50.1% (1462 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 52.3% (1526 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 54.7% (1595 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 57.1% (1667 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 59.7% (1743 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 61.8% (1803 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 63.7% (1859 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 65.6% (1913 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 67.4% (1966 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 69.0% (2013 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 70.9% (2070 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 72.6% (2119 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 74.0% (2159 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 75.1% (2190 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 76.1% (2222 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 77.4% (2258 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 78.7% (2296 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 79.6% (2323 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 81.2% (2368 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 82.1% (2397 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 83.0% (2421 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 83.7% (2443 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 84.5% (2467 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 85.4% (2491 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 86.4% (2521 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 87.5% (2553 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 88.8% (2592 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 89.5% (2611 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 90.6% (2643 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 91.5% (2670 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 92.7% (2705 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 93.7% (2734 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 94.7% (2764 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.9; 766 / 2918 (P = 26.25%) round 13]               
[00:00:00] Finding cutoff p=954 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=954 2.0% (57 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 4.7% (136 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 7.4% (215 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 9.8% (287 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 11.9% (347 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 14.7% (428 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 17.2% (502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 20.0% (585 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 22.6% (659 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 25.3% (738 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 28.4% (828 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 31.6% (922 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 34.1% (995 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 36.5% (1065 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 38.5% (1122 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 41.0% (1196 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 43.6% (1271 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 46.7% (1363 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 48.8% (1425 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 50.9% (1486 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 53.1% (1549 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 55.2% (1612 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 57.3% (1671 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 59.4% (1733 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 61.4% (1793 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 63.7% (1858 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 65.8% (1919 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 69.4% (2024 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 71.0% (2071 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 72.5% (2117 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 73.8% (2154 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 75.1% (2190 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 76.4% (2228 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 77.6% (2264 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 78.7% (2296 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 80.0% (2335 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 81.3% (2371 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 82.3% (2401 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 83.3% (2430 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 84.5% (2465 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 85.6% (2497 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 86.4% (2522 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 87.6% (2556 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 88.6% (2586 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 89.5% (2612 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 90.5% (2641 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 91.5% (2671 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 92.7% (2705 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 93.7% (2734 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.4; 855 / 2918 (P = 29.30%) round 14]               
[00:00:00] Finding cutoff p=951 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=951 1.7% (51 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 4.2% (123 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 6.6% (192 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 9.3% (272 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 11.7% (341 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 14.7% (429 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 16.8% (490 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 19.4% (567 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 21.9% (639 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 24.7% (720 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 27.2% (795 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 32.2% (941 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 34.7% (1013 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 37.8% (1103 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 40.4% (1178 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 43.2% (1262 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 46.2% (1347 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 48.3% (1410 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 50.3% (1467 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 52.6% (1536 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 55.1% (1607 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 57.3% (1671 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 59.2% (1726 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 61.2% (1785 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 63.2% (1845 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 65.3% (1905 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 67.0% (1954 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 69.0% (2012 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 70.7% (2063 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 72.4% (2112 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 73.7% (2151 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 74.8% (2184 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 76.0% (2218 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 77.1% (2249 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 78.0% (2275 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 79.4% (2318 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 80.8% (2357 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 81.9% (2391 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 82.8% (2417 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 83.7% (2443 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 84.6% (2468 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 85.5% (2496 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 86.4% (2521 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 87.5% (2553 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 88.5% (2582 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 89.4% (2610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 90.6% (2643 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 91.7% (2675 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 92.7% (2706 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 93.7% (2733 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 94.6% (2761 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=951 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.1; 912 / 2918 (P = 31.25%) round 15]               
[00:00:00] Finding cutoff p=947 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=947 1.0% (30 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 3.2% (92 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 6.1% (179 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 8.8% (257 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 11.7% (342 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 13.9% (405 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 16.2% (473 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 18.7% (547 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 21.7% (633 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 24.2% (706 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 26.9% (784 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 29.3% (856 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 32.0% (935 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 34.2% (999 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 36.8% (1074 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 38.8% (1132 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 41.4% (1208 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 44.4% (1297 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 47.4% (1382 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 49.3% (1439 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 51.0% (1488 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 53.0% (1547 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 55.1% (1607 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 56.9% (1659 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 58.7% (1713 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 60.8% (1775 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 62.7% (1829 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 64.6% (1884 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 66.7% (1946 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 68.7% (2005 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 70.4% (2053 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 71.9% (2098 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 73.4% (2143 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 75.0% (2188 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 76.0% (2218 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 77.9% (2272 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 79.4% (2316 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 80.6% (2351 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 81.6% (2381 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 82.4% (2405 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 83.5% (2436 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 84.4% (2463 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 85.5% (2496 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 86.4% (2520 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 87.4% (2551 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 88.5% (2583 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 89.7% (2617 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 90.7% (2647 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 91.7% (2675 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 92.8% (2708 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 93.6% (2730 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=947 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.7; 964 / 2918 (P = 33.04%) round 16]               
[00:00:00] Finding cutoff p=943 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=943 1.1% (32 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 3.4% (98 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 5.3% (154 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 8.4% (244 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 10.5% (306 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 12.7% (371 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 15.1% (442 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 17.9% (523 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 20.5% (597 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 23.6% (688 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 26.6% (777 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 29.3% (855 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 32.3% (942 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 34.4% (1004 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 37.1% (1084 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 39.0% (1137 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 41.4% (1209 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 46.0% (1342 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 48.6% (1418 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 50.7% (1478 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 52.3% (1527 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 54.2% (1583 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 56.0% (1633 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 57.0% (1662 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 59.0% (1722 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 60.6% (1767 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 62.6% (1826 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 64.1% (1871 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 66.8% (1948 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 68.5% (1998 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 70.0% (2044 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 71.5% (2085 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 72.9% (2128 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 74.1% (2162 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 75.2% (2195 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 76.4% (2229 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 78.3% (2285 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 79.6% (2322 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 80.7% (2355 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 81.8% (2387 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 82.6% (2410 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 83.4% (2435 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 84.5% (2467 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 85.5% (2494 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 86.6% (2527 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 87.6% (2557 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 88.7% (2587 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 89.7% (2617 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 90.5% (2640 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 91.7% (2675 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 92.7% (2705 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 93.6% (2732 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 95.7% (2793 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=943 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.3; 1040 / 2918 (P = 35.64%) round 17]               
[00:00:00] Finding cutoff p=938 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=938 1.0% (30 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 3.2% (92 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 5.8% (168 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 7.9% (231 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 10.9% (318 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 13.1% (382 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 15.5% (453 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 17.5% (510 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 20.7% (604 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 23.7% (692 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 25.7% (750 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 28.0% (818 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 30.8% (899 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 33.0% (963 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 35.4% (1034 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 38.0% (1109 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 40.0% (1168 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 42.3% (1233 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 45.3% (1322 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 47.8% (1395 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 49.6% (1446 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 51.3% (1497 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 53.3% (1555 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 55.2% (1610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 57.1% (1666 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 59.0% (1721 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 61.0% (1780 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 62.7% (1830 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 65.2% (1903 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 67.8% (1977 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 69.4% (2026 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 71.0% (2072 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 72.3% (2110 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 73.6% (2148 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 74.9% (2187 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 75.9% (2216 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 77.7% (2266 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 79.0% (2306 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 80.2% (2339 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 81.7% (2384 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 82.5% (2407 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 83.3% (2432 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 84.5% (2465 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 85.5% (2495 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 86.7% (2530 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 87.5% (2553 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 88.4% (2580 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 89.7% (2616 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 90.5% (2640 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 91.5% (2671 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 92.7% (2704 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 93.6% (2731 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 94.7% (2762 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 95.6% (2790 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.8; 1133 / 2918 (P = 38.83%) round 18]               
[00:00:00] Finding cutoff p=933 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=933 1.1% (33 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 3.2% (93 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 5.3% (156 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 8.3% (242 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 11.0% (320 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 13.6% (396 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 15.9% (465 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 18.2% (530 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 20.3% (593 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 23.5% (686 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 25.7% (750 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 27.8% (811 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 30.5% (891 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 32.7% (955 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 35.7% (1042 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 38.6% (1125 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 41.0% (1196 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 43.1% (1257 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 45.4% (1324 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 48.0% (1400 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 50.7% (1478 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 52.5% (1531 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 54.1% (1580 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 55.6% (1622 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 56.8% (1656 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 58.5% (1708 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 60.4% (1763 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 62.0% (1809 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 64.7% (1888 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 66.8% (1948 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 68.2% (1989 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 69.8% (2037 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 71.0% (2073 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 72.5% (2116 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 74.0% (2158 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 75.1% (2192 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 76.8% (2241 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 78.2% (2283 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 79.5% (2321 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 80.4% (2345 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 82.1% (2396 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 83.7% (2442 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 84.6% (2469 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 85.5% (2496 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 86.6% (2527 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 87.4% (2550 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 88.7% (2588 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 89.8% (2621 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 90.7% (2646 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 91.5% (2671 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 92.6% (2702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 93.7% (2733 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 94.8% (2765 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=933 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.3; 1210 / 2918 (P = 41.47%) round 19]               
[00:00:00] Finding cutoff p=928 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=928 1.0% (30 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 4.8% (141 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 7.6% (221 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 9.7% (282 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 11.9% (347 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 14.2% (413 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 16.1% (469 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 18.5% (539 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 21.4% (625 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 23.9% (697 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 25.8% (754 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 28.7% (838 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 31.0% (906 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 32.9% (961 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 36.1% (1052 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 38.1% (1113 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 40.0% (1166 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 41.8% (1220 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 44.7% (1305 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 46.8% (1366 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 48.5% (1416 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 50.2% (1464 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 51.5% (1503 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 53.1% (1548 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 54.2% (1582 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 55.6% (1621 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 57.3% (1671 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 59.2% (1726 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 61.2% (1785 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 63.9% (1864 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 65.3% (1904 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 67.2% (1961 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 68.6% (2001 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 69.7% (2034 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 71.0% (2072 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 72.5% (2116 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 74.6% (2178 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 76.5% (2232 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 77.4% (2259 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 78.6% (2295 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 79.5% (2320 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 80.4% (2347 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 82.3% (2402 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 83.4% (2434 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 84.6% (2468 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 85.6% (2497 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 86.4% (2521 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 87.6% (2557 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 88.6% (2585 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 89.7% (2617 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 90.6% (2644 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 91.7% (2676 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 92.6% (2703 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 93.6% (2731 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 94.7% (2763 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.8; 1285 / 2918 (P = 44.04%) round 20]               
[00:00:00] Finding cutoff p=925 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=925 1.6% (46 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 4.0% (118 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 6.5% (191 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 9.0% (262 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 10.5% (306 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 12.4% (361 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 14.9% (434 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 16.8% (489 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 19.4% (567 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 21.5% (627 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 23.9% (698 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 26.5% (772 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 28.9% (842 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 31.2% (910 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 32.9% (961 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 35.3% (1030 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 37.4% (1090 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 40.2% (1173 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 41.7% (1218 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 43.0% (1255 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 44.4% (1297 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 46.5% (1358 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 49.6% (1447 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 51.5% (1502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 53.0% (1547 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 54.3% (1584 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 55.9% (1632 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 58.4% (1705 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 60.7% (1772 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 62.7% (1830 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 64.8% (1892 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 66.7% (1945 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 68.1% (1987 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 69.6% (2031 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 70.8% (2065 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 72.4% (2114 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 74.7% (2180 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 76.0% (2217 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 77.0% (2246 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 78.3% (2286 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 79.5% (2319 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 80.6% (2351 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 82.2% (2400 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 83.3% (2431 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 84.6% (2470 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 85.5% (2494 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 86.8% (2534 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 87.6% (2556 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 88.6% (2586 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 89.4% (2610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 90.6% (2644 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 91.5% (2670 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 92.5% (2700 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 93.6% (2732 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 94.7% (2764 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=925 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.5; 1339 / 2918 (P = 45.89%) round 21]               
[00:00:00] Finding cutoff p=921 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=921 1.1% (33 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 2.7% (79 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 5.3% (155 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 7.7% (225 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 9.9% (290 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 11.7% (342 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 14.2% (415 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 16.4% (478 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 18.6% (544 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 20.6% (601 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 22.4% (654 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 24.7% (721 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 28.1% (821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 30.4% (886 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 33.8% (986 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 36.7% (1072 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 38.3% (1117 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 40.3% (1175 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 42.8% (1248 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 44.9% (1311 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 46.6% (1359 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 48.8% (1424 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 50.5% (1475 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 52.0% (1517 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 53.4% (1559 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 54.9% (1601 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 56.3% (1644 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 57.8% (1687 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 61.5% (1794 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 64.3% (1875 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 65.7% (1918 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 67.0% (1954 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 68.5% (1999 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 69.7% (2033 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 71.0% (2071 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 72.9% (2127 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 74.8% (2182 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 75.9% (2215 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 77.0% (2248 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 78.2% (2281 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 79.3% (2313 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 80.4% (2347 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 82.6% (2410 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 83.4% (2435 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 84.4% (2462 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 85.6% (2498 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 86.5% (2525 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 87.6% (2556 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 88.5% (2582 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 89.5% (2612 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 90.8% (2650 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 91.9% (2681 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 92.6% (2702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 93.6% (2732 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 94.7% (2764 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 95.6% (2790 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.1; 1402 / 2918 (P = 48.05%) round 22]               
[00:00:00] Finding cutoff p=918 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=918 1.1% (32 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 2.8% (82 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 5.9% (173 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 8.5% (248 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 11.4% (332 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 13.2% (386 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 14.9% (436 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 16.7% (486 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 19.7% (576 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 22.4% (655 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 24.8% (725 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 27.5% (802 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 30.9% (901 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 33.1% (966 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 35.8% (1046 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 37.8% (1103 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 39.5% (1152 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 41.6% (1215 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 43.8% (1279 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 45.2% (1320 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 46.3% (1350 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 48.5% (1415 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 49.7% (1450 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 51.2% (1493 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 53.2% (1551 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 54.9% (1603 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 56.1% (1638 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 58.2% (1699 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 60.6% (1768 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 63.2% (1845 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 65.0% (1898 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 66.6% (1943 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 68.0% (1985 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 69.6% (2031 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 71.1% (2074 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 72.4% (2112 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 73.9% (2156 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 75.7% (2208 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 76.8% (2240 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 78.1% (2280 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 79.2% (2311 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 81.5% (2378 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 83.0% (2423 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 83.8% (2444 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 84.7% (2473 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 85.5% (2496 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 86.4% (2522 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 88.0% (2567 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 88.7% (2589 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 89.6% (2615 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 90.5% (2642 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 91.7% (2677 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 92.5% (2700 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 93.8% (2736 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 94.7% (2762 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.8; 1453 / 2918 (P = 49.79%) round 23]               
[00:00:00] Finding cutoff p=914 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=914 1.5% (45 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 4.1% (121 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 6.2% (182 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 9.4% (274 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 10.9% (317 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 13.4% (391 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 16.1% (469 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 19.7% (574 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 22.7% (661 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 24.3% (708 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 26.6% (777 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 29.3% (856 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 31.2% (911 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 33.8% (985 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 36.1% (1053 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 38.3% (1117 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 40.2% (1174 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 42.7% (1247 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 44.4% (1295 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 45.7% (1334 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 47.1% (1374 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 48.9% (1426 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 50.5% (1473 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 52.4% (1528 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 53.8% (1571 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 55.3% (1613 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 56.5% (1650 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 58.5% (1708 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 61.2% (1786 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 62.8% (1832 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 64.6% (1886 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 66.3% (1935 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 67.7% (1976 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 68.7% (2004 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 69.8% (2036 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 72.1% (2104 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 73.8% (2153 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 75.0% (2188 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 76.2% (2223 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 77.1% (2251 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 78.3% (2284 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 80.2% (2341 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 82.1% (2395 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 83.0% (2421 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 83.8% (2444 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 84.7% (2473 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 85.7% (2500 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 86.5% (2523 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 87.7% (2559 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 88.7% (2588 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 89.6% (2615 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 90.5% (2641 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 91.7% (2676 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 92.5% (2700 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 93.7% (2733 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=914 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.4; 1524 / 2918 (P = 52.23%) round 24]               
[00:00:00] Finding cutoff p=910 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=910 1.6% (48 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 4.1% (120 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 7.1% (208 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 9.5% (277 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 11.2% (328 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 13.1% (383 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 15.5% (452 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 18.1% (527 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 20.7% (603 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 22.2% (649 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 24.3% (708 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 27.1% (790 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 29.2% (852 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 31.4% (916 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 33.1% (966 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 34.7% (1013 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 35.9% (1049 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 38.2% (1116 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 40.2% (1174 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 42.9% (1251 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 44.2% (1291 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 45.9% (1339 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 47.8% (1395 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 49.2% (1437 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 50.2% (1464 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 51.5% (1502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 53.3% (1556 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 54.5% (1589 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 57.5% (1679 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 59.0% (1723 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 60.5% (1764 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 62.1% (1812 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 63.5% (1852 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 64.6% (1884 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 66.1% (1929 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 68.5% (2000 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 70.8% (2066 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 71.8% (2095 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 73.2% (2136 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 74.6% (2177 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 76.1% (2221 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 77.5% (2260 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 78.8% (2298 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 79.5% (2320 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 80.6% (2351 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 81.2% (2370 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 83.0% (2423 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 84.0% (2451 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 84.8% (2475 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 85.6% (2497 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 86.5% (2524 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 87.6% (2556 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 88.4% (2580 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 89.9% (2623 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 90.8% (2649 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 91.7% (2675 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 92.6% (2702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 93.6% (2731 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 94.7% (2763 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 95.7% (2793 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=910 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.0; 1584 / 2918 (P = 54.28%) round 25]               
[00:00:00] Finding cutoff p=906 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=906 1.5% (45 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 3.8% (111 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 6.5% (191 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 9.1% (265 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 10.6% (310 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 12.3% (358 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 14.6% (425 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 17.5% (511 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 20.0% (583 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 21.5% (626 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 23.0% (671 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 26.9% (784 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 29.2% (851 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 31.8% (929 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 33.6% (980 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 35.3% (1029 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 37.2% (1086 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 40.0% (1168 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 41.4% (1209 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 42.3% (1234 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 43.8% (1279 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 45.1% (1317 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 47.5% (1387 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 48.5% (1416 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 49.9% (1457 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 51.5% (1502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 53.6% (1563 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 56.0% (1635 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 57.6% (1681 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 59.1% (1725 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 60.7% (1771 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 62.2% (1816 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 63.5% (1852 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 64.1% (1871 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 66.6% (1943 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 69.9% (2039 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 71.0% (2073 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 72.5% (2115 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 73.8% (2153 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 74.7% (2180 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 76.3% (2226 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 78.0% (2276 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 79.0% (2306 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 80.2% (2341 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 81.4% (2376 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 82.6% (2411 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 83.4% (2434 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 84.6% (2470 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 85.7% (2502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 86.8% (2533 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 87.6% (2555 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 88.7% (2588 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 89.8% (2620 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 90.5% (2642 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 91.6% (2672 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 92.6% (2701 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 93.7% (2733 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=906 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.6; 1646 / 2918 (P = 56.41%) round 26]               
[00:00:00] Finding cutoff p=903 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=903 1.5% (43 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 4.2% (123 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 6.3% (184 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 8.6% (251 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 10.2% (299 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 11.6% (339 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 14.4% (420 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 17.4% (509 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 20.1% (586 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 22.2% (648 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 24.8% (724 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 28.4% (828 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 31.2% (909 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 34.2% (999 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 36.1% (1053 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 37.8% (1102 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 40.1% (1169 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 41.6% (1214 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 42.5% (1240 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 44.1% (1288 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 45.4% (1325 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 46.3% (1350 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 47.9% (1399 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 49.6% (1448 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 51.2% (1494 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 52.4% (1529 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 54.0% (1576 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 56.8% (1656 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 59.1% (1724 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 60.6% (1767 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 61.9% (1805 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 63.1% (1841 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 64.2% (1874 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 65.4% (1907 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 66.6% (1943 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 68.8% (2009 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 71.0% (2072 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 72.2% (2106 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 73.4% (2143 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 74.5% (2173 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 75.5% (2202 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 77.2% (2253 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 78.6% (2295 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 79.6% (2322 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 80.4% (2345 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 81.3% (2373 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 82.5% (2406 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 84.2% (2456 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 85.1% (2482 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 85.8% (2503 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 86.5% (2523 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 87.7% (2558 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 88.9% (2593 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 89.6% (2615 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 91.0% (2654 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 91.5% (2670 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 92.5% (2700 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 93.7% (2735 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 94.7% (2763 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=903 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.3; 1688 / 2918 (P = 57.85%) round 27]               
[00:00:00] Finding cutoff p=898 [91.1Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=898 1.0% (30 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 2.3% (67 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 4.4% (127 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 6.0% (176 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 8.5% (247 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 9.5% (277 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 10.7% (313 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 13.1% (381 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 16.4% (478 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 18.5% (540 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 19.7% (576 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 22.9% (668 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 26.3% (767 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 28.2% (823 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 31.8% (929 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 33.9% (990 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 35.3% (1029 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 37.2% (1086 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 40.6% (1186 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 42.2% (1231 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 43.6% (1271 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 45.3% (1321 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 46.9% (1370 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 47.9% (1397 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 49.1% (1434 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 50.9% (1484 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 52.9% (1544 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 54.9% (1601 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 57.1% (1665 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 58.6% (1710 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 60.0% (1752 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 61.5% (1795 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 62.7% (1831 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 64.2% (1873 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 65.4% (1907 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 66.8% (1948 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 69.5% (2028 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 72.0% (2101 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 73.2% (2135 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 74.1% (2161 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 75.2% (2194 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 76.4% (2230 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 78.8% (2300 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 80.7% (2356 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 81.7% (2383 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 82.4% (2405 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 83.5% (2436 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 84.4% (2464 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 86.1% (2513 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 86.9% (2536 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 87.6% (2556 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 88.6% (2585 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 89.7% (2617 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 91.2% (2660 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 91.8% (2678 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 92.5% (2700 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 93.7% (2733 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 94.7% (2762 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=898 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.8; 1729 / 2918 (P = 59.25%) round 28]               
[00:00:00] Finding cutoff p=887 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=887 1.5% (43 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 3.5% (101 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 6.4% (186 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 9.5% (278 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 10.9% (318 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 13.2% (384 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 15.3% (447 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 18.3% (534 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 20.9% (609 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 22.7% (662 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 25.1% (732 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 27.7% (809 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 29.7% (866 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 32.8% (958 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 34.4% (1003 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 36.6% (1068 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 37.6% (1098 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 39.6% (1156 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 41.1% (1199 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 42.2% (1230 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 44.0% (1283 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 45.2% (1320 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 46.5% (1356 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 47.9% (1399 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 49.7% (1451 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 51.7% (1508 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 53.6% (1563 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 55.3% (1615 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 56.4% (1646 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 58.1% (1696 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 59.5% (1736 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 60.7% (1770 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 62.0% (1808 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 62.8% (1833 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 65.1% (1900 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 67.7% (1976 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 69.3% (2022 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 70.5% (2057 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 71.5% (2085 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 72.6% (2119 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 74.3% (2168 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 76.0% (2218 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 77.0% (2246 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 78.1% (2280 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 79.3% (2314 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 80.5% (2350 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 81.8% (2386 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 82.9% (2419 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 84.0% (2450 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 84.9% (2478 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 85.7% (2502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 86.5% (2524 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 87.5% (2553 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 88.5% (2583 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 89.6% (2615 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 90.5% (2642 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 91.5% (2670 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 92.7% (2706 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 93.6% (2732 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 94.6% (2761 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 95.6% (2790 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=887 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 88.7; 1764 / 2918 (P = 60.45%) round 29]               
[00:00:00] Finding cutoff p=877 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=877 1.4% (42 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 3.8% (112 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 6.4% (188 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 9.4% (274 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 10.8% (316 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 12.4% (363 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 14.9% (435 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 18.2% (532 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 20.9% (609 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 22.5% (656 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 25.2% (735 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 27.5% (802 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 29.9% (872 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 32.9% (961 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 34.5% (1008 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 36.9% (1076 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 38.0% (1110 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 40.5% (1182 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 42.9% (1253 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 44.3% (1294 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 45.3% (1321 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 46.4% (1355 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 48.2% (1406 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 49.5% (1445 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 51.1% (1492 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 52.2% (1524 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 53.3% (1555 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 55.2% (1611 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 58.0% (1691 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 59.7% (1742 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 61.2% (1786 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 62.3% (1819 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 63.4% (1849 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 64.3% (1875 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 65.7% (1917 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 68.4% (1995 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 70.4% (2053 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 71.8% (2096 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 72.9% (2128 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 73.9% (2155 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 74.8% (2182 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 76.7% (2238 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 78.1% (2279 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 79.1% (2308 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 80.0% (2333 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 81.1% (2367 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 82.0% (2392 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 82.8% (2417 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 83.6% (2439 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 85.2% (2485 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 86.1% (2511 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 86.8% (2532 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 87.6% (2555 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 88.8% (2591 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 89.4% (2610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 90.9% (2653 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 91.6% (2674 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 92.6% (2702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 93.6% (2731 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=877 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 87.7; 1830 / 2918 (P = 62.71%) round 30]               
[00:00:00] Finding cutoff p=867 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=867 1.6% (46 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 2.8% (83 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 5.7% (167 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 8.8% (257 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 9.8% (285 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 11.4% (332 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 13.7% (401 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 16.1% (469 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 18.1% (529 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 19.5% (569 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 21.8% (636 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 24.2% (705 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 26.5% (774 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 29.0% (847 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 30.9% (903 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 32.5% (947 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 33.9% (988 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 35.7% (1043 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 37.6% (1098 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 39.8% (1162 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 41.1% (1198 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 42.2% (1231 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 43.8% (1277 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 44.9% (1309 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 46.3% (1351 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 48.0% (1400 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 49.5% (1445 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 52.2% (1522 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 53.9% (1573 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 55.2% (1610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 57.2% (1669 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 58.5% (1707 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 59.7% (1743 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 61.2% (1785 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 62.1% (1813 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 63.7% (1858 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 65.8% (1919 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 66.8% (1948 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 68.1% (1986 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 69.0% (2012 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 70.2% (2049 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 72.8% (2125 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 74.3% (2168 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 75.4% (2199 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 76.4% (2228 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 77.5% (2262 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 78.8% (2299 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 79.4% (2317 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 80.5% (2350 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 81.8% (2388 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 82.6% (2411 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 83.7% (2443 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 84.5% (2467 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 85.4% (2493 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 86.5% (2524 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 87.7% (2559 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 89.1% (2601 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 89.7% (2618 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 90.5% (2640 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 91.6% (2673 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 92.5% (2700 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 93.7% (2733 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 94.8% (2765 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 95.9% (2797 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 96.7% (2822 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=867 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 86.7; 1895 / 2918 (P = 64.94%) round 31]               
[00:00:00] Finding cutoff p=856 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=856 1.1% (31 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 4.0% (116 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 6.5% (189 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 8.8% (257 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 10.8% (314 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 12.7% (371 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 15.0% (437 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 17.2% (502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 18.9% (551 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 20.2% (590 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 21.7% (634 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 24.1% (703 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 26.9% (784 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 29.4% (857 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 31.0% (904 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 32.7% (954 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 34.2% (997 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 36.3% (1059 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 38.1% (1112 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 40.2% (1174 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 41.3% (1205 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 42.6% (1243 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 43.8% (1279 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 45.1% (1315 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 46.6% (1360 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 48.4% (1412 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 50.4% (1470 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 52.7% (1537 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 54.7% (1595 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 55.7% (1625 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 57.5% (1677 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 58.5% (1707 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 59.5% (1736 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 60.6% (1768 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 61.4% (1791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 64.1% (1871 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 66.7% (1947 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 68.0% (1983 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 69.3% (2022 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 70.9% (2069 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 72.7% (2122 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 73.9% (2157 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 75.1% (2191 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 76.1% (2222 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 77.5% (2262 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 78.9% (2302 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 80.2% (2339 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 80.9% (2361 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 81.6% (2381 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 82.4% (2404 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 83.9% (2447 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 84.9% (2476 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 86.4% (2520 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 87.4% (2551 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 88.5% (2581 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 89.4% (2610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 90.7% (2647 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 91.7% (2675 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 92.6% (2702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 93.7% (2733 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 94.7% (2762 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 95.6% (2790 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 96.7% (2823 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=856 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.6; 1976 / 2918 (P = 67.72%) round 32]               
[00:00:00] Finding cutoff p=845 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=845 1.6% (46 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 3.6% (106 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 5.4% (159 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 8.1% (235 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 9.4% (273 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 10.9% (317 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 12.5% (366 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 15.5% (453 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 18.0% (524 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 19.1% (558 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 21.2% (620 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 23.8% (695 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 26.7% (779 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 30.3% (884 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 31.8% (929 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 32.9% (960 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 34.8% (1015 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 37.1% (1083 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 40.1% (1171 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 41.2% (1201 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 42.4% (1237 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 43.8% (1279 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 45.4% (1325 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 47.0% (1371 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 48.9% (1426 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 51.7% (1509 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 54.2% (1583 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 55.6% (1622 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 57.5% (1678 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 58.8% (1717 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 59.8% (1746 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 60.8% (1774 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 62.0% (1809 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 64.4% (1879 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 66.9% (1952 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 68.5% (1998 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 69.6% (2032 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 70.4% (2055 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 71.7% (2092 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 74.0% (2160 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 75.6% (2205 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 76.5% (2233 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 77.4% (2258 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 78.9% (2301 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 79.9% (2331 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 80.5% (2349 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 82.6% (2409 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 83.4% (2434 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 84.5% (2465 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 85.5% (2496 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 86.4% (2520 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 87.4% (2550 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 88.5% (2582 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 89.8% (2619 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 90.6% (2643 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 91.7% (2676 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 92.7% (2706 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 93.6% (2732 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 94.6% (2761 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 95.7% (2793 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=845 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.5; 2038 / 2918 (P = 69.84%) round 33]               
[00:00:00] Finding cutoff p=835 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=835 1.4% (41 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 3.6% (106 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 5.8% (168 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 8.4% (245 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 9.4% (273 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 10.6% (308 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 12.3% (358 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 15.1% (440 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 17.2% (502 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 19.2% (560 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 20.5% (598 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 22.8% (666 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 25.1% (733 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 27.7% (808 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 29.0% (847 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 30.4% (888 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 32.4% (944 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 35.3% (1029 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 36.7% (1070 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 38.9% (1135 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 40.5% (1181 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 41.3% (1205 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 43.1% (1258 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 44.2% (1289 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 45.7% (1334 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 47.3% (1379 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 49.2% (1435 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 51.2% (1493 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 53.6% (1565 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 55.3% (1613 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 56.6% (1652 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 57.8% (1686 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 58.8% (1717 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 59.7% (1742 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 61.4% (1793 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 63.6% (1855 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 65.4% (1909 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 67.0% (1956 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 68.1% (1987 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 69.3% (2021 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 70.2% (2047 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 72.4% (2113 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 73.9% (2155 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 75.1% (2192 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 76.2% (2224 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 77.3% (2256 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 78.5% (2291 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 79.5% (2321 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 80.9% (2361 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 81.7% (2385 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 82.7% (2414 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 83.6% (2438 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 84.9% (2476 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 85.7% (2500 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 86.8% (2532 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 88.0% (2569 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 89.0% (2597 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 89.7% (2618 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 90.6% (2643 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 91.6% (2672 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 92.6% (2701 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 93.6% (2730 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 83.5; 2109 / 2918 (P = 72.28%) round 34]               
[00:00:00] Finding cutoff p=824 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=824 1.0% (30 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 3.8% (110 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 5.4% (159 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 8.0% (233 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 8.6% (252 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 10.6% (308 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 13.5% (393 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 16.2% (474 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 18.5% (539 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 19.8% (579 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 21.9% (640 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 24.1% (702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 26.7% (779 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 30.4% (886 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 31.5% (919 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 32.6% (950 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 34.1% (994 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 36.3% (1058 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 37.4% (1090 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 38.5% (1123 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 39.7% (1158 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 40.8% (1192 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 41.8% (1221 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 43.2% (1261 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 44.5% (1299 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 45.8% (1336 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 47.3% (1381 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 49.6% (1447 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 51.6% (1507 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 53.1% (1548 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 54.8% (1599 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 55.8% (1627 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 57.0% (1663 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 57.6% (1682 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 60.2% (1757 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 62.3% (1817 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 63.2% (1843 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 64.2% (1874 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 64.9% (1894 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 66.2% (1933 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 68.3% (1993 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 70.2% (2048 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 71.3% (2081 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 72.6% (2118 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 73.6% (2147 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 74.7% (2179 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 76.4% (2229 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 77.1% (2250 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 78.4% (2288 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 79.2% (2310 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 80.3% (2342 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 81.4% (2374 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 82.6% (2411 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 84.7% (2471 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 85.9% (2507 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 86.4% (2522 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 88.1% (2571 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 88.8% (2591 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 89.5% (2611 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 90.6% (2643 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 91.8% (2678 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 92.5% (2700 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 93.6% (2731 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 94.7% (2762 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 96.8% (2825 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=824 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.4; 2188 / 2918 (P = 74.98%) round 35]               
[00:00:00] Finding cutoff p=815 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=815 1.5% (45 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 3.9% (114 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 5.9% (172 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 7.7% (225 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 9.0% (263 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 10.0% (291 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 11.6% (338 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 13.7% (399 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 16.3% (475 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 17.6% (513 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 19.1% (558 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 21.5% (626 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 23.1% (673 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 25.4% (740 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 26.9% (784 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 28.0% (816 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 29.5% (862 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 32.7% (953 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 34.5% (1007 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 36.6% (1067 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 37.5% (1095 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 38.5% (1122 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 40.1% (1169 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 41.1% (1198 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 42.8% (1250 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 44.1% (1286 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 45.8% (1337 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 48.5% (1414 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 50.8% (1483 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 51.8% (1512 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 53.9% (1572 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 54.5% (1591 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 55.8% (1627 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 56.8% (1658 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 58.9% (1718 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 60.7% (1772 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 62.1% (1811 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 62.9% (1836 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 64.1% (1869 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 66.4% (1938 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 68.4% (1995 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 69.5% (2027 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 70.6% (2061 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 72.1% (2104 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 73.3% (2138 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 74.2% (2164 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 76.2% (2223 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 77.5% (2261 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 78.4% (2287 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 79.4% (2317 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 80.6% (2353 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 81.4% (2375 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 82.9% (2418 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 83.9% (2447 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 84.9% (2476 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 85.4% (2491 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 87.0% (2538 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 87.7% (2560 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 88.9% (2593 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 89.4% (2610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 90.5% (2640 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 91.7% (2675 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 92.5% (2700 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 93.6% (2730 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 94.7% (2763 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 95.6% (2790 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 81.5; 2240 / 2918 (P = 76.76%) round 36]               
[00:00:00] Finding cutoff p=804 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=804 1.1% (31 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 4.3% (126 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 6.2% (182 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 8.7% (255 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 9.8% (285 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 10.7% (313 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 12.8% (374 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 14.6% (426 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 16.0% (468 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 17.5% (511 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 19.9% (580 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 21.6% (631 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 24.5% (715 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 26.7% (778 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 27.6% (806 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 29.0% (846 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 30.1% (878 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 31.8% (927 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 32.9% (960 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 34.7% (1012 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 36.2% (1057 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 37.1% (1083 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 38.1% (1112 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 39.3% (1148 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 40.7% (1188 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 41.5% (1212 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 43.0% (1255 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 45.8% (1335 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 48.1% (1404 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 49.2% (1435 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 50.8% (1482 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 52.2% (1524 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 53.5% (1562 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 54.7% (1596 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 56.3% (1643 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 58.1% (1694 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 59.9% (1749 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 60.8% (1775 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 61.9% (1806 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 63.6% (1855 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 65.3% (1905 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 65.8% (1921 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 67.0% (1954 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 68.7% (2004 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 69.8% (2036 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 70.7% (2063 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 71.6% (2088 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 73.1% (2133 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 74.2% (2164 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 75.3% (2197 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 77.1% (2250 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 78.1% (2280 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 79.6% (2323 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 80.7% (2354 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 81.7% (2385 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 82.3% (2402 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 84.0% (2450 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 84.5% (2466 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 85.4% (2492 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 87.0% (2538 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 87.8% (2562 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 88.6% (2586 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 89.7% (2617 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 90.6% (2644 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 91.8% (2678 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 92.5% (2700 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 93.9% (2740 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 94.6% (2760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 95.6% (2791 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 96.7% (2823 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=804 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.4; 2298 / 2918 (P = 78.75%) round 37]               
[00:00:00] Finding cutoff p=794 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=794 1.3% (38 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 3.3% (97 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 5.3% (156 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 7.5% (219 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 8.9% (260 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 10.0% (291 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 11.3% (329 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 13.8% (403 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 15.8% (460 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 17.7% (516 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 19.3% (562 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 21.2% (619 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 23.7% (693 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 26.1% (761 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 27.4% (800 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 28.3% (825 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 29.4% (859 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 31.9% (932 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 33.5% (978 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 35.2% (1027 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 36.8% (1073 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 38.1% (1113 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 39.4% (1150 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 40.8% (1191 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 42.5% (1241 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 44.2% (1289 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 45.3% (1322 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 47.8% (1395 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 50.0% (1459 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 51.1% (1490 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 52.4% (1529 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 53.6% (1563 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 54.6% (1594 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 55.6% (1621 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 56.6% (1652 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 58.6% (1710 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 60.6% (1767 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 61.6% (1797 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 62.1% (1812 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 63.2% (1845 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 63.9% (1864 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 65.0% (1896 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 67.0% (1956 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 68.0% (1984 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 69.4% (2025 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 70.6% (2059 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 71.8% (2095 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 72.4% (2113 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 73.9% (2156 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 74.8% (2182 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 75.4% (2200 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 76.1% (2222 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 77.4% (2259 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 78.7% (2297 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 79.8% (2330 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 80.5% (2350 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 82.0% (2393 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 83.0% (2421 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 83.8% (2445 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 84.4% (2462 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 85.8% (2504 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 86.9% (2535 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 87.6% (2556 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 88.7% (2589 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 89.6% (2614 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 90.6% (2645 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 91.8% (2678 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 92.6% (2702 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 93.8% (2736 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 94.7% (2763 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 96.8% (2824 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=794 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 79.4; 2346 / 2918 (P = 80.40%) round 38]               
[00:00:00] Finding cutoff p=783 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=783 1.4% (41 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 2.9% (86 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 5.2% (151 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 6.7% (195 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 7.7% (225 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 8.4% (246 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 9.9% (288 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 11.8% (343 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 13.5% (393 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 14.6% (426 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 16.5% (482 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 19.2% (559 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 20.7% (603 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 23.4% (682 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 25.4% (740 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 26.5% (772 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 27.7% (807 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 28.8% (840 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 30.1% (877 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 31.0% (906 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 32.7% (955 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 33.8% (986 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 35.3% (1029 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 36.8% (1074 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 39.0% (1137 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 40.3% (1176 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 41.7% (1218 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 43.8% (1277 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 46.4% (1354 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 47.4% (1383 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 49.0% (1430 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 49.9% (1456 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 50.6% (1476 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 51.6% (1505 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 53.9% (1572 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 55.9% (1630 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 57.7% (1685 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 58.6% (1710 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 59.7% (1742 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 61.6% (1797 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 63.7% (1859 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 64.5% (1882 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 65.2% (1903 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 66.8% (1948 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 67.5% (1970 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 68.7% (2004 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 69.6% (2030 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 71.5% (2085 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 72.4% (2114 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 73.2% (2137 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 74.3% (2169 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 75.6% (2207 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 76.6% (2236 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 77.5% (2261 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 78.2% (2282 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 79.9% (2331 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 80.8% (2359 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 81.5% (2379 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 82.2% (2400 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 83.8% (2444 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 84.6% (2469 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 85.7% (2501 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 86.6% (2526 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 87.5% (2553 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 88.4% (2580 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 89.5% (2611 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 90.5% (2642 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 91.6% (2672 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 92.8% (2708 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 93.8% (2736 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 94.8% (2767 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 95.7% (2793 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=783 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.3; 2397 / 2918 (P = 82.15%) round 39]               
[00:00:00] Finding cutoff p=772 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=772 1.6% (46 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 3.9% (114 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 5.7% (165 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 8.1% (236 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 9.0% (263 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 9.6% (280 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 11.5% (337 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 13.8% (402 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 15.5% (452 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 17.4% (508 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 19.3% (562 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 20.8% (607 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 23.0% (670 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 24.4% (713 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 25.3% (738 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 26.4% (769 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 28.3% (826 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 29.0% (846 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 30.5% (891 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 32.3% (942 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 33.5% (978 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 35.4% (1033 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 36.7% (1071 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 38.3% (1118 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 39.5% (1152 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 40.4% (1179 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 42.5% (1239 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 44.3% (1293 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 45.3% (1321 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 46.3% (1350 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 47.7% (1392 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 48.7% (1420 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 51.2% (1493 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 52.9% (1544 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 54.7% (1596 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 55.6% (1621 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 56.9% (1660 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 59.2% (1726 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 61.5% (1796 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 62.6% (1828 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 63.3% (1846 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 64.3% (1877 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 65.4% (1907 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 67.0% (1954 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 67.9% (1982 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 69.7% (2033 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 70.5% (2056 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 71.2% (2077 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 72.8% (2125 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 74.3% (2168 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 75.6% (2205 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 76.9% (2245 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 77.9% (2274 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 79.4% (2316 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 80.3% (2343 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 81.6% (2380 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 82.4% (2403 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 83.4% (2434 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 84.3% (2461 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 85.5% (2496 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 86.6% (2526 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 87.5% (2552 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 88.5% (2583 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 89.4% (2610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 91.1% (2658 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 91.9% (2683 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 92.6% (2701 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 93.7% (2734 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 94.9% (2768 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 95.8% (2794 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=772 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 77.2; 2443 / 2918 (P = 83.72%) round 40]               
[00:00:00] Finding cutoff p=763 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=763 1.5% (43 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 3.8% (111 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 5.8% (168 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 8.2% (240 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 9.4% (275 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 12.1% (352 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 14.8% (433 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 16.7% (486 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 18.3% (535 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 19.7% (576 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 22.1% (645 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 23.6% (688 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 26.0% (760 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 27.3% (797 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 28.4% (828 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 29.9% (872 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 32.6% (950 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 33.7% (982 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 34.7% (1012 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 35.8% (1046 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 36.6% (1069 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 37.9% (1106 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 39.0% (1137 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 40.5% (1182 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 41.8% (1220 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 43.0% (1255 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 45.0% (1313 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 47.0% (1372 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 47.8% (1394 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 49.3% (1438 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 50.3% (1469 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 51.5% (1504 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 52.6% (1534 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 54.6% (1593 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 57.1% (1667 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 58.5% (1706 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 59.3% (1729 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 60.1% (1753 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 60.8% (1775 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 62.9% (1834 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 64.5% (1881 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 65.1% (1900 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 66.4% (1939 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 68.2% (1989 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 69.1% (2016 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 70.1% (2046 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 71.4% (2083 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 73.6% (2148 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 74.5% (2173 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 76.0% (2219 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 76.8% (2242 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 77.9% (2272 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 78.8% (2300 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 79.2% (2311 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 80.3% (2344 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 82.2% (2399 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 83.3% (2431 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 84.4% (2463 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 85.6% (2497 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 86.4% (2521 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 87.6% (2555 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 88.6% (2584 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 89.4% (2610 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 90.5% (2641 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 91.6% (2672 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 92.7% (2705 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 93.6% (2731 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 94.8% (2765 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 95.6% (2790 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 96.7% (2822 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=763 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 76.3; 2474 / 2918 (P = 84.78%) round 41]               
[00:00:00] Finding cutoff p=754 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=754 1.4% (41 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 4.1% (119 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 5.5% (160 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 7.1% (207 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 8.2% (239 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 9.1% (266 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 10.7% (313 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 13.3% (389 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 15.6% (455 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 16.6% (485 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 18.6% (544 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 20.7% (605 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 23.0% (672 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 25.3% (737 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 26.7% (778 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 27.8% (811 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 28.9% (844 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 30.9% (902 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 31.9% (930 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 34.2% (997 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 35.5% (1035 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 36.4% (1063 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 37.4% (1091 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 38.7% (1129 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 39.9% (1164 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 41.2% (1201 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 42.5% (1240 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 45.0% (1313 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 46.9% (1369 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 47.9% (1397 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 49.1% (1434 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 50.4% (1472 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 51.7% (1509 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 52.5% (1532 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 54.6% (1594 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 56.8% (1657 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 58.4% (1705 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 58.8% (1717 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 59.6% (1740 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 60.7% (1770 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 62.6% (1826 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 64.5% (1881 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 64.9% (1895 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 65.9% (1924 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 67.2% (1960 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 68.0% (1985 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 69.2% (2020 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 70.0% (2043 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 71.9% (2098 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 72.6% (2118 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 73.4% (2141 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 74.5% (2174 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 75.6% (2207 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 76.9% (2245 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 77.7% (2266 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 78.5% (2290 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 79.5% (2321 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 81.0% (2365 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 82.3% (2401 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 83.5% (2437 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 84.6% (2468 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 85.5% (2495 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 86.9% (2535 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 87.5% (2553 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 88.7% (2588 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 89.7% (2616 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 90.7% (2648 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 91.5% (2670 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 92.9% (2712 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 93.6% (2730 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 94.8% (2767 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 95.9% (2797 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 96.6% (2820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 75.4; 2519 / 2918 (P = 86.33%) round 42]               
[00:00:00] Finding cutoff p=745 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=745 1.5% (43 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 3.6% (105 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 5.3% (155 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 7.2% (211 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 8.4% (245 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 9.9% (290 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 11.6% (338 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 13.5% (394 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 14.9% (436 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 16.1% (469 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 17.7% (517 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 19.6% (571 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 21.7% (633 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 24.3% (710 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 25.6% (748 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 26.9% (784 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 28.0% (816 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 29.4% (857 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 30.5% (889 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 32.2% (941 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 33.7% (984 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 34.9% (1019 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 36.9% (1077 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 38.0% (1109 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 39.0% (1137 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 40.1% (1169 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 41.1% (1198 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 43.2% (1260 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 45.2% (1320 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 46.4% (1353 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 47.9% (1399 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 49.1% (1434 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 50.3% (1469 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 50.9% (1484 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 51.9% (1515 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 54.2% (1581 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 55.8% (1629 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 57.4% (1674 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 58.0% (1692 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 58.7% (1713 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 60.8% (1774 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 62.4% (1820 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 62.9% (1835 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 63.8% (1862 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 65.1% (1900 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 66.2% (1931 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 68.1% (1988 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 69.1% (2017 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 70.0% (2044 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 71.0% (2072 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 72.4% (2112 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 73.8% (2153 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 75.0% (2189 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 75.7% (2210 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 77.8% (2271 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 79.1% (2308 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 79.7% (2327 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 80.3% (2344 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 81.8% (2386 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 82.5% (2407 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 83.5% (2436 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 84.6% (2470 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 85.8% (2505 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 86.4% (2522 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 87.4% (2551 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 88.7% (2589 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 89.6% (2614 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 91.0% (2654 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 91.7% (2676 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 92.6% (2701 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 93.8% (2736 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 94.9% (2768 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 95.7% (2793 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 96.7% (2822 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=745 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 74.5; 2541 / 2918 (P = 87.08%) round 43]               
[00:00:00] Finding cutoff p=735 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=735 1.4% (40 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 3.4% (98 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 5.2% (153 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 6.7% (196 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 7.7% (225 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 8.8% (256 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 10.1% (295 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 12.0% (351 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 14.3% (418 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 15.3% (446 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 16.8% (491 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 18.8% (550 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 20.8% (608 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 22.8% (664 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 23.8% (695 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 25.1% (732 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 26.5% (772 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 28.5% (833 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 29.8% (869 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 30.8% (900 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 33.2% (968 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 34.1% (996 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 36.3% (1059 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 37.9% (1105 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 39.9% (1165 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 40.8% (1191 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 41.8% (1219 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 43.9% (1282 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 45.9% (1338 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 46.7% (1362 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 48.3% (1409 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 49.5% (1443 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 50.9% (1484 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 51.9% (1515 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 53.9% (1572 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 55.0% (1605 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 56.3% (1644 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 56.8% (1657 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 57.6% (1681 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 59.8% (1746 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 61.4% (1792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 61.9% (1805 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 63.0% (1839 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 63.8% (1861 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 65.1% (1899 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 66.3% (1934 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 67.2% (1961 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 68.6% (2003 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 69.3% (2023 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 70.0% (2042 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 71.6% (2088 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 72.7% (2122 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 73.6% (2149 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 75.2% (2193 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 77.4% (2258 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 78.3% (2286 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 79.3% (2313 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 80.3% (2342 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 81.3% (2373 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 82.5% (2406 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 83.4% (2435 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 84.5% (2466 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 85.3% (2490 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 86.8% (2533 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 87.5% (2554 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 89.3% (2605 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 90.5% (2640 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 91.5% (2670 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 92.7% (2705 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 93.6% (2732 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 94.8% (2767 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 96.7% (2822 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=735 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 73.5; 2573 / 2918 (P = 88.18%) round 44]               
[00:00:00] Finding cutoff p=726 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=726 1.4% (41 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 3.0% (88 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 5.1% (148 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 7.0% (204 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 8.0% (233 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 8.8% (256 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 10.6% (310 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 13.1% (383 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 15.0% (437 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 16.3% (476 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 17.4% (509 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 19.1% (558 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 21.1% (617 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 23.7% (691 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 24.9% (727 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 26.1% (763 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 27.5% (802 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 29.4% (859 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 30.4% (887 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 31.9% (932 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 34.1% (994 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 35.7% (1042 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 36.9% (1077 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 38.0% (1108 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 39.5% (1153 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 40.5% (1183 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 42.9% (1252 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 44.9% (1310 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 45.8% (1337 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 47.4% (1382 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 48.4% (1413 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 50.1% (1462 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 51.3% (1496 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 52.0% (1518 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 54.3% (1584 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 56.5% (1650 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 58.2% (1699 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 58.7% (1714 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 59.9% (1748 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 61.7% (1801 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 63.5% (1853 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 64.1% (1871 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 65.1% (1899 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 66.3% (1936 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 67.2% (1960 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 67.9% (1981 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 70.5% (2056 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 71.1% (2075 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 72.1% (2105 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 73.5% (2146 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 74.7% (2180 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 75.9% (2215 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 76.5% (2231 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 78.2% (2282 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 79.3% (2315 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 81.0% (2365 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 81.6% (2381 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 82.7% (2414 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 83.4% (2435 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 84.7% (2472 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 85.6% (2498 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 86.4% (2522 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 87.6% (2556 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 88.7% (2589 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 89.7% (2616 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 90.6% (2645 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 91.7% (2675 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 92.8% (2708 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 93.8% (2738 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 94.7% (2762 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 95.7% (2792 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=726 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 72.6; 2596 / 2918 (P = 88.97%) round 45]               
[00:00:00] Finding cutoff p=716 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=716 1.5% (45 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 3.8% (112 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 5.6% (162 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 7.2% (210 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 8.4% (244 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 9.9% (290 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 11.7% (342 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 13.2% (384 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 14.2% (415 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 15.9% (465 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 17.5% (511 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 18.8% (548 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 21.1% (615 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 22.4% (655 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 23.5% (686 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 24.5% (716 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 26.7% (778 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 27.8% (812 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 29.2% (852 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 30.9% (903 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 32.0% (935 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 34.0% (992 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 35.4% (1033 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 36.9% (1077 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 37.9% (1106 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 39.6% (1155 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 41.3% (1204 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 42.7% (1247 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 43.8% (1279 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 45.4% (1326 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 46.9% (1369 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 47.9% (1399 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 49.2% (1436 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 50.1% (1463 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 51.8% (1511 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 53.4% (1558 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 54.4% (1586 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 55.1% (1607 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 55.6% (1622 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 58.2% (1699 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 60.2% (1757 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 60.8% (1774 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 62.0% (1808 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 63.3% (1847 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 64.4% (1878 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 64.9% (1893 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 67.5% (1971 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 68.0% (1984 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 69.4% (2024 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 70.2% (2048 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 71.8% (2095 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 73.0% (2130 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 74.1% (2162 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 76.4% (2230 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 77.8% (2271 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 78.6% (2293 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 79.2% (2311 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 80.3% (2343 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 82.1% (2395 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 82.4% (2404 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 83.3% (2432 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 84.5% (2466 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 85.4% (2493 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 87.0% (2538 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 87.7% (2560 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 88.4% (2580 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 89.6% (2615 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 90.6% (2645 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 91.5% (2671 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 92.8% (2709 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 93.8% (2737 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 94.7% (2762 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 95.9% (2798 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 96.7% (2823 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=716 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 71.6; 2622 / 2918 (P = 89.86%) round 46]               
[00:00:00] Finding cutoff p=707 [91.0Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=707 1.3% (39 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 2.8% (82 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 5.0% (147 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 7.6% (221 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 8.3% (243 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 9.3% (270 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 10.8% (314 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 12.8% (373 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 14.4% (420 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 16.6% (485 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 17.8% (519 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 19.7% (575 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 22.0% (642 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 23.2% (678 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 24.2% (707 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 25.6% (747 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 27.1% (790 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 28.1% (821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 29.5% (862 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 31.5% (920 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 32.0% (935 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 33.8% (985 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 35.4% (1032 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 37.2% (1086 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 38.6% (1125 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 40.0% (1166 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 42.0% (1225 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 43.9% (1280 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 44.9% (1309 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 46.3% (1350 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 47.4% (1382 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 48.8% (1424 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 49.4% (1441 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 51.3% (1497 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 52.7% (1539 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 54.5% (1589 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 55.1% (1609 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 55.8% (1627 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 56.8% (1656 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 58.7% (1713 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 60.5% (1765 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 61.1% (1782 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 62.2% (1816 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 63.8% (1861 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 65.6% (1913 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 66.4% (1938 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 68.4% (1996 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 68.9% (2011 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 70.5% (2057 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 72.5% (2117 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 73.5% (2144 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 74.4% (2172 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 76.7% (2239 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 77.8% (2270 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 78.6% (2294 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 79.3% (2315 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 80.6% (2351 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 81.2% (2370 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 82.7% (2414 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 83.8% (2446 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 84.4% (2464 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 85.3% (2490 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 86.6% (2526 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 87.6% (2555 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 88.8% (2591 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 90.1% (2628 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 90.6% (2645 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 91.6% (2672 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 92.9% (2711 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 93.7% (2733 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 94.7% (2762 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 96.1% (2805 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 96.7% (2821 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 97.7% (2850 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 98.7% (2880 of 2918), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=707 99.7% (2910 of 2918), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 70.7; 2641 / 2918 (P = 90.51%) round 47]               
[00:00:00] Finished Preparing TNF Graph Building [pTNF = 69.60] [91.0Gb / 503.5Gb]                                            
[00:00:00] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=2918 maxEdges=200
[00:00:00] Building TNF Graph 52.9% (1545 of 2918), ETA 0:00:00     [91.0Gb / 503.5Gb]                           
[00:00:00] Finished Building TNF Graph (112471 edges) [91.0Gb / 503.5Gb]                                          
[00:00:00] Cleaned up after Building TNF Graph (112471 edges) [91.0Gb / 503.5Gb]                                          
[00:00:00] Cleaned up TNF matrix of large contigs [91.0Gb / 503.5Gb]                                             
[00:00:00] Applying coverage correlations to TNF graph with 112471 edges
[00:00:00] Allocated memory for graph edges [91.0Gb / 503.5Gb]
[00:00:00] ... calculating abundance dist 1.0% (1135 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 2.0% (2267 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 3.0% (3379 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 4.0% (4509 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 5.0% (5642 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 6.0% (6755 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 7.0% (7882 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 8.0% (9006 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 9.0% (10140 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 10.0% (11250 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 11.0% (12379 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 12.0% (13509 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 13.0% (14640 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 14.0% (15763 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 15.0% (16891 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 16.0% (18006 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 17.0% (19133 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 18.0% (20251 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 19.0% (21384 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 20.0% (22520 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 21.0% (23636 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 22.0% (24750 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 23.0% (25894 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 24.0% (27010 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 25.0% (28145 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 26.0% (29265 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 27.0% (30383 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 28.0% (31515 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 29.0% (32646 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 30.0% (33777 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 31.0% (34876 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 32.0% (36011 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 33.0% (37148 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 34.0% (38273 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 35.0% (39388 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 36.0% (40515 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 37.0% (41643 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 38.0% (42768 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 39.0% (43889 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 40.0% (45004 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 41.0% (46126 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 42.0% (47268 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 43.0% (48389 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 44.0% (49517 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 45.0% (50641 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 46.0% (51759 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 47.0% (52889 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 48.0% (54002 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 49.0% (55137 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 50.0% (56250 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 51.0% (57387 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 52.0% (58519 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 53.0% (59645 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 54.0% (60772 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 55.0% (61895 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 56.0% (63006 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 57.0% (64139 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 58.0% (65268 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 59.0% (66396 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 60.0% (67504 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 61.0% (68642 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 62.0% (69755 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 63.0% (70875 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 64.0% (72015 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 65.0% (73143 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 66.0% (74258 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 67.0% (75397 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 68.0% (76514 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 69.0% (77629 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 70.0% (78772 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 71.0% (79901 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 72.0% (81012 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 73.0% (82132 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 74.0% (83253 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 75.0% (84376 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 76.0% (85502 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 77.0% (86630 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 78.0% (87757 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 79.0% (88875 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 80.0% (90006 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 81.0% (91128 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 82.0% (92255 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 83.0% (93375 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 84.0% (94500 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 85.0% (95628 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 86.0% (96754 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 87.0% (97875 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 88.0% (99007 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 89.0% (100125 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 90.0% (101252 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 91.0% (102375 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 92.0% (103503 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 93.0% (104629 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 94.0% (105751 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 95.0% (106877 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 96.0% (108001 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 97.0% (109130 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 98.0% (110250 of 112471), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 99.0% (111376 of 112471), ETA 0:00:00                              
[00:00:00] Calculating geometric means [91.0Gb / 503.5Gb]
[00:00:00] Traversing graph with 2918 nodes and 112471 edges [91.0Gb / 503.5Gb]
[00:00:00] Building SCR Graph and Binning (278 vertices and 301 edges) [P = 9.50%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 1.0% (1125 of 112471), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (555 vertices and 1274 edges) [P = 19.00%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 2.0% (2250 of 112471), ETA 0:00:00                               
[00:00:00] ... traversing graph 3.0% (3375 of 112471), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (832 vertices and 3280 edges) [P = 28.50%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 4.0% (4500 of 112471), ETA 0:00:00                               
[00:00:00] ... traversing graph 5.0% (5625 of 112471), ETA 0:00:00                               
[00:00:00] ... traversing graph 6.0% (6750 of 112471), ETA 0:00:00                               
[00:00:00] ... traversing graph 7.0% (7875 of 112471), ETA 0:00:00                               
[00:00:00] ... traversing graph 8.0% (9000 of 112471), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1109 vertices and 6808 edges) [P = 38.00%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 9.0% (10125 of 112471), ETA 0:00:00                               
[00:00:00] ... traversing graph 10.0% (11250 of 112471), ETA 0:00:00                               
[00:00:00] ... traversing graph 11.0% (12375 of 112471), ETA 0:00:00                               
[00:00:00] ... traversing graph 12.0% (13500 of 112471), ETA 0:00:00                               
[00:00:00] ... traversing graph 13.0% (14625 of 112471), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1381 vertices and 10632 edges) [P = 47.50%; 91.0Gb / 503.5Gb]                           
[00:00:00] Finished Traversing graph [91.0Gb / 503.5Gb]                                       
[00:00:00] Dissolved 2051 small clusters leaving 1647 leftover contigs to be re-merged into larger clusters
[00:00:00] Rescuing singleton large contigs                                   
[00:00:00] There are 9 bins already
[00:00:00] Outputting bins
[00:00:00] Writing cluster stats to: 03bins/metabat2/1507992/1507992.bin.BinInfo.txt
[00:00:00] 51.41% (7077066 bases) of large (>=1500) and 0.00% (0 bases) of small (<1500) contigs were binned.
9 bins (7077066 bases in total) formed.
[00:00:00] Finished
MetaBAT2 generated 9 bins for 1507992
MetaBAT2 binning completed for 1507992
Binning completed for sample: 1507992
