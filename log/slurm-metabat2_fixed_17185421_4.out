Decompressing scaffold file for 1507994...
Generating depth file for 1507994...
Running MetaBAT2 for 1507994 with 2kb minimum contig length and seed=123...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 2000, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 200000. with random seed=123
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [36.2Gb / 503.5Gb]
[00:00:00] Parsing assembly file [36.2Gb / 503.5Gb]
[00:00:00] ... processed 13 seqs, 13 long (>=2000), 0 short (>=1000) 1.0% (488715 of 46693117), ETA 0:00:01     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 29 seqs, 29 long (>=2000), 0 short (>=1000) 2.0% (943416 of 46693117), ETA 0:00:01     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 48 seqs, 48 long (>=2000), 0 short (>=1000) 3.0% (1415680 of 46693117), ETA 0:00:01     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 69 seqs, 69 long (>=2000), 0 short (>=1000) 4.0% (1874622 of 46693117), ETA 0:00:01     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 92 seqs, 92 long (>=2000), 0 short (>=1000) 5.0% (2336946 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 117 seqs, 117 long (>=2000), 0 short (>=1000) 6.0% (2812664 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 143 seqs, 143 long (>=2000), 0 short (>=1000) 7.0% (3278908 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 171 seqs, 171 long (>=2000), 0 short (>=1000) 8.0% (3744915 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 200 seqs, 200 long (>=2000), 0 short (>=1000) 9.0% (4205320 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 231 seqs, 231 long (>=2000), 0 short (>=1000) 10.0% (4678058 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 263 seqs, 263 long (>=2000), 0 short (>=1000) 11.0% (5147896 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 296 seqs, 296 long (>=2000), 0 short (>=1000) 12.0% (5612139 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 330 seqs, 330 long (>=2000), 0 short (>=1000) 13.0% (6071635 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 366 seqs, 366 long (>=2000), 0 short (>=1000) 14.0% (6537456 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 404 seqs, 404 long (>=2000), 0 short (>=1000) 15.0% (7009135 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 443 seqs, 443 long (>=2000), 0 short (>=1000) 16.0% (7474549 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 484 seqs, 484 long (>=2000), 0 short (>=1000) 17.0% (7945810 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 526 seqs, 526 long (>=2000), 0 short (>=1000) 18.0% (8410319 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 570 seqs, 570 long (>=2000), 0 short (>=1000) 19.0% (8877707 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 615 seqs, 615 long (>=2000), 0 short (>=1000) 20.0% (9342853 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 662 seqs, 662 long (>=2000), 0 short (>=1000) 21.0% (9815381 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 709 seqs, 709 long (>=2000), 0 short (>=1000) 22.0% (10273725 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 759 seqs, 759 long (>=2000), 0 short (>=1000) 23.0% (10744030 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 810 seqs, 810 long (>=2000), 0 short (>=1000) 24.0% (11206925 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 863 seqs, 863 long (>=2000), 0 short (>=1000) 25.0% (11674038 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 918 seqs, 918 long (>=2000), 0 short (>=1000) 26.0% (12142027 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 975 seqs, 975 long (>=2000), 0 short (>=1000) 27.0% (12609747 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1034 seqs, 1034 long (>=2000), 0 short (>=1000) 28.0% (13078819 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1094 seqs, 1094 long (>=2000), 0 short (>=1000) 29.0% (13541517 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1157 seqs, 1157 long (>=2000), 0 short (>=1000) 30.0% (14012596 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1221 seqs, 1221 long (>=2000), 0 short (>=1000) 31.0% (14475713 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1288 seqs, 1288 long (>=2000), 0 short (>=1000) 32.0% (14946375 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1356 seqs, 1356 long (>=2000), 0 short (>=1000) 33.0% (15411567 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1426 seqs, 1426 long (>=2000), 0 short (>=1000) 34.0% (15877791 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1498 seqs, 1498 long (>=2000), 0 short (>=1000) 35.0% (16342666 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1573 seqs, 1573 long (>=2000), 0 short (>=1000) 36.0% (16810297 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1651 seqs, 1651 long (>=2000), 0 short (>=1000) 37.0% (17281709 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1731 seqs, 1731 long (>=2000), 0 short (>=1000) 38.0% (17748704 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1813 seqs, 1813 long (>=2000), 0 short (>=1000) 39.0% (18213471 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1898 seqs, 1898 long (>=2000), 0 short (>=1000) 40.0% (18681633 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 1985 seqs, 1985 long (>=2000), 0 short (>=1000) 41.0% (19145533 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 2076 seqs, 2076 long (>=2000), 0 short (>=1000) 42.0% (19613717 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 2170 seqs, 2170 long (>=2000), 0 short (>=1000) 43.0% (20081008 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 2267 seqs, 2267 long (>=2000), 0 short (>=1000) 44.0% (20546571 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 2368 seqs, 2368 long (>=2000), 0 short (>=1000) 45.0% (21013916 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 2473 seqs, 2473 long (>=2000), 0 short (>=1000) 46.0% (21483052 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 2581 seqs, 2581 long (>=2000), 0 short (>=1000) 47.0% (21948254 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 2694 seqs, 2694 long (>=2000), 0 short (>=1000) 48.0% (22416329 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 2810 seqs, 2810 long (>=2000), 0 short (>=1000) 49.0% (22879952 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 2931 seqs, 2931 long (>=2000), 0 short (>=1000) 50.0% (23346822 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 3057 seqs, 3057 long (>=2000), 0 short (>=1000) 51.0% (23815326 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 3187 seqs, 3187 long (>=2000), 0 short (>=1000) 52.0% (24281481 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 3323 seqs, 3323 long (>=2000), 0 short (>=1000) 53.0% (24749818 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 3464 seqs, 3464 long (>=2000), 0 short (>=1000) 54.0% (25215549 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 3612 seqs, 3612 long (>=2000), 0 short (>=1000) 55.0% (25683284 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 3767 seqs, 3767 long (>=2000), 0 short (>=1000) 56.0% (26150552 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 3929 seqs, 3929 long (>=2000), 0 short (>=1000) 57.0% (26617612 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 4099 seqs, 4099 long (>=2000), 0 short (>=1000) 58.0% (27083087 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 4279 seqs, 4279 long (>=2000), 0 short (>=1000) 59.0% (27549136 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 4471 seqs, 4471 long (>=2000), 0 short (>=1000) 60.0% (28017626 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 4673 seqs, 4673 long (>=2000), 0 short (>=1000) 61.0% (28484135 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 4888 seqs, 4888 long (>=2000), 0 short (>=1000) 62.0% (28951280 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 5117 seqs, 5074 long (>=2000), 43 short (>=1000) 63.0% (29418547 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 5361 seqs, 5074 long (>=2000), 287 short (>=1000) 64.0% (29884622 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 5623 seqs, 5074 long (>=2000), 549 short (>=1000) 65.0% (30351280 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 5906 seqs, 5074 long (>=2000), 832 short (>=1000) 66.0% (30818200 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 6209 seqs, 5074 long (>=2000), 1135 short (>=1000) 67.0% (31285366 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 6532 seqs, 5074 long (>=2000), 1458 short (>=1000) 68.0% (31751460 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 6879 seqs, 5074 long (>=2000), 1805 short (>=1000) 69.0% (32219127 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 7250 seqs, 5074 long (>=2000), 2176 short (>=1000) 70.0% (32686049 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 7648 seqs, 5074 long (>=2000), 2574 short (>=1000) 71.0% (33152569 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 8075 seqs, 5074 long (>=2000), 3001 short (>=1000) 72.0% (33619838 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 8531 seqs, 5074 long (>=2000), 3391 short (>=1000) 73.0% (34086109 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 9017 seqs, 5074 long (>=2000), 3391 short (>=1000) 74.0% (34553068 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 9538 seqs, 5074 long (>=2000), 3391 short (>=1000) 75.0% (35020727 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 10093 seqs, 5074 long (>=2000), 3391 short (>=1000) 76.0% (35486866 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 10689 seqs, 5074 long (>=2000), 3391 short (>=1000) 77.0% (35954483 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 11324 seqs, 5074 long (>=2000), 3391 short (>=1000) 78.0% (36420754 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 12007 seqs, 5074 long (>=2000), 3391 short (>=1000) 79.0% (36887906 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 12743 seqs, 5074 long (>=2000), 3391 short (>=1000) 80.0% (37354980 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 13545 seqs, 5074 long (>=2000), 3391 short (>=1000) 81.0% (37821899 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 14417 seqs, 5074 long (>=2000), 3391 short (>=1000) 82.0% (38288681 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 15371 seqs, 5074 long (>=2000), 3391 short (>=1000) 83.0% (38755750 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 16411 seqs, 5074 long (>=2000), 3391 short (>=1000) 84.0% (39222322 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 17549 seqs, 5074 long (>=2000), 3391 short (>=1000) 85.0% (39689406 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 18796 seqs, 5074 long (>=2000), 3391 short (>=1000) 86.0% (40156469 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 20158 seqs, 5074 long (>=2000), 3391 short (>=1000) 87.0% (40623184 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 21654 seqs, 5074 long (>=2000), 3391 short (>=1000) 88.0% (41090308 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 23302 seqs, 5074 long (>=2000), 3391 short (>=1000) 89.0% (41557090 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 25104 seqs, 5074 long (>=2000), 3391 short (>=1000) 90.0% (42023934 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 27068 seqs, 5074 long (>=2000), 3391 short (>=1000) 91.0% (42490823 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 29241 seqs, 5074 long (>=2000), 3391 short (>=1000) 92.0% (42957857 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 31780 seqs, 5074 long (>=2000), 3391 short (>=1000) 93.0% (43424811 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 34765 seqs, 5074 long (>=2000), 3391 short (>=1000) 94.0% (43891667 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] ... processed 38950 seqs, 5074 long (>=2000), 3391 short (>=1000) 95.0% (44358575 of 46693117), ETA 0:00:00     [36.2Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 2000 bp are 5074, and small contigs >= 1000 bp are 3391                                                                  
[00:00:00] Allocating 5074 contigs by 1 samples abundances [36.2Gb / 503.5Gb]
[00:00:00] Allocating 5074 contigs by 1 samples variances [36.2Gb / 503.5Gb]
[00:00:00] Allocating 3391 small contigs by 1 samples abundances [36.2Gb / 503.5Gb]
[00:00:00] Reading 0.002377Gb abundance file [36.2Gb / 503.5Gb]
[00:00:00] ... processed 389 lines 389 contigs and 0 short contigs 1.0% (25581 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 781 lines 781 contigs and 0 short contigs 2.0% (51055 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 1179 lines 1179 contigs and 0 short contigs 3.0% (76575 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 1575 lines 1575 contigs and 0 short contigs 4.0% (102134 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 1970 lines 1970 contigs and 0 short contigs 5.0% (127632 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 2365 lines 2365 contigs and 0 short contigs 6.0% (153147 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 2760 lines 2760 contigs and 0 short contigs 7.0% (178649 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 3156 lines 3156 contigs and 0 short contigs 8.0% (204187 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 3551 lines 3551 contigs and 0 short contigs 9.0% (229680 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 3947 lines 3947 contigs and 0 short contigs 10.0% (255207 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 4343 lines 4343 contigs and 0 short contigs 11.0% (280746 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 4739 lines 4739 contigs and 0 short contigs 12.0% (306277 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 5135 lines 5074 contigs and 61 short contigs 13.0% (331764 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 5532 lines 5074 contigs and 458 short contigs 14.0% (357313 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 5929 lines 5074 contigs and 855 short contigs 15.0% (382857 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 6325 lines 5074 contigs and 1251 short contigs 16.0% (408337 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 6723 lines 5074 contigs and 1649 short contigs 17.0% (433883 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 7119 lines 5074 contigs and 2045 short contigs 18.0% (459369 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 7516 lines 5074 contigs and 2442 short contigs 19.0% (484904 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 7914 lines 5074 contigs and 2840 short contigs 20.0% (510427 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8311 lines 5074 contigs and 3237 short contigs 21.0% (535965 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 22.0% (561495 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 23.0% (587016 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 24.0% (612481 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 25.0% (638022 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 26.0% (663579 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 27.0% (689052 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 28.0% (714612 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 29.0% (740097 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 30.0% (765634 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 31.0% (791136 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 32.0% (816656 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 33.0% (842197 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 34.0% (867704 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 35.0% (893260 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 36.0% (918728 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 37.0% (944272 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 38.0% (969775 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 39.0% (995333 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 40.0% (1020819 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 41.0% (1046351 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 42.0% (1071860 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 43.0% (1097388 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 44.0% (1122924 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 45.0% (1148424 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 46.0% (1173939 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 47.0% (1199502 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 48.0% (1224968 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 49.0% (1250534 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 50.0% (1276051 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 51.0% (1301523 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 52.0% (1327086 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 53.0% (1352616 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 54.0% (1378124 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 55.0% (1403649 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 56.0% (1429170 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 57.0% (1454682 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 58.0% (1480193 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 59.0% (1505741 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 60.0% (1531210 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 61.0% (1556761 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 62.0% (1582252 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 63.0% (1607772 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 64.0% (1633315 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 65.0% (1658855 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 66.0% (1684333 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 67.0% (1709855 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 68.0% (1735385 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 69.0% (1760897 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 70.0% (1786431 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 71.0% (1811945 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 72.0% (1837465 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 73.0% (1862999 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 74.0% (1888492 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 75.0% (1914026 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 76.0% (1939544 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 77.0% (1965045 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 78.0% (1990601 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 79.0% (2016110 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 80.0% (2041651 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 81.0% (2067138 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 82.0% (2092691 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 83.0% (2118183 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 84.0% (2143695 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 85.0% (2169228 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 86.0% (2194741 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 87.0% (2220268 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 88.0% (2245769 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 89.0% (2271302 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 90.0% (2296828 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 91.0% (2322321 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 92.0% (2347890 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 93.0% (2373391 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 94.0% (2398901 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 95.0% (2424459 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 96.0% (2449953 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 97.0% (2475474 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 98.0% (2500996 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] ... processed 8465 lines 5074 contigs and 3391 short contigs 99.0% (2526516 of 2551906), ETA 0:00:00     [36.2Gb / 503.5Gb]                 
[00:00:00] Finished reading 40836 contigs and 1 coverages from 03bins/metabat2_fixed/1507994/temp/1507994.depth.txt [36.2Gb / 503.5Gb]. Ignored 32371 too small contigs.                                     
[00:00:00] Number of target contigs: 5074 of large (>= 2000) and 3391 of small ones (>=1000 & <2000). 
[00:00:00] Start contig TNF calculation. nobs = 5074
[00:00:00] Allocated memory for TNF [36.2Gb / 503.5Gb]
[00:00:00] Calculating TNF 1.1% (58 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 2.1% (109 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 3.0% (153 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 4.2% (211 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 5.2% (265 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 6.1% (312 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 7.2% (366 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 8.2% (417 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 9.1% (460 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 10.3% (521 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 11.1% (561 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 12.1% (613 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 13.3% (674 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 14.2% (722 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 15.4% (780 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 16.2% (824 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 17.2% (873 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 18.4% (934 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 19.2% (973 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 20.3% (1028 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 21.2% (1078 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 22.3% (1130 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 23.4% (1189 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 24.1% (1224 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 25.4% (1288 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 26.2% (1330 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 27.2% (1382 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 28.4% (1439 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 29.4% (1491 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 30.4% (1542 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 31.2% (1584 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 32.2% (1635 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 33.4% (1693 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 34.3% (1738 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 35.4% (1796 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 36.3% (1840 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 37.3% (1892 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 38.3% (1944 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 39.4% (1998 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 40.2% (2042 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 41.3% (2095 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 42.5% (2155 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 43.2% (2194 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 44.3% (2247 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 45.3% (2297 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 46.5% (2360 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 47.4% (2404 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 48.4% (2455 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 49.6% (2516 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 50.4% (2556 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 51.4% (2607 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 52.4% (2659 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 53.4% (2709 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 54.4% (2762 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 55.3% (2806 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 56.5% (2868 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 57.4% (2912 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 58.3% (2959 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 59.4% (3012 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 60.4% (3067 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 61.5% (3120 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 62.4% (3164 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 63.5% (3224 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 64.6% (3278 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 65.4% (3317 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 66.5% (3376 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 67.5% (3423 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 68.4% (3470 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 69.5% (3525 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 70.6% (3580 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 71.4% (3621 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 72.5% (3679 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 73.4% (3725 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 74.4% (3777 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 75.4% (3827 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 76.5% (3881 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 77.5% (3931 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 78.6% (3990 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 79.5% (4035 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 80.6% (4090 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 81.6% (4141 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 82.7% (4195 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 83.5% (4236 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 84.4% (4284 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 85.6% (4344 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 86.5% (4391 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 87.6% (4444 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 88.7% (4500 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 89.5% (4542 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 90.5% (4590 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 91.6% (4650 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 92.6% (4699 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 93.6% (4748 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 94.6% (4802 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 95.9% (4864 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 96.7% (4906 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 97.8% (4961 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 98.6% (5004 of 5074), ETA 0:00:00    
[00:00:00] Calculating TNF 99.7% (5058 of 5074), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [36.2Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.2% (59 of 5074), ETA 0:00:02                   
[00:00:00] ... processing TNF matrix 2.0% (102 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 3.1% (158 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 4.2% (214 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 5.0% (256 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 6.1% (312 of 5074), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 7.3% (368 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 8.1% (410 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 9.2% (466 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 10.3% (522 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 11.1% (564 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 12.2% (620 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 13.3% (676 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 14.2% (718 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 15.3% (777 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 16.1% (819 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 17.3% (877 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 18.1% (919 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 19.2% (976 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 20.4% (1033 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 21.2% (1075 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 22.3% (1131 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 23.1% (1174 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.2% (1229 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 25.3% (1285 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.2% (1329 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.3% (1386 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 28.1% (1428 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.2% (1484 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 30.4% (1541 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.2% (1582 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.3% (1638 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 33.4% (1694 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.2% (1736 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 35.3% (1793 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.2% (1836 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.3% (1891 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 38.4% (1947 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.2% (1989 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 40.3% (2046 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 41.5% (2104 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.3% (2146 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.4% (2203 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 44.2% (2245 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 45.3% (2301 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.5% (2359 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 47.3% (2401 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.4% (2457 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.5% (2512 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 50.3% (2554 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 51.4% (2610 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.3% (2653 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.4% (2708 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 54.5% (2764 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.3% (2807 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.4% (2862 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 57.5% (2918 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 58.4% (2961 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.5% (3017 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 60.3% (3060 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.4% (3117 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 62.5% (3173 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.4% (3215 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 64.5% (3272 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 65.3% (3315 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.5% (3372 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 67.5% (3427 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.4% (3469 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 69.5% (3527 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 70.6% (3584 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 71.5% (3626 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 72.6% (3682 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 73.4% (3723 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.5% (3779 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 75.6% (3835 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 76.4% (3878 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 77.6% (3935 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 78.7% (3991 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 79.5% (4033 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 80.6% (4089 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 81.7% (4144 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 82.5% (4186 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.6% (4243 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 84.4% (4284 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 85.5% (4340 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 86.6% (4396 of 5074), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 87.5% (4441 of 5074), ETA 0:00:00                   
[00:00:00] Finding cutoff p=999 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=999 6.7% (342 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 11.5% (585 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 12.5% (635 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 13.7% (695 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 15.3% (774 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 16.6% (841 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 17.3% (878 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 18.3% (930 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 19.1% (970 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 20.2% (1023 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 21.1% (1072 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 22.3% (1134 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 23.2% (1175 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 24.1% (1224 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 25.1% (1276 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 26.2% (1327 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 27.2% (1378 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 28.3% (1435 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 29.2% (1484 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 30.2% (1533 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 31.2% (1585 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 32.3% (1638 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 33.2% (1683 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 34.4% (1743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 35.4% (1795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 36.2% (1836 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 37.4% (1897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 38.3% (1944 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 39.4% (1999 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 40.2% (2040 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 41.3% (2095 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 42.3% (2144 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 43.3% (2199 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 44.2% (2245 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 45.3% (2296 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 46.3% (2350 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 47.4% (2403 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 48.3% (2452 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 49.4% (2507 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 50.4% (2559 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 51.3% (2604 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 52.3% (2656 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 53.5% (2713 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 54.7% (2778 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 55.3% (2808 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.4% (2862 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 57.4% (2914 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 58.7% (2977 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 59.4% (3015 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 60.8% (3083 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 61.4% (3116 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 63.1% (3203 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 63.8% (3239 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 64.3% (3265 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 65.6% (3330 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 66.4% (3367 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 67.4% (3422 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 68.4% (3473 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 69.6% (3531 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 70.7% (3586 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 71.4% (3625 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 72.4% (3676 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 73.5% (3729 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 74.6% (3787 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 75.5% (3829 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 76.4% (3878 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 77.6% (3936 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 78.4% (3978 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.5% (4033 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 80.4% (4082 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 81.4% (4132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 82.4% (4183 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 83.4% (4233 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 84.5% (4288 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 85.5% (4339 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 86.4% (4386 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 87.5% (4440 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.5% (4490 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.9; 0 / 5074 (P = 0.00%) round 1]               
[00:00:00] Finding cutoff p=996 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=996 1.8% (89 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 2.9% (147 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 4.0% (203 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 5.0% (253 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 6.2% (316 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 7.5% (383 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 8.7% (443 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 10.3% (523 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 12.0% (607 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 13.5% (684 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 16.0% (813 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 16.7% (846 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 17.4% (883 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 18.1% (919 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 19.4% (983 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 20.6% (1044 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 21.4% (1084 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 22.1% (1122 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 23.4% (1187 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 24.2% (1227 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 25.4% (1291 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 26.2% (1330 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 27.6% (1398 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 28.4% (1441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 29.4% (1491 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 30.4% (1545 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 31.4% (1595 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 32.6% (1652 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 33.2% (1686 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 34.3% (1742 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 35.5% (1799 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 36.5% (1854 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 37.3% (1891 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 38.5% (1956 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 39.3% (1994 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 40.5% (2056 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 41.4% (2100 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 42.6% (2162 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 43.5% (2206 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 44.7% (2267 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 45.5% (2309 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 46.3% (2348 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 47.7% (2421 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 48.6% (2464 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 49.4% (2507 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 50.5% (2561 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 51.5% (2615 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 52.6% (2671 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 53.3% (2703 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 54.4% (2758 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 55.4% (2812 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 56.5% (2867 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 57.4% (2912 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 58.3% (2958 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 59.6% (3024 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 60.6% (3073 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 61.5% (3119 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 62.4% (3168 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 63.4% (3218 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 64.4% (3266 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 65.5% (3322 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 66.6% (3379 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 67.5% (3424 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 68.6% (3479 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 69.5% (3526 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 70.4% (3572 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 71.5% (3630 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 72.6% (3682 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 73.5% (3731 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 74.5% (3778 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 75.4% (3826 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 76.4% (3876 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 77.6% (3937 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 78.6% (3989 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 79.7% (4043 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 80.6% (4088 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 81.5% (4134 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 82.4% (4182 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 83.6% (4242 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 84.6% (4291 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 85.6% (4344 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 86.6% (4393 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 87.6% (4445 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 88.5% (4493 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 89.6% (4547 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 90.5% (4593 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 91.5% (4644 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 92.5% (4694 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 93.6% (4749 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 94.5% (4797 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 95.5% (4848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 96.5% (4897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 97.5% (4949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 98.5% (5000 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=996 99.5% (5050 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.6; 4 / 5074 (P = 0.08%) round 2]               
[00:00:00] Finding cutoff p=994 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=994 1.9% (94 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 3.4% (174 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 4.9% (247 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 6.1% (312 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 7.6% (388 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 9.2% (465 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 10.8% (547 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 12.5% (632 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 14.6% (740 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 16.8% (850 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 18.3% (931 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 19.4% (982 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 20.4% (1033 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 21.3% (1079 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 22.3% (1132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 23.3% (1183 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 24.5% (1244 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 25.7% (1305 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 26.9% (1365 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 28.0% (1423 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 29.1% (1478 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 30.4% (1543 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 31.5% (1600 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 32.4% (1645 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 33.4% (1695 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 34.3% (1741 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 35.7% (1811 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 36.7% (1860 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 37.6% (1906 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 38.4% (1949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 39.3% (1995 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 40.2% (2041 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 41.7% (2118 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 42.6% (2159 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 43.4% (2201 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 44.7% (2267 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 45.8% (2324 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 46.7% (2368 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 47.6% (2413 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 48.4% (2457 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 49.4% (2505 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 50.8% (2577 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 51.4% (2609 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 52.6% (2669 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 53.3% (2705 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 54.3% (2755 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 55.3% (2808 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 56.6% (2871 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 57.6% (2921 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 58.5% (2966 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 59.4% (3015 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 60.3% (3062 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 61.5% (3123 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 62.4% (3167 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 63.6% (3229 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 64.5% (3275 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 65.5% (3322 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 66.4% (3369 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 67.6% (3430 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 68.4% (3471 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 69.5% (3526 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 70.5% (3575 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 71.4% (3624 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 72.6% (3683 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 73.6% (3733 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 74.5% (3782 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 75.5% (3829 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 76.4% (3876 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 77.6% (3937 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 78.5% (3985 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 79.6% (4037 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 80.4% (4082 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 81.4% (4132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 82.5% (4184 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 83.4% (4233 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 84.5% (4286 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 85.6% (4342 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 86.6% (4393 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 87.6% (4444 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 88.5% (4488 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 89.5% (4543 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 90.5% (4591 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 91.5% (4645 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 92.5% (4692 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 93.5% (4743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 94.6% (4798 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 95.5% (4845 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.4; 61 / 5074 (P = 1.20%) round 3]               
[00:00:00] Finding cutoff p=993 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=993 1.7% (85 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 3.3% (166 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 4.6% (234 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 6.1% (311 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 7.4% (378 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 8.8% (449 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 10.2% (519 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 11.7% (593 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 13.4% (680 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 14.9% (757 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 16.5% (835 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 18.0% (913 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 19.9% (1010 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 21.4% (1084 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 22.5% (1142 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 23.5% (1192 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 24.5% (1243 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 25.6% (1298 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 26.6% (1350 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 27.7% (1404 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 28.8% (1460 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 29.8% (1512 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 31.0% (1571 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 32.0% (1623 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 33.1% (1681 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 34.7% (1763 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 36.0% (1825 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 37.0% (1879 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 38.2% (1940 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 39.3% (1993 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 40.6% (2062 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 41.6% (2109 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 42.4% (2152 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 43.3% (2195 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 44.7% (2266 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 45.6% (2312 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 46.4% (2356 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 47.3% (2399 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 48.3% (2449 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 49.7% (2521 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 50.6% (2568 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 51.4% (2609 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 52.6% (2667 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 53.3% (2704 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 54.4% (2762 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 55.7% (2826 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 56.5% (2867 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 57.7% (2929 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 58.4% (2965 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 59.6% (3024 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 60.7% (3079 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 61.4% (3113 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 62.6% (3177 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 63.4% (3216 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 64.5% (3273 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 65.6% (3329 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 66.4% (3367 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 67.5% (3423 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 68.5% (3475 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 69.5% (3528 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 70.5% (3578 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 71.6% (3632 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 72.7% (3687 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 73.7% (3738 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 74.4% (3775 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 75.4% (3828 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 76.7% (3894 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 77.4% (3929 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 78.4% (3978 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 79.5% (4035 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 80.6% (4090 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 81.6% (4141 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 82.7% (4197 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 83.4% (4234 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 84.6% (4294 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 85.6% (4344 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 86.6% (4395 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 87.5% (4440 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 88.5% (4493 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 89.6% (4547 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 90.5% (4590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 91.5% (4642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 92.5% (4695 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 93.5% (4746 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 94.5% (4796 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 95.5% (4848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 96.5% (4897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.3; 113 / 5074 (P = 2.23%) round 4]               
[00:00:00] Finding cutoff p=991 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=991 1.5% (77 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 2.8% (141 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 3.9% (199 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 5.1% (261 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 6.5% (332 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 8.0% (406 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 9.3% (472 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 10.7% (543 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 12.1% (612 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 13.5% (686 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 15.0% (763 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 16.4% (833 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 17.9% (908 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 19.6% (997 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 21.7% (1102 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 23.2% (1178 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 24.2% (1229 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 25.2% (1280 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 26.3% (1336 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 27.3% (1386 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 28.4% (1443 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 29.4% (1492 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 30.4% (1542 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 31.4% (1593 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 32.4% (1646 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 33.5% (1701 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 34.5% (1752 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 35.7% (1813 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 36.8% (1869 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 37.8% (1920 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 38.9% (1975 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 40.0% (2029 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 40.9% (2073 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 41.8% (2121 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 42.6% (2161 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 43.4% (2200 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 44.2% (2245 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 45.6% (2314 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 46.5% (2359 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 47.4% (2405 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 49.6% (2518 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 50.5% (2563 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 51.4% (2610 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 52.3% (2654 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 53.5% (2713 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 54.7% (2774 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 55.5% (2814 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 56.6% (2872 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 57.7% (2927 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 58.4% (2963 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 59.6% (3023 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 60.3% (3061 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 61.5% (3120 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 62.7% (3181 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 63.4% (3216 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 64.7% (3281 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 65.6% (3329 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 66.7% (3383 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 67.6% (3431 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 68.7% (3484 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 69.4% (3519 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 70.4% (3570 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 71.4% (3622 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 72.4% (3673 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 73.7% (3741 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 74.4% (3775 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 75.4% (3825 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 76.7% (3894 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 77.4% (3928 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 78.8% (3996 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 79.6% (4040 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 80.4% (4082 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 81.6% (4142 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 82.5% (4185 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 83.7% (4247 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 84.5% (4290 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 85.5% (4336 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 86.4% (4386 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 87.6% (4444 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 88.6% (4496 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 89.6% (4547 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 90.5% (4594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 91.5% (4644 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 92.6% (4698 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 93.5% (4743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 94.5% (4795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 95.5% (4845 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=991 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.1; 228 / 5074 (P = 4.49%) round 5]               
[00:00:00] Finding cutoff p=990 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=990 1.5% (76 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 2.9% (148 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 4.3% (218 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 6.1% (309 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 7.4% (376 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 8.8% (445 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 10.1% (512 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 11.4% (578 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 12.7% (646 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 14.2% (722 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 15.7% (797 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 17.2% (875 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 18.8% (955 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 20.4% (1033 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 22.3% (1132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 23.3% (1184 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 24.4% (1238 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 25.5% (1294 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 26.4% (1341 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 27.4% (1389 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 28.4% (1442 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 29.8% (1513 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 30.9% (1570 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 32.0% (1622 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 33.0% (1674 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 34.1% (1729 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 35.1% (1779 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 36.1% (1832 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 37.2% (1887 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 38.4% (1946 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 39.3% (1993 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 40.6% (2062 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 41.4% (2102 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 42.2% (2142 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 43.6% (2210 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 44.5% (2256 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 45.3% (2299 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 46.6% (2367 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 47.6% (2415 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 48.4% (2457 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 49.7% (2522 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 50.6% (2565 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 51.4% (2608 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 52.5% (2665 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 54.0% (2738 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 54.6% (2771 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 55.4% (2809 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 56.5% (2867 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 57.6% (2924 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 58.4% (2963 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 59.4% (3016 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 60.6% (3077 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 61.3% (3111 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 62.5% (3171 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 63.4% (3219 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 64.4% (3270 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 65.4% (3320 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 66.4% (3369 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 67.4% (3421 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 68.7% (3484 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 69.4% (3520 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 70.7% (3587 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 71.6% (3633 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 72.6% (3682 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 73.5% (3731 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 74.5% (3779 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 75.5% (3832 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 76.5% (3882 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 77.5% (3933 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 78.7% (3993 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 79.5% (4036 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 80.7% (4094 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 81.6% (4140 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 82.5% (4184 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 83.6% (4243 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 84.5% (4289 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 85.4% (4335 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 86.5% (4391 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 87.5% (4438 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 88.5% (4493 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 89.6% (4544 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 90.6% (4596 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 91.6% (4646 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 92.6% (4699 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 93.6% (4747 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 94.5% (4796 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 95.5% (4847 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 96.5% (4897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.0; 285 / 5074 (P = 5.62%) round 6]               
[00:00:00] Finding cutoff p=985 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=985 1.5% (78 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 2.9% (148 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 4.2% (213 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 5.5% (280 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 6.7% (341 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 8.0% (405 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 9.5% (481 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 10.7% (545 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 12.1% (616 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 13.5% (686 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 14.9% (757 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 16.3% (827 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 17.9% (906 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 19.4% (985 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 21.4% (1087 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 22.5% (1141 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 23.5% (1193 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 24.4% (1240 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 25.5% (1294 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 26.6% (1348 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 27.6% (1398 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 28.6% (1453 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 29.6% (1502 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 30.7% (1556 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 31.8% (1614 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 32.8% (1665 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 33.8% (1716 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 34.9% (1770 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 35.8% (1818 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 37.1% (1881 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 38.1% (1934 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 39.2% (1989 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 40.2% (2040 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 41.5% (2106 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 42.8% (2171 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 43.6% (2211 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 44.4% (2255 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 45.3% (2299 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 46.6% (2366 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 47.5% (2411 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 48.4% (2455 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 49.3% (2503 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 50.7% (2571 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 51.5% (2613 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 52.5% (2666 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 53.3% (2704 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 54.4% (2759 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 55.5% (2818 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 56.6% (2873 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 57.4% (2911 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 58.5% (2969 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 59.6% (3023 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 60.4% (3066 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 61.5% (3123 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 62.3% (3162 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 63.5% (3221 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 64.5% (3273 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 65.7% (3334 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 66.4% (3370 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 67.5% (3427 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 68.5% (3476 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 69.5% (3528 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 70.5% (3577 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 71.6% (3632 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 72.6% (3683 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 73.6% (3735 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 74.6% (3785 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 75.5% (3833 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 76.6% (3886 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 77.6% (3939 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 78.6% (3989 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 79.4% (4031 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 80.7% (4093 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 81.5% (4136 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 82.4% (4182 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 83.5% (4237 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 84.6% (4294 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 85.7% (4349 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 86.5% (4389 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 87.6% (4444 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 88.5% (4492 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 89.5% (4541 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 90.5% (4590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 91.6% (4646 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 92.6% (4697 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 93.5% (4743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 94.5% (4794 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 95.5% (4847 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=985 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.5; 598 / 5074 (P = 11.79%) round 7]               
[00:00:00] Finding cutoff p=981 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=981 1.6% (81 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 2.9% (145 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 4.1% (210 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 5.4% (275 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 6.7% (341 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 8.0% (404 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 9.3% (474 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 10.8% (547 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 12.2% (617 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 13.5% (684 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 14.8% (749 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 16.2% (820 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 17.8% (901 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 19.4% (985 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 21.5% (1090 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 22.4% (1138 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 23.4% (1189 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 24.4% (1238 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 25.3% (1285 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 26.3% (1336 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 27.3% (1387 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 28.3% (1436 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 29.8% (1513 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 30.9% (1570 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 31.9% (1618 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 33.0% (1673 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 34.1% (1729 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 35.2% (1785 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 36.8% (1866 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 37.9% (1923 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 39.0% (1979 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 40.0% (2030 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 40.9% (2076 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 41.8% (2119 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 42.6% (2163 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 43.4% (2202 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 44.4% (2253 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 45.3% (2297 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 46.7% (2370 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 47.6% (2415 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 48.5% (2459 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 49.3% (2501 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 50.7% (2571 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 51.6% (2616 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 52.4% (2660 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 53.5% (2715 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 54.3% (2756 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 55.4% (2812 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 56.5% (2866 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 57.5% (2920 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 58.8% (2985 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 59.6% (3025 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 60.7% (3081 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 61.5% (3118 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 62.7% (3180 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 63.4% (3218 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 64.5% (3274 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 65.6% (3329 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 66.3% (3366 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 67.5% (3426 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 68.5% (3476 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 69.5% (3525 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 70.5% (3578 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 71.7% (3637 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 72.7% (3689 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 73.6% (3737 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 74.7% (3790 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 75.7% (3842 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 76.6% (3889 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 77.7% (3941 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 78.7% (3995 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 79.6% (4040 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 80.4% (4081 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 81.7% (4144 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 82.6% (4191 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 83.5% (4236 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 84.4% (4284 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 85.7% (4347 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 86.6% (4394 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 87.6% (4443 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 88.5% (4490 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 89.5% (4543 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 90.6% (4595 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 91.5% (4641 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 92.5% (4694 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 93.5% (4745 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 94.5% (4797 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 95.5% (4848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 96.6% (4899 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=981 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.1; 860 / 5074 (P = 16.95%) round 8]               
[00:00:00] Finding cutoff p=978 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=978 1.6% (81 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 2.9% (147 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 4.2% (211 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 5.3% (271 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 6.5% (332 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 7.9% (401 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 9.3% (471 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 10.6% (538 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 11.9% (606 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 13.4% (680 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 15.3% (776 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 16.8% (850 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 18.3% (927 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 19.8% (1003 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 21.8% (1106 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 22.8% (1155 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 23.7% (1205 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 24.7% (1255 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 25.8% (1307 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 26.7% (1355 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 27.7% (1406 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 28.7% (1456 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 29.7% (1506 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 30.9% (1566 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 31.8% (1616 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 33.0% (1673 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 34.0% (1725 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 35.0% (1777 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 36.2% (1835 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 37.2% (1886 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 38.3% (1944 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 39.4% (2001 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 40.6% (2060 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 41.5% (2105 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 42.3% (2146 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 43.6% (2212 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 44.5% (2257 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 45.4% (2302 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 46.3% (2348 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 47.6% (2414 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 48.5% (2459 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 49.4% (2506 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 50.3% (2553 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 51.7% (2625 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 52.5% (2665 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 53.3% (2703 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 54.4% (2759 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 55.6% (2820 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 56.3% (2856 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 57.4% (2914 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 58.5% (2967 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 59.7% (3028 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 60.4% (3066 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 61.5% (3121 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 62.4% (3167 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 63.6% (3227 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 64.6% (3276 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 65.4% (3320 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 66.5% (3373 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 67.4% (3421 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 68.6% (3483 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 69.7% (3537 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 70.7% (3588 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 71.7% (3637 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 72.4% (3672 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 73.7% (3740 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 74.6% (3786 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 75.6% (3838 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 76.7% (3894 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 77.5% (3930 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 78.4% (3979 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 79.5% (4034 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 80.7% (4097 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 81.6% (4139 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 82.4% (4183 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 83.6% (4244 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 84.5% (4286 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 85.7% (4348 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 86.6% (4392 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 87.5% (4438 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 88.5% (4493 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 89.6% (4546 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 90.6% (4596 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 91.6% (4648 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 92.5% (4692 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 93.5% (4743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 94.5% (4796 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 95.5% (4846 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 97.5% (4949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=978 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.8; 1074 / 5074 (P = 21.17%) round 9]               
[00:00:00] Finding cutoff p=975 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=975 1.8% (92 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 3.2% (162 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 4.4% (221 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 5.5% (281 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 6.7% (340 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 8.0% (404 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 9.3% (471 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 10.7% (542 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 12.0% (607 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 13.3% (677 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 14.6% (743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 15.9% (805 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 17.2% (872 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 18.6% (944 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 20.4% (1033 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 22.3% (1129 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 23.3% (1180 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 24.2% (1229 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 25.2% (1277 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 26.6% (1352 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 27.6% (1402 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 28.6% (1452 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 29.7% (1507 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 30.8% (1562 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 31.8% (1613 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 32.7% (1661 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 33.9% (1721 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 35.0% (1776 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 36.0% (1827 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 37.1% (1880 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 38.1% (1932 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 39.2% (1991 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 40.3% (2046 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 41.2% (2092 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 42.5% (2157 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 43.3% (2196 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 44.6% (2265 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 45.5% (2309 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 46.4% (2356 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 47.7% (2420 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 48.5% (2463 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 49.3% (2504 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 50.6% (2569 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 51.5% (2615 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 52.3% (2654 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 53.4% (2710 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 54.5% (2766 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 55.6% (2820 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 56.7% (2878 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 57.5% (2918 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 58.3% (2959 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 59.5% (3018 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 60.6% (3074 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 61.7% (3130 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 62.5% (3172 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 63.5% (3222 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 64.4% (3266 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 65.4% (3317 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 66.6% (3381 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 67.6% (3428 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 68.5% (3476 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 69.5% (3527 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 70.5% (3577 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 71.5% (3626 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 72.4% (3675 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 73.6% (3736 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 74.6% (3786 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 75.4% (3825 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 76.7% (3893 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 77.6% (3935 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 78.7% (3991 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 79.6% (4038 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 80.5% (4084 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 81.7% (4144 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 82.5% (4187 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 83.5% (4239 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 84.6% (4292 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 85.5% (4336 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 86.6% (4392 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 87.5% (4440 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 88.5% (4490 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 89.5% (4540 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 90.5% (4590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 91.5% (4642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 92.6% (4698 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 93.6% (4747 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 94.5% (4797 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 95.5% (4845 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 96.5% (4897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=975 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.5; 1289 / 5074 (P = 25.40%) round 10]               
[00:00:00] Finding cutoff p=972 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=972 1.8% (90 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 3.1% (159 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 4.3% (216 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 5.4% (273 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 6.8% (343 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 8.0% (406 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 9.3% (471 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 10.5% (534 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 11.9% (604 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 13.2% (670 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 14.5% (735 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 15.8% (802 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 17.1% (870 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 18.6% (946 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 20.2% (1026 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 22.0% (1118 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 23.0% (1166 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 24.0% (1216 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 24.9% (1265 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 26.0% (1317 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 27.0% (1369 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 27.9% (1418 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 28.9% (1468 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 29.9% (1517 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 30.9% (1567 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 31.9% (1620 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 32.9% (1667 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 34.5% (1748 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 35.5% (1803 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 36.7% (1861 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 37.8% (1918 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 38.8% (1970 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 39.9% (2025 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 40.8% (2070 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 41.7% (2115 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 42.5% (2156 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 43.3% (2195 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 44.6% (2262 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 45.4% (2305 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 46.3% (2347 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 47.6% (2416 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 48.4% (2457 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 49.7% (2524 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 50.6% (2566 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 51.5% (2611 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 52.5% (2664 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 53.5% (2717 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 54.6% (2770 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 55.3% (2805 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 56.5% (2865 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 57.8% (2933 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 58.5% (2969 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 59.6% (3024 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 60.4% (3063 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 61.5% (3121 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 62.6% (3176 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 63.7% (3234 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 64.4% (3269 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 65.6% (3328 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 66.3% (3366 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 67.4% (3419 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 68.4% (3470 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 69.7% (3537 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 70.4% (3570 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 71.7% (3638 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 72.7% (3689 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 73.4% (3723 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 74.6% (3787 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 75.8% (3847 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 76.5% (3883 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 77.5% (3934 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 78.7% (3991 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 79.6% (4037 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 80.5% (4085 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 81.5% (4134 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 82.4% (4183 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 83.7% (4246 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 84.7% (4298 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 85.5% (4336 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 86.5% (4390 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 87.5% (4442 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 88.6% (4496 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 89.6% (4545 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 90.5% (4590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 91.5% (4642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 92.5% (4693 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 93.5% (4744 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 94.5% (4796 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 95.5% (4846 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.2; 1472 / 5074 (P = 29.01%) round 11]               
[00:00:00] Finding cutoff p=968 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=968 1.7% (85 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 3.1% (156 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 4.3% (216 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 5.3% (270 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 6.6% (334 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 7.9% (401 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 9.2% (466 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 10.6% (536 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 11.9% (606 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 13.2% (669 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 14.4% (733 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 15.6% (794 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 16.9% (859 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 18.5% (937 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 20.6% (1046 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 22.5% (1140 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 23.3% (1184 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 24.3% (1235 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 25.3% (1284 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 26.4% (1338 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 27.4% (1388 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 28.3% (1435 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 29.3% (1486 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 30.2% (1533 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 31.3% (1589 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 32.2% (1635 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 33.3% (1690 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 34.3% (1739 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 35.4% (1797 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 36.4% (1848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 37.5% (1902 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 38.5% (1956 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 39.7% (2013 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 40.6% (2060 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 41.5% (2105 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 42.3% (2146 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 43.5% (2209 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 44.3% (2250 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 45.3% (2296 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 46.6% (2367 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 47.5% (2409 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 48.4% (2455 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 49.8% (2525 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 50.6% (2565 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 51.3% (2605 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 52.7% (2674 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 53.4% (2711 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 54.5% (2766 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 55.3% (2805 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 56.4% (2860 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 57.4% (2912 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 58.5% (2967 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 59.5% (3021 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 60.6% (3077 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 61.4% (3116 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 62.6% (3174 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 63.7% (3232 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 64.6% (3276 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 65.5% (3324 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 66.6% (3377 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 67.6% (3428 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 68.5% (3478 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 69.5% (3526 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 70.5% (3577 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 71.4% (3622 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 72.4% (3676 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 73.4% (3723 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 74.7% (3789 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 75.7% (3843 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 76.5% (3883 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 77.4% (3927 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 78.5% (3984 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 79.4% (4030 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 80.8% (4098 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 81.4% (4132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 82.5% (4186 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 83.5% (4238 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 84.6% (4291 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 85.5% (4337 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 86.6% (4393 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 87.7% (4450 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 88.6% (4498 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 89.6% (4547 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 90.6% (4595 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 91.5% (4645 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 92.6% (4699 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 93.6% (4749 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 94.5% (4795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 95.5% (4846 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 96.5% (4897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.8; 1689 / 5074 (P = 33.29%) round 12]               
[00:00:00] Finding cutoff p=964 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=964 2.0% (100 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 4.2% (212 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 5.4% (272 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 6.4% (326 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 7.8% (397 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 9.0% (457 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 10.4% (530 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 11.7% (594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 12.8% (652 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 14.2% (718 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 15.3% (777 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 16.6% (844 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 17.9% (909 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 19.6% (993 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 21.3% (1081 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 22.9% (1164 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 23.8% (1209 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 24.9% (1261 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 25.8% (1307 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 26.7% (1354 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 27.6% (1400 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 28.6% (1450 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 29.4% (1493 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 30.5% (1548 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 31.6% (1601 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 32.5% (1649 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 33.5% (1700 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 34.5% (1749 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 35.6% (1806 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 36.6% (1859 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 37.7% (1912 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 38.7% (1966 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 39.9% (2025 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 40.8% (2069 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 41.7% (2118 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 42.4% (2153 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 43.2% (2194 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 44.4% (2253 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 45.3% (2296 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 46.5% (2358 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 47.4% (2404 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 48.6% (2466 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 49.8% (2528 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 50.6% (2565 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 51.5% (2611 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 52.3% (2656 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 53.5% (2717 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 54.3% (2754 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 55.3% (2808 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 56.4% (2860 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 57.4% (2913 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 58.9% (2988 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 59.6% (3022 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 60.6% (3076 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 61.4% (3114 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 62.6% (3176 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 63.6% (3225 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 64.4% (3269 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 65.7% (3332 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 66.6% (3378 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 67.5% (3423 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 68.4% (3472 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 69.5% (3525 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 70.4% (3574 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 71.7% (3639 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 72.6% (3686 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 73.6% (3732 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 74.5% (3778 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 75.7% (3843 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 76.6% (3886 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 77.5% (3931 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 78.5% (3985 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 79.4% (4030 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 80.5% (4086 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 81.4% (4132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 82.7% (4195 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 83.7% (4246 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 84.5% (4285 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 85.6% (4342 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 86.5% (4387 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 87.5% (4440 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 88.6% (4494 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 89.6% (4544 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 90.6% (4596 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 91.7% (4653 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 92.5% (4693 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 93.5% (4745 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 94.5% (4794 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 95.5% (4845 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 96.5% (4897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=964 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.4; 1884 / 5074 (P = 37.13%) round 13]               
[00:00:00] Finding cutoff p=959 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=959 1.9% (96 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 3.2% (163 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 4.3% (218 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 5.4% (276 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 6.8% (343 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 8.0% (405 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 9.4% (479 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 10.5% (534 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 11.7% (594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 13.2% (672 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 14.4% (731 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 15.5% (787 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 16.8% (850 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 18.2% (925 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 20.1% (1018 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 21.9% (1109 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 22.7% (1154 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 23.6% (1198 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 24.5% (1244 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 25.3% (1286 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 26.2% (1330 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 27.1% (1377 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 28.6% (1452 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 29.6% (1501 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 30.5% (1548 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 31.4% (1595 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 32.4% (1643 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 33.5% (1701 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 34.5% (1749 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 35.8% (1815 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 36.9% (1870 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 38.0% (1926 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 39.1% (1986 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 40.1% (2037 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 41.2% (2089 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 41.9% (2127 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 42.7% (2166 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 43.4% (2204 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 44.6% (2264 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 45.5% (2311 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 46.5% (2357 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 47.3% (2402 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 49.5% (2512 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 50.3% (2552 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 51.6% (2619 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 52.4% (2658 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 53.4% (2711 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 54.5% (2765 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 55.6% (2823 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 56.3% (2856 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 57.6% (2923 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 58.3% (2959 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 59.5% (3017 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 60.4% (3067 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 61.6% (3126 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 62.6% (3177 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 63.3% (3213 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 64.6% (3278 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 65.4% (3320 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 66.5% (3374 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 67.4% (3420 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 68.3% (3468 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 69.6% (3532 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 70.6% (3581 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 71.6% (3631 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 72.4% (3676 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 73.5% (3731 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 74.6% (3786 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 75.5% (3830 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 76.6% (3886 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 77.4% (3929 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 78.6% (3990 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 79.8% (4047 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 80.7% (4095 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 81.7% (4145 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 82.5% (4187 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 83.6% (4244 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 84.6% (4294 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 85.6% (4345 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 86.4% (4386 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 87.4% (4437 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 88.6% (4496 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 89.6% (4546 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 90.5% (4594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 91.5% (4645 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 92.5% (4692 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 93.5% (4746 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 94.5% (4796 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 95.5% (4847 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 96.5% (4898 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.9; 2134 / 5074 (P = 42.06%) round 14]               
[00:00:00] Finding cutoff p=956 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=956 2.1% (108 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 3.3% (165 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 4.6% (231 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 5.7% (291 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 6.8% (344 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 8.0% (408 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 9.4% (479 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 10.7% (543 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 11.9% (602 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 13.2% (668 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 14.4% (733 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 15.6% (790 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 16.8% (850 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 17.9% (909 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 19.4% (983 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 21.4% (1085 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 23.3% (1182 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 24.1% (1225 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 25.6% (1297 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 26.5% (1343 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 27.5% (1394 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 28.4% (1441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 29.3% (1487 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 30.2% (1533 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 31.2% (1582 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 32.7% (1660 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 33.8% (1717 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 34.9% (1769 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 35.8% (1817 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 36.8% (1868 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 38.0% (1928 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 39.0% (1980 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 40.1% (2033 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 41.1% (2085 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 42.1% (2136 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 42.9% (2175 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 43.7% (2215 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 44.4% (2253 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 45.6% (2315 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 46.5% (2359 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 47.4% (2406 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 48.7% (2471 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 49.6% (2515 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 50.4% (2558 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 51.6% (2616 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 52.3% (2656 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 53.6% (2721 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 54.3% (2754 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 55.4% (2809 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 56.4% (2863 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 57.5% (2919 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 58.7% (2976 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 59.6% (3026 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 60.7% (3081 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 61.4% (3115 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 62.5% (3173 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 63.4% (3215 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 64.5% (3274 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 65.5% (3322 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 66.5% (3374 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 67.5% (3424 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 68.5% (3475 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 69.4% (3519 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 70.7% (3585 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 71.6% (3634 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 72.6% (3683 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 73.4% (3726 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 74.4% (3774 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 75.5% (3832 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 76.7% (3891 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 77.6% (3939 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 78.5% (3984 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 79.5% (4034 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 80.5% (4086 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 81.6% (4139 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 82.6% (4190 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 83.6% (4242 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 84.6% (4295 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 85.4% (4335 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 86.6% (4392 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 87.5% (4440 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 88.5% (4491 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 89.5% (4539 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 90.5% (4594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 91.5% (4642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 92.5% (4692 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 93.5% (4744 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 94.6% (4800 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 95.5% (4848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 97.6% (4950 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=956 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.6; 2280 / 5074 (P = 44.93%) round 15]               
[00:00:00] Finding cutoff p=953 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=953 1.8% (93 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 3.2% (161 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 4.8% (243 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 5.8% (295 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 7.1% (362 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 8.4% (427 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 9.9% (503 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 11.2% (566 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 12.3% (625 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 13.5% (686 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 14.8% (752 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 15.9% (807 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 17.0% (864 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 18.5% (937 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 20.3% (1029 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 21.8% (1108 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 23.4% (1188 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 24.2% (1228 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 25.6% (1300 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 26.5% (1346 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 27.4% (1392 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 28.3% (1437 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 29.3% (1488 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 30.3% (1538 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 31.2% (1582 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 32.5% (1649 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 33.7% (1708 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 34.5% (1752 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 35.7% (1811 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 36.7% (1862 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 37.9% (1923 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 38.9% (1975 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 40.0% (2032 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 41.0% (2081 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 41.9% (2126 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 42.6% (2162 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 43.4% (2202 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 44.7% (2266 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 45.3% (2296 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 46.6% (2367 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 47.4% (2407 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 48.3% (2450 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 49.6% (2517 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 50.8% (2578 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 51.5% (2614 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 52.3% (2656 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 53.7% (2726 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 54.5% (2767 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 55.6% (2822 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 56.6% (2872 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 57.7% (2929 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 58.4% (2964 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 59.4% (3015 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 60.5% (3070 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 61.6% (3125 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 62.7% (3182 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 63.4% (3218 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 64.3% (3265 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 65.6% (3330 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 66.6% (3379 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 67.6% (3428 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 68.5% (3477 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 69.4% (3520 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 70.6% (3584 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 71.6% (3632 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 72.5% (3680 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 73.4% (3725 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 74.9% (3799 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 75.5% (3830 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 76.7% (3892 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 77.6% (3935 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 78.5% (3982 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 79.5% (4034 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 80.4% (4080 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 81.5% (4137 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 82.6% (4189 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 83.5% (4237 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 84.6% (4293 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 85.5% (4338 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 86.5% (4387 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 87.5% (4441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 88.5% (4492 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 89.6% (4547 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 90.6% (4597 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 91.5% (4641 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 92.6% (4697 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 93.6% (4747 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 94.5% (4794 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 95.6% (4849 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 97.5% (4949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=953 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.3; 2434 / 5074 (P = 47.97%) round 16]               
[00:00:00] Finding cutoff p=950 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=950 1.9% (96 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 3.2% (164 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 4.5% (228 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 5.5% (281 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 6.7% (340 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 8.0% (408 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 9.4% (479 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 10.4% (529 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 11.6% (587 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 12.9% (653 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 13.9% (704 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 15.1% (766 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 16.9% (855 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 18.5% (937 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 19.9% (1012 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 21.5% (1093 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 22.5% (1143 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 23.3% (1183 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 24.2% (1230 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 25.6% (1299 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 26.4% (1342 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 27.5% (1393 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 28.5% (1448 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 29.3% (1489 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 30.2% (1531 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 31.9% (1617 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 32.7% (1658 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 33.6% (1704 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 34.5% (1752 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 35.7% (1813 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 37.2% (1887 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 38.3% (1941 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 39.4% (1997 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 40.3% (2047 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 41.2% (2092 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 42.4% (2153 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 43.2% (2193 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 44.5% (2256 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 45.3% (2298 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 46.5% (2358 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 47.4% (2406 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 48.6% (2466 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 49.3% (2502 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 50.4% (2558 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 51.6% (2618 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 52.4% (2658 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 53.6% (2721 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 54.3% (2757 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 55.5% (2818 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 56.5% (2865 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 57.4% (2915 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 58.5% (2968 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 59.5% (3017 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 60.4% (3066 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 61.5% (3122 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 62.6% (3178 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 63.5% (3222 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 64.5% (3273 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 65.4% (3318 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 66.5% (3373 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 67.4% (3421 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 68.6% (3480 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 69.5% (3528 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 70.4% (3570 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 71.8% (3643 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 72.4% (3674 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 73.6% (3732 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 74.5% (3778 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 75.4% (3828 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 76.7% (3894 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 77.5% (3932 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 78.9% (4004 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 79.6% (4040 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 80.4% (4082 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 81.5% (4136 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 82.5% (4186 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 83.5% (4239 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 84.6% (4293 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 85.4% (4335 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 86.6% (4393 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 87.6% (4446 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 88.5% (4489 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 89.6% (4546 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 90.5% (4590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 91.6% (4646 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 92.6% (4697 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 93.6% (4747 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 94.5% (4795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 95.5% (4845 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 97.6% (4951 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.0; 2570 / 5074 (P = 50.65%) round 17]               
[00:00:00] Finding cutoff p=945 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=945 2.2% (113 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 3.6% (185 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 4.7% (239 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 5.5% (278 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 6.7% (339 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 8.0% (407 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 9.5% (483 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 10.6% (540 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 11.7% (594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 13.2% (668 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 14.1% (717 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 15.5% (784 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 16.4% (830 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 17.7% (898 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 19.3% (978 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 20.6% (1047 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 22.3% (1133 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 23.6% (1198 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 24.6% (1246 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 25.4% (1291 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 26.3% (1336 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 27.3% (1386 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 28.2% (1432 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 29.5% (1498 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 30.5% (1546 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 31.5% (1598 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 32.4% (1646 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 33.7% (1710 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 34.7% (1762 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 35.5% (1801 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 36.5% (1851 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 37.8% (1917 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 38.9% (1972 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 39.9% (2027 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 41.1% (2083 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 41.9% (2126 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 42.9% (2175 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 43.5% (2209 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 44.3% (2247 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 45.5% (2310 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 46.5% (2361 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 47.4% (2407 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 48.6% (2468 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 49.5% (2510 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 50.3% (2554 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 51.4% (2606 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 52.6% (2667 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 53.3% (2706 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 54.6% (2768 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 55.4% (2809 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 56.5% (2867 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 57.8% (2933 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 58.5% (2966 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 59.5% (3021 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 60.4% (3065 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 61.5% (3120 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 62.6% (3175 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 63.3% (3214 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 64.5% (3272 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 65.4% (3316 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 66.6% (3380 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 67.6% (3430 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 68.4% (3473 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 69.5% (3528 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 70.4% (3570 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 71.6% (3634 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 72.4% (3674 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 73.6% (3732 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 74.4% (3775 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 75.6% (3834 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 76.6% (3885 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 77.9% (3951 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 78.5% (3982 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 79.9% (4056 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 80.5% (4083 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 81.7% (4144 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 82.6% (4193 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 83.5% (4238 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 84.5% (4290 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 85.5% (4339 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 86.5% (4391 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 87.6% (4444 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 88.6% (4496 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 89.5% (4540 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 90.5% (4594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 91.5% (4643 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 92.6% (4699 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 93.6% (4747 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 94.6% (4799 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 95.5% (4847 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 96.5% (4897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.5; 2783 / 5074 (P = 54.85%) round 18]               
[00:00:00] Finding cutoff p=941 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=941 2.1% (105 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 3.3% (169 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 4.4% (222 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 5.6% (282 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 6.7% (338 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 7.9% (402 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 9.0% (459 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 10.2% (520 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 11.4% (577 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 12.6% (638 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 13.3% (675 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 14.4% (732 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 15.5% (789 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 16.9% (859 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 18.5% (939 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 19.9% (1012 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 21.1% (1071 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 22.6% (1149 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 23.3% (1182 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 24.1% (1224 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 25.5% (1295 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 26.3% (1336 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 27.4% (1389 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 28.5% (1444 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 29.5% (1495 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 30.3% (1538 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 31.7% (1606 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 32.5% (1651 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 33.4% (1696 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 34.3% (1741 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 35.5% (1801 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 36.3% (1842 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 37.3% (1892 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 38.4% (1949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 39.6% (2007 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 40.7% (2065 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 41.5% (2106 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 42.4% (2149 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 43.4% (2204 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 44.3% (2250 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 45.4% (2305 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 46.5% (2360 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 47.3% (2398 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 48.3% (2453 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 49.8% (2525 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 50.5% (2564 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 51.5% (2614 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 52.3% (2656 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 53.5% (2716 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 54.3% (2754 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 55.4% (2809 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 56.5% (2866 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 57.3% (2908 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 58.3% (2959 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 59.4% (3015 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 60.4% (3064 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 61.5% (3118 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 62.6% (3177 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 63.6% (3225 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 64.6% (3279 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 65.5% (3326 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 66.7% (3382 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 67.4% (3422 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 68.5% (3478 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 69.5% (3524 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 71.0% (3601 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 72.0% (3652 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 72.6% (3684 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 73.5% (3728 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 74.4% (3775 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 75.6% (3834 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 76.5% (3883 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 77.5% (3930 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 78.5% (3985 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 79.5% (4035 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 80.4% (4082 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 81.7% (4143 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 82.7% (4196 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 83.5% (4236 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 84.6% (4292 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 85.6% (4341 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 86.6% (4393 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 87.5% (4438 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 88.6% (4494 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 89.5% (4539 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 90.6% (4595 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 91.6% (4648 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 92.5% (4694 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 93.6% (4747 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 94.6% (4798 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 95.5% (4846 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=941 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.1; 2937 / 5074 (P = 57.88%) round 19]               
[00:00:00] Finding cutoff p=938 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=938 1.9% (94 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 3.1% (159 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 4.1% (209 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 5.1% (257 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 6.6% (336 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 8.1% (413 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 9.2% (465 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 10.3% (522 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 11.5% (585 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 12.5% (633 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 13.5% (687 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 14.5% (738 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 16.1% (818 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 17.8% (901 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 19.1% (969 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 20.6% (1047 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 22.5% (1141 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 23.2% (1178 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 24.4% (1236 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 25.1% (1276 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 26.5% (1344 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 27.5% (1396 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 28.5% (1444 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 29.3% (1485 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 30.2% (1532 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 31.2% (1582 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 32.6% (1656 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 33.5% (1700 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 34.6% (1758 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 35.9% (1823 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 36.8% (1866 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 37.9% (1921 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 38.8% (1968 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 39.6% (2008 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 40.5% (2053 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 41.4% (2101 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 42.7% (2165 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 43.7% (2215 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 44.3% (2247 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 45.3% (2296 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 46.6% (2365 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 47.3% (2402 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 48.6% (2466 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 49.4% (2505 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 50.7% (2570 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 51.4% (2606 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 52.5% (2662 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 53.3% (2705 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 54.4% (2762 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 55.3% (2805 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 56.7% (2875 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 57.3% (2909 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 58.5% (2967 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 59.6% (3024 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 60.3% (3061 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 61.4% (3114 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 62.3% (3163 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 63.3% (3213 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 64.6% (3277 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 65.4% (3318 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 66.6% (3377 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 67.6% (3430 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 68.6% (3481 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 70.0% (3554 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 71.0% (3603 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 71.7% (3638 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 72.5% (3678 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 73.5% (3730 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 74.7% (3788 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 75.8% (3848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 76.9% (3900 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 77.4% (3928 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 78.7% (3994 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 79.5% (4032 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 80.5% (4086 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 81.5% (4136 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 82.6% (4193 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 83.7% (4247 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 84.5% (4286 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 85.5% (4340 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 86.5% (4389 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 87.5% (4441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 88.5% (4491 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 89.5% (4540 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 90.5% (4590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 91.6% (4646 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 92.6% (4696 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 93.5% (4744 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 94.5% (4795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 95.5% (4848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 96.6% (4899 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 97.5% (4949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=938 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.8; 3017 / 5074 (P = 59.46%) round 20]               
[00:00:00] Finding cutoff p=934 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=934 1.9% (96 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 3.1% (157 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 4.0% (205 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 5.3% (270 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 6.5% (331 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 8.2% (416 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 9.2% (469 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 10.4% (526 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 11.7% (596 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 12.9% (653 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 13.9% (705 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 15.0% (762 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 16.5% (838 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 17.9% (907 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 19.0% (963 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 20.8% (1057 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 21.9% (1112 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 23.3% (1181 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 24.4% (1236 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 25.3% (1285 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 26.2% (1331 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 27.9% (1415 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 28.8% (1463 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 29.4% (1492 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 30.3% (1535 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 31.4% (1592 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 32.3% (1640 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 33.2% (1684 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 35.1% (1779 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 35.9% (1821 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 36.8% (1867 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 37.9% (1922 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 38.8% (1967 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 39.7% (2013 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 40.6% (2059 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 41.5% (2107 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 42.7% (2169 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 43.6% (2210 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 44.5% (2259 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 45.6% (2313 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 46.4% (2353 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 47.3% (2399 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 48.4% (2456 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 49.6% (2516 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 50.3% (2554 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 51.5% (2611 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 52.6% (2667 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 53.4% (2711 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 54.8% (2782 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 55.5% (2818 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 56.6% (2870 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 57.5% (2919 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 58.7% (2976 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 59.7% (3027 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 60.4% (3064 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 61.5% (3122 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 62.5% (3171 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 63.3% (3214 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 64.4% (3270 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 65.4% (3316 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 66.6% (3379 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 67.4% (3422 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 68.5% (3475 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 69.9% (3547 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 70.7% (3586 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 71.6% (3635 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 72.4% (3676 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 73.4% (3725 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 74.4% (3776 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 75.6% (3835 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 76.5% (3880 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 77.5% (3932 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 78.6% (3986 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 79.4% (4030 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 80.5% (4085 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 81.5% (4133 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 82.5% (4185 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 83.4% (4234 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 84.5% (4285 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 85.5% (4338 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 86.5% (4388 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 87.7% (4448 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 88.5% (4489 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 89.6% (4544 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 90.5% (4593 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 91.5% (4644 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 92.6% (4698 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 93.5% (4743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 94.6% (4799 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 95.6% (4849 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 96.6% (4899 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=934 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.4; 3144 / 5074 (P = 61.96%) round 21]               
[00:00:00] Finding cutoff p=930 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=930 1.9% (94 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 3.4% (171 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 4.4% (224 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 5.2% (264 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 6.1% (309 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 7.4% (376 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 8.9% (454 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 10.0% (505 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 11.1% (562 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 12.6% (637 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 13.5% (686 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 14.6% (739 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 15.8% (800 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 17.4% (882 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 18.3% (929 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 19.4% (986 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 20.5% (1040 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 21.7% (1099 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 23.0% (1168 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 23.5% (1193 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 24.4% (1237 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 25.1% (1275 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 26.5% (1345 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 27.4% (1390 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 28.3% (1438 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 29.8% (1514 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 30.6% (1552 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 31.5% (1597 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 32.2% (1635 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 33.5% (1700 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 34.3% (1740 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 35.6% (1807 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 36.6% (1857 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 37.5% (1901 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 38.7% (1964 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 39.6% (2009 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 40.5% (2057 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 41.6% (2110 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 42.8% (2170 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 43.3% (2198 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 44.6% (2262 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 45.4% (2302 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 46.5% (2358 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 47.6% (2415 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 48.3% (2452 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 49.5% (2510 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 50.3% (2553 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 52.4% (2659 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 53.4% (2712 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 54.3% (2754 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 55.3% (2807 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 56.3% (2857 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 57.3% (2908 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 58.5% (2969 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 59.4% (3013 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 60.5% (3070 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 61.5% (3120 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 62.4% (3168 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 63.5% (3223 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 64.4% (3266 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 65.4% (3318 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 66.5% (3373 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 67.7% (3435 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 68.5% (3475 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 69.6% (3532 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 70.5% (3579 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 71.7% (3640 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 72.7% (3691 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 73.6% (3737 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 74.7% (3791 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 75.4% (3827 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 76.4% (3878 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 77.6% (3936 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 78.4% (3980 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 79.5% (4033 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 80.9% (4107 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 81.6% (4139 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 82.6% (4191 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 83.5% (4238 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 84.5% (4288 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 85.4% (4335 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 86.4% (4386 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 87.5% (4441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 88.5% (4490 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 89.6% (4544 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 90.5% (4594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 91.5% (4643 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 92.5% (4692 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 93.5% (4745 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 94.5% (4796 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 95.5% (4846 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 97.6% (4950 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=930 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.0; 3246 / 5074 (P = 63.97%) round 22]               
[00:00:00] Finding cutoff p=927 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=927 1.9% (97 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 3.0% (150 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 3.9% (198 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 4.9% (248 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 5.4% (272 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 6.7% (342 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 8.3% (422 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 9.3% (472 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 10.1% (512 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 12.0% (608 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 13.0% (662 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 14.2% (721 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 15.2% (770 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 16.8% (852 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 18.5% (941 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 19.8% (1005 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 21.2% (1077 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 22.5% (1141 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 23.4% (1188 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 24.4% (1238 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 25.4% (1288 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 26.2% (1329 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 27.3% (1384 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 28.2% (1429 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 29.5% (1497 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 30.8% (1563 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 31.7% (1606 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 32.9% (1667 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 33.7% (1711 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 35.0% (1777 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 36.0% (1827 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 36.9% (1874 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 38.1% (1935 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 39.1% (1984 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 39.9% (2023 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 40.6% (2061 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 41.5% (2108 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 42.5% (2156 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 43.3% (2196 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 44.4% (2253 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 45.3% (2297 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 46.5% (2359 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 47.3% (2401 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 48.5% (2461 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 49.6% (2516 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 50.5% (2560 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 51.8% (2627 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 53.2% (2698 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 53.7% (2726 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 54.5% (2764 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 55.7% (2827 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 56.3% (2857 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 57.7% (2926 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 58.5% (2969 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 59.5% (3018 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 60.5% (3069 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 61.5% (3122 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 62.6% (3176 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 63.4% (3217 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 64.5% (3271 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 65.7% (3335 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 66.4% (3367 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 67.4% (3418 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 68.9% (3494 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 69.7% (3537 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 70.5% (3577 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 71.5% (3629 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 72.6% (3683 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 73.9% (3748 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 74.7% (3791 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 75.8% (3845 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 76.7% (3890 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 77.4% (3927 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 78.4% (3978 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 79.7% (4042 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 80.6% (4090 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 81.4% (4132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 82.6% (4191 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 83.4% (4234 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 84.6% (4295 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 85.6% (4341 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 86.6% (4392 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 87.5% (4442 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 88.5% (4493 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 89.6% (4545 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 90.5% (4593 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 91.7% (4653 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 92.6% (4697 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 93.5% (4743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 94.5% (4797 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 95.5% (4845 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 96.5% (4898 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 97.6% (4950 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=927 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.7; 3330 / 5074 (P = 65.63%) round 23]               
[00:00:00] Finding cutoff p=924 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=924 1.8% (91 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 3.0% (150 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 3.8% (195 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 4.7% (238 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 5.4% (276 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 6.8% (345 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 8.4% (424 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 9.3% (470 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 10.1% (513 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 11.3% (574 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 12.2% (621 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 13.6% (691 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 16.0% (813 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 17.5% (888 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 18.6% (946 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 19.8% (1004 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 20.9% (1061 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 22.4% (1137 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 23.3% (1184 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 24.5% (1242 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 25.3% (1283 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 26.4% (1339 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 27.2% (1380 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 28.5% (1447 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 29.7% (1509 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 30.4% (1540 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 31.2% (1585 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 33.0% (1672 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 33.8% (1713 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 34.8% (1766 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 36.0% (1829 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 36.9% (1873 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 37.7% (1914 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 38.5% (1956 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 39.5% (2004 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 40.5% (2054 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 41.9% (2125 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 43.0% (2181 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 43.7% (2219 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 44.4% (2251 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 45.4% (2303 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 46.3% (2350 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 47.6% (2416 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 48.3% (2453 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 49.5% (2512 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 50.8% (2577 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 52.1% (2642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 52.8% (2678 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 53.5% (2717 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 54.5% (2763 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 55.7% (2827 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 56.3% (2856 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 57.3% (2908 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 58.5% (2967 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 59.3% (3009 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 60.3% (3061 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 61.5% (3120 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 62.5% (3170 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 63.6% (3228 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 64.4% (3268 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 65.5% (3322 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 66.4% (3367 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 67.6% (3431 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 68.4% (3470 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 69.4% (3522 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 70.6% (3583 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 71.8% (3643 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 73.0% (3703 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 73.8% (3747 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 74.9% (3801 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 75.8% (3846 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 76.6% (3886 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 77.7% (3944 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 78.6% (3989 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 79.6% (4038 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 80.7% (4094 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 81.5% (4133 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 82.7% (4195 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 83.5% (4236 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 84.4% (4284 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 85.6% (4345 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 86.5% (4389 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 87.7% (4450 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 88.5% (4493 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 89.5% (4542 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 90.6% (4595 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 91.5% (4642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 92.6% (4701 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 93.6% (4748 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 94.5% (4795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 95.5% (4846 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 97.5% (4949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.4; 3413 / 5074 (P = 67.26%) round 24]               
[00:00:00] Finding cutoff p=921 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=921 1.7% (87 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 3.0% (151 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 3.8% (192 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 4.6% (232 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 5.2% (266 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 6.3% (320 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 7.6% (385 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 8.8% (444 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 9.6% (487 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 10.9% (552 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 11.6% (587 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 12.4% (628 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 13.3% (673 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 14.7% (746 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 16.0% (814 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 17.0% (862 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 17.9% (907 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 18.9% (959 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 19.9% (1012 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 21.1% (1069 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 22.3% (1134 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 23.6% (1197 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 24.4% (1239 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 25.2% (1279 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 26.3% (1336 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 27.5% (1395 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 28.4% (1443 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 29.6% (1503 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 30.3% (1536 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 31.4% (1593 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 32.6% (1654 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 33.4% (1696 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 34.4% (1743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 35.4% (1798 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 36.6% (1855 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 37.3% (1894 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 38.3% (1941 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 39.4% (2000 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 40.4% (2051 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 41.9% (2128 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 42.6% (2160 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 43.4% (2202 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 44.3% (2248 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 45.5% (2309 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 46.3% (2350 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 47.3% (2402 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 48.4% (2457 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 49.3% (2500 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 50.5% (2563 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 51.4% (2610 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 52.6% (2669 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 53.4% (2710 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 54.5% (2763 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 55.5% (2818 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 56.4% (2863 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 57.7% (2927 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 58.3% (2958 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 59.6% (3024 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 60.4% (3063 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 61.4% (3115 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 62.3% (3162 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 63.4% (3219 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 64.4% (3266 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 65.6% (3330 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 66.4% (3371 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 67.5% (3426 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 68.5% (3476 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 69.6% (3532 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 70.9% (3596 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 71.7% (3640 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 72.7% (3690 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 73.5% (3731 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 74.6% (3783 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 75.4% (3828 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 76.4% (3876 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 77.5% (3931 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 78.5% (3982 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 79.7% (4044 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 80.6% (4089 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 81.7% (4145 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 82.4% (4182 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 83.8% (4254 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 84.5% (4288 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 85.6% (4341 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 86.8% (4404 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 87.5% (4439 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 88.6% (4497 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 89.5% (4540 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 90.5% (4594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 91.5% (4643 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 92.6% (4697 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 93.5% (4744 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 94.5% (4794 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 95.6% (4853 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.1; 3495 / 5074 (P = 68.88%) round 25]               
[00:00:00] Finding cutoff p=917 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=917 1.8% (91 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 2.8% (142 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 3.7% (189 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 4.6% (231 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 5.0% (256 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 6.1% (312 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 7.8% (394 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 8.6% (436 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 9.5% (480 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 10.6% (540 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 11.5% (586 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 12.6% (640 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 13.3% (674 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 14.5% (737 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 15.9% (806 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 16.9% (857 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 17.8% (905 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 19.1% (971 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 20.6% (1044 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 21.7% (1101 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 22.4% (1138 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 23.3% (1180 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 24.5% (1245 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 25.7% (1306 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 26.5% (1343 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 27.6% (1401 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 29.0% (1472 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 29.8% (1510 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 30.3% (1539 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 31.9% (1620 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 32.5% (1651 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 33.5% (1702 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 34.7% (1759 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 35.6% (1806 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 36.2% (1837 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 37.4% (1899 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 38.2% (1939 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 40.0% (2030 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 41.6% (2109 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 42.2% (2143 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 43.7% (2216 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 44.4% (2253 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 45.4% (2302 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 46.5% (2360 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 47.6% (2414 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 48.5% (2459 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 49.9% (2533 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 50.5% (2564 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 51.4% (2607 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 52.4% (2661 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 53.3% (2704 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 54.7% (2776 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 55.4% (2809 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 56.5% (2865 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 57.6% (2923 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 58.4% (2965 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 59.3% (3010 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 60.3% (3061 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 61.7% (3130 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 62.5% (3173 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 63.5% (3221 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 65.0% (3296 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 66.3% (3364 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 66.8% (3388 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 67.5% (3427 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 68.4% (3470 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 69.7% (3537 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 70.6% (3580 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 71.4% (3621 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 72.9% (3700 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 73.4% (3726 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 74.5% (3781 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 75.6% (3836 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 76.5% (3883 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 77.7% (3942 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 78.9% (4003 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 79.5% (4035 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 80.4% (4081 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 81.5% (4133 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 82.4% (4182 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 83.9% (4255 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 84.8% (4303 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 85.6% (4343 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 86.8% (4403 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 87.5% (4441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 88.7% (4503 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 89.5% (4540 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 90.5% (4593 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 91.5% (4645 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 92.6% (4698 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 93.5% (4745 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 94.6% (4798 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 95.6% (4850 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 96.5% (4897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 97.5% (4949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=917 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.7; 3597 / 5074 (P = 70.89%) round 26]               
[00:00:00] Finding cutoff p=912 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=912 2.0% (104 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 3.1% (155 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 4.4% (223 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 5.8% (293 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 7.8% (396 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 8.7% (441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 9.5% (482 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 10.7% (543 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 11.6% (588 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 12.5% (634 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 13.2% (668 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 14.9% (755 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 16.1% (816 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 17.1% (869 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 18.5% (939 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 19.3% (980 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 20.5% (1038 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 22.1% (1122 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 23.3% (1181 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 24.2% (1230 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 25.2% (1279 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 26.3% (1334 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 28.2% (1430 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 29.3% (1487 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 30.9% (1569 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 31.7% (1608 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 32.7% (1661 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 33.9% (1718 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 34.5% (1751 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 35.6% (1805 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 36.4% (1847 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 37.2% (1889 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 38.3% (1941 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 39.3% (1993 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 40.2% (2042 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 41.5% (2105 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 42.4% (2151 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 43.2% (2193 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 44.5% (2257 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 45.3% (2300 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 46.2% (2346 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 48.0% (2438 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 48.8% (2475 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 49.4% (2508 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 50.4% (2558 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 51.4% (2607 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 52.3% (2653 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 53.3% (2703 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 54.4% (2759 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 55.3% (2806 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 56.3% (2857 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 58.1% (2947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 58.5% (2970 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 59.5% (3017 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 60.5% (3070 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 61.4% (3116 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 62.5% (3172 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 64.2% (3256 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 64.9% (3294 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 65.7% (3332 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 66.4% (3367 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 67.5% (3427 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 68.6% (3481 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 69.6% (3534 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 70.6% (3580 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 71.6% (3634 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 72.5% (3679 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 73.5% (3727 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 74.6% (3786 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 75.4% (3826 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 76.4% (3878 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 77.7% (3942 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 78.7% (3995 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 79.6% (4040 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 80.5% (4085 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 81.4% (4132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 82.6% (4193 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 83.8% (4250 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 84.6% (4291 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 85.7% (4347 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 86.8% (4405 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 87.6% (4447 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 88.6% (4494 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 89.7% (4551 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 90.6% (4595 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 91.5% (4642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 92.6% (4700 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 93.5% (4743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 94.5% (4795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 95.6% (4849 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 96.6% (4899 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 97.5% (4949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=912 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.2; 3689 / 5074 (P = 72.70%) round 27]               
[00:00:00] Finding cutoff p=908 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=908 1.9% (94 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 2.9% (149 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 3.8% (191 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 4.4% (224 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 5.0% (256 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 6.1% (311 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 7.4% (373 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 8.2% (418 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 9.1% (463 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 10.3% (524 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 11.2% (568 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 12.3% (626 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 13.2% (671 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 14.3% (727 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 15.9% (806 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 16.9% (858 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 17.8% (902 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 18.9% (959 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 20.1% (1022 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 21.5% (1091 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 22.6% (1149 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 23.2% (1178 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 24.6% (1249 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 25.6% (1299 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 26.4% (1337 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 27.2% (1379 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 29.0% (1470 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 29.7% (1508 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 30.4% (1545 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 31.9% (1621 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 32.4% (1646 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 33.9% (1721 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 35.2% (1787 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 36.3% (1843 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 37.2% (1890 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 38.4% (1949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 39.4% (1998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 40.8% (2069 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 41.3% (2095 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 42.7% (2166 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 43.4% (2204 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 44.2% (2245 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 45.4% (2305 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 46.8% (2373 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 48.0% (2437 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 48.7% (2472 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 49.3% (2504 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 50.5% (2562 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 51.7% (2623 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 52.5% (2662 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 53.4% (2709 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 54.3% (2755 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 55.4% (2812 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 56.4% (2860 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 57.6% (2923 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 58.3% (2960 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 59.6% (3026 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 60.4% (3063 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 61.4% (3116 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 62.5% (3172 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 63.6% (3227 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 64.6% (3277 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 65.6% (3327 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 66.4% (3370 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 67.6% (3431 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 68.7% (3487 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 69.7% (3536 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 70.6% (3580 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 71.5% (3627 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 72.4% (3674 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 73.4% (3725 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 74.5% (3782 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 75.5% (3830 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 76.7% (3892 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 77.4% (3929 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 78.7% (3993 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 79.5% (4036 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 80.6% (4089 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 81.5% (4135 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 82.5% (4184 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 83.6% (4243 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 84.5% (4285 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 85.7% (4346 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 86.7% (4398 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 87.6% (4446 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 88.6% (4498 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 89.5% (4540 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 90.6% (4597 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 91.6% (4646 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 92.5% (4694 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 93.6% (4751 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 94.6% (4798 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 95.6% (4853 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 96.6% (4899 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.8; 3784 / 5074 (P = 74.58%) round 28]               
[00:00:00] Finding cutoff p=904 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=904 2.0% (101 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 3.1% (155 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 4.3% (218 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 5.6% (285 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 7.7% (393 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 8.8% (447 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 9.6% (487 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 10.8% (546 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 11.6% (588 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 12.5% (634 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 13.4% (679 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 14.7% (746 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 16.2% (824 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 17.4% (881 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 18.7% (951 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 19.9% (1011 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 21.5% (1093 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 22.2% (1128 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 23.3% (1181 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 24.7% (1251 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 25.5% (1296 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 26.1% (1326 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 27.2% (1380 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 28.7% (1457 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 29.4% (1493 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 30.4% (1544 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 31.6% (1605 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 32.4% (1643 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 33.3% (1692 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 34.6% (1756 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 35.4% (1797 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 36.5% (1853 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 37.6% (1909 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 38.7% (1965 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 40.0% (2030 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 40.6% (2062 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 41.5% (2106 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 42.4% (2152 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 43.4% (2204 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 44.9% (2280 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 45.3% (2301 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 46.3% (2347 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 47.5% (2408 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 48.5% (2461 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 49.4% (2507 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 50.5% (2561 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 51.6% (2620 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 52.3% (2653 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 53.6% (2718 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 54.6% (2772 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 55.4% (2811 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 56.4% (2862 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 57.6% (2923 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 58.5% (2966 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 59.5% (3019 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 60.4% (3065 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 61.4% (3113 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 62.4% (3167 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 63.9% (3242 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 64.9% (3293 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 65.6% (3327 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 66.4% (3367 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 67.3% (3417 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 69.0% (3500 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 69.8% (3541 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 70.8% (3591 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 71.7% (3636 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 72.5% (3678 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 73.4% (3725 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 74.8% (3793 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 75.4% (3825 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 76.4% (3876 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 77.7% (3941 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 78.5% (3984 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 79.6% (4041 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 80.6% (4089 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 81.5% (4136 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 82.5% (4184 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 83.4% (4234 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 84.4% (4284 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 85.6% (4342 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 86.8% (4405 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 87.5% (4441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 88.6% (4496 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 89.6% (4547 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 90.5% (4590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 91.5% (4641 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 92.5% (4693 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 93.5% (4744 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 94.5% (4797 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 95.5% (4848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 98.5% (4999 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.4; 3857 / 5074 (P = 76.01%) round 29]               
[00:00:00] Finding cutoff p=900 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=900 2.0% (103 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 3.1% (157 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 4.5% (227 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 5.2% (265 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 6.6% (337 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 8.4% (424 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 9.2% (467 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 11.3% (571 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 12.4% (627 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 13.2% (671 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 15.0% (762 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 16.4% (833 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 17.2% (875 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 18.7% (948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 19.6% (994 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 20.5% (1042 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 21.9% (1112 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 22.4% (1139 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 23.4% (1189 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 24.5% (1241 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 25.2% (1278 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 27.3% (1386 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 28.2% (1432 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 29.2% (1482 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 30.4% (1543 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 31.8% (1612 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 32.9% (1671 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 33.7% (1708 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 34.4% (1743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 35.2% (1788 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 36.6% (1858 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 37.8% (1919 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 39.2% (1990 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 40.3% (2045 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 41.6% (2113 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 42.2% (2143 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 43.4% (2204 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 44.3% (2246 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 45.3% (2301 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 46.3% (2348 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 47.7% (2422 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 48.5% (2459 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 49.4% (2505 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 50.5% (2562 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 51.4% (2607 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 52.5% (2666 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 53.7% (2724 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 54.3% (2756 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 55.3% (2806 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 56.6% (2874 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 57.4% (2914 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 58.6% (2972 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 59.6% (3023 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 60.6% (3073 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 61.5% (3120 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 63.1% (3200 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 63.9% (3240 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 64.5% (3271 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 65.5% (3321 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 66.5% (3374 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 67.4% (3420 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 68.4% (3469 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 69.4% (3519 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 70.8% (3593 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 71.5% (3626 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 72.4% (3674 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 73.5% (3729 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 74.7% (3790 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 75.6% (3834 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 76.5% (3880 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 77.5% (3933 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 78.5% (3981 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 79.6% (4041 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 80.4% (4081 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 81.7% (4144 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 82.4% (4182 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 83.5% (4238 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 84.6% (4292 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 85.8% (4351 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 86.7% (4400 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 87.5% (4442 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 88.5% (4493 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 89.6% (4544 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 90.5% (4591 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 91.5% (4641 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 92.6% (4699 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 93.5% (4745 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 94.5% (4796 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 95.6% (4849 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 98.5% (4999 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=900 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.0; 3935 / 5074 (P = 77.55%) round 30]               
[00:00:00] Finding cutoff p=889 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=889 1.9% (94 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 2.9% (148 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 3.7% (188 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 4.5% (226 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 5.1% (258 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 6.4% (324 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 7.7% (393 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 8.6% (437 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 9.3% (471 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 10.4% (526 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 11.3% (572 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 12.1% (613 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 13.8% (702 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 16.0% (811 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 16.9% (858 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 17.8% (904 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 18.6% (942 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 19.6% (996 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 20.9% (1059 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 21.9% (1113 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 22.7% (1153 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 23.4% (1186 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 24.9% (1261 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 25.4% (1291 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 26.2% (1328 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 28.2% (1433 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 29.2% (1483 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 31.0% (1572 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 31.7% (1610 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 32.7% (1658 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 33.8% (1715 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 34.6% (1754 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 35.5% (1801 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 36.4% (1846 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 37.8% (1920 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 39.5% (2002 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 40.4% (2048 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 41.6% (2111 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 42.3% (2145 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 43.7% (2218 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 44.3% (2246 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 45.7% (2319 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 46.9% (2380 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 47.6% (2415 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 48.3% (2449 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 49.3% (2500 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 50.3% (2553 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 51.5% (2613 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 52.5% (2664 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 54.2% (2748 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 54.6% (2772 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 55.5% (2818 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 56.9% (2887 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 57.4% (2915 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 58.3% (2960 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 59.3% (3009 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 60.5% (3071 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 61.5% (3123 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 63.1% (3204 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 64.1% (3252 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 65.0% (3297 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 65.4% (3318 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 66.3% (3366 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 67.3% (3417 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 69.1% (3506 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 69.7% (3537 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 70.6% (3580 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 71.6% (3632 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 72.4% (3675 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 73.5% (3727 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 74.6% (3783 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 75.5% (3829 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 76.5% (3880 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 77.5% (3933 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 78.5% (3982 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 79.8% (4050 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 80.5% (4086 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 81.6% (4142 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 82.6% (4192 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 83.6% (4242 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 84.6% (4294 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 85.6% (4343 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 86.7% (4398 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 87.5% (4441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 88.6% (4496 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 89.5% (4542 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 90.5% (4594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 91.5% (4645 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 92.5% (4692 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 93.5% (4743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 94.5% (4796 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 95.5% (4847 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 96.5% (4898 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 98.5% (5000 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 88.9; 3988 / 5074 (P = 78.60%) round 31]               
[00:00:00] Finding cutoff p=879 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=879 1.7% (88 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 2.7% (136 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 3.3% (168 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 4.1% (208 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 5.5% (280 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 7.2% (365 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 8.2% (417 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 9.6% (486 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 10.4% (526 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 11.6% (589 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 12.3% (624 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 14.3% (724 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 16.0% (813 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 17.1% (870 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 18.6% (944 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 19.8% (1005 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 20.5% (1038 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 21.9% (1109 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 22.5% (1141 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 23.3% (1182 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 24.8% (1256 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 25.4% (1289 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 26.3% (1332 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 27.8% (1413 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 28.6% (1452 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 29.3% (1487 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 31.2% (1585 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 32.5% (1648 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 33.7% (1710 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 34.4% (1747 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 35.5% (1800 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 36.2% (1839 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 37.3% (1895 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 38.5% (1952 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 39.8% (2019 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 40.4% (2052 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 41.5% (2105 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 42.3% (2145 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 43.4% (2202 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 44.3% (2247 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 45.3% (2300 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 47.2% (2393 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 48.0% (2436 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 48.6% (2465 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 49.4% (2508 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 50.6% (2566 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 51.8% (2629 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 52.4% (2658 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 53.6% (2720 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 54.4% (2759 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 55.5% (2817 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 56.9% (2886 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 57.7% (2930 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 58.4% (2965 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 59.3% (3010 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 60.5% (3068 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 61.5% (3123 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 63.3% (3212 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 64.1% (3251 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 64.5% (3275 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 65.5% (3322 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 66.5% (3374 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 67.4% (3420 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 68.5% (3478 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 69.4% (3520 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 70.5% (3578 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 71.4% (3624 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 72.6% (3682 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 73.6% (3732 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 74.4% (3776 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 75.5% (3832 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 76.5% (3881 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 77.4% (3927 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 78.5% (3985 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 79.5% (4033 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 80.7% (4095 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 81.4% (4132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 82.8% (4203 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 83.5% (4239 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 84.5% (4290 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 85.5% (4338 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 86.4% (4386 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 87.5% (4440 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 88.7% (4499 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 89.5% (4539 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 90.6% (4597 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 91.5% (4642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 92.5% (4692 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 93.5% (4745 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 94.5% (4797 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 95.5% (4848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 98.5% (4999 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=879 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 87.9; 4088 / 5074 (P = 80.57%) round 32]               
[00:00:00] Finding cutoff p=869 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=869 1.8% (89 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 2.5% (126 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 3.5% (180 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 4.1% (210 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 5.4% (273 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 6.9% (351 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 7.8% (398 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 8.5% (433 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 9.7% (490 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 10.4% (526 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 11.3% (574 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 13.0% (659 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 14.5% (737 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 15.2% (770 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 16.4% (834 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 17.2% (871 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 18.5% (939 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 19.6% (992 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 20.2% (1026 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 21.7% (1103 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 22.5% (1143 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 23.6% (1198 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 24.3% (1233 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 25.2% (1280 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 26.5% (1343 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 27.3% (1383 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 28.5% (1447 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 29.6% (1501 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 30.2% (1531 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 31.3% (1586 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 32.3% (1638 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 33.5% (1701 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 34.2% (1737 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 35.3% (1789 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 37.3% (1891 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 38.4% (1947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 39.8% (2021 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 40.7% (2066 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 41.3% (2094 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 42.7% (2168 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 43.3% (2199 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 44.6% (2264 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 46.0% (2334 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 46.8% (2373 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 47.5% (2408 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 48.5% (2461 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 49.6% (2515 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 50.6% (2566 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 51.3% (2603 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 52.7% (2674 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 53.5% (2716 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 54.6% (2770 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 56.0% (2839 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 56.3% (2859 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 57.4% (2912 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 58.5% (2970 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 59.6% (3025 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 60.5% (3072 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 61.5% (3122 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 62.6% (3178 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 63.5% (3223 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 64.6% (3280 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 65.7% (3334 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 66.5% (3375 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 67.4% (3422 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 69.0% (3500 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 69.9% (3545 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 70.8% (3593 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 71.5% (3627 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 72.4% (3676 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 73.4% (3725 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 74.4% (3776 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 75.5% (3832 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 76.5% (3881 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 77.5% (3932 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 78.5% (3985 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 79.5% (4034 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 80.7% (4094 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 81.8% (4149 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 82.5% (4188 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 83.5% (4237 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 84.7% (4300 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 85.5% (4337 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 86.6% (4392 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 87.5% (4442 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 88.5% (4491 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 89.5% (4539 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 90.5% (4590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 91.5% (4643 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 92.5% (4694 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 93.5% (4743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 94.5% (4795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 95.5% (4845 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 97.6% (4950 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=869 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 86.9; 4198 / 5074 (P = 82.74%) round 33]               
[00:00:00] Finding cutoff p=860 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=860 1.8% (92 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 2.7% (136 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 3.5% (179 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 4.3% (220 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 5.5% (280 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 7.2% (366 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 8.4% (428 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 9.5% (482 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 10.3% (522 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 11.4% (580 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 12.1% (614 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 13.4% (678 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 14.8% (750 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 15.7% (795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 16.8% (851 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 17.8% (901 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 18.6% (944 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 19.8% (1005 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 20.8% (1056 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 21.4% (1086 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 22.6% (1148 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 23.3% (1181 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 24.2% (1229 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 25.2% (1279 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 26.9% (1364 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 27.8% (1412 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 28.3% (1437 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 30.0% (1524 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 30.6% (1555 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 31.3% (1589 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 32.3% (1640 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 33.5% (1700 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 34.3% (1741 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 35.4% (1795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 36.4% (1845 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 37.7% (1912 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 38.2% (1938 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 40.0% (2028 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 40.7% (2066 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 41.2% (2091 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 42.3% (2147 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 43.5% (2205 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 45.1% (2289 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 45.7% (2319 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 46.3% (2351 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 47.4% (2405 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 48.3% (2451 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 49.4% (2508 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 50.7% (2571 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 51.4% (2610 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 52.4% (2660 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 54.0% (2741 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 54.6% (2768 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 55.5% (2818 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 56.3% (2859 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 57.6% (2922 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 58.3% (2959 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 59.3% (3009 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 60.4% (3067 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 62.0% (3144 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 62.5% (3171 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 63.5% (3224 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 64.4% (3270 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 65.4% (3318 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 66.5% (3374 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 67.4% (3419 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 68.8% (3491 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 69.5% (3526 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 70.8% (3590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 71.4% (3625 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 72.5% (3679 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 73.8% (3744 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 74.6% (3783 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 75.6% (3837 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 76.4% (3878 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 77.5% (3933 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 78.5% (3983 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 79.5% (4036 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 80.4% (4081 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 81.7% (4143 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 82.7% (4195 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 83.9% (4255 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 84.8% (4301 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 85.4% (4335 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 86.7% (4401 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 87.5% (4441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 88.6% (4496 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 89.8% (4555 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 90.6% (4595 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 91.6% (4648 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 92.5% (4692 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 93.6% (4748 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 94.6% (4798 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 95.5% (4845 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 96.5% (4898 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 98.5% (4999 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=860 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 86.0; 4286 / 5074 (P = 84.47%) round 34]               
[00:00:00] Finding cutoff p=851 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=851 2.0% (104 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 3.4% (174 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 4.6% (233 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 5.1% (258 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 6.1% (310 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 7.7% (389 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 8.6% (438 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 9.5% (480 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 10.6% (540 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 11.3% (575 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 12.6% (637 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 13.3% (675 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 14.4% (729 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 15.8% (800 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 16.7% (849 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 17.4% (885 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 18.4% (933 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 19.5% (989 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 21.0% (1065 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 21.6% (1094 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 22.2% (1124 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 23.7% (1203 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 24.6% (1250 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 25.5% (1295 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 26.3% (1332 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 27.7% (1403 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 28.3% (1434 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 29.3% (1485 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 30.2% (1533 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 31.9% (1621 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 32.7% (1657 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 33.2% (1685 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 34.2% (1734 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 35.2% (1785 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 36.4% (1849 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 37.7% (1914 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 38.5% (1952 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 39.6% (2008 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 40.7% (2067 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 41.3% (2095 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 42.4% (2152 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 43.3% (2197 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 44.2% (2244 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 45.5% (2311 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 46.8% (2375 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 47.4% (2406 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 48.3% (2451 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 49.3% (2504 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 50.3% (2552 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 51.4% (2608 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 52.3% (2654 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 53.7% (2727 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 54.3% (2754 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 55.6% (2820 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 56.6% (2871 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 57.4% (2911 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 58.5% (2966 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 59.5% (3018 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 60.4% (3066 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 61.4% (3114 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 62.6% (3176 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 63.6% (3225 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 64.7% (3282 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 65.4% (3319 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 66.5% (3376 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 67.5% (3423 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 68.8% (3493 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 69.9% (3548 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 70.9% (3597 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 71.5% (3630 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 72.6% (3685 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 73.5% (3729 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 74.6% (3784 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 75.6% (3836 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 76.6% (3886 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 77.6% (3935 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 78.5% (3984 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 79.7% (4043 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 80.6% (4091 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 81.5% (4137 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 82.5% (4187 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 83.6% (4244 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 84.9% (4306 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 85.5% (4340 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 86.5% (4388 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 87.6% (4447 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 88.6% (4495 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 89.5% (4542 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 90.5% (4590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 91.6% (4646 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 92.6% (4697 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 93.5% (4744 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 94.5% (4795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 95.7% (4855 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 96.5% (4898 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 97.5% (4949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=851 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.1; 4363 / 5074 (P = 85.99%) round 35]               
[00:00:00] Finding cutoff p=841 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=841 2.0% (99 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 2.8% (144 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 3.5% (180 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 4.3% (218 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 5.8% (292 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 7.3% (371 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 8.1% (412 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 9.7% (491 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 10.4% (530 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 11.5% (581 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 12.1% (614 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 13.5% (687 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 15.1% (764 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 16.0% (811 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 16.7% (848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 17.7% (900 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 18.4% (933 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 19.8% (1003 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 20.7% (1051 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 21.9% (1109 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 22.6% (1145 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 23.2% (1175 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 24.1% (1224 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 25.5% (1292 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 27.0% (1370 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 27.8% (1410 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 28.4% (1439 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 30.1% (1526 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 30.6% (1551 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 31.2% (1582 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 32.2% (1634 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 33.2% (1684 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 34.5% (1748 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 35.5% (1802 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 36.2% (1838 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 37.6% (1909 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 38.6% (1958 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 39.5% (2004 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 40.4% (2051 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 41.2% (2092 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 42.3% (2146 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 43.5% (2205 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 45.0% (2284 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 45.7% (2321 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 46.3% (2349 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 47.4% (2405 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 48.5% (2459 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 49.3% (2504 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 50.4% (2557 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 52.1% (2642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 52.6% (2670 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 53.4% (2709 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 55.0% (2789 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 55.5% (2818 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 56.3% (2858 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 57.4% (2915 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 58.8% (2983 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 59.5% (3018 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 60.4% (3064 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 61.5% (3118 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 62.7% (3181 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 63.3% (3213 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 64.4% (3267 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 65.7% (3332 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 66.7% (3386 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 67.6% (3432 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 68.6% (3481 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 69.4% (3519 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 70.4% (3574 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 71.6% (3633 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 72.6% (3686 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 73.4% (3725 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 74.6% (3785 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 75.4% (3826 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 76.5% (3882 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 77.6% (3935 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 78.5% (3984 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 79.4% (4030 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 80.5% (4083 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 81.6% (4140 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 82.7% (4194 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 83.7% (4247 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 84.9% (4308 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 85.5% (4337 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 86.5% (4391 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 87.6% (4445 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 88.6% (4497 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 89.6% (4545 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 90.5% (4594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 91.5% (4643 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 92.5% (4695 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 93.7% (4752 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 94.5% (4797 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 95.5% (4848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 96.5% (4897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=841 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.1; 4444 / 5074 (P = 87.58%) round 36]               
[00:00:00] Finding cutoff p=831 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=831 1.8% (91 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 2.6% (130 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 3.4% (170 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 4.3% (220 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 5.7% (288 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 7.3% (371 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 8.4% (424 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 10.0% (509 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 10.7% (545 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 11.5% (583 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 13.3% (675 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 14.6% (741 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 15.4% (783 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 16.2% (822 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 17.1% (868 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 18.8% (956 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 19.7% (998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 20.8% (1054 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 21.6% (1098 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 22.9% (1163 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 23.4% (1189 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 24.6% (1249 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 25.9% (1315 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 26.7% (1357 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 27.2% (1380 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 29.1% (1479 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 30.8% (1564 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 31.3% (1588 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 32.3% (1639 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 33.2% (1685 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 34.3% (1742 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 35.7% (1809 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 36.2% (1836 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 37.3% (1891 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 38.6% (1958 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 39.3% (1995 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 40.4% (2051 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 41.4% (2102 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 42.3% (2144 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 43.5% (2207 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 44.5% (2260 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 45.4% (2304 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 46.3% (2349 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 47.3% (2399 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 49.4% (2509 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 50.9% (2583 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 51.4% (2610 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 52.3% (2653 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 53.5% (2713 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 54.6% (2769 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 55.5% (2817 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 56.3% (2858 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 57.5% (2920 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 58.3% (2960 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 60.2% (3057 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 61.2% (3104 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 61.7% (3130 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 62.5% (3172 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 63.6% (3227 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 64.4% (3267 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 66.0% (3350 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 66.8% (3389 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 67.8% (3439 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 68.6% (3480 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 69.6% (3531 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 70.8% (3593 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 71.6% (3633 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 72.4% (3672 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 73.6% (3737 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 74.7% (3788 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 75.6% (3834 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 76.7% (3891 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 77.5% (3934 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 78.5% (3984 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 79.6% (4041 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 80.4% (4082 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 81.4% (4132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 82.5% (4188 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 83.5% (4235 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 84.5% (4290 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 85.7% (4346 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 86.8% (4402 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 87.6% (4447 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 88.5% (4489 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 89.7% (4550 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 90.6% (4598 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 91.5% (4642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 92.6% (4697 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 93.5% (4745 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 94.5% (4794 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 95.6% (4852 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 98.5% (4999 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=831 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 83.1; 4516 / 5074 (P = 89.00%) round 37]               
[00:00:00] Finding cutoff p=822 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=822 1.7% (86 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 2.6% (134 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 3.4% (170 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 4.5% (226 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 5.2% (263 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 6.8% (345 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 7.4% (375 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 9.2% (468 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 10.3% (524 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 12.3% (623 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 13.8% (700 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 14.4% (731 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 15.4% (781 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 16.1% (816 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 17.1% (868 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 18.5% (939 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 19.6% (995 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 21.1% (1069 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 22.0% (1117 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 23.0% (1169 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 23.4% (1188 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 24.5% (1245 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 25.9% (1313 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 26.7% (1354 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 27.3% (1385 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 29.1% (1476 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 29.8% (1510 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 30.4% (1544 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 31.3% (1590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 32.2% (1634 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 33.4% (1693 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 34.5% (1751 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 35.5% (1802 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 37.5% (1905 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 38.4% (1948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 40.0% (2031 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 40.7% (2065 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 41.5% (2105 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 42.6% (2162 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 43.2% (2194 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 44.5% (2256 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 45.5% (2309 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 46.5% (2358 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 47.3% (2402 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 48.3% (2450 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 49.3% (2502 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 50.3% (2550 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 51.3% (2604 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 52.4% (2660 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 53.5% (2713 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 54.4% (2758 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 55.5% (2818 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 56.5% (2865 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 57.6% (2925 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 58.3% (2960 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 59.3% (3009 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 60.6% (3075 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 61.4% (3114 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 62.4% (3167 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 63.9% (3240 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 64.6% (3277 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 66.1% (3352 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 66.9% (3395 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 67.8% (3442 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 68.6% (3482 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 69.4% (3523 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 70.7% (3586 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 71.4% (3623 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 72.6% (3682 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 73.4% (3724 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 74.4% (3775 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 75.5% (3829 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 76.4% (3878 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 77.4% (3928 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 78.4% (3980 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 79.5% (4035 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 80.5% (4086 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 81.5% (4133 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 82.4% (4183 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 83.4% (4233 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 84.6% (4293 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 85.6% (4341 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 86.6% (4396 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 87.5% (4441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 88.5% (4489 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 89.5% (4542 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 90.6% (4598 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 91.6% (4647 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 92.5% (4694 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 93.5% (4745 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 94.6% (4798 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 95.8% (4861 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 96.5% (4896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=822 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.2; 4567 / 5074 (P = 90.01%) round 38]               
[00:00:00] Finding cutoff p=811 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=811 1.8% (89 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 2.6% (131 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 3.3% (166 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 4.2% (213 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 5.2% (266 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 6.8% (345 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 7.5% (380 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 9.1% (464 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 10.5% (532 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 11.1% (562 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 12.3% (622 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 14.1% (715 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 15.3% (775 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 16.5% (835 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 17.3% (880 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 18.5% (939 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 19.6% (996 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 21.0% (1066 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 21.6% (1097 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 22.7% (1154 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 23.2% (1178 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 24.2% (1230 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 25.6% (1297 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 26.4% (1338 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 28.3% (1434 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 29.3% (1487 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 30.5% (1548 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 31.4% (1595 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 32.4% (1642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 33.3% (1691 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 35.2% (1786 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 36.6% (1855 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 37.9% (1922 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 38.5% (1954 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 39.4% (1998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 40.4% (2050 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 41.2% (2091 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 42.5% (2155 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 43.6% (2214 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 44.6% (2265 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 45.4% (2305 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 46.4% (2352 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 47.5% (2409 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 48.6% (2466 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 49.7% (2520 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 50.5% (2560 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 51.3% (2601 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 52.5% (2662 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 53.9% (2736 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 54.4% (2759 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 55.5% (2816 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 56.7% (2875 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 57.4% (2913 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 58.6% (2975 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 59.6% (3022 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 60.8% (3083 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 61.4% (3116 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 62.5% (3171 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 63.5% (3221 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 64.5% (3273 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 65.4% (3316 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 67.1% (3405 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 67.4% (3422 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 68.4% (3470 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 69.4% (3521 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 70.4% (3571 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 71.6% (3632 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 72.7% (3687 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 73.6% (3737 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 74.4% (3776 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 75.4% (3828 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 76.6% (3885 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 77.6% (3937 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 78.5% (3981 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 79.6% (4041 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 80.5% (4083 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 81.7% (4146 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 82.5% (4188 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 83.6% (4244 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 84.6% (4295 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 85.7% (4347 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 86.7% (4397 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 87.6% (4443 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 88.5% (4492 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 89.6% (4546 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 90.5% (4590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 91.6% (4649 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 92.5% (4694 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 93.5% (4745 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 94.7% (4803 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 95.5% (4847 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 96.5% (4898 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 97.6% (4952 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 98.5% (5000 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=811 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 81.1; 4620 / 5074 (P = 91.05%) round 39]               
[00:00:00] Finding cutoff p=800 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=800 1.6% (83 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 2.4% (120 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 3.2% (164 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 4.1% (207 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 5.2% (264 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 7.1% (359 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 8.1% (409 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 9.5% (484 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 10.4% (528 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 11.6% (591 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 12.2% (619 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 13.5% (686 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 15.1% (764 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 15.6% (793 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 16.2% (824 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 17.6% (891 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 18.4% (933 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 19.6% (992 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 20.6% (1045 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 22.0% (1114 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 22.8% (1157 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 23.9% (1213 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 24.3% (1233 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 25.4% (1288 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 26.7% (1356 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 27.7% (1403 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 28.1% (1428 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 29.6% (1501 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 30.8% (1561 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 32.0% (1623 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 32.5% (1650 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 33.6% (1704 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 34.4% (1743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 35.8% (1817 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 37.2% (1885 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 37.7% (1915 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 38.3% (1943 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 39.4% (2000 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 40.3% (2046 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 41.4% (2101 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 42.3% (2146 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 43.8% (2224 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 44.4% (2255 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 45.3% (2297 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 46.6% (2362 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 47.4% (2405 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 48.4% (2458 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 49.7% (2522 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 50.4% (2558 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 52.1% (2643 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 52.6% (2670 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 53.6% (2722 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 54.6% (2770 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 55.4% (2812 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 56.3% (2856 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 57.3% (2907 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 58.5% (2966 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 59.4% (3012 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 60.5% (3069 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 61.6% (3126 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 62.5% (3173 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 63.3% (3213 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 64.7% (3282 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 65.8% (3338 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 66.8% (3387 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 67.6% (3428 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 68.4% (3471 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 69.4% (3521 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 70.6% (3582 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 71.6% (3632 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 72.4% (3673 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 73.7% (3738 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 74.5% (3782 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 75.5% (3831 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 76.7% (3890 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 77.9% (3952 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 78.6% (3987 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 79.7% (4046 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 80.4% (4080 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 81.7% (4145 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 83.0% (4212 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 83.4% (4233 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 84.4% (4284 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 85.5% (4340 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 86.5% (4389 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 87.5% (4439 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 88.6% (4497 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 89.5% (4542 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 90.5% (4592 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 91.5% (4641 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 92.6% (4697 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 93.6% (4748 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 94.5% (4796 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 95.5% (4846 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 96.6% (4899 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 97.5% (4949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=800 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.0; 4683 / 5074 (P = 92.29%) round 40]               
[00:00:00] Finding cutoff p=789 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=789 1.8% (91 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 2.5% (128 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 3.3% (166 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 4.0% (204 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 5.5% (277 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 7.1% (359 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 9.2% (466 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 10.3% (525 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 12.2% (618 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 13.7% (696 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 14.2% (718 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 15.5% (786 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 16.1% (818 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 17.2% (871 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 18.5% (937 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 20.4% (1034 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 22.0% (1115 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 22.4% (1137 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 23.5% (1194 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 24.8% (1258 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 25.4% (1290 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 27.1% (1375 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 27.6% (1401 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 28.5% (1445 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 29.3% (1489 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 30.5% (1550 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 31.3% (1586 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 32.5% (1648 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 33.3% (1688 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 34.5% (1753 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 35.5% (1800 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 36.8% (1866 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 37.4% (1896 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 38.4% (1947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 39.4% (1999 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 40.5% (2053 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 41.3% (2097 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 42.4% (2151 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 43.4% (2201 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 44.3% (2247 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 45.4% (2306 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 46.3% (2348 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 47.6% (2417 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 48.2% (2448 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 50.0% (2536 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 50.3% (2553 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 51.3% (2604 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 52.5% (2666 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 53.5% (2717 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 54.5% (2766 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 56.3% (2858 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 57.6% (2925 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 58.4% (2963 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 59.5% (3017 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 60.6% (3077 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 61.9% (3143 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 62.9% (3190 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 63.6% (3228 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 64.4% (3267 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 65.4% (3318 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 66.6% (3377 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 67.6% (3428 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 68.7% (3487 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 69.6% (3534 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 70.5% (3578 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 71.6% (3633 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 72.5% (3679 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 73.5% (3728 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 74.6% (3787 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 75.7% (3839 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 76.5% (3880 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 77.5% (3930 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 78.5% (3983 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 79.5% (4033 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 80.7% (4096 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 81.6% (4138 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 82.5% (4187 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 83.5% (4238 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 84.6% (4293 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 85.5% (4340 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 86.8% (4405 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 87.5% (4439 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 88.6% (4494 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 89.5% (4541 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 90.5% (4592 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 91.5% (4645 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 92.5% (4694 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 93.7% (4752 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 94.5% (4797 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 95.5% (4848 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 96.6% (4899 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 97.6% (4951 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=789 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.9; 4730 / 5074 (P = 93.22%) round 41]               
[00:00:00] Finding cutoff p=780 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=780 1.8% (90 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 2.6% (133 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 3.4% (170 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 4.3% (219 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 5.4% (274 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 7.1% (359 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 9.3% (472 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 11.3% (573 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 12.9% (656 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 14.8% (749 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 15.5% (787 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 16.4% (834 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 17.6% (893 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 18.4% (935 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 19.6% (996 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 20.2% (1025 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 21.3% (1082 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 23.0% (1167 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 23.6% (1198 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 24.6% (1246 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 26.0% (1320 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 26.5% (1346 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 27.2% (1378 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 28.9% (1465 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 29.3% (1486 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 30.3% (1536 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 31.3% (1588 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 32.4% (1645 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 33.4% (1696 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 34.4% (1746 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 35.7% (1810 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 37.0% (1877 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 37.6% (1906 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 39.0% (1979 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 39.4% (1999 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 40.5% (2055 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 41.5% (2105 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 42.3% (2145 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 43.6% (2211 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 44.7% (2266 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 45.3% (2301 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 46.3% (2347 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 47.8% (2423 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 48.6% (2468 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 49.4% (2507 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 50.7% (2570 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 51.5% (2615 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 52.3% (2654 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 54.1% (2744 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 54.5% (2766 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 55.4% (2810 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 56.3% (2859 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 57.3% (2909 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 58.3% (2960 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 60.2% (3053 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 61.0% (3096 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 61.7% (3131 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 62.3% (3162 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 63.9% (3242 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 64.5% (3275 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 66.0% (3348 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 66.8% (3391 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 67.8% (3440 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 68.5% (3477 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 69.6% (3530 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 70.9% (3596 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 71.4% (3623 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 72.5% (3680 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 73.5% (3727 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 74.6% (3784 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 75.6% (3834 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 76.5% (3880 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 77.5% (3930 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 78.5% (3982 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 79.6% (4041 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 80.5% (4087 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 81.4% (4132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 82.5% (4184 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 83.9% (4257 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 84.4% (4284 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 85.6% (4341 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 86.7% (4399 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 87.5% (4438 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 88.6% (4498 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 89.5% (4539 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 90.6% (4597 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 91.5% (4642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 92.5% (4695 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 93.6% (4750 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 94.5% (4795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 95.5% (4847 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 96.6% (4899 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=780 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.0; 4762 / 5074 (P = 93.85%) round 42]               
[00:00:00] Finding cutoff p=770 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=770 1.8% (92 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 2.8% (141 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 3.7% (187 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 4.2% (211 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 5.7% (289 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 7.2% (365 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 8.3% (419 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 9.7% (491 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 10.4% (526 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 11.5% (582 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 13.6% (688 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 15.2% (773 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 16.3% (826 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 18.2% (925 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 19.4% (982 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 20.7% (1050 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 21.4% (1087 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 22.5% (1141 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 23.1% (1174 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 24.2% (1226 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 25.4% (1287 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 26.3% (1333 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 28.1% (1426 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 28.5% (1446 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 29.3% (1486 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 30.6% (1554 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 31.3% (1589 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 32.2% (1632 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 33.2% (1687 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 34.3% (1739 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 35.7% (1812 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 36.5% (1851 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 37.2% (1888 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 38.2% (1940 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 39.3% (1992 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 40.3% (2045 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 41.3% (2096 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 42.3% (2144 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 43.3% (2195 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 44.4% (2254 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 45.3% (2301 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 46.3% (2351 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 47.3% (2402 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 48.5% (2461 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 49.5% (2511 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 50.4% (2558 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 52.0% (2638 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 52.4% (2661 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 53.3% (2703 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 54.4% (2762 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 55.4% (2812 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 56.3% (2857 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 57.8% (2935 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 58.6% (2975 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 59.4% (3013 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 60.6% (3073 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 61.4% (3114 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 62.7% (3179 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 63.7% (3233 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 64.5% (3273 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 65.4% (3318 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 66.3% (3366 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 67.4% (3420 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 68.4% (3469 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 69.6% (3532 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 70.4% (3570 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 71.7% (3636 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 72.5% (3678 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 73.4% (3726 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 74.4% (3775 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 75.4% (3827 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 76.6% (3885 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 77.8% (3949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 78.4% (3978 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 79.7% (4042 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 80.5% (4087 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 82.0% (4163 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 82.8% (4199 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 83.4% (4233 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 84.8% (4305 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 85.5% (4338 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 86.4% (4386 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 87.5% (4441 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 88.5% (4488 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 89.5% (4542 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 90.5% (4592 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 91.6% (4650 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 92.5% (4692 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 93.5% (4743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 94.6% (4798 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 95.6% (4851 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 96.6% (4901 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 97.5% (4949 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 98.5% (4998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=770 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 77.0; 4795 / 5074 (P = 94.50%) round 43]               
[00:00:00] Finding cutoff p=761 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=761 2.0% (102 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 3.5% (176 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 4.2% (211 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 5.6% (286 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 7.3% (371 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 8.2% (414 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 9.9% (502 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 10.6% (539 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 11.7% (592 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 12.2% (617 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 13.6% (692 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 15.6% (791 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 16.3% (825 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 17.8% (903 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 19.0% (966 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 19.6% (997 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 20.3% (1032 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 21.1% (1073 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 23.0% (1169 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 23.6% (1198 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 24.4% (1240 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 25.6% (1300 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 26.5% (1343 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 27.2% (1381 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 28.6% (1452 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 29.7% (1509 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 30.9% (1570 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 31.5% (1599 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 32.6% (1653 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 33.2% (1685 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 34.7% (1763 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 36.3% (1843 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 37.3% (1892 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 38.5% (1952 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 39.4% (1999 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 40.3% (2043 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 41.3% (2096 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 42.4% (2151 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 43.3% (2195 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 44.6% (2261 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 45.3% (2300 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 46.2% (2346 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 47.4% (2407 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 48.6% (2465 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 49.5% (2511 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 50.6% (2568 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 52.1% (2646 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 52.7% (2672 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 53.7% (2727 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 54.7% (2773 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 55.4% (2810 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 56.6% (2870 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 57.4% (2914 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 58.8% (2983 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 59.5% (3017 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 60.6% (3076 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 61.3% (3111 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 62.3% (3163 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 63.5% (3222 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 64.4% (3266 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 65.4% (3316 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 67.0% (3398 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 67.6% (3430 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 68.4% (3471 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 69.4% (3522 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 70.7% (3587 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 71.6% (3633 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 72.6% (3683 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 73.7% (3738 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 74.4% (3777 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 75.6% (3835 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 76.6% (3889 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 77.4% (3929 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 78.5% (3983 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 79.5% (4033 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 80.6% (4089 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 81.4% (4132 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 82.8% (4203 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 83.6% (4241 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 84.5% (4290 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 85.5% (4340 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 86.6% (4395 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 87.6% (4444 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 88.7% (4501 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 89.6% (4544 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 90.6% (4596 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 91.5% (4642 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 92.5% (4694 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 93.5% (4745 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 94.5% (4795 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 95.6% (4850 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 96.5% (4897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 97.5% (4948 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 98.5% (4999 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=761 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 76.1; 4820 / 5074 (P = 94.99%) round 44]               
[00:00:00] Finding cutoff p=750 [36.4Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=750 1.9% (97 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 2.6% (131 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 3.4% (174 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 4.0% (205 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 5.6% (284 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 7.5% (381 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 8.0% (408 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 10.2% (519 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 11.8% (597 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 12.4% (630 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 14.2% (718 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 16.2% (822 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 17.3% (879 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 19.1% (968 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 19.6% (994 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 20.3% (1031 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 21.1% (1071 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 22.6% (1148 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 23.8% (1209 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 24.3% (1235 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 25.3% (1282 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 26.5% (1346 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 27.4% (1389 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 29.3% (1488 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 30.6% (1552 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 31.7% (1610 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 32.2% (1635 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 33.3% (1689 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 34.7% (1761 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 35.7% (1813 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 37.3% (1894 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 38.4% (1946 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 39.4% (1998 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 40.3% (2044 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 41.5% (2108 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 42.3% (2144 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 43.7% (2216 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 44.8% (2275 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 45.5% (2311 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 46.4% (2353 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 47.6% (2416 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 48.7% (2472 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 49.4% (2508 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 51.0% (2590 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 51.5% (2612 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 52.6% (2668 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 53.7% (2723 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 54.7% (2773 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 55.6% (2823 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 56.3% (2859 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 57.4% (2914 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 58.4% (2961 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 59.5% (3017 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 60.7% (3082 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 61.4% (3115 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 62.5% (3171 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 63.8% (3235 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 65.0% (3299 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 65.8% (3339 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 66.6% (3380 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 67.5% (3423 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 68.4% (3470 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 69.5% (3527 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 70.4% (3573 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 71.5% (3629 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 72.6% (3685 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 73.5% (3731 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 74.5% (3778 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 75.6% (3837 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 76.5% (3880 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 77.7% (3943 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 78.5% (3983 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 79.8% (4048 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 80.7% (4094 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 81.8% (4149 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 82.4% (4182 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 83.6% (4242 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 84.5% (4290 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 85.4% (4335 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 86.5% (4389 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 87.6% (4443 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 88.5% (4489 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 89.7% (4549 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 90.5% (4594 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 91.5% (4644 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 92.6% (4697 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 93.5% (4743 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 94.5% (4794 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 95.5% (4847 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 96.5% (4897 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 97.5% (4947 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 98.5% (4999 of 5074), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=750 99.5% (5049 of 5074), ETA 0:00:00                              
[00:00:00] Finished Preparing TNF Graph Building [pTNF = 76.10] [36.2Gb / 503.5Gb]                                            
[00:00:00] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=5074 maxEdges=200
[00:00:00] Building TNF Graph 32.5% (1648 of 5074), ETA 0:00:00     [36.3Gb / 503.5Gb]                           
[00:00:00] Building TNF Graph 65.0% (3296 of 5074), ETA 0:00:00     [36.3Gb / 503.5Gb]                           
[00:00:01] Building TNF Graph 97.4% (4944 of 5074), ETA 0:00:00     [36.3Gb / 503.5Gb]                           
[00:00:01] Finished Building TNF Graph (219051 edges) [36.3Gb / 503.5Gb]                                          
[00:00:01] Cleaned up after Building TNF Graph (219051 edges) [36.3Gb / 503.5Gb]                                          
[00:00:01] Cleaned up TNF matrix of large contigs [36.3Gb / 503.5Gb]                                             
[00:00:01] Applying coverage correlations to TNF graph with 219051 edges
[00:00:01] Allocated memory for graph edges [36.3Gb / 503.5Gb]
[00:00:01] ... calculating abundance dist 1.0% (2191 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 2.0% (4386 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 3.0% (6577 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 4.0% (8766 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 5.0% (10962 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 6.0% (13157 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 7.0% (15342 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 8.0% (17542 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 9.0% (19724 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 10.0% (21921 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 11.0% (24112 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 12.0% (26301 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 13.0% (28483 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 14.0% (30683 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 15.0% (32873 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 16.0% (35066 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 17.0% (37258 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 18.0% (39451 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 19.0% (41631 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 20.0% (43828 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 21.0% (46023 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 22.0% (48214 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 23.0% (50410 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 24.0% (52601 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 25.0% (54776 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 26.0% (56974 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 27.0% (59173 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 28.0% (61362 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 29.0% (63543 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 30.0% (65740 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 31.0% (67924 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 32.0% (70114 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 33.0% (72309 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 34.0% (74496 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 35.0% (76702 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 36.0% (78881 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 37.0% (81083 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 38.0% (83263 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 39.0% (85463 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 40.0% (87656 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 41.0% (89839 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 42.0% (92029 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 43.0% (94222 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 44.0% (96414 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 45.0% (98604 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 46.0% (100800 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 47.0% (102990 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 48.0% (105170 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 49.0% (107375 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 50.0% (109556 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 51.0% (111749 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 52.0% (113935 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 53.0% (116123 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 54.0% (118326 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 55.0% (120509 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 56.0% (122697 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 57.0% (124891 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 58.0% (127083 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 59.0% (129281 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 60.0% (131465 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 61.0% (133653 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 62.0% (135846 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 63.0% (138043 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 64.0% (140225 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 65.0% (142418 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 66.0% (144622 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 67.0% (146801 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 68.0% (148998 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 69.0% (151191 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 70.0% (153383 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 71.0% (155561 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 72.0% (157752 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 73.0% (159955 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 74.0% (162134 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 75.0% (164329 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 76.0% (166527 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 77.0% (168716 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 78.0% (170899 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 79.0% (173095 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 80.0% (175290 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 81.0% (177479 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 82.0% (179663 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 83.0% (181856 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 84.0% (184053 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 85.0% (186238 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 86.0% (188430 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 87.0% (190628 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 88.0% (192812 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 89.0% (195006 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 90.0% (197200 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 91.0% (199399 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 92.0% (201580 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 93.0% (203773 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 94.0% (205960 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 95.0% (208153 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 96.0% (210339 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 97.0% (212533 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 98.0% (214725 of 219051), ETA 0:00:00                              
[00:00:01] ... calculating abundance dist 99.0% (216910 of 219051), ETA 0:00:00                              
[00:00:01] Calculating geometric means [36.3Gb / 503.5Gb]
[00:00:01] Traversing graph with 5074 nodes and 219051 edges [36.3Gb / 503.5Gb]
[00:00:01] Building SCR Graph and Binning (483 vertices and 790 edges) [P = 9.50%; 36.3Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 1.0% (2191 of 219051), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (965 vertices and 2928 edges) [P = 19.00%; 36.3Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 2.0% (4382 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 3.0% (6573 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 4.0% (8764 of 219051), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (1447 vertices and 5306 edges) [P = 28.50%; 36.3Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 5.0% (10955 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 6.0% (13146 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 7.0% (15337 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 8.0% (17528 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 9.0% (19719 of 219051), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (1929 vertices and 7382 edges) [P = 38.00%; 36.3Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 10.0% (21910 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 11.0% (24101 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 12.0% (26292 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 13.0% (28483 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 14.0% (30674 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 15.0% (32865 of 219051), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (2411 vertices and 9053 edges) [P = 47.50%; 36.3Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 16.0% (35056 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 17.0% (37247 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 18.0% (39438 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 19.0% (41629 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 20.0% (43820 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 21.0% (46011 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 22.0% (48202 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 23.0% (50393 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 24.0% (52584 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 25.0% (54775 of 219051), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (2893 vertices and 10256 edges) [P = 57.00%; 36.3Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 26.0% (56966 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 27.0% (59157 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 28.0% (61348 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 29.0% (63539 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 30.0% (65730 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 31.0% (67921 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 32.0% (70112 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 33.0% (72303 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 34.0% (74494 of 219051), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (3376 vertices and 11318 edges) [P = 66.50%; 36.3Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 35.0% (76685 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 36.0% (78876 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 37.0% (81067 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 38.0% (83258 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 39.0% (85449 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 40.0% (87640 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 41.0% (89831 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 42.0% (92022 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 43.0% (94213 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 44.0% (96404 of 219051), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (3857 vertices and 12983 edges) [P = 76.00%; 36.3Gb / 503.5Gb]                           
[00:00:01] ... traversing graph 45.0% (98595 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 46.0% (100786 of 219051), ETA 0:00:00                               
[00:00:01] ... traversing graph 47.0% (102977 of 219051), ETA 0:00:00                               
[00:00:01] Building SCR Graph and Binning (3976 vertices and 13569 edges) [P = 85.50%; 36.3Gb / 503.5Gb]                           
[00:00:01] Finished Traversing graph [36.3Gb / 503.5Gb]                                       
[00:00:01] Dissolved 1745 small clusters leaving 1254 leftover contigs to be re-merged into larger clusters
[00:00:01] Rescuing singleton large contigs                                   
[00:00:01] There are 1 bins already
[00:00:01] Outputting bins
[00:00:01] Writing cluster stats to: 03bins/metabat2_fixed/1507994/1507994.bin.BinInfo.txt
[00:00:01] 79.26% (23243184 bases) of large (>=2000) and 0.00% (0 bases) of small (<2000) contigs were binned.
1 bins (23243184 bases in total) formed.
[00:00:01] Finished
MetaBAT2 generated 1 bins for 1507994
MetaBAT2 binning completed for 1507994
