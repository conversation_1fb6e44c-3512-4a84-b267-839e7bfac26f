Processing sample: 1507999
Command line: /usr/local/bin/spades.py	-s	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507999.anqdpht.fastq.gz	--sc	-o	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999	-t	16	-m	128	

System information:
  SPAdes version: 3.15.5
  Python version: 3.10.6
  OS: Linux-4.18.0-553.5.1.el8_10.x86_64-x86_64-with-glibc2.28

Output dir: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999
Mode: read error correction and assembling
Debug mode is turned OFF

Dataset parameters:
  Single-cell mode
  Reads:
    Library number: 1, library type: single
      left reads: not specified
      right reads: not specified
      interlaced reads: not specified
      single reads: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507999.anqdpht.fastq.gz']
      merged reads: not specified
Read error correction parameters:
  Iterations: 1
  PHRED offset will be auto-detected
  Corrected reads will be compressed
Assembly parameters:
  k: [21, 33, 55]
  Repeat resolution is enabled
  Mismatch careful mode is turned OFF
  MismatchCorrector will be SKIPPED
  Coverage cutoff is turned OFF
Other parameters:
  Dir for temp files: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/tmp
  Threads: 16
  Memory limit (in Gb): 128


======= SPAdes pipeline started. Log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/spades.log

/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507999.anqdpht.fastq.gz: max reads length: 150

Reads length: 150


===== Before start started. 


===== Read error correction started. 


===== Read error correction started. 


== Running: /usr/local/bin/spades-hammer /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/corrected/configs/config.info

  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  75)   Starting BayesHammer, built from N/A, git revision N/A
  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  76)   Loading config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/corrected/configs/config.info
  0:00:00.001     1M / 33M   INFO    General                 (main.cpp                  :  78)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 33M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.001     1M / 33M   INFO    General                 (main.cpp                  :  86)   Trying to determine PHRED offset
  0:00:00.004     1M / 33M   INFO    General                 (main.cpp                  :  92)   Determined value is 33
  0:00:00.004     1M / 33M   INFO    General                 (hammer_tools.cpp          :  38)   Hamming graph threshold tau=1, k=21, subkmer positions = [ 0 10 ]
  0:00:00.004     1M / 33M   INFO    General                 (main.cpp                  : 113)   Size of aux. kmer data 24 bytes
     === ITERATION 0 begins ===
  0:00:00.011     1M / 33M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 16 files using 16 threads. This might take a while.
  0:00:00.014     1M / 33M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.014     1M / 33M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66666 Gb
  0:00:00.014     1M / 33M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 4194304
  0:00:00.042  9217M / 9217M INFO   K-mer Splitting          (kmer_data.cpp             :  97)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507999.anqdpht.fastq.gz
  0:00:15.116  9217M / 14G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 3690741 reads
  0:00:30.897  9217M / 14G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 7320460 reads
  0:00:48.332  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 11020797 reads
  0:01:04.305  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 14624316 reads
  0:01:13.711  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 16390540 reads
  0:01:13.712  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 112)   Total 16390540 reads processed
  0:01:13.712     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:23.853     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 275815574 kmers in total.
  0:01:23.862     1M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:32.188   211M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 275815574 kmers, 199223296 bytes occupied (5.77845 bits per kmer).
  0:01:32.196   211M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 354)   Arranging kmers in hash map order
  0:01:41.991  4427M / 15G   INFO    General                 (main.cpp                  : 148)   Clustering Hamming graph.
  0:03:48.108  4427M / 15G   INFO    General                 (main.cpp                  : 155)   Extracting clusters:
  0:03:48.108  4427M / 15G   INFO    General                 (concurrent_dsu.cpp        :  18)   Connecting to root
  0:03:48.538  4427M / 15G   INFO    General                 (concurrent_dsu.cpp        :  34)   Calculating counts
  0:04:16.489  8779M / 15G   INFO    General                 (concurrent_dsu.cpp        :  63)   Writing down entries
  0:04:55.354  4427M / 15G   INFO    General                 (main.cpp                  : 167)   Clustering done. Total clusters: 139355088
  0:04:55.529  2319M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 371)   Collecting K-mer information, this takes a while.
  0:04:57.688  8635M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 377)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507999.anqdpht.fastq.gz
  0:06:25.913  8635M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 384)   Collection done, postprocessing.
  0:06:26.363  8635M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 397)   There are 275815574 kmers in total. Among them 159983108 (58.0037%) are singletons.
  0:06:26.363  8635M / 15G   INFO    General                 (main.cpp                  : 173)   Subclustering Hamming graph
  0:10:09.216  8636M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 650)   Subclustering done. Total 10071 non-read kmers were generated.
  0:10:09.216  8636M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 651)   Subclustering statistics:
  0:10:09.216  8636M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 652)     Total singleton hamming clusters: 91499352. Among them 42084354 (45.9942%) are good
  0:10:09.216  8636M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 653)     Total singleton subclusters: 6052168. Among them 6042678 (99.8432%) are good
  0:10:09.216  8636M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 654)     Total non-singleton subcluster centers: 50336905. Among them 36232163 (71.9793%) are good
  0:10:09.216  8636M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 655)     Average size of non-trivial subcluster: 3.66248 kmers
  0:10:09.216  8636M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 656)     Average number of sub-clusters per non-singleton cluster: 1.17831
  0:10:09.216  8636M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 657)     Total solid k-mers: 84359195
  0:10:09.216  8636M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 658)     Substitution probabilities: [4,4]((0.926489,0.0347028,0.032451,0.00635723),(0.0276164,0.932545,0.00986168,0.0299772),(0.0292888,0.00967679,0.931917,0.0291176),(0.00624026,0.0326018,0.0350684,0.92609))
  0:10:09.363  8636M / 15G   INFO    General                 (main.cpp                  : 178)   Finished clustering.
  0:10:09.363  8636M / 15G   INFO    General                 (main.cpp                  : 197)   Starting solid k-mers expansion in 16 threads.
  0:10:45.907  8636M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 0 produced 11303638 new k-mers.
  0:11:22.319  8636M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 1 produced 849460 new k-mers.
  0:11:58.754  8636M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 2 produced 54000 new k-mers.
  0:12:35.191  8636M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 3 produced 4451 new k-mers.
  0:13:11.498  8636M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 4 produced 494 new k-mers.
  0:13:47.880  8636M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 5 produced 23 new k-mers.
  0:14:24.361  8636M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 6 produced 0 new k-mers.
  0:14:24.361  8636M / 15G   INFO    General                 (main.cpp                  : 222)   Solid k-mers finalized
  0:14:24.361  8636M / 15G   INFO    General                 (hammer_tools.cpp          : 222)   Starting read correction in 16 threads.
  0:14:24.361  8636M / 15G   INFO    General                 (hammer_tools.cpp          : 266)   Correcting single reads: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507999.anqdpht.fastq.gz
  0:14:26.866  9397M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 0 of 1600000 reads.
  0:14:43.688  9519M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 0
  0:14:44.947  9519M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 0
  0:14:47.260  9591M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 1 of 1600000 reads.
  0:14:56.229  9591M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 1
  0:14:57.427  9591M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 1
  0:14:59.710  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 2 of 1600000 reads.
  0:15:08.888  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 2
  0:15:10.305  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 2
  0:15:12.611  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 3 of 1600000 reads.
  0:15:15.939  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 3
  0:15:17.120  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 3
  0:15:19.404  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 4 of 1600000 reads.
  0:15:22.906  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 4
  0:15:24.098  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 4
  0:15:26.415  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 5 of 1600000 reads.
  0:15:30.348  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 5
  0:15:31.595  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 5
  0:15:33.879  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 6 of 1600000 reads.
  0:15:37.112  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 6
  0:15:38.300  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 6
  0:15:40.587  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 7 of 1600000 reads.
  0:15:43.624  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 7
  0:15:44.810  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 7
  0:15:47.104  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 8 of 1600000 reads.
  0:15:55.820  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 8
  0:15:57.015  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 8
  0:15:59.342  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 9 of 1600000 reads.
  0:16:35.495  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 9
  0:16:36.726  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 9
  0:16:37.442  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 10 of 390540 reads.
  0:16:47.265  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 10
  0:16:47.576  9599M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 10
  0:16:51.329  8636M / 15G   INFO    General                 (hammer_tools.cpp          : 276)   Correction done. Changed 5661166 bases in 2462273 reads.
  0:16:51.370  8636M / 15G   INFO    General                 (hammer_tools.cpp          : 277)   Failed to correct 0 bases out of 2384094322.
  0:16:52.044     1M / 15G   INFO    General                 (main.cpp                  : 255)   Saving corrected dataset description to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/corrected/corrected.yaml
  0:16:52.063     1M / 15G   INFO    General                 (main.cpp                  : 262)   All done. Exiting.

===== Read error correction finished. 


===== corrected reads compression started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/compress_all.py --input_file /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/corrected/corrected.yaml --ext_python_modules_home /usr/local/share/spades --max_threads 16 --output_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/corrected --gzip_output

== Compressing corrected reads (with gzip)
== Files to compress: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/corrected/1507999.anqdpht.fastq.00.0_0.cor.fastq']
== Files compression is finished
== Dataset yaml file is updated

===== corrected reads compression finished. 


===== Read error correction finished. 


===== Assembling started. 


===== K21 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K21/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K21/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K21/configs/config.info
  0:00:00.012     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K21/configs/mda_mode.info
  0:00:00.013     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.013     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.013     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.013     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/dataset.info) with K=21
  0:00:00.013     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.013     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.013     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.014     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.033     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.033     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.043     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.048     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.055     1M / 35M   INFO    General                 (read_converter.cpp        :  72)   Converting reads to binary format for library #0 (takes a while)
  0:00:00.055     1M / 35M   INFO    General                 (read_converter.cpp        :  73)   Converting paired reads
  0:00:00.072    23M / 35M   INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:00.072     1M / 35M   INFO    General                 (read_converter.cpp        :  86)   Converting single reads
  0:00:00.313    33M / 44M   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:00:00.323    36M / 51M   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:00:00.342    40M / 60M   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:00:00.470    35M / 88M   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:00:00.633    40M / 109M  INFO    General                 (binary_converter.cpp      :  96)   262144 reads processed
  0:00:01.050    34M / 138M  INFO    General                 (binary_converter.cpp      :  96)   524288 reads processed
  0:00:01.763    38M / 216M  INFO    General                 (binary_converter.cpp      :  96)   1048576 reads processed
  0:00:03.324    45M / 238M  INFO    General                 (binary_converter.cpp      :  96)   2097152 reads processed
  0:00:06.499    45M / 314M  INFO    General                 (binary_converter.cpp      :  96)   4194304 reads processed
  0:00:12.822    44M / 331M  INFO    General                 (binary_converter.cpp      :  96)   8388608 reads processed
  0:00:25.143    31M / 331M  INFO    General                 (binary_converter.cpp      : 111)   16366011 reads written
  0:00:25.144    21M / 331M  INFO    General                 (read_converter.cpp        :  92)   Converting merged reads
  0:00:25.151    31M / 331M  INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:25.945     1M / 331M  INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:26.120     1M / 331M  INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:26.121     1M / 331M  INFO    General                 (construction.cpp          : 159)   Average read length 145.508
  0:00:26.121     1M / 331M  INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:26.121     1M / 331M  INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:26.135     1M / 331M  INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:26.136     1M / 331M  INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:26.136     1M / 331M  INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:00:32.412  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 10529125 reads
  0:00:37.020  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 19837066 reads
  0:00:43.888  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 29344562 reads
  0:00:48.927  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 31257565 reads
  0:00:51.464  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 32189524 reads
  0:00:53.458  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 32732022 reads
  0:00:53.463     1M / 7441M INFO    General                 (kmer_splitters.hpp        : 131)   Used 32732022 reads
  0:00:53.880     1M / 7441M INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:58.722     1M / 7441M INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 78511637 kmers in total.
  0:00:58.733     1M / 7441M INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:59.202     1M / 7441M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:59.207     1M / 7441M INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:59.228     1M / 7441M INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:59.228     1M / 7441M INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:59.228     1M / 7441M INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:01:08.409  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 78511637 kmers
  0:01:08.411  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 199)   Used 78511637 kmers.
  0:01:08.413     1M / 7441M INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:12.843     1M / 7441M INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 76424012 kmers in total.
  0:01:12.845     1M / 7441M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:14.423    59M / 7441M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 76424012 kmers, 55317432 bytes occupied (5.79058 bits per kmer).
  0:01:14.423    59M / 7441M INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:18.778   136M / 7441M INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:21.550   136M / 7441M INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:21.608   135M / 7441M INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:01:21.608   135M / 7441M INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:01:21.609   135M / 7441M INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:01:24.217   215M / 7441M INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 681618
  0:01:24.244   215M / 7441M INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 690248
  0:01:24.267   135M / 7441M INFO   Early tip clipping       (early_simplification.hpp  :  49)   10778164 22-mers were removed by early tip clipper
  0:01:24.267   135M / 7441M INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:24.770   135M / 7441M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:27.734   327M / 7441M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 5227082 sequences extracted
  0:01:29.096   327M / 7441M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:30.026   327M / 7441M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 3 loops collected
  0:01:30.167   327M / 7441M INFO    General                 (debruijn_graph_constructor: 487)   Total 10454170 edges to create
  0:01:30.168   733M / 7441M INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:34.019   893M / 7441M INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:34.084   893M / 7441M INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:34.113   925M / 7441M INFO    General                 (debruijn_graph_constructor: 503)   Total 3139460 vertices to create
  0:01:34.113  1218M / 7441M INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:36.844   949M / 7441M INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:36.845   949M / 7441M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:37.458  1007M / 7441M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 78511637 kmers, 56825288 bytes occupied (5.79025 bits per kmer).
  0:01:37.541  1307M / 7441M INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:01:56.051  1307M / 7441M INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:01:58.368  1307M / 7441M INFO    General                 (coverage_filling.hpp      :  82)   Processed 10452761 edges
  0:02:00.642   894M / 7441M INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:02:01.910   895M / 7441M INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 13
  0:02:01.910   895M / 7441M INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 29.8989
  0:02:01.910   895M / 7441M INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 364
  0:02:01.925   894M / 7441M INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 29.8989
  0:02:01.925   894M / 7441M INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:02:01.925   894M / 7441M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:02:01.929   894M / 7441M INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:02:01.930   894M / 7441M INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:02:01.930   894M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:02:01.989   894M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 0 times
  0:02:01.990   894M / 7441M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:02:01.990   894M / 7441M INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:02:01.990   894M / 7441M INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:01.990   894M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:02:01.990   894M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:02.154   894M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 18702 times
  0:02:02.155   894M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:26.523   973M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 230090 times
  0:02:26.528   973M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:33.734  1024M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1210856 times
  0:02:33.749  1024M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:02:33.749  1024M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:34.221   988M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 10846 times
  0:02:34.223   988M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:39.830   936M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 41397 times
  0:02:39.837   936M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:41.289   933M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 242996 times
  0:02:41.301   933M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:02:41.301   933M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.410   915M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 197 times
  0:02:41.411   915M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:43.177   903M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 12334 times
  0:02:43.181   903M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:43.611   895M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 72131 times
  0:02:43.622   895M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:02:43.622   895M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:43.666   893M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 24 times
  0:02:43.666   893M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:44.574   888M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2902 times
  0:02:44.575   888M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:44.813   889M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 38385 times
  0:02:44.819   889M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:02:44.819   889M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:44.839   888M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3 times
  0:02:44.839   888M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:45.571   886M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1148 times
  0:02:45.571   886M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:45.738   887M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 25605 times
  0:02:45.742   887M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:02:45.742   887M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:45.758   886M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:45.758   886M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:46.913   885M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 732 times
  0:02:46.913   885M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:47.042   879M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 19437 times
  0:02:47.045   879M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:02:47.045   879M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:47.055   878M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:47.055   878M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:48.091   877M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 500 times
  0:02:48.092   877M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:48.180   877M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 15431 times
  0:02:48.182   877M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:02:48.182   877M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:48.188   877M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3 times
  0:02:48.188   877M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:49.098   876M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 379 times
  0:02:49.099   876M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:49.175   877M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 12056 times
  0:02:49.176   877M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:02:49.176   877M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:49.181   876M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:49.181   876M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:50.107   875M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 315 times
  0:02:50.108   875M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:50.173   876M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 10164 times
  0:02:50.175   876M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:02:50.175   876M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:50.179   876M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:02:50.179   876M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:50.951   875M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 211 times
  0:02:50.951   875M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:51.002   875M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 8137 times
  0:02:51.003   875M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:02:51.003   875M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:51.019   875M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 141 times
  0:02:51.019   875M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:52.345   877M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 822 times
  0:02:52.346   877M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:52.360   809M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 5 times
  0:02:52.360   809M / 7441M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:02:52.360   809M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:52.363   809M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:52.363   809M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:52.363   809M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:52.363   809M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:52.363   809M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:02:52.385   796M / 7441M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:02:52.385   796M / 7441M INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:02:52.385   796M / 7441M INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:02:52.385   796M / 7441M INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:02:52.386   796M / 7441M INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:52.386   796M / 7441M INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:52.386   796M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:52.588   796M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 2941 times
  0:02:52.588   796M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:53.023   796M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 833 times
  0:02:53.024   796M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:53.037   796M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:53.037   796M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:53.050   796M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 17 times
  0:02:53.050   796M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:53.971   800M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 71 times
  0:02:53.971   800M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:55.076   801M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1279 times
  0:02:55.077   801M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:55.213   801M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 33 times
  0:02:55.213   801M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:55.607   797M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 9 times
  0:02:55.607   797M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:55.620   797M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:55.620   797M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:55.633   797M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:55.633   797M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:56.536   800M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2 times
  0:02:56.536   800M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:57.493   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1 times
  0:02:57.493   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:57.676   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:02:57.676   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:58.067   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:02:58.068   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:58.068   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:58.068   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:58.068   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:58.068   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:58.068   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:58.069   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:58.069   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:02:58.069   804M / 7441M INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:02:58.129   804M / 7441M INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:02:58.276   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:02:58.290   804M / 7441M INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 1530 times
  0:02:58.313   796M / 7441M INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:02:58.313   796M / 7441M INFO    General                 (simplification.cpp        : 496)     Average coverage = 48.7791
  0:02:58.332   796M / 7441M INFO    General                 (simplification.cpp        : 497)     Total length = 40317617
  0:02:58.369   796M / 7441M INFO    General                 (simplification.cpp        : 498)     Median edge length: 537
  0:02:58.369   796M / 7441M INFO    General                 (simplification.cpp        : 499)     Edges: 677534
  0:02:58.369   796M / 7441M INFO    General                 (simplification.cpp        : 500)     Vertices: 403036
  0:02:58.369   796M / 7441M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:02:58.378   796M / 7441M INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K21/simplified_contigs
  0:02:58.392   799M / 7441M INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:02:58.395   799M / 7441M INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:02:58.411   799M / 7441M INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:02:58.421   799M / 7441M INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:02:58.450   799M / 7441M INFO    General                 (binary_converter.cpp      :  96)   262144 reads processed
  0:02:58.479   799M / 7441M INFO    General                 (binary_converter.cpp      : 111)   338882 reads written
  0:02:58.561   796M / 7441M INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:02:58.758     1M / 7441M INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 2 minutes 58 seconds

===== K21 finished. 


===== K33 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K33/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K33/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K33/configs/config.info
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K33/configs/mda_mode.info
  0:00:00.000     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/dataset.info) with K=33
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.007     1M / 35M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.007     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.007     1M / 35M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K21/simplified_contigs
  0:00:00.158     1M / 35M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.159     1M / 35M   INFO    General                 (construction.cpp          : 159)   Average read length 145.508
  0:00:00.159     1M / 35M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.159     1M / 35M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.174     1M / 35M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.175     1M / 35M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.175     1M / 35M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:06.550  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 6211533 reads
  0:00:12.137  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 11710987 reads
  0:00:17.223  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 16998219 reads
  0:00:22.050  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 22483699 reads
  0:00:28.431  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 28577913 reads
  0:00:34.216  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 30999425 reads
  0:00:39.870  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 31973572 reads
  0:00:42.765  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 32453470 reads
  0:00:44.868  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 32928257 reads
  0:00:46.733  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 33324814 reads
  0:00:48.983     1M / 7095M INFO    General                 (kmer_splitters.hpp        : 131)   Used 33409786 reads
  0:00:49.401     1M / 7095M INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:54.648     1M / 7095M INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 100372366 kmers in total.
  0:00:54.649     1M / 7095M INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:55.141     1M / 7095M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:55.141     1M / 7095M INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:55.146     1M / 7095M INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:55.147     1M / 7095M INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:55.147     1M / 7095M INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:01:05.294  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 60093280 kmers
  0:01:12.340  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 100372439 kmers
  0:01:12.340  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 199)   Used 100372439 kmers.
  0:01:12.342     2M / 7095M INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:19.626     2M / 7095M INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 98833408 kmers in total.
  0:01:19.629     2M / 7095M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:21.844    76M / 7095M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 98833408 kmers, 71501616 bytes occupied (5.78765 bits per kmer).
  0:01:21.844    76M / 7095M INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:27.378   172M / 7095M INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:31.207   172M / 7095M INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:31.335   172M / 7095M INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:01:31.335   172M / 7095M INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:01:31.335   172M / 7095M INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:01:34.905   252M / 7095M INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 1001045
  0:01:34.951   252M / 7095M INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 1015478
  0:01:35.004   172M / 7095M INFO   Early tip clipping       (early_simplification.hpp  :  49)   19464083 34-mers were removed by early tip clipper
  0:01:35.004   172M / 7095M INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:35.220   172M / 7095M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:39.235   375M / 7095M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 4270971 sequences extracted
  0:01:41.063   375M / 7095M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:41.867   376M / 7095M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 10 loops collected
  0:01:42.011   375M / 7095M INFO    General                 (debruijn_graph_constructor: 487)   Total 8541962 edges to create
  0:01:42.012   705M / 7095M INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:45.142   837M / 7095M INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:45.196   837M / 7095M INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:45.218   869M / 7095M INFO    General                 (debruijn_graph_constructor: 503)   Total 2732023 vertices to create
  0:01:45.218  1122M / 7095M INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:47.587   891M / 7095M INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:47.587   891M / 7095M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:48.446   965M / 7095M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 100372366 kmers, 72612872 bytes occupied (5.78748 bits per kmer).
  0:01:48.480  1350M / 7095M INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:02:05.756  1350M / 7095M INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:02:08.697  1350M / 7095M INFO    General                 (coverage_filling.hpp      :  82)   Processed 8541127 edges
  0:02:10.560   784M / 7095M INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:02:11.469   785M / 7095M INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 11
  0:02:11.469   785M / 7095M INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 22.4688
  0:02:11.470   785M / 7095M INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 247
  0:02:11.484   784M / 7095M INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 22.4688
  0:02:11.484   784M / 7095M INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:02:11.484   784M / 7095M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:02:11.484   784M / 7095M INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:02:11.484   784M / 7095M INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:02:11.484   784M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:02:11.539   784M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 63 times
  0:02:11.539   784M / 7095M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:02:11.539   784M / 7095M INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:02:11.539   784M / 7095M INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:11.540   784M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:02:11.540   784M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:11.776   782M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 47176 times
  0:02:11.777   782M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:23.688   795M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 207638 times
  0:02:23.692   795M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:30.100   843M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 950655 times
  0:02:30.119   843M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:02:30.119   843M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:30.486   806M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 16687 times
  0:02:30.489   806M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:33.865   756M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 38637 times
  0:02:33.874   756M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:35.096   744M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 166019 times
  0:02:35.109   744M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:02:35.109   744M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:35.202   733M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 411 times
  0:02:35.202   733M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:36.345   725M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 11352 times
  0:02:36.348   725M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:36.721   725M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 51262 times
  0:02:36.729   725M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:02:36.729   725M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:36.765   722M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 45 times
  0:02:36.765   722M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:37.287   720M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 3497 times
  0:02:37.288   720M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:37.467   715M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 22950 times
  0:02:37.472   715M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:02:37.472   715M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:37.488   714M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 12 times
  0:02:37.488   714M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:38.292   713M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1362 times
  0:02:38.292   713M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:38.401   713M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 14152 times
  0:02:38.404   713M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:02:38.404   713M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:38.413   712M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 6 times
  0:02:38.413   712M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:39.179   712M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 738 times
  0:02:39.180   712M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:39.253   712M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 9036 times
  0:02:39.255   712M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:02:39.255   712M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:39.261   712M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5 times
  0:02:39.261   712M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:39.843   708M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 445 times
  0:02:39.844   708M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:39.900   708M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 6949 times
  0:02:39.901   708M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:02:39.901   708M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:39.906   708M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3 times
  0:02:39.906   708M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.326   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 331 times
  0:02:40.326   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:40.369   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 5300 times
  0:02:40.370   707M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:02:40.370   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:40.374   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:40.374   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.694   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 259 times
  0:02:40.694   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:40.732   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 4558 times
  0:02:40.733   707M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:02:40.733   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:40.736   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:40.736   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.010   706M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 210 times
  0:02:41.010   706M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:41.042   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3588 times
  0:02:41.043   707M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:02:41.043   707M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.060   706M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 130 times
  0:02:41.060   706M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.841   705M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 895 times
  0:02:41.841   705M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:41.851   669M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 15 times
  0:02:41.852   669M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:02:41.852   669M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.854   669M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:41.854   669M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.858   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2 times
  0:02:41.858   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:41.858   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:02:41.858   665M / 7095M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 13
  0:02:41.858   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.858   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:41.858   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.858   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:41.858   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:41.858   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:02:41.876   664M / 7095M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:02:41.876   664M / 7095M INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:02:41.876   664M / 7095M INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:02:41.876   664M / 7095M INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:02:41.876   664M / 7095M INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:41.876   664M / 7095M INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:41.876   664M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:42.010   664M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 2185 times
  0:02:42.011   664M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:42.349   663M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 1344 times
  0:02:42.350   663M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:42.359   663M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:42.359   663M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:42.367   663M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 22 times
  0:02:42.367   663M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:42.686   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 73 times
  0:02:42.686   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:43.037   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 962 times
  0:02:43.038   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:43.116   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 22 times
  0:02:43.116   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:43.398   664M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 13 times
  0:02:43.399   664M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:43.407   664M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:43.407   664M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:43.416   664M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:43.416   664M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:43.710   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2 times
  0:02:43.711   665M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:44.016   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1 times
  0:02:44.017   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:44.131   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1 times
  0:02:44.131   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:44.413   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:02:44.413   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:44.413   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:44.413   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:44.413   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:44.413   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:44.414   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:44.414   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:44.414   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:02:44.414   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:44.532   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:02:44.532   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:44.800   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:02:44.800   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:44.801   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:44.801   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:44.801   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:44.801   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:44.801   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:44.801   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:44.801   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:02:44.801   667M / 7095M INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:02:44.891   667M / 7095M INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:02:44.963   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:02:44.993   667M / 7095M INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 20200 times
  0:02:45.006   662M / 7095M INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:02:45.006   662M / 7095M INFO    General                 (simplification.cpp        : 496)     Average coverage = 41.3702
  0:02:45.017   662M / 7095M INFO    General                 (simplification.cpp        : 497)     Total length = 42413573
  0:02:45.037   662M / 7095M INFO    General                 (simplification.cpp        : 498)     Median edge length: 1622
  0:02:45.037   662M / 7095M INFO    General                 (simplification.cpp        : 499)     Edges: 316742
  0:02:45.037   662M / 7095M INFO    General                 (simplification.cpp        : 500)     Vertices: 211372
  0:02:45.037   662M / 7095M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:02:45.048   662M / 7095M INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K33/simplified_contigs
  0:02:45.072   665M / 7095M INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:02:45.075   665M / 7095M INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:02:45.095   665M / 7095M INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:02:45.106   665M / 7095M INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:02:45.128   665M / 7095M INFO    General                 (binary_converter.cpp      : 111)   158395 reads written
  0:02:45.215   662M / 7095M INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:02:45.310     1M / 7095M INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 2 minutes 45 seconds

===== K33 finished. 


===== K55 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/configs/config.info
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/configs/mda_mode.info
  0:00:00.000     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/dataset.info) with K=55
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 242)   Will need read mapping, kmer mapper will be attached
  0:00:00.001     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.009     1M / 35M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.010     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.010     1M / 35M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K33/simplified_contigs
  0:00:00.174     1M / 35M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.174     1M / 35M   INFO    General                 (construction.cpp          : 159)   Average read length 145.508
  0:00:00.174     1M / 35M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.174     1M / 35M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.194     1M / 35M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.194     1M / 35M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.194     1M / 35M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:08.577  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 8026016 reads
  0:00:15.873  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 15041393 reads
  0:00:23.044  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 22113803 reads
  0:00:30.178  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 29664355 reads
  0:00:34.926  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 31698750 reads
  0:00:38.522  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 32374530 reads
  0:00:41.164  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 32947714 reads
  0:00:43.667  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 33048812 reads
  0:00:43.670     1M / 7272M INFO    General                 (kmer_splitters.hpp        : 131)   Used 33048812 reads
  0:00:44.089     1M / 7272M INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:51.104     1M / 7272M INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 122467392 kmers in total.
  0:00:51.115     1M / 7272M INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:51.884     1M / 7272M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:51.886     1M / 7272M INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:51.899     1M / 7272M INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:51.900     1M / 7272M INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:51.900     1M / 7272M INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:58.739  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 12014609 kmers
  0:01:02.989  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 24264727 kmers
  0:01:06.802  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 36515348 kmers
  0:01:10.655  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 48785846 kmers
  0:01:14.512  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 61039288 kmers
  0:01:18.954  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 73279638 kmers
  0:01:24.755  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 85516851 kmers
  0:01:31.494  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 97754932 kmers
  0:01:36.232  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 109999323 kmers
  0:01:41.666  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 122249440 kmers
  0:01:44.302  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 194)   Processed 122468272 kmers
  0:01:44.302  9602M / 9602M INFO    General                 (kmer_splitters.hpp        : 199)   Used 122468272 kmers.
  0:01:44.303     2M / 7272M INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:51.398     2M / 7272M INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 121862127 kmers in total.
  0:01:51.409     2M / 7272M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:54.557    91M / 7272M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 121862127 kmers, 88135816 bytes occupied (5.78594 bits per kmer).
  0:01:54.557    91M / 7272M INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:02:00.665   211M / 7272M INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:02:05.423   211M / 7272M INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:02:05.590   211M / 7272M INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:02:05.753   211M / 7272M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:02:11.643   462M / 7272M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 4981233 sequences extracted
  0:02:14.320   462M / 7272M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:02:15.242   462M / 7272M INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 0 loops collected
  0:02:15.451   462M / 7272M INFO    General                 (debruijn_graph_constructor: 487)   Total 9962466 edges to create
  0:02:15.452   847M / 7272M INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:02:19.053  1004M / 7272M INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:02:19.117  1004M / 7272M INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:02:19.159  1068M / 7272M INFO    General                 (debruijn_graph_constructor: 503)   Total 4375968 vertices to create
  0:02:19.160  1473M / 7272M INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:02:22.933  1174M / 7272M INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:02:22.933  1174M / 7272M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:02:23.926  1263M / 7272M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 122467392 kmers, 88572512 bytes occupied (5.78587 bits per kmer).
  0:02:23.968  1731M / 7272M INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:02:39.163  1731M / 7272M INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:02:43.810  1731M / 7272M INFO    General                 (coverage_filling.hpp      :  82)   Processed 9962133 edges
  0:02:45.480  1007M / 7272M INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:02:46.174  1008M / 7272M INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 8
  0:02:46.175  1008M / 7272M INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 12.0971
  0:02:46.175  1008M / 7272M INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 203
  0:02:46.182  1007M / 7272M INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 12.0971
  0:02:46.182  1007M / 7272M INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:02:46.182  1007M / 7272M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: early_gapcloser)
  0:02:46.182  1007M / 7272M INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:02:46.182  1007M / 7272M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:02:46.182  1007M / 7272M INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:02:46.183  1007M / 7272M INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:02:46.183  1007M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:02:46.257  1007M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 17 times
  0:02:46.257  1007M / 7272M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:02:46.257  1007M / 7272M INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:02:46.257  1007M / 7272M INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:46.257  1007M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:02:46.258  1007M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:51.726   922M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1467335 times
  0:02:51.739   922M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:11.255  1746M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 134556 times
  0:03:11.257  1746M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:13.290  1772M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 276386 times
  0:03:13.297  1772M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:03:13.297  1772M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:13.570  1753M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 23848 times
  0:03:13.574  1753M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:18.240  1851M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 18565 times
  0:03:18.241  1851M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:19.180  1849M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 128030 times
  0:03:19.183  1849M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:03:19.183  1849M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:19.284  1837M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3393 times
  0:03:19.286  1837M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:21.897  1906M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 10696 times
  0:03:21.898  1906M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:22.316  1903M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 51256 times
  0:03:22.320  1903M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:03:22.321  1903M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:22.363  1897M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 443 times
  0:03:22.364  1897M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:23.760  1927M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 4801 times
  0:03:23.762  1927M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:23.988  1928M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 25611 times
  0:03:23.992  1928M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:03:23.992  1928M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:24.012  1927M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 121 times
  0:03:24.013  1927M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:24.835  1939M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2358 times
  0:03:24.838  1939M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:24.990  1940M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 15491 times
  0:03:24.992  1940M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:03:24.992  1940M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:25.004  1939M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 70 times
  0:03:25.004  1939M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:25.618  1947M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1281 times
  0:03:25.620  1947M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:25.690  1947M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 7530 times
  0:03:25.692  1947M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:03:25.692  1947M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:25.698  1947M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 24 times
  0:03:25.699  1947M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:26.052  1948M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 525 times
  0:03:26.054  1948M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:26.100  1948M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 5180 times
  0:03:26.101  1948M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:03:26.102  1948M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:26.106  1948M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 13 times
  0:03:26.106  1948M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:26.368  1950M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 352 times
  0:03:26.369  1950M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:26.401  1950M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3678 times
  0:03:26.403  1950M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:03:26.403  1950M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:26.406  1950M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 11 times
  0:03:26.407  1950M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:26.589  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 240 times
  0:03:26.590  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:26.615  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2811 times
  0:03:26.616  1951M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:03:26.617  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:26.619  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 9 times
  0:03:26.619  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:26.779  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 189 times
  0:03:26.780  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:26.799  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2330 times
  0:03:26.800  1951M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:03:26.801  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:26.827  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 803 times
  0:03:26.828  1951M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:27.131  1955M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 404 times
  0:03:27.133  1955M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:27.149  1938M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 7 times
  0:03:27.150  1938M / 7272M INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:03:27.150  1938M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:27.152  1938M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:27.152  1938M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:27.153  1938M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:27.154  1938M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:27.154  1938M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:03:27.191  1935M / 7272M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: late_gapcloser)
  0:03:27.192  1935M / 7272M INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:03:27.193  1935M / 7272M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:03:27.194  1935M / 7272M INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:03:27.195  1935M / 7272M INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:03:27.195  1935M / 7272M INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:03:27.196  1935M / 7272M INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:03:27.196  1935M / 7272M INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:03:27.197  1935M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:27.197  1935M / 7272M INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:27.198  1935M / 7272M INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:27.538  1934M / 7272M INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:27.832  1934M / 7272M INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:35.270  1934M / 7272M INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:35.643  1934M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:35.645  1934M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:35.663  1934M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 17 times
  0:03:35.664  1934M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:35.746  1934M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1181 times
  0:03:35.747  1934M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:37.494  1988M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 2867 times
  0:03:37.495  1988M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:37.513  1988M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 8 times
  0:03:37.514  1988M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:37.531  1988M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 63 times
  0:03:37.532  1988M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:37.641  1990M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 45 times
  0:03:37.642  1990M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:37.955  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 795 times
  0:03:37.957  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:37.957  1998M / 7272M INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:37.958  1998M / 7272M INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:38.247  1998M / 7272M INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:38.509  1998M / 7272M INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:44.020  1998M / 7272M INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:44.383  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:44.385  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:44.402  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:03:44.403  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:44.455  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 15 times
  0:03:44.456  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:45.048  1999M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 5 times
  0:03:45.049  1999M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:45.066  1999M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:45.067  1999M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:45.084  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:45.084  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:45.200  1999M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2 times
  0:03:45.201  1999M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:45.319  1999M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 3 times
  0:03:45.321  1999M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:45.322  1999M / 7272M INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:45.322  1999M / 7272M INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:45.605  1998M / 7272M INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:45.867  1998M / 7272M INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:51.037  1998M / 7272M INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:51.404  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:51.406  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:51.423  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:03:51.424  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:51.492  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1 times
  0:03:51.493  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:52.089  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:03:52.091  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:52.092  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:52.093  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:52.093  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:52.094  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:52.095  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1 times
  0:03:52.095  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:52.097  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1 times
  0:03:52.097  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:52.098  1998M / 7272M INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:52.099  1998M / 7272M INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:52.378  1998M / 7272M INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:52.641  1998M / 7272M INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:57.778  1998M / 7272M INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:58.147  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:58.149  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:58.167  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:03:58.168  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:58.237  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:03:58.238  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:58.841  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:03:58.843  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:58.844  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:58.844  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:58.845  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:58.845  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:58.846  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:58.847  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:58.847  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:03:58.848  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:58.848  1998M / 7272M INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:58.849  1998M / 7272M INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:59.126  1998M / 7272M INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:59.392  1998M / 7272M INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:04:04.528  1998M / 7272M INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:04:04.897  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 0 times
  0:04:04.899  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:04:04.916  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:04:04.917  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:04:04.988  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:04:04.989  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:04:05.579  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:04:05.581  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:04:05.582  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:04:05.582  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:04:05.583  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:04:05.583  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:04:05.584  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:04:05.585  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:04:05.585  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:04:05.586  1998M / 7272M INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:04:05.666  1998M / 7272M INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:04:05.784  1998M / 7272M INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:04:05.938  1994M / 7272M INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 122608 times
  0:04:05.952  1992M / 7272M INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:04:05.953  1992M / 7272M INFO    General                 (simplification.cpp        : 496)     Average coverage = 31.329
  0:04:05.965  1992M / 7272M INFO    General                 (simplification.cpp        : 497)     Total length = 43805547
  0:04:05.981  1992M / 7272M INFO    General                 (simplification.cpp        : 498)     Median edge length: 2668
  0:04:05.982  1992M / 7272M INFO    General                 (simplification.cpp        : 499)     Edges: 193443
  0:04:05.982  1992M / 7272M INFO    General                 (simplification.cpp        : 500)     Vertices: 141784
  0:04:05.983  1992M / 7272M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Mismatch Correction (id: mismatch_correction)
  0:04:05.983  1992M / 7272M INFO    General                 (graph_pack.cpp            :  67)   Index refill
  0:04:05.984  1992M / 7272M INFO    General                 (edge_index.hpp            : 156)   Using small index (max_id = 10012281)
  0:04:06.079  1992M / 7272M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:04:07.035  2023M / 7272M INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 43791804 kmers, 31629968 bytes occupied (5.77824 bits per kmer).
  0:04:07.155  2359M / 7272M INFO    General                 (edge_index_builders.hpp   : 252)   Collecting edge information from graph, this takes a while.
  0:04:07.787  2359M / 7272M INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 24050468 kmers to process
  0:04:11.700  2359M / 7272M INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:04:11.703  2359M / 7272M INFO    General                 (mismatch_correction.cpp   : 400)   Collect potential mismatches
  0:04:14.277  2378M / 7272M INFO    General                 (mismatch_correction.cpp   : 192)   Total 94939 edges (out of 193443) with 2718012 potential mismatch positions (28.629 positions per edge)
  0:04:14.284  2378M / 7272M INFO    General                 (mismatch_correction.cpp   : 402)   Potential mismatches collected
  0:04:15.951  2380M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:04:15.958  2382M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:04:15.961  2384M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:04:15.969  2386M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:04:15.981  2391M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:04:15.989  2393M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:04:16.141  2394M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:04:17.567  2398M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:04:19.629  2407M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8400000 reads
  0:04:24.805  2426M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 16800000 reads
  0:04:51.563  2983M / 7272M INFO    General                 (sequence_mapper_notifier.h:  95)   Total 32732022 reads processed
  0:04:54.359  2568M / 7272M INFO    General                 (mismatch_correction.cpp   : 395)   All edges processed
  0:04:54.384  2363M / 7272M INFO    General                 (mismatch_correction.cpp   : 450)   Corrected 886 nucleotides
  0:04:54.389  2363M / 7272M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:04:54.390  2363M / 7272M INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/before_rr.fasta
  0:04:54.870  2363M / 7272M INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/assembly_graph_after_simplification.gfa
  0:04:55.371  2363M / 7272M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Paired Information Counting (id: late_pair_info_count)
  0:04:55.574  2399M / 7272M INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 24087192 kmers to process
  0:04:59.580  2399M / 7272M INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:04:59.599  2399M / 7272M INFO    General                 (pair_info_count.cpp       : 339)   Min edge length for estimation: 2668
  0:04:59.601  2399M / 7272M INFO    General                 (pair_info_count.cpp       : 411)   Mapping single reads of library #0
  0:04:59.602  2399M / 7272M INFO    General                 (pair_info_count.cpp       :  48)   Selecting usual mapper
  0:05:01.457  2401M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:05:01.484  2402M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:05:01.489  2403M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:05:01.502  2403M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:05:01.610  2404M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:05:01.662  2405M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:05:01.800  2406M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:05:03.583  2409M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:05:06.944  2411M / 7272M INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8400000 reads
  0:05:26.036  2580M / 7272M INFO    General                 (sequence_mapper_notifier.h:  95)   Total 16366011 reads processed
  0:05:26.039  2579M / 7272M INFO    General                 (pair_info_count.cpp       : 413)   Total paths obtained from single reads: 755757
  0:05:26.040  2579M / 7272M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Distance Estimation (id: distance_estimation)
  0:05:26.041  2579M / 7272M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Repeat Resolving (id: repeat_resolving)
  0:05:26.041  2579M / 7272M INFO    General                 (repeat_resolving.cpp      :  87)   Using Path-Extend repeat resolving
  0:05:26.044  2579M / 7272M INFO    General                 (launcher.cpp              : 600)   ExSPAnder repeat resolving tool started
  0:05:26.283  2966M / 7272M INFO    General                 (launcher.cpp              : 418)   Creating main extenders, unique edge length = 2000
  0:05:26.285  2976M / 7272M INFO    General                 (launcher.cpp              : 342)   filling path container
  0:05:30.636  6922M / 7433M INFO    General                 (extenders_logic.cpp       :  47)   resolvable_repeat_length_bound set to 10000
  0:05:39.772  6936M / 7437M INFO    General                 (extenders_logic.cpp       : 543)   Using 0 paired-end libraries
  0:05:39.776  6936M / 7437M INFO    General                 (extenders_logic.cpp       : 544)   Using 0 paired-end scaffolding libraries
  0:05:39.779  6936M / 7437M INFO    General                 (extenders_logic.cpp       : 545)   Using 1 single read library
  0:05:39.781  6936M / 7437M INFO    General                 (launcher.cpp              : 447)   Total number of extenders is 1
  0:05:39.783  6936M / 7437M INFO    General                 (path_extenders.cpp        :  36)   Processed 0 paths from 95929 (0%)
  0:05:41.527  6936M / 7440M INFO    General                 (path_extenders.cpp        :  34)   Processed 128 paths from 95929 (0%)
  0:05:42.567  6937M / 7441M INFO    General                 (path_extenders.cpp        :  34)   Processed 256 paths from 95929 (0%)
  0:05:45.127  6938M / 7442M INFO    General                 (path_extenders.cpp        :  34)   Processed 512 paths from 95929 (0%)
  0:05:47.111  6941M / 7445M INFO    General                 (path_extenders.cpp        :  34)   Processed 1024 paths from 95929 (1%)
  0:05:48.656  6945M / 7449M INFO    General                 (path_extenders.cpp        :  34)   Processed 2048 paths from 95929 (2%)
  0:05:51.873  6954M / 7458M INFO    General                 (path_extenders.cpp        :  34)   Processed 4096 paths from 95929 (4%)
  0:05:56.662  6971M / 7475M INFO    General                 (path_extenders.cpp        :  34)   Processed 8192 paths from 95929 (8%)
  0:05:58.453  6977M / 7481M INFO    General                 (path_extenders.cpp        :  36)   Processed 9593 paths from 95929 (10%)
  0:06:03.363  7005M / 7509M INFO    General                 (path_extenders.cpp        :  34)   Processed 16384 paths from 95929 (17%)
  0:06:04.761  7017M / 7521M INFO    General                 (path_extenders.cpp        :  36)   Processed 19186 paths from 95929 (20%)
  0:06:09.476  7063M / 7567M INFO    General                 (path_extenders.cpp        :  36)   Processed 28779 paths from 95929 (30%)
  0:06:12.096  7083M / 7587M INFO    General                 (path_extenders.cpp        :  34)   Processed 32768 paths from 95929 (34%)
  0:06:17.051  7110M / 7615M INFO    General                 (path_extenders.cpp        :  36)   Processed 38372 paths from 95929 (40%)
  0:06:45.301  7159M / 7663M INFO    General                 (path_extenders.cpp        :  36)   Processed 47965 paths from 95929 (50%)
  0:07:21.540  7201M / 7706M INFO    General                 (path_extenders.cpp        :  36)   Processed 57558 paths from 95929 (60%)
  0:08:04.153  7229M / 7734M INFO    General                 (path_extenders.cpp        :  34)   Processed 65536 paths from 95929 (68%)
  0:08:11.136  7233M / 7738M INFO    General                 (path_extenders.cpp        :  36)   Processed 67151 paths from 95929 (70%)
  0:08:34.638  7249M / 7753M INFO    General                 (path_extenders.cpp        :  36)   Processed 76744 paths from 95929 (80%)
  0:08:43.045  7262M / 7767M INFO    General                 (path_extenders.cpp        :  36)   Processed 86337 paths from 95929 (90%)
  0:08:46.126  7272M / 7777M INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:08:46.130  7272M / 7777M INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:08:46.314  7220M / 7777M INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:08:46.318  7220M / 7777M INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:08:46.320  7220M / 7777M INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:08:46.351  7220M / 7777M INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:08:46.354  7220M / 7777M INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:08:46.627  7225M / 7780M INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:08:46.929  7227M / 7781M INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:08:47.181  7482M / 7990M INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:08:47.488  7206M / 7990M INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:08:47.585  7197M / 7991M INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:08:47.594  7197M / 7991M INFO    General                 (launcher.cpp              : 454)   Closing gaps in paths
  0:08:47.743  7435M / 7992M INFO    General                 (launcher.cpp              : 484)   Gap closing completed
  0:08:47.786  7449M / 8001M INFO    General                 (launcher.cpp              : 302)   Traversing tandem repeats
  0:08:48.069  7451M / 8015M INFO    General                 (launcher.cpp              : 312)   Traversed 1602 loops
  0:08:48.073  7451M / 8015M INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:08:48.075  7451M / 8015M INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:08:48.113  7442M / 8015M INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:08:48.119  7442M / 8015M INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:08:48.122  7442M / 8015M INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:08:48.154  7442M / 8015M INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:08:48.157  7442M / 8015M INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:08:48.230  7442M / 8015M INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:08:48.302  7443M / 8015M INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:08:48.330  7466M / 8015M INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:08:48.376  7445M / 8015M INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:08:48.450  7447M / 8015M INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:08:48.454  7447M / 8015M INFO    General                 (launcher.cpp              : 664)   ExSPAnder repeat resolving tool finished
  0:08:49.066  2815M / 8015M INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:08:49.342  2815M / 8015M INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/before_rr.fasta
  0:08:49.879  2815M / 8015M INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/assembly_graph_with_scaffolds.gfa
  0:08:50.137  2815M / 8015M INFO    General                 (contig_output_stage.cpp   : 165)   Outputting FastG graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/assembly_graph.fastg
  0:08:51.166  2815M / 8015M INFO    General                 (contig_output_stage.cpp   : 196)   Breaking scaffolds
  0:08:51.548  3142M / 8015M INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/final_contigs.fasta
  0:08:51.842  3142M / 8015M INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/final_contigs.paths
  0:08:52.201  2872M / 8015M INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/scaffolds.fasta
  0:08:52.593  2872M / 8015M INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/scaffolds.paths
  0:08:52.734  2872M / 8015M INFO    General                 (contig_output_stage.cpp   : 111)   Populating GFA with scaffold paths
  0:08:52.907  2815M / 8015M INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:08:54.963     1M / 8015M INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 8 minutes 55 seconds

===== K55 finished. 


===== Copy files started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/copy_files.py /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/final_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/K55/final_contigs.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/contigs.paths


===== Copy files finished. 


===== Assembling finished. 


===== Breaking scaffolds started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/breaking_scaffolds_script.py --result_scaffolds_filename /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/scaffolds.fasta --misc_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/misc --threshold_for_breaking_scaffolds 3


===== Breaking scaffolds finished. 


===== Terminate started. 


===== Terminate finished. 

 * Corrected reads are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/corrected/
 * Assembled contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/contigs.fasta
 * Assembled scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/scaffolds.fasta
 * Paths in the assembly graph corresponding to the contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/contigs.paths
 * Paths in the assembly graph corresponding to the scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/scaffolds.paths
 * Assembly graph is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/assembly_graph.fastg
 * Assembly graph in GFA format is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/assembly_graph_with_scaffolds.gfa

======= SPAdes pipeline finished.

SPAdes log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507999/spades.log

Thank you for using SPAdes!
SPAdes assembly completed for 1507999
Assembly completed for sample: 1507999
