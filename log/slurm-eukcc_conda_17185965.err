29-04-2025 10:05:37:  EukCC version 2.1.0
29-04-2025 10:05:37:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:05:37:  
eukcc:
<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:05:37:  
hmmer:
<PERSON>, <PERSON> "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:05:37:  
pplacer:
<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:05:37:  
epa-ng:
<PERSON>, <PERSON>, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:05:37:  
metaEuk:
<PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. "Meta<PERSON>uk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:05:37:  ####################################
29-04-2025 10:05:37:  Set sequence type to DNA
29-04-2025 10:05:51:  Doing a first pass placement in the base database
29-04-2025 10:05:51:  Searching for marker genes in base database
29-04-2025 10:05:51:  Found 1 marker genes, placing them in the tree using epa-ng
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 13349 was translated into 2813651
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 4790 was translated into 4792
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134006 was translated into 479712
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134013 was translated into 223615
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 453998 was translated into 2049356
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 2498572 was translated into 1280935
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 91191 was translated into 3075448
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
29-04-2025 10:05:57:  Genome belongs to clade: protozoa (Best TaxID: 33677)
29-04-2025 10:05:57:  Searching for marker genes in protozoa database
29-04-2025 10:05:57:  No placement marker genes found.
29-04-2025 10:05:59:  EukCC version 2.1.0
29-04-2025 10:05:59:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:05:59:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:05:59:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:05:59:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:05:59:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:05:59:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:05:59:  ####################################
29-04-2025 10:05:59:  Set sequence type to DNA
29-04-2025 10:06:11:  Doing a first pass placement in the base database
29-04-2025 10:06:11:  Searching for marker genes in base database
29-04-2025 10:06:11:  No placement marker genes found.
29-04-2025 10:06:13:  EukCC version 2.1.0
29-04-2025 10:06:13:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:06:13:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:06:13:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:06:13:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:06:13:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:06:13:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:06:13:  ####################################
29-04-2025 10:06:13:  Set sequence type to DNA
29-04-2025 10:06:28:  Doing a first pass placement in the base database
29-04-2025 10:06:28:  Searching for marker genes in base database
29-04-2025 10:06:28:  No placement marker genes found.
29-04-2025 10:06:29:  EukCC version 2.1.0
29-04-2025 10:06:29:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:06:29:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:06:29:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:06:29:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:06:29:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:06:29:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:06:29:  ####################################
29-04-2025 10:06:29:  Set sequence type to DNA
29-04-2025 10:06:41:  Doing a first pass placement in the base database
29-04-2025 10:06:41:  Searching for marker genes in base database
29-04-2025 10:06:42:  No placement marker genes found.
29-04-2025 10:06:43:  EukCC version 2.1.0
29-04-2025 10:06:43:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:06:43:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:06:43:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:06:43:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:06:43:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:06:43:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:06:43:  ####################################
29-04-2025 10:06:43:  Set sequence type to DNA
29-04-2025 10:07:00:  Doing a first pass placement in the base database
29-04-2025 10:07:00:  Searching for marker genes in base database
29-04-2025 10:07:00:  No placement marker genes found.
29-04-2025 10:07:01:  EukCC version 2.1.0
29-04-2025 10:07:01:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:07:01:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:07:01:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:07:01:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:07:01:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:07:01:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:07:01:  ####################################
29-04-2025 10:07:01:  Set sequence type to DNA
29-04-2025 10:07:18:  Doing a first pass placement in the base database
29-04-2025 10:07:18:  Searching for marker genes in base database
29-04-2025 10:07:18:  No placement marker genes found.
29-04-2025 10:07:20:  EukCC version 2.1.0
29-04-2025 10:07:20:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:07:20:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:07:20:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:07:20:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:07:20:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:07:20:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:07:20:  ####################################
29-04-2025 10:07:20:  Set sequence type to DNA
29-04-2025 10:07:42:  Doing a first pass placement in the base database
29-04-2025 10:07:42:  Searching for marker genes in base database
29-04-2025 10:07:43:  Found 3 marker genes, placing them in the tree using epa-ng
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 13349 was translated into 2813651
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 4790 was translated into 4792
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134006 was translated into 479712
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134013 was translated into 223615
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 453998 was translated into 2049356
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 2498572 was translated into 1280935
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 91191 was translated into 3075448
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
29-04-2025 10:07:52:  Genome belongs to clade: protozoa (Best TaxID: 33677)
29-04-2025 10:07:52:  Searching for marker genes in protozoa database
29-04-2025 10:07:52:  Found 1 marker genes, placing them in the tree using epa-ng
29-04-2025 10:07:54:  Automatically locating best SCMG set
29-04-2025 10:08:13:  Searching fasta for selected markers
29-04-2025 10:08:13:  Completeness: 2.78
29-04-2025 10:08:13:  Contamination: 0.0
29-04-2025 10:08:13:  Max silent contamination: 98.22
29-04-2025 10:08:13:  Wrote output to: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/04quality/eukcc_conda/1507992/1507992.bin.2/eukcc.csv
29-04-2025 10:08:15:  EukCC version 2.1.0
29-04-2025 10:08:15:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:08:15:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:08:15:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:08:15:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:08:15:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:08:15:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:08:15:  ####################################
29-04-2025 10:08:15:  Set sequence type to DNA
29-04-2025 10:08:33:  Doing a first pass placement in the base database
29-04-2025 10:08:33:  Searching for marker genes in base database
29-04-2025 10:08:33:  Found 1 marker genes, placing them in the tree using epa-ng
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 13349 was translated into 2813651
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 4790 was translated into 4792
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134006 was translated into 479712
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134013 was translated into 223615
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 453998 was translated into 2049356
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 2498572 was translated into 1280935
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 91191 was translated into 3075448
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
29-04-2025 10:08:40:  Genome belongs to clade: protozoa (Best TaxID: 33677)
29-04-2025 10:08:40:  Searching for marker genes in protozoa database
29-04-2025 10:08:40:  Found 3 marker genes, placing them in the tree using epa-ng
29-04-2025 10:08:43:  Automatically locating best SCMG set
29-04-2025 10:08:57:  Searching fasta for selected markers
29-04-2025 10:08:57:  Completeness: 0.0
29-04-2025 10:08:57:  Contamination: 0.0
29-04-2025 10:08:57:  Max silent contamination: 100.0
29-04-2025 10:08:57:  Wrote output to: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/04quality/eukcc_conda/1507992/1507992.bin.3/eukcc.csv
29-04-2025 10:08:58:  EukCC version 2.1.0
29-04-2025 10:08:58:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:08:58:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:08:58:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:08:58:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:08:58:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:08:58:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:08:58:  ####################################
29-04-2025 10:08:58:  Set sequence type to DNA
29-04-2025 10:09:16:  Doing a first pass placement in the base database
29-04-2025 10:09:16:  Searching for marker genes in base database
29-04-2025 10:09:16:  Found 1 marker genes, placing them in the tree using epa-ng
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 13349 was translated into 2813651
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 4790 was translated into 4792
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134006 was translated into 479712
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134013 was translated into 223615
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 453998 was translated into 2049356
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 2498572 was translated into 1280935
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 91191 was translated into 3075448
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
29-04-2025 10:09:22:  Genome belongs to clade: protozoa (Best TaxID: 33677)
29-04-2025 10:09:22:  Searching for marker genes in protozoa database
29-04-2025 10:09:22:  No placement marker genes found.
29-04-2025 10:09:24:  EukCC version 2.1.0
29-04-2025 10:09:24:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:09:24:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:09:24:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:09:24:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:09:24:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:09:24:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:09:24:  ####################################
29-04-2025 10:09:24:  Set sequence type to DNA
29-04-2025 10:09:35:  Doing a first pass placement in the base database
29-04-2025 10:09:35:  Searching for marker genes in base database
29-04-2025 10:09:35:  No placement marker genes found.
29-04-2025 10:09:36:  EukCC version 2.1.0
29-04-2025 10:09:36:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:09:36:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:09:36:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:09:36:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:09:36:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:09:36:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:09:36:  ####################################
29-04-2025 10:09:36:  Set sequence type to DNA
29-04-2025 10:10:00:  Doing a first pass placement in the base database
29-04-2025 10:10:00:  Searching for marker genes in base database
29-04-2025 10:10:00:  Found 5 marker genes, placing them in the tree using epa-ng
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 13349 was translated into 2813651
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 4790 was translated into 4792
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134006 was translated into 479712
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134013 was translated into 223615
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 453998 was translated into 2049356
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 2498572 was translated into 1280935
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 91191 was translated into 3075448
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
29-04-2025 10:10:10:  Genome belongs to clade: protozoa (Best TaxID: 33677)
29-04-2025 10:10:10:  Searching for marker genes in protozoa database
29-04-2025 10:10:11:  Found 4 marker genes, placing them in the tree using epa-ng
29-04-2025 10:10:14:  Automatically locating best SCMG set
29-04-2025 10:10:27:  Searching fasta for selected markers
29-04-2025 10:10:27:  Completeness: 25.0
29-04-2025 10:10:27:  Contamination: 0.0
29-04-2025 10:10:27:  Max silent contamination: 99.43
29-04-2025 10:10:27:  Wrote output to: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/04quality/eukcc_conda/1507993/1507993.bin.1/eukcc.csv
29-04-2025 10:10:29:  EukCC version 2.1.0
29-04-2025 10:10:29:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:10:29:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:10:29:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:10:29:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:10:29:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:10:29:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:10:29:  ####################################
29-04-2025 10:10:29:  Set sequence type to DNA
29-04-2025 10:10:42:  Doing a first pass placement in the base database
29-04-2025 10:10:42:  Searching for marker genes in base database
29-04-2025 10:10:42:  No placement marker genes found.
29-04-2025 10:10:43:  EukCC version 2.1.0
29-04-2025 10:10:43:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:10:43:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:10:43:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:10:43:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:10:43:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:10:43:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:10:43:  ####################################
29-04-2025 10:10:43:  Set sequence type to DNA
29-04-2025 10:11:00:  Doing a first pass placement in the base database
29-04-2025 10:11:00:  Searching for marker genes in base database
29-04-2025 10:11:00:  Found 2 marker genes, placing them in the tree using epa-ng
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 13349 was translated into 2813651
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 4790 was translated into 4792
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134006 was translated into 479712
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134013 was translated into 223615
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 453998 was translated into 2049356
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 2498572 was translated into 1280935
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 91191 was translated into 3075448
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
29-04-2025 10:11:07:  Genome belongs to clade: protozoa (Best TaxID: 33677)
29-04-2025 10:11:07:  Searching for marker genes in protozoa database
29-04-2025 10:11:07:  Found 2 marker genes, placing them in the tree using epa-ng
29-04-2025 10:11:09:  Automatically locating best SCMG set
29-04-2025 10:11:23:  Searching fasta for selected markers
29-04-2025 10:11:23:  Completeness: 0.0
29-04-2025 10:11:23:  Contamination: 0.0
29-04-2025 10:11:24:  Max silent contamination: 100.0
29-04-2025 10:11:24:  Wrote output to: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/04quality/eukcc_conda/1507993/1507993.bin.3/eukcc.csv
29-04-2025 10:11:25:  EukCC version 2.1.0
29-04-2025 10:11:25:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:11:25:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:11:25:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:11:25:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:11:25:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:11:25:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:11:25:  ####################################
29-04-2025 10:11:25:  Set sequence type to DNA
29-04-2025 10:11:37:  Doing a first pass placement in the base database
29-04-2025 10:11:37:  Searching for marker genes in base database
29-04-2025 10:11:37:  No placement marker genes found.
29-04-2025 10:11:39:  EukCC version 2.1.0
29-04-2025 10:11:39:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:11:39:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:11:39:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:11:39:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:11:39:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:11:39:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:11:39:  ####################################
29-04-2025 10:11:39:  Set sequence type to DNA
29-04-2025 10:12:24:  Doing a first pass placement in the base database
29-04-2025 10:12:24:  Searching for marker genes in base database
29-04-2025 10:12:24:  Found 14 marker genes, placing them in the tree using epa-ng
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 13349 was translated into 2813651
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 4790 was translated into 4792
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134006 was translated into 479712
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134013 was translated into 223615
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 453998 was translated into 2049356
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 2498572 was translated into 1280935
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 91191 was translated into 3075448
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
29-04-2025 10:12:44:  Genome belongs to clade: protozoa (Best TaxID: 33677)
29-04-2025 10:12:44:  Searching for marker genes in protozoa database
29-04-2025 10:12:45:  Found 13 marker genes, placing them in the tree using epa-ng
29-04-2025 10:12:51:  Automatically locating best SCMG set
29-04-2025 10:13:04:  Searching fasta for selected markers
29-04-2025 10:13:05:  Completeness: 36.11
29-04-2025 10:13:05:  Contamination: 0.0
29-04-2025 10:13:06:  Max silent contamination: 99.59
29-04-2025 10:13:06:  Wrote output to: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/04quality/eukcc_conda/1507994/1507994.bin.1/eukcc.csv
29-04-2025 10:13:08:  EukCC version 2.1.0
29-04-2025 10:13:08:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:13:08:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:13:08:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:13:08:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:13:08:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:13:08:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:13:08:  ####################################
29-04-2025 10:13:08:  Set sequence type to DNA
29-04-2025 10:13:57:  Doing a first pass placement in the base database
29-04-2025 10:13:57:  Searching for marker genes in base database
29-04-2025 10:13:58:  Found 16 marker genes, placing them in the tree using epa-ng
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 13349 was translated into 2813651
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 4790 was translated into 4792
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134006 was translated into 479712
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134013 was translated into 223615
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 453998 was translated into 2049356
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 2498572 was translated into 1280935
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 91191 was translated into 3075448
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
29-04-2025 10:14:21:  Genome belongs to clade: protozoa (Best TaxID: 5754)
29-04-2025 10:14:21:  Searching for marker genes in protozoa database
29-04-2025 10:14:21:  Found 14 marker genes, placing them in the tree using epa-ng
29-04-2025 10:14:29:  Automatically locating best SCMG set
29-04-2025 10:14:42:  Searching fasta for selected markers
29-04-2025 10:14:43:  Completeness: 33.33
29-04-2025 10:14:43:  Contamination: 0.0
29-04-2025 10:14:43:  Max silent contamination: 99.58
29-04-2025 10:14:44:  Wrote output to: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/04quality/eukcc_conda/1507995/1507995.bin.1/eukcc.csv
29-04-2025 10:14:46:  EukCC version 2.1.0
29-04-2025 10:14:46:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:14:46:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:14:46:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:14:46:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:14:46:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:14:46:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:14:46:  ####################################
29-04-2025 10:14:46:  Set sequence type to DNA
29-04-2025 10:15:30:  Doing a first pass placement in the base database
29-04-2025 10:15:30:  Searching for marker genes in base database
29-04-2025 10:15:30:  Found 14 marker genes, placing them in the tree using epa-ng
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 13349 was translated into 2813651
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 4790 was translated into 4792
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134006 was translated into 479712
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134013 was translated into 223615
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 453998 was translated into 2049356
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 2498572 was translated into 1280935
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 91191 was translated into 3075448
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
29-04-2025 10:15:52:  Genome belongs to clade: protozoa (Best TaxID: 5754)
29-04-2025 10:15:52:  Searching for marker genes in protozoa database
29-04-2025 10:15:53:  Found 13 marker genes, placing them in the tree using epa-ng
29-04-2025 10:15:59:  Automatically locating best SCMG set
29-04-2025 10:16:13:  Searching fasta for selected markers
29-04-2025 10:16:14:  Completeness: 36.11
29-04-2025 10:16:14:  Contamination: 0.0
29-04-2025 10:16:14:  Max silent contamination: 99.48
29-04-2025 10:16:15:  Wrote output to: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/04quality/eukcc_conda/1507996/1507996.bin.1/eukcc.csv
29-04-2025 10:16:18:  EukCC version 2.1.0
29-04-2025 10:16:18:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:16:18:  
eukcc:
Saary, Paul, Alex L. Mitchell, and Robert D. Finn. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:16:18:  
hmmer:
Eddy, Sean R. "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:16:18:  
pplacer:
Matsen, Frederick A., Robin B. Kodner, and E. Virginia Armbrust. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:16:18:  
epa-ng:
Barbera, Pierre, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:16:18:  
metaEuk:
Levy Karin, Eli, Milot Mirdita, and Johannes Söding. "MetaEuk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:16:18:  ####################################
29-04-2025 10:16:18:  Set sequence type to DNA
29-04-2025 10:17:03:  Doing a first pass placement in the base database
29-04-2025 10:17:03:  Searching for marker genes in base database
29-04-2025 10:17:03:  Found 13 marker genes, placing them in the tree using epa-ng
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 13349 was translated into 2813651
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 4790 was translated into 4792
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134006 was translated into 479712
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 134013 was translated into 223615
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 453998 was translated into 2049356
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 2498572 was translated into 1280935
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/lib/python3.12/site-packages/ete3/ncbi_taxonomy/ncbiquery.py:243: UserWarning: taxid 91191 was translated into 3075448
  warnings.warn("taxid %s was translated into %s" %(taxid, merged_conversion[taxid]))
29-04-2025 10:17:22:  Genome belongs to clade: protozoa (Best TaxID: 5754)
29-04-2025 10:17:22:  Searching for marker genes in protozoa database
29-04-2025 10:17:23:  Found 12 marker genes, placing them in the tree using epa-ng
29-04-2025 10:17:29:  Automatically locating best SCMG set
29-04-2025 10:17:42:  Searching fasta for selected markers
29-04-2025 10:17:43:  Completeness: 33.33
29-04-2025 10:17:43:  Contamination: 0.0
29-04-2025 10:17:43:  Max silent contamination: 99.58
29-04-2025 10:17:44:  Wrote output to: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/04quality/eukcc_conda/1507999/1507999.bin.1/eukcc.csv
