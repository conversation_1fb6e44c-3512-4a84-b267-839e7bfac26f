Copying protein files to 06taxonomy/eukulele/all_proteins...
  Copying 05genes/prodigal/1507990/1507990.bin.1/1507990.bin.1.proteins.faa to 06taxonomy/eukulele/all_proteins/1507990.bin.1.proteins.faa
  Copying 05genes/prodigal/1507990/1507990.bin.2/1507990.bin.2.proteins.faa to 06taxonomy/eukulele/all_proteins/1507990.bin.2.proteins.faa
  Copying 05genes/prodigal/1507990/1507990.bin.3/1507990.bin.3.proteins.faa to 06taxonomy/eukulele/all_proteins/1507990.bin.3.proteins.faa
  Copying 05genes/prodigal/1507990/1507990.bin.4/1507990.bin.4.proteins.faa to 06taxonomy/eukulele/all_proteins/1507990.bin.4.proteins.faa
  Copying 05genes/prodigal/1507990/1507990.bin.5/1507990.bin.5.proteins.faa to 06taxonomy/eukulele/all_proteins/1507990.bin.5.proteins.faa
  Copying 05genes/prodigal/1507992/1507992.bin.1/1507992.bin.1.proteins.faa to 06taxonomy/eukulele/all_proteins/1507992.bin.1.proteins.faa
  Copying 05genes/prodigal/1507992/1507992.bin.2/1507992.bin.2.proteins.faa to 06taxonomy/eukulele/all_proteins/1507992.bin.2.proteins.faa
  Copying 05genes/prodigal/1507992/1507992.bin.3/1507992.bin.3.proteins.faa to 06taxonomy/eukulele/all_proteins/1507992.bin.3.proteins.faa
  Copying 05genes/prodigal/1507992/1507992.bin.4/1507992.bin.4.proteins.faa to 06taxonomy/eukulele/all_proteins/1507992.bin.4.proteins.faa
  Copying 05genes/prodigal/1507992/1507992.bin.5/1507992.bin.5.proteins.faa to 06taxonomy/eukulele/all_proteins/1507992.bin.5.proteins.faa
  Copying 05genes/prodigal/1507993/1507993.bin.1/1507993.bin.1.proteins.faa to 06taxonomy/eukulele/all_proteins/1507993.bin.1.proteins.faa
  Copying 05genes/prodigal/1507993/1507993.bin.2/1507993.bin.2.proteins.faa to 06taxonomy/eukulele/all_proteins/1507993.bin.2.proteins.faa
  Copying 05genes/prodigal/1507993/1507993.bin.3/1507993.bin.3.proteins.faa to 06taxonomy/eukulele/all_proteins/1507993.bin.3.proteins.faa
  Copying 05genes/prodigal/1507993/1507993.bin.4/1507993.bin.4.proteins.faa to 06taxonomy/eukulele/all_proteins/1507993.bin.4.proteins.faa
  Copying 05genes/prodigal/1507994/1507994.bin.1/1507994.bin.1.proteins.faa to 06taxonomy/eukulele/all_proteins/1507994.bin.1.proteins.faa
  Copying 05genes/prodigal/1507995/1507995.bin.1/1507995.bin.1.proteins.faa to 06taxonomy/eukulele/all_proteins/1507995.bin.1.proteins.faa
  Copying 05genes/prodigal/1507996/1507996.bin.1/1507996.bin.1.proteins.faa to 06taxonomy/eukulele/all_proteins/1507996.bin.1.proteins.faa
  Copying 05genes/prodigal/1507999/1507999.bin.1/1507999.bin.1.proteins.faa to 06taxonomy/eukulele/all_proteins/1507999.bin.1.proteins.faa
Running EUKulele on all protein files...
All reference files for MarRef-MMETSP downloaded to ./marmmetsp
Running EUKulele with command line arguments, as no valid configuration file was provided.
The current EUKulele version is 2.0.9

Setting things up...
Specified reference directory, reference FASTA, and protein map/taxonomy table not found. Using database in location: ./marmmetsp.
Automatically downloading database marmmetsp . If you intended to use an existing database folder, be sure a reference FASTA, protein map, and taxonomy table are provided. Check the documentation for details.
Creating a diamond reference from database files...
Diamond database file already created; will not re-create database.
Aligning to reference database...
Aligning sample 1507990.bin.1.proteins...
Aligning sample 1507990.bin.2.proteins...
Aligning sample 1507990.bin.3.proteins...
Aligning sample 1507990.bin.4.proteins...
Aligning sample 1507990.bin.5.proteins...
Aligning sample 1507992.bin.1.proteins...
Aligning sample 1507992.bin.2.proteins...
Aligning sample 1507992.bin.3.proteins...
Aligning sample 1507992.bin.4.proteins...
Aligning sample 1507992.bin.5.proteins...
Aligning sample 1507993.bin.1.proteins...
Aligning sample 1507993.bin.2.proteins...
Aligning sample 1507993.bin.3.proteins...
Aligning sample 1507993.bin.4.proteins...
Aligning sample 1507994.bin.1.proteins...
Aligning sample 1507995.bin.1.proteins...
Aligning sample 1507996.bin.1.proteins...
Aligning sample 1507999.bin.1.proteins...
Diamond process exited for sample 1507993.bin.2.proteins.
Diamond did not complete successfully for sample 1507993.bin.2.proteins with rc code -9
Diamond process exited for sample 1507992.bin.4.proteins.
Diamond did not complete successfully for sample 1507992.bin.4.proteins with rc code -9
Diamond process exited for sample 1507990.bin.3.proteins.
Diamond did not complete successfully for sample 1507990.bin.3.proteins with rc code -9
Diamond process exited for sample 1507992.bin.1.proteins.
Diamond did not complete successfully for sample 1507992.bin.1.proteins with rc code -9
Diamond process exited for sample 1507992.bin.5.proteins.
Diamond did not complete successfully for sample 1507992.bin.5.proteins with rc code -9
Diamond process exited for sample 1507996.bin.1.proteins.
Diamond did not complete successfully for sample 1507996.bin.1.proteins with rc code -9
Diamond process exited for sample 1507990.bin.1.proteins.
Diamond did not complete successfully for sample 1507990.bin.1.proteins with rc code -9
Diamond process exited for sample 1507990.bin.4.proteins.
Diamond did not complete successfully for sample 1507990.bin.4.proteins with rc code -9
Diamond process exited for sample 1507993.bin.4.proteins.
Diamond did not complete successfully for sample 1507993.bin.4.proteins with rc code -9
Diamond process exited for sample 1507990.bin.5.proteins.
Diamond process exited for sample 1507990.bin.2.proteins.
Diamond process exited for sample 1507992.bin.2.proteins.
Diamond process exited for sample 1507992.bin.3.proteins.
Diamond process exited for sample 1507993.bin.3.proteins.
Diamond process exited for sample 1507993.bin.1.proteins.
Diamond process exited for sample 1507994.bin.1.proteins.
Diamond process exited for sample 1507995.bin.1.proteins.
Diamond process exited for sample 1507999.bin.1.proteins.
Alignment did not complete successfully.
EUKulele analysis completed.
Creating summary of taxonomic classifications...
Warning: EUKulele results file not found
Warning: EUKulele results file not found
