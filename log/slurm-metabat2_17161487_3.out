Pulling MetaBAT2 Docker image...
Processing binning for sample: 1507993
Decompressing scaffold file for 1507993...
Generating depth file for 1507993...
Running MetaBAT2 for 1507993...
MetaBAT 2 (2.17.66-ga512006-dirty) using minContig 1500, minCV 1.0, minCVSum 1.0, maxP 95%, minS 60, maxEdges 200 and minClsSize 200000. with random seed=1745882681
[00:00:00] Executing with 16 threads
[00:00:00] Parsing abundance file header [91.0Gb / 503.5Gb]
[00:00:00] Parsing assembly file [91.0Gb / 503.5Gb]
[00:00:00] ... processed 8 seqs, 8 long (>=1500), 0 short (>=1000) 1.1% (366367 of 33755644), ETA 0:00:02     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 18 seqs, 18 long (>=1500), 0 short (>=1000) 2.0% (677256 of 33755644), ETA 0:00:02     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 31 seqs, 31 long (>=1500), 0 short (>=1000) 3.1% (1035823 of 33755644), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 44 seqs, 44 long (>=1500), 0 short (>=1000) 4.0% (1357284 of 33755644), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 59 seqs, 59 long (>=1500), 0 short (>=1000) 5.0% (1700644 of 33755644), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 74 seqs, 74 long (>=1500), 0 short (>=1000) 6.0% (2025555 of 33755644), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 91 seqs, 91 long (>=1500), 0 short (>=1000) 7.0% (2365821 of 33755644), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 109 seqs, 109 long (>=1500), 0 short (>=1000) 8.0% (2700673 of 33755644), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 129 seqs, 129 long (>=1500), 0 short (>=1000) 9.0% (3050061 of 33755644), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 149 seqs, 149 long (>=1500), 0 short (>=1000) 10.0% (3381427 of 33755644), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 170 seqs, 170 long (>=1500), 0 short (>=1000) 11.0% (3716895 of 33755644), ETA 0:00:01     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 192 seqs, 192 long (>=1500), 0 short (>=1000) 12.0% (4054893 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 215 seqs, 215 long (>=1500), 0 short (>=1000) 13.0% (4392463 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 239 seqs, 239 long (>=1500), 0 short (>=1000) 14.0% (4729628 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 265 seqs, 265 long (>=1500), 0 short (>=1000) 15.0% (5075429 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 291 seqs, 291 long (>=1500), 0 short (>=1000) 16.0% (5408390 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 318 seqs, 318 long (>=1500), 0 short (>=1000) 17.0% (5741757 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 347 seqs, 347 long (>=1500), 0 short (>=1000) 18.0% (6083921 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 377 seqs, 377 long (>=1500), 0 short (>=1000) 19.0% (6418914 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 409 seqs, 409 long (>=1500), 0 short (>=1000) 20.0% (6760381 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 441 seqs, 441 long (>=1500), 0 short (>=1000) 21.0% (7088871 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 476 seqs, 476 long (>=1500), 0 short (>=1000) 22.0% (7434292 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 511 seqs, 511 long (>=1500), 0 short (>=1000) 23.0% (7768474 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 548 seqs, 548 long (>=1500), 0 short (>=1000) 24.0% (8106891 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 586 seqs, 586 long (>=1500), 0 short (>=1000) 25.0% (8442449 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 626 seqs, 626 long (>=1500), 0 short (>=1000) 26.0% (8783352 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 667 seqs, 667 long (>=1500), 0 short (>=1000) 27.0% (9120336 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 709 seqs, 709 long (>=1500), 0 short (>=1000) 28.0% (9452239 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 754 seqs, 754 long (>=1500), 0 short (>=1000) 29.0% (9795969 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 799 seqs, 799 long (>=1500), 0 short (>=1000) 30.0% (10129622 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 846 seqs, 846 long (>=1500), 0 short (>=1000) 31.0% (10466137 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 895 seqs, 895 long (>=1500), 0 short (>=1000) 32.0% (10805347 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 946 seqs, 946 long (>=1500), 0 short (>=1000) 33.0% (11145397 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 998 seqs, 998 long (>=1500), 0 short (>=1000) 34.0% (11480305 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1053 seqs, 1053 long (>=1500), 0 short (>=1000) 35.0% (11819605 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1110 seqs, 1110 long (>=1500), 0 short (>=1000) 36.0% (12157850 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1169 seqs, 1169 long (>=1500), 0 short (>=1000) 37.0% (12492138 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1231 seqs, 1231 long (>=1500), 0 short (>=1000) 38.0% (12829168 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1296 seqs, 1296 long (>=1500), 0 short (>=1000) 39.0% (13169700 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1363 seqs, 1363 long (>=1500), 0 short (>=1000) 40.0% (13505137 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1433 seqs, 1433 long (>=1500), 0 short (>=1000) 41.0% (13841578 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1506 seqs, 1506 long (>=1500), 0 short (>=1000) 42.0% (14179265 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1582 seqs, 1582 long (>=1500), 0 short (>=1000) 43.0% (14516786 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1661 seqs, 1661 long (>=1500), 0 short (>=1000) 44.0% (14853208 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1744 seqs, 1744 long (>=1500), 0 short (>=1000) 45.0% (15193055 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1829 seqs, 1829 long (>=1500), 0 short (>=1000) 46.0% (15528070 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 1919 seqs, 1919 long (>=1500), 0 short (>=1000) 47.0% (15867619 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2013 seqs, 2013 long (>=1500), 0 short (>=1000) 48.0% (16205290 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2111 seqs, 2111 long (>=1500), 0 short (>=1000) 49.0% (16542980 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2213 seqs, 2213 long (>=1500), 0 short (>=1000) 50.0% (16877978 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2322 seqs, 2322 long (>=1500), 0 short (>=1000) 51.0% (17215930 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2437 seqs, 2437 long (>=1500), 0 short (>=1000) 52.0% (17553255 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2559 seqs, 2559 long (>=1500), 0 short (>=1000) 53.0% (17891747 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2688 seqs, 2688 long (>=1500), 0 short (>=1000) 54.0% (18230550 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2823 seqs, 2823 long (>=1500), 0 short (>=1000) 55.0% (18566082 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 2967 seqs, 2967 long (>=1500), 0 short (>=1000) 56.0% (18905287 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3117 seqs, 3117 long (>=1500), 0 short (>=1000) 57.0% (19241046 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3277 seqs, 3277 long (>=1500), 0 short (>=1000) 58.0% (19579186 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3446 seqs, 3446 long (>=1500), 0 short (>=1000) 59.0% (19916364 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3626 seqs, 3626 long (>=1500), 0 short (>=1000) 60.0% (20253436 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 3818 seqs, 3818 long (>=1500), 0 short (>=1000) 61.0% (20591494 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4021 seqs, 4021 long (>=1500), 0 short (>=1000) 62.0% (20928763 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4236 seqs, 4236 long (>=1500), 0 short (>=1000) 63.0% (21266962 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4463 seqs, 4321 long (>=1500), 142 short (>=1000) 64.0% (21604869 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4703 seqs, 4321 long (>=1500), 382 short (>=1000) 65.0% (21942316 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 4957 seqs, 4321 long (>=1500), 636 short (>=1000) 66.0% (22279744 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 5226 seqs, 4321 long (>=1500), 905 short (>=1000) 67.0% (22617328 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 5510 seqs, 4321 long (>=1500), 1189 short (>=1000) 68.0% (22954780 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 5811 seqs, 4321 long (>=1500), 1490 short (>=1000) 69.0% (23292246 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 6130 seqs, 4321 long (>=1500), 1809 short (>=1000) 70.0% (23629404 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 6467 seqs, 4321 long (>=1500), 1985 short (>=1000) 71.0% (23966724 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 6824 seqs, 4321 long (>=1500), 1985 short (>=1000) 72.0% (24304122 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 7202 seqs, 4321 long (>=1500), 1985 short (>=1000) 73.0% (24642475 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 7601 seqs, 4321 long (>=1500), 1985 short (>=1000) 74.0% (24979595 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 8026 seqs, 4321 long (>=1500), 1985 short (>=1000) 75.0% (25317227 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 8478 seqs, 4321 long (>=1500), 1985 short (>=1000) 76.0% (25654597 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 8960 seqs, 4321 long (>=1500), 1985 short (>=1000) 77.0% (25992172 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 9474 seqs, 4321 long (>=1500), 1985 short (>=1000) 78.0% (26329521 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 10021 seqs, 4321 long (>=1500), 1985 short (>=1000) 79.0% (26667380 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 10602 seqs, 4321 long (>=1500), 1985 short (>=1000) 80.0% (27004834 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 11224 seqs, 4321 long (>=1500), 1985 short (>=1000) 81.0% (27342520 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 11890 seqs, 4321 long (>=1500), 1985 short (>=1000) 82.0% (27680052 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 12607 seqs, 4321 long (>=1500), 1985 short (>=1000) 83.0% (28017274 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 13382 seqs, 4321 long (>=1500), 1985 short (>=1000) 84.0% (28354856 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 14220 seqs, 4321 long (>=1500), 1985 short (>=1000) 85.0% (28692549 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 15122 seqs, 4321 long (>=1500), 1985 short (>=1000) 86.0% (29029975 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 16095 seqs, 4321 long (>=1500), 1985 short (>=1000) 87.0% (29367701 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 17143 seqs, 4321 long (>=1500), 1985 short (>=1000) 88.0% (29705114 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 18272 seqs, 4321 long (>=1500), 1985 short (>=1000) 89.0% (30042664 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 19480 seqs, 4321 long (>=1500), 1985 short (>=1000) 90.0% (30380221 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 20762 seqs, 4321 long (>=1500), 1985 short (>=1000) 91.0% (30717905 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 22124 seqs, 4321 long (>=1500), 1985 short (>=1000) 92.0% (31055275 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 23583 seqs, 4321 long (>=1500), 1985 short (>=1000) 93.0% (31392884 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 25157 seqs, 4321 long (>=1500), 1985 short (>=1000) 94.0% (31730419 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] ... processed 27219 seqs, 4321 long (>=1500), 1985 short (>=1000) 95.0% (32067958 of 33755644), ETA 0:00:00     [91.0Gb / 503.5Gb]     
[00:00:00] Number of large contigs >= 1500 bp are 4321, and small contigs >= 1000 bp are 1985                                                                  
[00:00:00] Allocating 4321 contigs by 1 samples abundances [91.0Gb / 503.5Gb]
[00:00:00] Allocating 4321 contigs by 1 samples variances [91.0Gb / 503.5Gb]
[00:00:00] Allocating 1985 small contigs by 1 samples abundances [91.0Gb / 503.5Gb]
[00:00:00] Reading 0.001668Gb abundance file [91.0Gb / 503.5Gb]
[00:00:00] ... processed 273 lines 273 contigs and 0 short contigs 1.0% (17943 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 549 lines 549 contigs and 0 short contigs 2.0% (35849 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 832 lines 832 contigs and 0 short contigs 3.0% (53798 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1112 lines 1112 contigs and 0 short contigs 4.0% (71669 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1390 lines 1390 contigs and 0 short contigs 5.0% (89592 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1668 lines 1668 contigs and 0 short contigs 6.0% (107472 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 1947 lines 1947 contigs and 0 short contigs 7.0% (125396 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 2226 lines 2226 contigs and 0 short contigs 8.0% (143317 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 2505 lines 2505 contigs and 0 short contigs 9.0% (161224 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 2784 lines 2784 contigs and 0 short contigs 10.0% (179140 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 3064 lines 3064 contigs and 0 short contigs 11.0% (197065 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 3344 lines 3344 contigs and 0 short contigs 12.0% (214983 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 3624 lines 3624 contigs and 0 short contigs 13.0% (232915 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 3904 lines 3904 contigs and 0 short contigs 14.0% (250818 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4184 lines 4184 contigs and 0 short contigs 15.0% (268712 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4464 lines 4321 contigs and 143 short contigs 16.0% (286602 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 4744 lines 4321 contigs and 423 short contigs 17.0% (304535 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 5024 lines 4321 contigs and 703 short contigs 18.0% (322435 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 5305 lines 4321 contigs and 984 short contigs 19.0% (340363 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 5585 lines 4321 contigs and 1264 short contigs 20.0% (358286 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 5865 lines 4321 contigs and 1544 short contigs 21.0% (376153 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6145 lines 4321 contigs and 1824 short contigs 22.0% (394086 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 23.0% (412015 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 24.0% (429921 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 25.0% (447846 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 26.0% (465729 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 27.0% (483668 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 28.0% (501559 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 29.0% (519461 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 30.0% (537400 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 31.0% (555275 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 32.0% (573205 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 33.0% (591099 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 34.0% (609044 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 35.0% (626941 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 36.0% (644860 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 37.0% (662800 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 38.0% (680671 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 39.0% (698599 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 40.0% (716524 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 41.0% (734453 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 42.0% (752342 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 43.0% (770232 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 44.0% (788161 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 45.0% (806043 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 46.0% (824009 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 47.0% (841915 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 48.0% (859827 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 49.0% (877711 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 50.0% (895610 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 51.0% (913574 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 52.0% (931461 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 53.0% (949340 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 54.0% (967248 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 55.0% (985220 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 56.0% (1003073 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 57.0% (1021000 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 58.0% (1038924 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 59.0% (1056833 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 60.0% (1074780 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 61.0% (1092635 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 62.0% (1110567 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 63.0% (1128517 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 64.0% (1146382 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 65.0% (1164333 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 66.0% (1182223 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 67.0% (1200144 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 68.0% (1218060 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 69.0% (1235951 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 70.0% (1253878 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 71.0% (1271786 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 72.0% (1289727 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 73.0% (1307634 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 74.0% (1325530 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 75.0% (1343449 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 76.0% (1361314 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 77.0% (1379233 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 78.0% (1397197 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 79.0% (1415062 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 80.0% (1433005 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 81.0% (1450901 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 82.0% (1468805 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 83.0% (1486734 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 84.0% (1504634 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 85.0% (1522569 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 86.0% (1540432 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 87.0% (1558405 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 88.0% (1576302 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 89.0% (1594221 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 90.0% (1612104 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 91.0% (1630056 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 92.0% (1647920 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 93.0% (1665848 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 94.0% (1683765 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 95.0% (1701688 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 96.0% (1719579 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 97.0% (1737511 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 98.0% (1755396 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] ... processed 6306 lines 4321 contigs and 1985 short contigs 99.0% (1773294 of 1791121), ETA 0:00:00     [91.0Gb / 503.5Gb]                 
[00:00:00] Finished reading 28715 contigs and 1 coverages from 03bins/metabat2/1507993/temp/1507993.depth.txt [91.0Gb / 503.5Gb]. Ignored 22409 too small contigs.                                     
[00:00:00] Number of target contigs: 4321 of large (>= 1500) and 1985 of small ones (>=1000 & <1500). 
[00:00:00] Start contig TNF calculation. nobs = 4321
[00:00:00] Allocated memory for TNF [91.0Gb / 503.5Gb]
[00:00:00] Calculating TNF 1.3% (56 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 2.1% (92 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 3.3% (141 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 4.3% (185 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 5.2% (224 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 6.3% (271 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 7.2% (310 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 8.3% (360 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 9.3% (403 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 10.4% (450 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 11.6% (500 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 12.3% (533 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 13.4% (580 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 14.4% (624 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 15.5% (671 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 16.6% (718 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 17.5% (755 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 18.6% (805 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 19.4% (840 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 20.5% (885 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 21.6% (932 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 22.7% (981 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 23.4% (1013 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 24.6% (1064 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 25.5% (1101 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 26.8% (1159 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 27.7% (1195 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 28.5% (1232 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 29.6% (1279 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 30.7% (1326 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 31.9% (1378 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 32.9% (1422 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 33.6% (1454 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 34.9% (1507 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 35.7% (1542 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 36.7% (1584 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 37.7% (1630 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 38.8% (1677 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 39.8% (1718 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 40.9% (1768 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 41.8% (1807 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 42.9% (1855 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 43.8% (1894 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 45.0% (1943 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 46.1% (1991 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 47.2% (2041 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 47.9% (2071 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 49.2% (2126 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 49.9% (2157 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 51.2% (2213 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 52.0% (2248 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 53.1% (2296 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 54.0% (2335 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 55.1% (2383 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 56.4% (2435 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 57.1% (2469 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 58.2% (2514 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 59.5% (2570 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 60.1% (2596 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 61.1% (2640 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 62.3% (2690 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 63.3% (2737 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 64.3% (2780 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 65.4% (2824 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 66.5% (2872 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 67.4% (2914 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 68.3% (2950 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 69.4% (2999 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 70.6% (3049 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 71.5% (3089 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 72.5% (3134 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 73.7% (3186 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 74.5% (3217 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 75.4% (3256 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 76.7% (3314 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 77.5% (3350 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 78.4% (3388 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 79.6% (3439 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 80.5% (3477 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 81.6% (3528 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 82.5% (3564 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 83.5% (3608 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 84.6% (3656 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 85.7% (3705 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 86.6% (3741 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 87.7% (3788 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 88.6% (3829 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 89.7% (3878 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 91.0% (3933 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 91.9% (3972 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 92.8% (4008 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 93.9% (4057 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 94.9% (4100 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 96.0% (4147 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 96.7% (4180 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 97.8% (4227 of 4321), ETA 0:00:00    
[00:00:00] Calculating TNF 99.1% (4283 of 4321), ETA 0:00:00    
[00:00:00] Finished contig TNF calculation.  [91.0Gb / 503.5Gb]                                
[00:00:00] ... processing TNF matrix 1.0% (45 of 4321), ETA 0:00:04                   
[00:00:00] ... processing TNF matrix 2.2% (96 of 4321), ETA 0:00:02                   
[00:00:00] ... processing TNF matrix 3.3% (144 of 4321), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 4.1% (176 of 4321), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 5.2% (224 of 4321), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 6.3% (272 of 4321), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 7.4% (320 of 4321), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 8.1% (352 of 4321), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 9.3% (400 of 4321), ETA 0:00:01                   
[00:00:00] ... processing TNF matrix 10.4% (448 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 11.5% (496 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 12.2% (528 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 13.3% (576 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 14.4% (624 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 15.6% (672 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 16.3% (704 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 17.4% (752 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 18.5% (800 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 19.6% (848 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 20.4% (880 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 21.5% (928 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 22.6% (976 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 23.7% (1024 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 24.4% (1056 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 25.5% (1104 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 26.7% (1152 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 27.8% (1200 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 28.5% (1233 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 29.6% (1281 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 30.8% (1329 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 31.9% (1377 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 32.6% (1410 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 33.8% (1461 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 34.9% (1509 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 35.7% (1541 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 36.8% (1589 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 37.9% (1637 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.0% (1686 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 39.8% (1718 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 40.9% (1766 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.0% (1816 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 42.8% (1849 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 43.9% (1897 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 45.0% (1945 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.1% (1994 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 46.9% (2026 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 48.0% (2074 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.2% (2124 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 49.9% (2157 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 51.0% (2205 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 52.2% (2254 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 53.3% (2303 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 54.0% (2335 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 55.2% (2384 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 56.3% (2432 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 57.0% (2464 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 58.1% (2512 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 59.2% (2560 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 60.4% (2608 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 61.1% (2640 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 62.2% (2688 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 63.4% (2738 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 64.5% (2786 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 65.2% (2819 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 66.4% (2867 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 67.5% (2915 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 68.6% (2963 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 69.3% (2995 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 70.4% (3044 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 71.6% (3092 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 72.3% (3124 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 73.4% (3172 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 74.5% (3220 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 75.6% (3268 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 76.4% (3300 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 77.5% (3348 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 78.6% (3396 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 79.7% (3444 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 80.4% (3476 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 81.6% (3524 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 82.7% (3572 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 83.8% (3621 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 84.5% (3653 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 85.7% (3702 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 86.8% (3752 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 87.6% (3785 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 88.7% (3833 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 89.8% (3881 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 90.9% (3929 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 91.7% (3961 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 92.8% (4009 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 93.9% (4057 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 95.0% (4105 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 95.8% (4139 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 96.9% (4187 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 98.0% (4236 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 98.8% (4268 of 4321), ETA 0:00:00                   
[00:00:00] ... processing TNF matrix 99.9% (4316 of 4321), ETA 0:00:00                   
[00:00:00] Finding cutoff p=999 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=999 1.5% (66 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 2.7% (115 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 3.8% (165 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 4.9% (212 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 5.9% (254 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 7.2% (309 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 8.4% (361 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 9.5% (410 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 10.7% (462 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 11.9% (516 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 13.2% (569 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 14.5% (627 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 16.0% (692 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 18.1% (784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 18.8% (813 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 19.6% (848 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 20.5% (885 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 21.7% (938 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 23.1% (998 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 23.8% (1029 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 24.6% (1063 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 26.0% (1125 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 26.8% (1156 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 28.1% (1213 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 28.7% (1241 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 30.0% (1297 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 31.0% (1338 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 31.8% (1372 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 33.2% (1434 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 34.0% (1467 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 34.9% (1506 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 35.8% (1547 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 37.1% (1605 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 37.9% (1637 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 39.2% (1695 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 40.0% (1728 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 40.9% (1767 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 42.2% (1823 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 43.0% (1857 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 44.2% (1908 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 44.9% (1938 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 46.4% (2003 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 47.0% (2032 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 48.2% (2083 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 49.3% (2131 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 50.0% (2161 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 51.3% (2217 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 51.9% (2244 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 53.1% (2294 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 54.0% (2332 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 55.1% (2382 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 56.4% (2436 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 57.3% (2475 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 58.6% (2530 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 59.2% (2558 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 60.3% (2606 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 61.3% (2649 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 62.3% (2690 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 63.2% (2730 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 64.3% (2780 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 65.2% (2817 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 66.4% (2870 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 67.3% (2907 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 68.4% (2954 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 69.5% (3003 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 70.4% (3042 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 71.7% (3096 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 72.5% (3134 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 73.6% (3180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 74.3% (3212 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 75.5% (3261 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 76.6% (3308 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 77.6% (3353 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 78.6% (3396 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 79.7% (3444 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 80.4% (3476 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 81.5% (3522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 82.6% (3569 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 83.6% (3613 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 84.5% (3653 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 85.7% (3705 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 86.7% (3746 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 87.7% (3790 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 88.7% (3833 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 89.6% (3873 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 90.7% (3920 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 91.6% (3960 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 92.8% (4009 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 94.8% (4095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 95.7% (4136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 97.8% (4226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 98.8% (4270 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=999 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.9; 0 / 4321 (P = 0.00%) round 1]               
[00:00:00] Finding cutoff p=998 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=998 1.1% (49 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 2.9% (125 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 4.4% (191 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 6.3% (274 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 8.1% (349 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 10.0% (433 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 11.8% (509 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 14.4% (621 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 16.7% (720 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 17.8% (768 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 19.0% (819 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 20.3% (876 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 21.8% (940 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 23.0% (995 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 24.3% (1048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 25.6% (1106 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 26.8% (1157 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 28.1% (1214 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 29.2% (1262 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 30.5% (1318 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 31.7% (1371 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 33.0% (1424 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 34.3% (1482 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 35.6% (1540 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 36.9% (1595 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 38.4% (1659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 41.0% (1771 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 42.5% (1835 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 43.9% (1898 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 45.2% (1954 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 46.6% (2012 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 47.6% (2058 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 48.9% (2112 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 50.0% (2160 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 51.3% (2216 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 52.7% (2276 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 54.1% (2336 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 55.3% (2390 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 56.6% (2445 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 57.7% (2492 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 58.7% (2535 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 59.7% (2579 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 60.7% (2625 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 61.7% (2667 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 62.7% (2710 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 63.8% (2756 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 64.8% (2801 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 65.8% (2844 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 66.9% (2889 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 67.8% (2930 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 68.9% (2977 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 69.9% (3019 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 70.7% (3054 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 71.4% (3086 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 72.7% (3142 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 73.4% (3170 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 74.6% (3224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 75.5% (3263 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 76.6% (3309 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 77.6% (3352 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 78.6% (3395 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 79.5% (3435 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 80.5% (3479 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 81.5% (3522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 82.7% (3572 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 83.5% (3609 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 84.6% (3657 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 85.9% (3710 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 86.6% (3743 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 87.6% (3786 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 88.6% (3830 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 89.7% (3874 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 90.7% (3919 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 91.7% (3964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 92.7% (4004 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 93.7% (4049 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 94.7% (4093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 97.8% (4225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=998 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.8; 0 / 4321 (P = 0.00%) round 2]               
[00:00:00] Finding cutoff p=995 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=995 1.1% (47 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 2.7% (115 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 4.7% (203 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 6.5% (282 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 8.4% (362 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 10.3% (444 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 12.2% (526 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 14.4% (621 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 16.5% (713 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 19.1% (824 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 21.4% (926 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 22.7% (982 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 24.3% (1049 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 25.8% (1115 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 27.3% (1178 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 28.7% (1240 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 30.1% (1302 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 31.6% (1367 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 33.0% (1427 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 34.8% (1503 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 36.3% (1567 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 37.8% (1634 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 39.3% (1698 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 40.9% (1766 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 43.4% (1877 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 44.7% (1930 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 46.1% (1990 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 47.5% (2052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 48.7% (2106 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 50.0% (2160 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 51.3% (2217 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 52.7% (2277 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 54.0% (2334 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 55.3% (2389 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 56.5% (2442 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 57.8% (2496 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 58.9% (2545 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 60.0% (2591 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 60.9% (2633 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 62.1% (2684 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 63.2% (2731 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 64.4% (2782 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 65.4% (2827 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 66.4% (2869 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 67.3% (2909 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 68.3% (2950 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 69.3% (2994 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 70.6% (3050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 71.3% (3081 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 72.5% (3133 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 73.5% (3178 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 74.5% (3218 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 75.5% (3261 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 76.5% (3305 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 77.5% (3348 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 78.6% (3397 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 79.7% (3443 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 80.5% (3479 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 81.6% (3528 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 82.6% (3571 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 83.7% (3615 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 84.6% (3656 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 85.6% (3700 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 86.7% (3748 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 87.8% (3792 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 88.8% (3837 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 89.7% (3874 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 90.7% (3918 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 91.7% (3964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 92.8% (4010 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 94.8% (4095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 95.7% (4136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=995 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.5; 22 / 4321 (P = 0.51%) round 3]               
[00:00:00] Finding cutoff p=994 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=994 1.1% (49 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 2.9% (125 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 4.7% (204 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 6.7% (288 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 8.6% (373 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 10.5% (454 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 12.7% (548 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 14.9% (644 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 17.3% (749 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 19.9% (860 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 22.4% (969 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 23.8% (1030 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 25.3% (1095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 27.1% (1171 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 28.6% (1236 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 30.2% (1304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 31.8% (1375 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 33.5% (1446 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 35.0% (1514 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 36.6% (1582 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 38.2% (1651 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 39.9% (1723 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 41.4% (1791 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 43.0% (1860 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 44.5% (1921 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 45.7% (1974 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 47.1% (2034 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 49.3% (2130 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 50.6% (2187 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 51.9% (2243 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 53.3% (2304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 54.6% (2361 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 55.9% (2417 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 57.3% (2475 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 58.5% (2529 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 59.8% (2585 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 61.0% (2636 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 62.2% (2687 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 63.4% (2739 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 64.5% (2786 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 65.8% (2842 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 66.8% (2885 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 67.7% (2926 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 68.8% (2973 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 69.8% (3017 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 70.9% (3063 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 71.8% (3101 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 72.7% (3140 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 73.5% (3178 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 74.6% (3225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 75.4% (3256 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 76.4% (3300 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 77.5% (3347 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 78.5% (3391 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 79.6% (3441 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 80.6% (3484 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 81.5% (3521 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 82.6% (3569 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 83.6% (3611 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 84.6% (3654 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 85.6% (3700 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 86.7% (3748 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 87.6% (3785 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 88.7% (3831 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 89.7% (3874 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 90.7% (3921 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 91.7% (3963 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 92.7% (4005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 93.7% (4049 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 94.8% (4095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=994 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.4; 41 / 4321 (P = 0.95%) round 4]               
[00:00:00] Finding cutoff p=993 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=993 1.1% (49 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 3.0% (131 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 5.2% (225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 7.1% (307 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 9.2% (397 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 11.2% (484 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 13.2% (572 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 15.3% (663 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 17.6% (761 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 19.9% (861 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 22.9% (991 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 24.7% (1068 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 26.3% (1136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 28.0% (1210 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 29.6% (1278 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 31.4% (1357 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 32.9% (1420 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 34.6% (1496 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 36.2% (1564 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 37.8% (1635 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 39.6% (1710 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 41.3% (1786 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 43.0% (1859 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 44.5% (1921 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 45.8% (1981 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 47.1% (2034 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 48.5% (2097 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 50.1% (2165 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 51.4% (2223 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 52.8% (2281 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 54.2% (2343 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 55.8% (2409 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 57.9% (2500 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 59.0% (2550 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 60.2% (2602 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 61.5% (2659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 62.8% (2714 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 63.8% (2756 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 64.7% (2797 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 65.7% (2837 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 66.6% (2877 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 67.6% (2920 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 68.5% (2962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 69.6% (3006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 70.5% (3047 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 71.5% (3091 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 72.4% (3130 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 73.6% (3179 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 74.6% (3224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 75.4% (3257 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 76.5% (3304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 77.5% (3348 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 78.5% (3390 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 79.6% (3438 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 80.4% (3476 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 81.7% (3532 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 82.5% (3566 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 83.6% (3612 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 84.6% (3657 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 85.7% (3702 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 86.7% (3748 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 87.6% (3785 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 88.7% (3832 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 89.8% (3879 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 90.7% (3917 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 91.7% (3962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 92.7% (4004 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 93.8% (4052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 94.7% (4094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 96.8% (4183 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=993 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.3; 66 / 4321 (P = 1.53%) round 5]               
[00:00:00] Finding cutoff p=992 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=992 1.9% (84 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 3.8% (165 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 5.8% (249 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 7.6% (328 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 9.6% (415 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 11.8% (510 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 13.9% (601 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 15.9% (687 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 18.0% (778 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 20.2% (872 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 22.8% (984 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 25.3% (1095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 27.0% (1165 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 28.6% (1234 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 30.1% (1299 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 31.8% (1373 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 33.5% (1448 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 35.3% (1526 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 36.9% (1596 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 38.7% (1674 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 40.3% (1743 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 42.0% (1814 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 43.5% (1879 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 44.9% (1940 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 46.2% (1998 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 47.6% (2058 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 49.2% (2128 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 50.6% (2188 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 52.0% (2248 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 53.4% (2309 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 54.8% (2366 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 56.0% (2421 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 57.4% (2481 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 58.7% (2536 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 59.9% (2589 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 61.1% (2638 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 62.0% (2681 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 63.0% (2724 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 64.5% (2789 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 65.6% (2835 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 66.7% (2883 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 67.6% (2919 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 68.4% (2954 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 69.5% (3005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 70.3% (3039 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 71.6% (3095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 72.4% (3129 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 73.5% (3175 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 74.6% (3222 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 75.6% (3267 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 76.4% (3300 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 77.4% (3344 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 78.5% (3392 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 79.4% (3432 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 80.6% (3483 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 81.5% (3522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 82.7% (3574 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 83.7% (3615 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 84.7% (3658 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 85.7% (3701 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 86.6% (3741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 87.6% (3784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 88.6% (3830 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 89.7% (3878 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 90.8% (3922 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 91.8% (3965 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 92.7% (4006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 93.8% (4054 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 94.7% (4093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 95.8% (4138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=992 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.2; 93 / 4321 (P = 2.15%) round 6]               
[00:00:00] Finding cutoff p=990 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=990 1.1% (46 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 2.9% (125 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 5.2% (223 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 8.7% (375 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 10.5% (453 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 12.5% (542 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 14.7% (636 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 17.0% (735 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 19.3% (835 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 21.7% (936 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 24.3% (1052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 26.0% (1122 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 27.7% (1197 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 29.4% (1270 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 31.1% (1344 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 32.7% (1415 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 34.5% (1491 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 36.1% (1561 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 37.9% (1636 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 39.4% (1704 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 41.1% (1777 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 42.9% (1852 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 44.3% (1916 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 45.7% (1974 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 47.0% (2031 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 48.5% (2094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 49.8% (2151 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 51.2% (2213 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 52.8% (2280 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 54.1% (2339 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 55.5% (2396 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 56.6% (2446 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 57.7% (2493 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 59.0% (2549 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 60.2% (2602 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 61.3% (2649 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 62.3% (2693 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 63.3% (2737 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 64.3% (2777 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 65.2% (2819 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 66.6% (2879 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 67.4% (2914 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 68.6% (2963 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 69.4% (3000 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 70.7% (3053 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 71.5% (3089 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 72.3% (3124 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 73.3% (3169 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 74.4% (3215 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 75.4% (3257 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 76.5% (3304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 77.7% (3356 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 78.5% (3394 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 79.7% (3444 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 80.6% (3481 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 81.5% (3522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 82.6% (3571 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 83.6% (3613 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 84.6% (3657 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 85.6% (3700 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 86.7% (3745 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 87.7% (3789 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 88.7% (3834 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 89.7% (3875 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 90.7% (3917 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 91.7% (3962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 92.7% (4006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 93.8% (4054 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 94.8% (4095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 95.7% (4136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=990 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 99.0; 169 / 4321 (P = 3.91%) round 7]               
[00:00:00] Finding cutoff p=986 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=986 1.1% (47 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 3.0% (131 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 4.7% (205 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 6.7% (291 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 8.8% (380 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 10.7% (464 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 12.8% (553 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 15.0% (646 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 17.2% (742 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 20.5% (886 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 23.1% (998 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 25.5% (1100 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 27.1% (1173 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 28.7% (1240 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 30.4% (1312 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 31.9% (1379 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 33.6% (1450 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 35.3% (1525 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 36.9% (1593 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 38.8% (1676 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 40.4% (1745 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 42.1% (1820 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 43.6% (1885 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 45.0% (1945 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 46.4% (2005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 47.8% (2066 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 49.3% (2132 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 50.8% (2193 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 52.2% (2256 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 53.6% (2318 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 55.0% (2375 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 56.0% (2421 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 57.1% (2469 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 58.4% (2523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 59.7% (2581 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 60.8% (2628 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 61.8% (2669 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 62.8% (2713 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 63.8% (2758 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 64.8% (2799 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 65.7% (2839 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 66.7% (2880 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 67.5% (2917 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 68.3% (2951 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 69.5% (3005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 70.3% (3039 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 71.6% (3094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 72.3% (3126 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 73.4% (3170 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 74.4% (3215 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 75.7% (3271 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 76.4% (3303 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 77.6% (3354 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 78.5% (3392 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 79.6% (3439 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 80.5% (3479 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 81.8% (3534 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 82.6% (3570 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 83.6% (3612 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 84.6% (3656 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 85.6% (3698 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 86.6% (3741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 87.6% (3786 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 88.6% (3829 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 89.7% (3874 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 90.7% (3920 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 91.7% (3962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 92.7% (4004 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 94.7% (4093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=986 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.6; 327 / 4321 (P = 7.57%) round 8]               
[00:00:00] Finding cutoff p=983 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=983 1.1% (49 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 3.3% (141 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 5.3% (227 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 7.2% (313 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 9.3% (403 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 11.4% (493 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 13.5% (585 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 15.6% (676 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 17.8% (769 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 20.1% (870 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 22.8% (985 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 25.1% (1084 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 26.9% (1161 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 28.5% (1231 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 30.2% (1303 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 32.9% (1423 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 34.7% (1500 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 36.4% (1574 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 38.2% (1651 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 39.9% (1723 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 41.5% (1793 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 43.0% (1860 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 44.5% (1923 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 45.8% (1981 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 47.2% (2039 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 48.6% (2100 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 50.0% (2159 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 51.4% (2223 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 52.8% (2283 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 54.1% (2338 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 55.3% (2391 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 56.4% (2437 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 57.6% (2488 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 58.8% (2539 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 60.0% (2594 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 61.1% (2642 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 62.3% (2691 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 63.2% (2732 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 64.2% (2773 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 65.2% (2816 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 66.2% (2861 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 67.4% (2913 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 68.2% (2949 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 69.5% (3001 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 70.4% (3040 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 71.6% (3093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 72.4% (3129 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 73.5% (3176 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 74.5% (3220 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 75.6% (3268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 76.6% (3309 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 77.5% (3347 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 78.7% (3399 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 79.5% (3436 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 80.7% (3487 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 81.6% (3527 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 82.8% (3576 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 83.7% (3618 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 84.7% (3660 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 85.6% (3700 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 86.6% (3744 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 87.7% (3791 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 88.7% (3834 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 89.6% (3873 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 90.6% (3916 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 91.6% (3960 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 92.7% (4004 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 93.8% (4054 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 94.8% (4096 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=983 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 98.3; 457 / 4321 (P = 10.58%) round 9]               
[00:00:00] Finding cutoff p=979 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=979 1.1% (47 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 2.8% (122 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 4.7% (202 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 6.6% (287 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 8.5% (367 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 10.4% (449 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 12.4% (534 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 14.3% (620 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 16.4% (708 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 18.9% (816 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 21.4% (924 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 24.1% (1043 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 25.6% (1107 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 27.3% (1181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 28.8% (1246 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 30.6% (1321 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 32.2% (1392 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 33.8% (1461 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 35.3% (1524 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 37.0% (1598 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 38.6% (1667 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 41.4% (1788 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 43.0% (1860 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 44.3% (1915 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 45.7% (1974 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 46.9% (2027 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 48.4% (2092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 49.8% (2150 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 51.1% (2209 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 52.5% (2268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 53.7% (2322 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 54.9% (2373 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 56.1% (2425 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 57.3% (2474 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 58.4% (2525 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 59.5% (2573 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 60.6% (2619 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 61.5% (2659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 62.5% (2699 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 63.5% (2745 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 64.5% (2786 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 65.5% (2829 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 66.4% (2870 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 67.3% (2906 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 68.5% (2960 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 69.3% (2994 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 70.5% (3045 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 71.3% (3080 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 72.5% (3133 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 73.5% (3178 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 74.5% (3219 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 75.6% (3265 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 76.6% (3311 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 77.4% (3344 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 78.6% (3396 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 79.4% (3433 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 80.6% (3484 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 81.5% (3523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 82.7% (3573 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 83.7% (3617 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 84.6% (3657 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 85.6% (3699 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 86.6% (3742 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 87.6% (3786 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 88.6% (3830 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 89.7% (3878 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 90.8% (3922 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 91.8% (3966 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 92.7% (4006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 93.7% (4049 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 94.7% (4094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 95.7% (4136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=979 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.9; 625 / 4321 (P = 14.46%) round 10]               
[00:00:00] Finding cutoff p=976 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=976 1.1% (49 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 2.8% (123 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 4.8% (209 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 6.8% (295 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 8.8% (379 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 10.6% (460 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 12.6% (546 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 14.7% (637 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 16.8% (727 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 19.0% (820 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 21.4% (926 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 24.4% (1055 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 26.1% (1128 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 27.7% (1197 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 29.3% (1266 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 31.1% (1345 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 32.9% (1421 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 34.4% (1486 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 36.0% (1557 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 37.9% (1637 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 39.5% (1705 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 41.2% (1780 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 43.1% (1861 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 44.4% (1917 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 45.6% (1972 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 48.0% (2074 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 49.4% (2136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 51.0% (2205 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 52.4% (2263 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 53.7% (2319 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 54.8% (2368 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 56.0% (2418 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 57.1% (2468 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 58.3% (2518 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 59.5% (2570 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 60.4% (2610 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 61.4% (2652 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 62.3% (2694 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 63.2% (2732 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 64.2% (2776 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 65.2% (2817 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 66.2% (2860 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 67.4% (2914 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 68.6% (2965 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 69.5% (3001 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 70.3% (3036 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 71.4% (3087 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 72.5% (3134 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 73.5% (3178 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 74.7% (3226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 75.6% (3268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 76.7% (3313 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 77.6% (3352 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 78.7% (3400 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 79.5% (3437 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 80.7% (3486 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 81.6% (3525 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 82.7% (3574 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 83.7% (3617 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 84.7% (3659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 85.7% (3702 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 86.7% (3745 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 87.8% (3792 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 88.8% (3835 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 89.6% (3872 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 90.6% (3916 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 91.7% (3964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 92.8% (4008 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 93.8% (4051 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 94.7% (4092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 95.7% (4136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=976 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.6; 741 / 4321 (P = 17.15%) round 11]               
[00:00:00] Finding cutoff p=972 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=972 1.1% (46 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 2.8% (122 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 4.7% (205 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 6.6% (284 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 8.5% (369 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 10.6% (456 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 12.6% (545 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 14.7% (634 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 16.7% (721 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 18.8% (813 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 21.2% (915 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 23.7% (1023 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 26.0% (1124 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 27.8% (1202 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 29.4% (1270 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 31.0% (1339 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 32.8% (1417 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 34.7% (1499 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 36.2% (1565 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 37.7% (1628 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 39.3% (1699 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 40.9% (1767 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 42.9% (1853 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 44.1% (1906 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 45.4% (1963 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 46.8% (2021 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 48.1% (2079 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 49.6% (2144 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 50.9% (2198 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 52.2% (2257 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 54.8% (2369 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 56.0% (2421 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 57.2% (2472 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 58.2% (2516 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 59.4% (2566 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 60.4% (2611 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 61.4% (2655 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 62.3% (2693 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 63.2% (2732 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 64.2% (2775 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 65.6% (2834 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 66.6% (2877 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 67.5% (2917 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 68.3% (2952 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 69.6% (3007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 70.4% (3041 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 71.6% (3092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 72.3% (3125 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 73.5% (3174 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 74.4% (3216 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 75.5% (3262 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 76.5% (3306 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 77.5% (3350 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 78.6% (3397 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 79.5% (3436 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 80.6% (3484 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 81.5% (3523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 82.6% (3571 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 83.6% (3614 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 84.6% (3656 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 85.6% (3700 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 86.6% (3744 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 87.6% (3787 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 88.7% (3831 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 89.6% (3872 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 90.6% (3916 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 91.6% (3960 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 92.9% (4013 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 93.8% (4053 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 94.8% (4095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 95.8% (4138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=972 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 97.2; 860 / 4321 (P = 19.90%) round 12]               
[00:00:00] Finding cutoff p=968 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=968 1.1% (46 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 2.8% (121 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 4.7% (203 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 6.5% (282 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 8.4% (363 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 10.3% (444 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 12.4% (534 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 14.2% (615 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 16.2% (698 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 18.3% (790 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 20.4% (882 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 23.0% (995 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 25.6% (1108 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 27.2% (1174 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 28.7% (1240 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 30.4% (1315 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 32.0% (1384 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 33.7% (1458 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 35.4% (1529 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 37.1% (1601 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 38.6% (1669 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 40.2% (1735 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 42.4% (1832 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 43.6% (1884 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 44.8% (1937 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 46.3% (2001 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 47.6% (2056 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 49.0% (2117 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 50.4% (2176 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 51.6% (2230 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 53.0% (2289 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 54.2% (2340 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 55.2% (2384 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 56.4% (2437 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 57.7% (2492 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 59.5% (2571 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 60.6% (2618 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 61.4% (2654 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 62.4% (2698 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 63.4% (2741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 64.3% (2780 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 65.3% (2823 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 66.3% (2863 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 67.6% (2921 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 68.4% (2955 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 69.6% (3007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 70.4% (3043 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 71.6% (3092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 72.3% (3124 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 73.3% (3168 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 74.4% (3213 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 75.4% (3259 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 76.5% (3304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 77.4% (3345 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 78.6% (3395 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 79.4% (3433 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 80.5% (3479 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 81.6% (3527 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 82.6% (3567 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 83.5% (3610 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 84.8% (3664 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 85.5% (3696 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 86.6% (3740 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 87.6% (3785 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 88.6% (3828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 89.7% (3875 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 90.7% (3919 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 91.6% (3960 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 92.7% (4005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 94.7% (4094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=968 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.8; 1011 / 4321 (P = 23.40%) round 13]               
[00:00:00] Finding cutoff p=965 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=965 1.1% (47 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 2.9% (127 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 5.0% (215 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 6.8% (295 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 8.5% (367 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 10.5% (452 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 12.5% (539 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 14.5% (625 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 16.5% (713 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 18.5% (799 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 20.6% (891 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 22.8% (986 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 25.3% (1093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 27.2% (1176 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 28.7% (1239 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 30.4% (1314 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 32.1% (1387 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 33.9% (1463 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 35.3% (1526 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 36.8% (1591 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 38.4% (1659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 39.9% (1726 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 41.9% (1809 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 43.1% (1862 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 44.4% (1920 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 45.7% (1975 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 47.1% (2035 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 48.5% (2095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 49.8% (2154 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 51.1% (2209 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 52.4% (2263 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 53.5% (2313 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 54.6% (2358 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 55.7% (2407 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 57.0% (2461 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 58.0% (2506 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 59.0% (2550 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 60.0% (2594 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 61.1% (2638 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 62.4% (2695 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 63.2% (2731 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 64.6% (2791 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 65.5% (2830 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 66.4% (2870 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 67.5% (2916 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 68.2% (2949 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 69.4% (3000 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 70.6% (3051 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 71.4% (3085 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 72.5% (3132 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 73.5% (3176 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 74.5% (3219 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 75.6% (3266 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 76.6% (3308 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 77.6% (3352 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 78.7% (3399 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 79.5% (3434 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 80.6% (3484 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 81.5% (3523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 82.6% (3571 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 83.6% (3614 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 84.7% (3658 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 85.7% (3702 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 86.6% (3744 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 87.6% (3787 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 88.6% (3829 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 89.7% (3878 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 90.6% (3916 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 91.8% (3965 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 92.8% (4010 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 93.8% (4051 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 94.8% (4095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=965 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.5; 1123 / 4321 (P = 25.99%) round 14]               
[00:00:00] Finding cutoff p=962 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=962 1.2% (50 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 3.8% (165 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 5.7% (247 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 7.5% (326 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 9.4% (407 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 11.2% (484 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 12.9% (558 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 15.0% (647 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 17.0% (736 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 19.1% (824 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 21.4% (923 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 23.7% (1024 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 26.1% (1128 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 27.8% (1200 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 29.3% (1265 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 31.0% (1341 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 32.9% (1420 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 34.3% (1483 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 35.7% (1541 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 37.1% (1604 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 38.6% (1666 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 40.2% (1735 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 42.2% (1825 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 43.6% (1885 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 44.9% (1939 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 46.1% (1993 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 47.4% (2049 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 48.7% (2105 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 50.0% (2159 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 51.3% (2216 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 52.5% (2269 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 53.6% (2318 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 54.8% (2368 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 56.0% (2418 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 57.1% (2469 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 58.2% (2513 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 59.3% (2561 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 60.3% (2607 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 61.4% (2651 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 62.2% (2688 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 63.5% (2744 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 64.5% (2789 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 67.3% (2908 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 68.5% (2961 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 69.3% (2996 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 70.5% (3045 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 71.3% (3081 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 72.5% (3131 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 73.5% (3174 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 74.5% (3217 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 75.5% (3261 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 76.6% (3312 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 77.6% (3354 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 78.4% (3389 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 79.5% (3437 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 80.7% (3488 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 81.6% (3526 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 82.6% (3570 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 83.7% (3617 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 84.7% (3658 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 85.7% (3705 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 86.6% (3741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 87.7% (3790 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 88.7% (3833 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 89.7% (3874 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 90.6% (3916 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 91.6% (3960 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 92.7% (4005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 94.7% (4092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 95.7% (4136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=962 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 96.2; 1219 / 4321 (P = 28.21%) round 15]               
[00:00:00] Finding cutoff p=959 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=959 1.0% (44 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 2.8% (119 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 4.8% (208 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 6.4% (275 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 8.2% (354 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 10.1% (438 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 13.9% (602 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 15.9% (688 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 17.9% (773 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 19.8% (856 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 22.0% (950 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 24.4% (1055 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 26.8% (1159 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 28.4% (1226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 29.8% (1289 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 31.6% (1364 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 33.3% (1440 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 34.9% (1507 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 36.4% (1575 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 38.0% (1643 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 39.6% (1709 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 41.2% (1782 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 43.3% (1869 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 44.5% (1923 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 45.8% (1978 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 47.0% (2031 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 48.4% (2093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 50.0% (2160 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 51.3% (2215 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 52.5% (2267 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 53.6% (2318 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 54.6% (2360 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 55.8% (2409 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 56.7% (2451 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 58.0% (2507 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 59.0% (2548 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 60.0% (2593 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 60.9% (2630 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 61.9% (2674 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 62.8% (2712 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 63.7% (2752 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 64.6% (2791 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 65.6% (2833 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 66.5% (2872 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 67.3% (2906 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 68.5% (2959 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 69.3% (2996 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 70.3% (3039 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 71.5% (3091 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 72.5% (3134 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 73.5% (3176 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 74.5% (3219 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 75.6% (3267 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 76.6% (3312 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 77.6% (3355 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 78.4% (3389 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 79.5% (3434 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 80.7% (3485 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 81.5% (3523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 82.6% (3569 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 83.6% (3612 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 84.5% (3653 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 85.6% (3698 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 86.7% (3748 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 87.7% (3790 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 88.8% (3835 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 89.6% (3873 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 90.7% (3919 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 91.7% (3962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 92.8% (4008 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 94.7% (4094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 96.8% (4183 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=959 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.9; 1314 / 4321 (P = 30.41%) round 16]               
[00:00:00] Finding cutoff p=954 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=954 1.1% (47 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 2.8% (120 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 4.4% (192 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 6.1% (262 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 7.8% (337 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 9.8% (422 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 11.5% (498 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 13.4% (577 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 15.1% (654 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 18.1% (780 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 20.2% (872 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 22.4% (968 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 24.9% (1075 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 27.1% (1169 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 28.3% (1225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 30.3% (1308 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 32.0% (1383 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 33.6% (1451 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 35.2% (1520 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 36.7% (1585 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 38.0% (1641 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 39.6% (1709 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 41.7% (1802 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 43.0% (1859 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 44.2% (1911 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 45.5% (1966 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 46.8% (2022 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 48.2% (2082 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 49.5% (2139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 50.7% (2191 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 52.0% (2248 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 53.1% (2293 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 54.1% (2337 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 55.1% (2382 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 56.4% (2437 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 57.4% (2480 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 58.4% (2522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 59.3% (2564 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 60.4% (2611 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 61.4% (2651 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 62.2% (2688 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 63.5% (2743 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 64.4% (2783 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 65.3% (2820 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 66.3% (2864 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 67.6% (2922 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 68.5% (2961 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 69.2% (2992 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 70.6% (3049 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 71.3% (3081 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 72.4% (3130 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 73.3% (3168 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 74.9% (3235 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 75.6% (3265 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 76.5% (3305 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 77.5% (3348 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 78.6% (3397 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 79.4% (3433 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 80.5% (3480 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 81.6% (3527 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 82.7% (3573 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 83.5% (3609 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 84.8% (3663 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 85.7% (3704 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 86.7% (3745 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 87.6% (3784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 88.6% (3828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 89.7% (3874 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 90.8% (3922 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 91.8% (3965 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 92.8% (4012 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 94.7% (4093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=954 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.4; 1469 / 4321 (P = 34.00%) round 17]               
[00:00:00] Finding cutoff p=950 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=950 1.0% (44 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 2.9% (125 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 4.7% (201 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 6.4% (276 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 8.2% (355 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 10.0% (430 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 11.8% (510 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 13.7% (590 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 15.3% (663 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 17.1% (738 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 19.0% (819 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 22.5% (971 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 25.0% (1079 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 27.1% (1169 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 28.6% (1235 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 30.4% (1313 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 32.1% (1388 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 33.9% (1463 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 35.3% (1524 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 36.8% (1591 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 38.4% (1659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 39.7% (1717 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 41.9% (1812 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 43.3% (1869 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 44.5% (1923 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 45.8% (1979 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 47.2% (2038 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 48.5% (2094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 49.8% (2151 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 51.0% (2202 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 52.3% (2259 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 53.3% (2302 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 54.3% (2347 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 55.3% (2389 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 56.5% (2442 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 57.6% (2490 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 58.7% (2537 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 59.7% (2581 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 60.5% (2614 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 61.4% (2655 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 62.2% (2689 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 63.6% (2747 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 64.3% (2780 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 65.3% (2821 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 66.3% (2863 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 67.5% (2917 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 68.4% (2955 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 69.5% (3004 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 70.3% (3037 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 71.4% (3084 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 72.5% (3131 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 73.6% (3180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 75.2% (3249 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 75.8% (3277 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 76.5% (3304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 77.5% (3349 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 78.6% (3397 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 79.4% (3432 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 80.5% (3477 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 81.6% (3525 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 82.6% (3570 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 83.7% (3616 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 84.7% (3659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 85.7% (3702 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 86.7% (3747 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 87.6% (3787 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 88.7% (3831 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 89.6% (3872 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 90.6% (3916 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 91.8% (3966 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 92.9% (4014 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 94.7% (4094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 95.8% (4138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 96.8% (4183 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=950 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 95.0; 1585 / 4321 (P = 36.68%) round 18]               
[00:00:00] Finding cutoff p=945 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=945 1.1% (47 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 2.7% (115 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 4.4% (191 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 6.0% (260 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 7.9% (340 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 9.4% (408 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 11.2% (483 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 13.0% (561 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 14.6% (630 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 16.2% (700 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 17.9% (774 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 20.0% (863 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 21.9% (945 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 25.9% (1120 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 27.5% (1189 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 29.5% (1276 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 31.2% (1348 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 32.9% (1422 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 34.3% (1484 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 35.7% (1544 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 37.1% (1602 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 38.4% (1661 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 40.6% (1754 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 42.0% (1816 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 43.4% (1874 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 44.5% (1925 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 45.9% (1983 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 47.2% (2040 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 48.3% (2089 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 49.6% (2142 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 50.8% (2197 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 52.0% (2246 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 53.4% (2306 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 54.6% (2361 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 55.7% (2405 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 56.7% (2448 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 57.7% (2494 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 58.9% (2544 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 59.8% (2583 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 60.6% (2619 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 61.5% (2657 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 62.4% (2696 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 63.2% (2732 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 64.2% (2772 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 65.6% (2834 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 66.4% (2871 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 67.3% (2909 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 68.3% (2950 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 69.5% (3005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 70.3% (3036 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 71.4% (3086 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 72.4% (3128 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 73.6% (3179 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 74.4% (3213 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 75.4% (3259 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 76.7% (3314 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 77.6% (3354 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 78.6% (3395 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 79.4% (3433 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 80.5% (3477 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 81.6% (3526 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 82.6% (3570 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 83.7% (3615 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 84.7% (3659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 85.7% (3702 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 86.6% (3742 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 87.7% (3789 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 88.6% (3828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 89.7% (3878 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 90.7% (3918 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 91.6% (3960 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 92.7% (4007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 94.7% (4093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 95.7% (4136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 96.7% (4180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=945 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.5; 1731 / 4321 (P = 40.06%) round 19]               
[00:00:00] Finding cutoff p=942 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=942 1.7% (75 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 3.5% (151 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 5.2% (224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 6.9% (299 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 8.6% (372 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 10.3% (446 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 12.1% (522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 14.0% (603 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 15.6% (672 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 17.6% (760 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 19.7% (850 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 21.7% (939 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 23.6% (1021 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 26.2% (1133 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 29.1% (1257 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 31.0% (1340 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 32.5% (1404 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 34.2% (1476 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 35.5% (1533 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 36.8% (1591 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 38.3% (1653 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 40.5% (1750 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 41.9% (1810 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 43.2% (1867 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 44.5% (1922 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 45.8% (1979 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 47.2% (2041 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 48.5% (2094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 49.7% (2148 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 51.1% (2206 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 52.0% (2248 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 53.4% (2308 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 54.8% (2368 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 55.7% (2405 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 56.7% (2450 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 57.8% (2497 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 58.9% (2543 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 59.8% (2586 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 60.7% (2624 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 61.6% (2661 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 62.4% (2697 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 63.2% (2729 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 64.6% (2793 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 65.5% (2830 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 66.4% (2867 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 67.2% (2904 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 68.6% (2964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 69.4% (2998 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 70.4% (3041 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 71.5% (3091 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 72.5% (3132 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 73.3% (3169 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 74.4% (3216 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 75.4% (3259 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 76.4% (3301 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 77.6% (3354 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 78.5% (3392 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 79.7% (3445 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 80.5% (3478 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 81.6% (3525 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 82.6% (3568 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 83.7% (3618 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 84.7% (3659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 85.6% (3697 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 86.7% (3747 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 87.7% (3788 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 88.6% (3828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 89.7% (3876 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 90.7% (3919 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 91.7% (3961 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 92.7% (4007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 93.8% (4051 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 94.8% (4097 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 96.7% (4180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=942 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 94.2; 1801 / 4321 (P = 41.68%) round 20]               
[00:00:00] Finding cutoff p=937 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=937 1.1% (46 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 2.6% (114 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 4.2% (182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 5.7% (248 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 7.2% (313 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 8.9% (385 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 10.5% (454 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 12.2% (528 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 13.7% (591 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 15.4% (666 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 17.2% (743 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 19.1% (824 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 21.2% (917 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 23.2% (1001 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 25.6% (1108 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 27.7% (1197 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 30.8% (1331 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 32.4% (1401 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 33.8% (1462 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 35.2% (1522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 36.6% (1580 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 38.0% (1641 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 39.9% (1722 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 41.4% (1791 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 42.7% (1844 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 43.8% (1894 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 44.9% (1941 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 46.3% (1999 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 47.5% (2052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 48.6% (2100 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 49.8% (2152 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 50.8% (2197 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 51.9% (2241 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 52.6% (2273 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 53.8% (2326 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 54.8% (2368 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 55.8% (2409 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 56.7% (2452 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 57.8% (2498 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 58.7% (2538 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 59.8% (2583 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 60.8% (2626 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 61.7% (2666 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 62.6% (2705 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 63.4% (2741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 64.4% (2782 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 65.2% (2818 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 66.2% (2860 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 67.4% (2914 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 68.3% (2953 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 69.6% (3007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 70.4% (3040 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 71.6% (3093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 72.6% (3139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 73.6% (3179 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 74.3% (3212 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 75.7% (3270 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 76.6% (3311 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 77.6% (3354 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 78.5% (3392 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 79.6% (3440 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 80.7% (3487 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 81.5% (3523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 82.5% (3564 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 83.8% (3622 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 84.5% (3652 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 85.7% (3701 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 86.6% (3744 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 87.6% (3784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 88.8% (3835 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 89.6% (3873 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 90.7% (3921 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 91.7% (3962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 92.7% (4006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 94.7% (4093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 96.8% (4183 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=937 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.7; 1925 / 4321 (P = 44.55%) round 21]               
[00:00:00] Finding cutoff p=932 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=932 1.1% (47 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 2.5% (107 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 4.0% (172 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 5.6% (242 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 7.0% (304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 8.5% (367 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 10.3% (444 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 12.1% (522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 13.7% (590 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 15.4% (664 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 17.1% (740 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 19.1% (826 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 21.1% (913 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 23.0% (994 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 25.2% (1087 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 28.6% (1236 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 30.6% (1323 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 32.2% (1391 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 33.6% (1453 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 35.1% (1517 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 36.4% (1572 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 37.8% (1634 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 40.1% (1731 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 41.3% (1783 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 42.5% (1838 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 43.7% (1888 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 45.0% (1945 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 46.3% (1999 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 47.4% (2048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 48.5% (2097 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 49.7% (2148 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 50.8% (2193 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 51.8% (2239 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 52.8% (2281 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 54.2% (2341 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 55.5% (2396 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 56.6% (2447 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 57.8% (2496 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 58.6% (2531 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 59.2% (2558 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 60.1% (2599 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 61.1% (2641 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 62.4% (2696 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 63.3% (2735 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 64.2% (2774 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 65.4% (2824 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 66.3% (2863 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 67.5% (2917 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 68.3% (2952 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 69.4% (3000 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 70.6% (3052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 71.3% (3082 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 72.4% (3127 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 73.4% (3171 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 74.6% (3224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 75.6% (3266 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 76.7% (3314 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 77.6% (3354 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 78.5% (3392 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 79.6% (3441 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 80.4% (3476 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 81.5% (3520 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 82.6% (3569 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 83.8% (3619 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 84.6% (3654 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 85.7% (3702 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 86.6% (3743 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 87.6% (3786 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 88.8% (3836 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 89.7% (3874 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 90.7% (3920 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 92.0% (3977 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 92.8% (4010 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 93.8% (4051 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 94.7% (4092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 96.7% (4180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=932 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 93.2; 2062 / 4321 (P = 47.72%) round 22]               
[00:00:00] Finding cutoff p=928 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=928 1.0% (45 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 2.5% (106 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 4.0% (172 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 5.3% (229 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 6.9% (299 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 8.5% (367 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 10.0% (430 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 11.3% (489 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 13.0% (560 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 14.4% (623 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 16.0% (693 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 18.2% (788 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 20.1% (867 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 22.0% (952 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 24.0% (1037 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 28.0% (1212 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 30.1% (1300 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 31.6% (1366 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 32.8% (1417 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 34.2% (1479 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 35.7% (1541 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 37.0% (1598 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 39.0% (1685 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 40.2% (1735 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 41.4% (1787 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 42.8% (1848 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 44.3% (1915 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 45.4% (1962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 46.6% (2015 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 47.8% (2064 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 48.9% (2113 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 50.0% (2160 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 51.1% (2207 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 52.1% (2252 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 53.2% (2298 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 54.1% (2339 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 55.5% (2400 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 56.8% (2455 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 57.7% (2495 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 58.4% (2525 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 59.2% (2560 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 60.5% (2614 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 61.4% (2655 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 62.2% (2689 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 63.1% (2728 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 64.4% (2784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 65.5% (2832 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 66.4% (2870 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 67.4% (2912 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 68.5% (2959 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 69.3% (2996 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 70.3% (3036 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 71.9% (3105 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 72.8% (3147 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 73.4% (3172 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 74.4% (3213 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 75.6% (3265 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 76.5% (3305 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 77.7% (3357 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 78.5% (3393 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 79.4% (3432 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 80.5% (3480 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 81.5% (3522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 82.6% (3571 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 83.7% (3617 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 84.6% (3655 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 85.7% (3704 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 86.6% (3744 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 87.6% (3784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 88.8% (3835 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 89.7% (3876 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 90.7% (3921 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 92.0% (3975 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 92.7% (4006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 94.8% (4095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=928 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.8; 2156 / 4321 (P = 49.90%) round 23]               
[00:00:00] Finding cutoff p=924 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=924 1.6% (68 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 3.1% (134 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 4.5% (195 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 5.9% (257 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 7.5% (325 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 8.9% (385 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 10.1% (435 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 11.6% (500 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 13.2% (569 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 14.3% (618 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 16.2% (698 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 18.0% (776 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 19.7% (851 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 21.6% (932 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 26.1% (1127 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 28.0% (1210 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 29.8% (1286 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 31.2% (1347 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 32.6% (1407 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 33.7% (1455 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 35.0% (1512 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 37.4% (1615 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 38.6% (1669 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 39.9% (1724 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 41.2% (1782 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 42.9% (1852 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 43.9% (1897 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 45.1% (1949 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 45.8% (1981 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 47.3% (2044 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 48.4% (2093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 49.5% (2141 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 50.6% (2187 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 52.3% (2261 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 53.3% (2303 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 54.2% (2341 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 55.2% (2384 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 56.2% (2429 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 57.3% (2475 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 58.2% (2516 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 59.4% (2568 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 60.3% (2605 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 61.2% (2644 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 62.1% (2685 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 63.5% (2742 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 64.3% (2780 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 65.7% (2839 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 66.5% (2872 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 67.2% (2904 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 68.5% (2960 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 69.7% (3012 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 70.6% (3049 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 71.6% (3092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 72.5% (3132 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 73.3% (3169 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 74.6% (3223 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 75.5% (3264 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 76.4% (3302 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 77.6% (3352 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 78.4% (3389 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 79.7% (3442 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 80.5% (3478 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 81.5% (3522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 82.5% (3564 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 83.6% (3614 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 84.6% (3656 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 85.7% (3704 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 86.7% (3745 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 87.7% (3791 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 88.7% (3833 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 89.9% (3884 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 90.7% (3918 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 91.9% (3971 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 92.8% (4012 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 93.8% (4052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 94.8% (4095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=924 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.4; 2248 / 4321 (P = 52.02%) round 24]               
[00:00:00] Finding cutoff p=921 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=921 1.5% (63 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 3.4% (149 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 4.7% (202 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 5.8% (252 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 7.3% (315 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 8.8% (381 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 10.2% (441 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 11.8% (509 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 13.3% (576 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 14.8% (638 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 16.6% (719 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 18.2% (787 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 20.2% (871 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 23.0% (993 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 26.3% (1138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 28.3% (1221 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 29.9% (1293 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 31.1% (1344 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 32.4% (1402 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 33.7% (1455 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 35.1% (1517 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 37.1% (1602 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 38.3% (1657 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 39.7% (1715 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 40.9% (1767 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 42.6% (1839 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 43.6% (1884 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 44.7% (1932 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 45.6% (1972 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 46.8% (2023 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 47.8% (2067 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 49.0% (2118 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 50.0% (2161 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 51.6% (2228 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 52.5% (2268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 53.1% (2296 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 54.2% (2342 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 55.5% (2396 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 56.3% (2434 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 57.4% (2480 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 58.3% (2517 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 59.2% (2557 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 60.4% (2609 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 61.1% (2642 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 62.4% (2695 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 63.2% (2732 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 64.4% (2782 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 65.3% (2822 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 66.5% (2873 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 67.4% (2912 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 68.6% (2964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 69.3% (2995 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 70.6% (3051 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 71.5% (3088 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 72.5% (3134 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 73.5% (3176 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 74.5% (3219 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 75.4% (3257 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 76.6% (3311 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 77.4% (3346 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 78.6% (3395 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 79.5% (3434 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 80.7% (3485 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 81.7% (3530 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 82.5% (3565 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 83.5% (3610 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 84.6% (3657 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 85.8% (3706 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 86.6% (3742 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 87.6% (3787 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 88.6% (3829 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 89.7% (3874 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 90.7% (3918 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 91.8% (3966 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 92.7% (4005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 94.7% (4092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 96.7% (4180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=921 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 92.1; 2322 / 4321 (P = 53.74%) round 25]               
[00:00:00] Finding cutoff p=918 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=918 1.5% (63 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 3.1% (132 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 4.5% (193 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 5.9% (255 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 7.4% (320 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 8.7% (376 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 10.1% (435 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 11.4% (491 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 12.9% (557 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 14.1% (608 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 15.7% (680 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 19.0% (820 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 20.6% (889 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 22.4% (970 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 25.9% (1121 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 28.1% (1216 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 29.7% (1282 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 31.0% (1340 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 32.5% (1405 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 33.8% (1459 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 35.1% (1517 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 37.3% (1610 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 38.5% (1662 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 39.7% (1716 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 40.9% (1769 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 42.7% (1843 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 43.7% (1889 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 44.7% (1933 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 45.8% (1978 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 47.0% (2032 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 48.0% (2073 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 49.3% (2129 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 50.3% (2172 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 51.6% (2229 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 52.4% (2264 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 53.3% (2304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 54.2% (2341 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 55.1% (2380 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 56.1% (2426 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 57.3% (2478 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 58.2% (2514 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 59.5% (2570 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 60.4% (2610 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 61.3% (2649 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 62.6% (2704 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 63.2% (2733 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 64.2% (2774 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 65.3% (2821 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 66.4% (2867 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 67.2% (2905 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 68.3% (2953 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 69.8% (3014 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 71.3% (3080 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 72.6% (3139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 73.6% (3182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 74.4% (3214 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 75.5% (3263 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 76.4% (3303 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 77.5% (3349 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 78.6% (3395 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 79.7% (3442 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 80.7% (3485 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 81.5% (3521 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 82.6% (3567 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 83.5% (3609 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 84.5% (3653 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 85.7% (3701 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 86.6% (3741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 87.9% (3798 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 88.6% (3828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 89.6% (3873 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 90.7% (3918 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 91.7% (3962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 92.7% (4007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 94.7% (4094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=918 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.8; 2381 / 4321 (P = 55.10%) round 26]               
[00:00:00] Finding cutoff p=915 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=915 1.4% (62 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 2.8% (121 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 4.2% (182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 5.3% (231 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 6.8% (293 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 8.3% (357 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 9.8% (422 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 11.0% (474 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 12.6% (543 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 14.0% (606 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 16.3% (704 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 18.1% (784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 19.7% (851 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 21.5% (928 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 24.6% (1063 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 27.9% (1207 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 29.3% (1268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 30.9% (1335 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 32.3% (1397 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 33.7% (1456 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 35.1% (1516 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 37.3% (1613 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 38.5% (1664 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 39.5% (1707 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 40.8% (1761 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 42.4% (1834 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 43.7% (1890 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 44.5% (1922 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 45.5% (1964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 46.6% (2013 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 47.6% (2058 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 48.6% (2102 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 49.7% (2148 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 51.3% (2217 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 52.3% (2258 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 53.0% (2288 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 54.6% (2361 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 55.6% (2402 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 56.5% (2440 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 57.5% (2485 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 58.4% (2523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 59.2% (2556 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 60.6% (2620 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 61.7% (2665 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 62.5% (2699 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 63.3% (2736 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 64.2% (2772 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 65.4% (2827 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 66.2% (2862 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 67.4% (2914 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 68.5% (2958 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 69.5% (3005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 71.0% (3067 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 71.9% (3108 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 72.6% (3137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 73.6% (3180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 74.6% (3224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 75.4% (3260 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 76.4% (3300 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 77.6% (3355 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 78.5% (3393 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 79.4% (3433 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 80.5% (3478 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 81.6% (3527 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 82.7% (3572 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 83.7% (3618 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 84.7% (3661 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 85.6% (3699 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 86.7% (3747 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 87.7% (3789 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 88.8% (3836 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 89.8% (3882 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 90.7% (3920 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 91.8% (3965 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 92.7% (4004 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 93.8% (4052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 94.8% (4096 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 96.8% (4183 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=915 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.5; 2439 / 4321 (P = 56.45%) round 27]               
[00:00:00] Finding cutoff p=911 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=911 1.5% (64 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 3.1% (133 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 4.5% (194 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 5.6% (243 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 7.2% (312 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 8.5% (369 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 9.8% (425 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 11.1% (479 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 13.7% (594 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 15.1% (653 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 16.6% (716 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 18.4% (797 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 20.2% (871 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 21.7% (936 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 25.0% (1079 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 27.8% (1203 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 29.3% (1266 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 30.7% (1325 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 32.1% (1386 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 33.4% (1443 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 34.4% (1488 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 36.5% (1577 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 37.8% (1632 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 39.1% (1688 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 39.9% (1726 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 41.3% (1785 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 42.9% (1853 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 44.1% (1906 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 44.9% (1941 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 46.1% (1993 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 47.2% (2039 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 48.3% (2085 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 49.6% (2143 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 50.7% (2190 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 51.5% (2227 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 52.5% (2269 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 53.3% (2301 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 54.4% (2349 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 55.2% (2385 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 56.4% (2437 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 57.2% (2472 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 58.9% (2543 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 59.7% (2579 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 60.5% (2616 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 61.4% (2655 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 62.3% (2690 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 63.4% (2738 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 64.4% (2783 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 65.3% (2821 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 66.2% (2860 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 67.3% (2908 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 68.8% (2971 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 69.9% (3020 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 70.4% (3041 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 71.5% (3091 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 72.5% (3133 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 73.5% (3177 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 74.5% (3219 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 75.6% (3265 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 76.4% (3301 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 77.5% (3350 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 78.7% (3399 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 79.5% (3435 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 80.5% (3477 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 81.6% (3526 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 82.5% (3564 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 83.7% (3615 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 84.7% (3660 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 85.7% (3702 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 87.1% (3765 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 87.8% (3793 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 88.6% (3828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 89.7% (3876 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 91.1% (3935 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 91.8% (3967 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 92.9% (4013 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 93.8% (4052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 94.8% (4096 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=911 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 91.1; 2499 / 4321 (P = 57.83%) round 28]               
[00:00:00] Finding cutoff p=908 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=908 1.4% (62 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 2.9% (125 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 4.3% (184 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 5.1% (221 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 6.5% (283 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 8.7% (374 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 9.8% (423 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 11.3% (488 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 12.3% (533 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 13.8% (595 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 15.5% (671 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 16.8% (728 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 18.4% (797 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 20.0% (864 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 23.0% (995 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 26.0% (1122 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 27.8% (1202 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 29.0% (1252 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 30.3% (1309 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 31.7% (1368 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 32.9% (1423 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 35.2% (1522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 36.3% (1567 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 37.5% (1620 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 38.8% (1675 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 40.3% (1742 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 41.8% (1807 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 43.0% (1856 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 43.8% (1894 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 44.9% (1938 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 46.3% (1999 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 47.1% (2037 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 48.6% (2102 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 49.6% (2143 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 50.6% (2187 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 51.8% (2239 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 52.9% (2287 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 53.6% (2316 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 54.6% (2360 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 55.4% (2395 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 56.5% (2440 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 57.4% (2479 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 58.3% (2519 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 59.1% (2553 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 60.4% (2611 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 61.3% (2648 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 62.4% (2695 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 63.3% (2734 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 64.4% (2784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 65.5% (2832 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 66.5% (2873 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 67.3% (2907 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 68.7% (2967 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 69.7% (3013 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 70.5% (3045 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 71.5% (3089 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 72.5% (3132 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 73.5% (3177 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 74.4% (3215 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 75.6% (3267 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 76.4% (3301 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 77.4% (3345 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 78.5% (3394 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 80.0% (3458 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 80.6% (3484 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 81.6% (3524 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 82.5% (3566 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 83.6% (3612 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 84.7% (3661 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 85.6% (3697 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 87.0% (3760 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 87.6% (3787 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 88.7% (3831 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 89.8% (3880 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 91.0% (3931 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 91.7% (3964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 92.8% (4010 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 94.7% (4093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 95.7% (4136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 97.8% (4225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=908 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.8; 2572 / 4321 (P = 59.52%) round 29]               
[00:00:00] Finding cutoff p=904 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=904 1.5% (65 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 4.0% (175 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 5.4% (234 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 6.8% (292 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 8.0% (347 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 9.5% (412 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 10.6% (457 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 12.0% (519 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 13.3% (574 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 14.4% (622 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 16.1% (696 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 17.4% (753 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 19.3% (835 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 20.9% (905 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 24.2% (1047 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 26.9% (1161 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 28.4% (1228 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 29.7% (1284 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 30.8% (1333 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 32.3% (1394 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 33.3% (1438 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 35.8% (1545 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 37.0% (1600 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 37.9% (1639 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 39.2% (1692 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 40.7% (1760 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 41.8% (1807 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 43.0% (1856 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 43.9% (1897 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 45.2% (1954 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 46.2% (1996 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 47.1% (2037 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 48.6% (2102 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 49.7% (2147 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 50.6% (2186 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 51.7% (2234 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 52.8% (2282 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 53.7% (2319 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 54.5% (2354 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 55.0% (2377 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 56.2% (2429 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 57.3% (2477 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 58.7% (2538 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 59.7% (2579 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 60.4% (2608 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 61.5% (2656 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 62.4% (2697 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 63.5% (2743 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 64.5% (2786 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 65.4% (2828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 66.4% (2867 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 67.6% (2919 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 68.9% (2977 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 69.6% (3007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 70.4% (3040 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 71.6% (3093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 72.5% (3131 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 73.4% (3171 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 74.6% (3222 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 75.5% (3261 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 76.6% (3308 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 77.6% (3354 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 79.0% (3415 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 79.8% (3446 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 80.6% (3481 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 81.6% (3524 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 82.6% (3571 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 83.7% (3615 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 84.7% (3659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 85.6% (3698 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 86.6% (3741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 87.6% (3787 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 88.6% (3828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 89.7% (3878 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 90.8% (3922 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 91.8% (3965 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 92.7% (4006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 93.8% (4053 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 94.7% (4094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 97.8% (4226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=904 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 90.4; 2654 / 4321 (P = 61.42%) round 30]               
[00:00:00] Finding cutoff p=899 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=899 1.0% (44 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 2.1% (90 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 3.5% (151 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 4.6% (197 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 5.6% (244 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 6.8% (295 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 7.8% (339 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 9.3% (404 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 10.5% (452 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 12.0% (518 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 13.1% (566 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 14.6% (630 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 15.8% (683 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 17.1% (741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 18.6% (805 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 21.0% (907 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 24.1% (1041 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 26.8% (1160 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 28.0% (1210 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 29.2% (1263 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 30.2% (1303 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 31.4% (1356 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 33.3% (1439 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 34.4% (1486 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 35.7% (1541 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 36.7% (1584 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 37.7% (1630 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 38.9% (1681 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 40.3% (1741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 41.3% (1783 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 42.4% (1833 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 43.5% (1881 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 44.4% (1918 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 45.1% (1950 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 46.3% (2001 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 47.4% (2047 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 48.4% (2093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 49.4% (2135 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 51.9% (2241 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 52.7% (2276 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 53.3% (2304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 54.1% (2338 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 55.1% (2382 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 56.1% (2425 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 57.3% (2476 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 58.4% (2523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 59.5% (2572 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 60.9% (2632 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 61.6% (2662 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 62.3% (2692 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 63.6% (2749 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 64.3% (2778 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 65.3% (2823 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 66.5% (2874 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 67.6% (2920 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 68.3% (2951 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 69.3% (2996 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 70.7% (3055 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 71.3% (3082 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 72.3% (3125 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 73.6% (3182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 74.4% (3213 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 75.4% (3260 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 76.6% (3310 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 77.5% (3347 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 78.8% (3405 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 79.5% (3435 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 80.5% (3477 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 81.5% (3523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 82.6% (3571 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 83.7% (3616 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 84.7% (3659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 85.7% (3701 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 86.7% (3746 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 87.6% (3785 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 88.7% (3833 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 89.7% (3875 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 90.7% (3920 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 91.8% (3967 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 92.7% (4005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 94.8% (4095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 97.8% (4225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=899 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 89.9; 2759 / 4321 (P = 63.85%) round 31]               
[00:00:00] Finding cutoff p=889 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=889 1.1% (47 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 3.0% (128 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 4.3% (184 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 5.4% (234 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 6.6% (285 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 7.7% (333 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 9.2% (396 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 10.3% (446 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 11.6% (501 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 13.4% (578 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 14.3% (619 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 15.6% (673 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 17.1% (740 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 18.7% (808 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 21.2% (916 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 25.0% (1082 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 26.6% (1148 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 28.2% (1220 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 29.1% (1259 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 30.5% (1319 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 32.6% (1407 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 33.5% (1446 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 34.8% (1503 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 36.0% (1555 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 37.5% (1620 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 38.6% (1669 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 40.0% (1727 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 40.9% (1766 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 42.1% (1817 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 43.2% (1867 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 44.2% (1912 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 46.0% (1989 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 47.5% (2053 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 48.3% (2085 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 49.1% (2120 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 50.2% (2167 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 51.6% (2231 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 52.4% (2265 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 53.3% (2301 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 54.5% (2356 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 55.2% (2386 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 56.5% (2443 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 57.7% (2492 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 58.5% (2527 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 59.1% (2555 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 60.4% (2611 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 61.2% (2645 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 62.4% (2696 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 63.5% (2742 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 64.4% (2782 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 65.8% (2842 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 66.7% (2881 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 67.3% (2908 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 68.5% (2960 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 69.6% (3009 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 70.7% (3053 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 71.6% (3093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 72.4% (3129 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 73.6% (3180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 74.5% (3221 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 75.7% (3270 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 76.6% (3312 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 77.5% (3347 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 78.8% (3405 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 79.6% (3441 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 80.6% (3481 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 81.5% (3522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 82.8% (3578 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 83.5% (3610 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 84.7% (3659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 86.0% (3715 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 86.6% (3744 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 87.8% (3795 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 88.7% (3831 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 89.6% (3872 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 91.1% (3936 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 91.7% (3962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 92.7% (4007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 94.8% (4095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 95.8% (4138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 96.7% (4180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 97.8% (4226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=889 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 88.9; 2806 / 4321 (P = 64.94%) round 32]               
[00:00:00] Finding cutoff p=878 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=878 1.4% (61 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 3.0% (131 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 4.5% (193 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 5.6% (240 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 7.0% (302 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 8.0% (344 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 9.5% (412 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 10.7% (461 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 11.9% (513 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 13.3% (573 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 14.5% (625 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 15.5% (670 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 17.3% (746 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 18.8% (814 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 21.5% (927 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 24.6% (1062 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 26.5% (1147 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 27.9% (1204 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 29.0% (1251 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 30.0% (1295 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 31.4% (1356 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 33.0% (1425 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 34.1% (1475 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 35.1% (1518 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 36.1% (1558 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 37.8% (1632 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 38.9% (1683 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 41.3% (1783 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 42.6% (1841 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 43.4% (1876 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 44.7% (1931 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 45.8% (1981 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 48.1% (2080 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 49.2% (2125 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 50.4% (2176 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 51.8% (2239 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 52.6% (2272 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 53.2% (2300 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 54.1% (2336 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 55.1% (2380 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 56.4% (2435 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 57.3% (2478 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 58.2% (2516 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 59.4% (2565 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 60.7% (2623 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 61.4% (2651 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 62.2% (2686 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 63.6% (2746 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 64.3% (2780 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 65.2% (2817 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 66.4% (2868 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 67.4% (2914 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 68.8% (2971 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 69.6% (3007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 70.3% (3038 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 71.5% (3089 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 72.5% (3131 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 73.6% (3180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 74.6% (3222 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 75.5% (3263 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 76.4% (3303 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 77.4% (3346 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 78.9% (3411 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 79.5% (3437 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 80.5% (3479 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 81.5% (3520 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 82.5% (3565 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 83.7% (3618 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 84.7% (3662 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 85.5% (3696 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 86.7% (3745 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 87.6% (3784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 88.8% (3835 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 89.6% (3872 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 90.7% (3921 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 91.7% (3963 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 92.8% (4008 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 94.8% (4096 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 95.8% (4138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=878 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 87.8; 2876 / 4321 (P = 66.56%) round 33]               
[00:00:00] Finding cutoff p=868 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=868 1.4% (62 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 3.2% (139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 4.5% (196 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 5.7% (246 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 7.1% (305 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 8.3% (360 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 9.6% (416 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 10.7% (462 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 12.2% (527 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 13.5% (582 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 15.0% (649 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 16.3% (704 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 18.1% (783 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 19.5% (844 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 22.4% (967 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 25.9% (1119 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 27.4% (1182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 28.4% (1228 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 29.8% (1289 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 31.2% (1346 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 32.1% (1388 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 34.1% (1473 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 35.2% (1521 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 36.7% (1586 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 38.6% (1667 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 39.6% (1713 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 40.6% (1755 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 41.7% (1802 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 42.9% (1852 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 43.9% (1895 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 45.0% (1946 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 46.5% (2008 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 47.3% (2042 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 48.1% (2080 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 49.2% (2128 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 50.5% (2181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 51.1% (2207 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 52.0% (2245 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 53.2% (2300 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 54.2% (2343 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 55.3% (2389 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 56.2% (2429 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 57.0% (2465 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 58.3% (2521 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 59.6% (2574 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 60.1% (2596 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 61.3% (2648 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 62.2% (2689 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 63.4% (2738 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 65.0% (2808 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 65.9% (2847 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 66.6% (2876 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 67.5% (2915 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 68.5% (2959 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 69.4% (2997 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 70.6% (3052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 71.5% (3089 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 72.5% (3133 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 73.3% (3168 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 74.7% (3229 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 75.6% (3266 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 76.4% (3300 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 77.9% (3364 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 78.6% (3395 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 79.6% (3441 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 80.7% (3487 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 81.5% (3522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 82.5% (3566 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 83.7% (3618 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 84.7% (3658 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 86.0% (3716 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 86.6% (3740 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 87.7% (3790 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 88.6% (3828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 90.1% (3894 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 90.9% (3928 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 91.6% (3960 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 92.8% (4009 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 94.7% (4094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 95.8% (4138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=868 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 86.8; 2979 / 4321 (P = 68.94%) round 34]               
[00:00:00] Finding cutoff p=857 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=857 1.4% (60 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 2.9% (127 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 4.0% (172 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 5.3% (228 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 6.5% (281 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 7.7% (332 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 9.3% (402 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 10.3% (443 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 11.6% (500 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 12.8% (551 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 14.2% (614 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 15.5% (670 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 17.5% (757 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 19.0% (820 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 21.5% (929 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 24.9% (1077 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 27.1% (1170 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 28.2% (1218 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 29.9% (1294 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 31.0% (1340 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 32.2% (1391 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 33.9% (1465 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 35.1% (1515 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 35.8% (1546 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 37.0% (1599 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 38.2% (1651 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 39.4% (1704 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 40.5% (1750 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 41.7% (1801 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 42.7% (1846 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 43.8% (1893 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 45.0% (1944 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 46.4% (2005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 47.4% (2046 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 48.0% (2076 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 49.2% (2127 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 50.8% (2196 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 51.5% (2226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 52.3% (2262 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 53.0% (2288 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 54.1% (2339 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 55.4% (2394 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 56.4% (2436 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 57.3% (2474 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 58.1% (2511 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 59.9% (2590 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 60.7% (2621 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 61.2% (2645 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 62.5% (2700 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 63.4% (2739 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 64.5% (2787 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 65.8% (2842 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 66.8% (2885 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 67.4% (2912 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 68.5% (2961 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 69.4% (2997 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 70.4% (3040 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 71.4% (3085 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 72.6% (3136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 73.6% (3179 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 74.4% (3213 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 75.6% (3268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 76.6% (3311 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 77.4% (3346 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 78.6% (3395 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 79.6% (3438 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 80.7% (3486 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 81.5% (3522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 82.6% (3569 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 83.6% (3614 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 84.7% (3659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 85.7% (3701 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 86.6% (3740 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 87.8% (3792 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 88.7% (3833 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 89.9% (3883 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 90.7% (3918 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 91.7% (3963 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 92.8% (4008 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 94.7% (4094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 95.7% (4136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 96.7% (4180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 97.8% (4226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=857 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 85.7; 3088 / 4321 (P = 71.46%) round 35]               
[00:00:00] Finding cutoff p=846 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=846 1.3% (58 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 2.9% (124 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 4.0% (173 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 5.2% (223 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 6.7% (288 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 7.7% (331 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 9.2% (397 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 10.3% (443 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 11.3% (488 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 12.6% (543 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 14.2% (612 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 15.4% (665 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 17.7% (765 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 19.2% (831 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 22.4% (968 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 25.4% (1097 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 26.7% (1154 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 27.8% (1200 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 28.8% (1246 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 29.8% (1289 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 31.0% (1339 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 33.0% (1426 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 33.9% (1466 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 34.8% (1505 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 35.9% (1550 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 37.0% (1598 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 38.2% (1649 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 39.4% (1702 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 40.5% (1748 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 41.4% (1787 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 42.4% (1830 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 43.5% (1878 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 44.2% (1908 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 45.2% (1953 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 46.1% (1991 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 46.9% (2027 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 48.1% (2080 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 49.1% (2121 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 50.1% (2164 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 51.0% (2204 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 52.2% (2257 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 53.1% (2296 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 54.2% (2343 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 55.3% (2389 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 56.4% (2438 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 57.0% (2464 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 58.2% (2515 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 59.5% (2569 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 60.6% (2617 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 61.2% (2643 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 62.3% (2690 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 63.6% (2750 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 65.0% (2808 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 65.8% (2845 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 66.6% (2876 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 67.3% (2910 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 68.4% (2957 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 69.3% (2993 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 70.6% (3050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 71.3% (3080 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 72.4% (3128 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 73.6% (3180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 74.5% (3221 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 75.4% (3256 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 76.9% (3321 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 77.5% (3350 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 78.5% (3394 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 79.5% (3437 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 80.5% (3477 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 81.6% (3524 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 82.6% (3569 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 83.6% (3612 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 84.7% (3661 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 85.9% (3710 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 86.7% (3748 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 87.6% (3786 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 88.7% (3832 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 90.0% (3887 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 90.7% (3921 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 91.7% (3964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 92.7% (4005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 94.8% (4097 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 95.8% (4138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 97.8% (4226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=846 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 84.6; 3192 / 4321 (P = 73.87%) round 36]               
[00:00:00] Finding cutoff p=835 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=835 1.3% (55 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 2.7% (118 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 3.7% (161 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 4.8% (207 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 6.5% (280 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 7.7% (334 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 9.1% (392 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 10.0% (433 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 11.2% (485 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 12.4% (537 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 13.8% (597 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 15.0% (647 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 16.7% (722 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 18.4% (797 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 21.3% (919 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 24.5% (1057 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 26.8% (1160 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 27.7% (1196 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 28.7% (1240 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 29.7% (1282 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 30.8% (1330 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 32.5% (1405 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 33.3% (1441 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 34.3% (1484 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 35.2% (1523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 36.7% (1584 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 37.7% (1629 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 38.8% (1677 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 40.2% (1737 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 41.2% (1779 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 42.2% (1822 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 43.3% (1871 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 44.8% (1935 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 45.6% (1969 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 46.4% (2007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 47.2% (2039 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 48.4% (2090 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 49.5% (2138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 50.0% (2162 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 51.4% (2219 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 52.3% (2258 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 53.5% (2311 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 54.2% (2343 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 55.0% (2376 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 56.3% (2433 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 57.5% (2486 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 58.2% (2514 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 59.2% (2559 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 60.4% (2609 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 61.4% (2653 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 62.9% (2718 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 64.0% (2764 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 64.7% (2794 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 65.4% (2825 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 66.3% (2864 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 67.3% (2906 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 68.5% (2959 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 69.4% (3000 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 70.4% (3044 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 71.3% (3082 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 72.6% (3138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 73.5% (3176 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 74.5% (3217 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 75.4% (3259 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 76.6% (3312 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 77.6% (3355 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 78.6% (3398 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 79.6% (3438 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 80.5% (3477 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 81.5% (3521 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 82.5% (3565 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 83.6% (3613 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 84.7% (3659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 85.6% (3698 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 86.6% (3744 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 87.6% (3786 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 88.6% (3828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 89.8% (3881 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 91.0% (3931 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 91.7% (3961 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 92.7% (4006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 94.7% (4093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 95.8% (4138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 96.8% (4184 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 97.8% (4225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=835 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 83.5; 3289 / 4321 (P = 76.12%) round 37]               
[00:00:00] Finding cutoff p=825 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=825 1.4% (61 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 2.8% (119 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 3.6% (154 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 5.0% (215 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 5.9% (253 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 6.9% (297 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 8.2% (353 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 9.4% (406 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 10.3% (446 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 11.5% (498 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 13.4% (579 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 14.4% (623 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 16.1% (696 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 17.7% (766 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 20.2% (875 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 23.4% (1010 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 25.2% (1088 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 26.4% (1139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 27.3% (1180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 28.4% (1226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 29.4% (1271 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 31.3% (1351 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 32.2% (1390 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 33.2% (1436 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 34.0% (1470 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 35.5% (1532 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 36.3% (1568 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 37.2% (1606 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 37.9% (1637 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 38.9% (1681 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 39.8% (1720 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 41.3% (1784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 43.1% (1863 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 44.0% (1903 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 44.9% (1941 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 46.7% (2018 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 47.5% (2052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 48.4% (2093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 49.2% (2127 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 50.0% (2161 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 51.2% (2211 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 52.2% (2256 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 53.0% (2288 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 54.2% (2342 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 55.5% (2400 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 56.1% (2426 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 57.0% (2465 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 58.4% (2523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 59.1% (2555 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 61.1% (2639 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 61.9% (2675 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 62.6% (2704 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 63.3% (2734 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 64.2% (2776 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 65.3% (2821 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 66.4% (2867 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 67.4% (2911 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 68.3% (2951 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 69.5% (3004 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 70.5% (3045 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 71.5% (3088 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 73.1% (3158 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 74.1% (3201 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 74.7% (3226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 75.4% (3258 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 76.5% (3306 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 77.4% (3346 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 78.6% (3395 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 79.8% (3446 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 80.6% (3484 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 82.1% (3549 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 82.7% (3573 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 83.9% (3624 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 84.7% (3661 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 85.6% (3697 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 86.7% (3746 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 87.9% (3800 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 88.8% (3839 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 89.7% (3877 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 90.8% (3925 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 91.7% (3964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 92.7% (4007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 94.8% (4097 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 96.9% (4185 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 97.8% (4225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=825 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 82.5; 3365 / 4321 (P = 77.88%) round 38]               
[00:00:00] Finding cutoff p=815 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=815 1.3% (58 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 2.7% (117 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 3.4% (148 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 4.9% (211 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 5.7% (247 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 6.8% (294 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 8.0% (347 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 9.3% (400 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 10.6% (456 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 11.9% (514 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 12.9% (558 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 14.3% (620 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 15.6% (673 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 18.0% (776 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 20.6% (888 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 22.2% (960 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 23.9% (1032 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 25.8% (1115 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 26.8% (1159 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 27.9% (1206 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 29.7% (1285 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 30.8% (1329 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 31.9% (1377 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 32.8% (1418 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 34.1% (1474 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 35.2% (1523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 35.9% (1551 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 36.9% (1594 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 38.0% (1643 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 38.9% (1682 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 39.9% (1722 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 41.7% (1804 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 43.3% (1870 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 43.8% (1892 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 44.9% (1940 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 46.0% (1986 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 47.2% (2039 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 48.4% (2093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 49.0% (2119 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 50.5% (2180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 51.2% (2212 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 52.0% (2245 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 53.1% (2295 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 54.2% (2340 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 55.1% (2383 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 56.7% (2451 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 57.6% (2488 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 58.0% (2508 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 59.6% (2574 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 60.8% (2627 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 61.4% (2653 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 62.1% (2684 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 63.2% (2732 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 64.2% (2776 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 65.4% (2824 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 66.3% (2864 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 67.4% (2912 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 68.4% (2956 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 69.5% (3002 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 70.4% (3042 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 71.5% (3089 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 72.4% (3129 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 73.5% (3175 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 74.5% (3220 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 75.6% (3266 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 76.5% (3304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 77.6% (3351 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 78.5% (3390 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 79.6% (3438 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 80.6% (3482 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 81.5% (3522 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 82.6% (3567 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 83.8% (3619 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 84.6% (3656 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 85.9% (3713 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 86.9% (3756 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 87.7% (3788 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 88.9% (3841 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 89.7% (3876 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 90.7% (3918 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 91.8% (3967 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 92.7% (4005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 94.9% (4099 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 95.7% (4136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 96.7% (4180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 97.8% (4225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=815 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 81.5; 3425 / 4321 (P = 79.26%) round 39]               
[00:00:00] Finding cutoff p=806 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=806 1.2% (54 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 2.9% (126 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 3.9% (169 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 5.0% (215 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 5.9% (255 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 6.9% (298 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 7.8% (338 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 9.0% (389 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 9.6% (416 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 10.9% (469 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 12.5% (541 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 14.1% (611 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 15.3% (661 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 18.2% (788 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 20.7% (893 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 22.7% (983 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 24.4% (1056 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 25.5% (1104 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 26.6% (1148 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 28.4% (1226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 30.0% (1297 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 30.8% (1331 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 32.0% (1382 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 32.8% (1418 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 34.0% (1469 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 35.3% (1525 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 36.0% (1557 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 36.9% (1594 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 37.7% (1630 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 39.2% (1692 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 40.7% (1757 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 41.8% (1805 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 43.2% (1867 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 44.8% (1934 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 45.5% (1964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 46.3% (2000 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 47.0% (2033 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 47.9% (2068 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 49.8% (2150 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 50.7% (2189 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 51.4% (2221 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 52.5% (2269 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 53.3% (2302 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 54.3% (2348 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 55.4% (2394 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 56.5% (2440 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 57.2% (2472 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 58.1% (2509 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 59.6% (2574 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 60.6% (2619 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 61.5% (2656 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 62.1% (2685 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 63.2% (2730 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 64.2% (2776 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 65.5% (2832 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 66.3% (2864 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 67.3% (2907 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 68.2% (2948 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 69.4% (2998 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 70.9% (3062 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 72.0% (3110 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 72.5% (3133 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 73.4% (3172 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 74.7% (3227 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 75.6% (3265 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 76.6% (3310 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 77.5% (3347 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 78.4% (3388 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 79.4% (3432 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 80.6% (3483 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 81.7% (3530 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 82.7% (3574 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 83.6% (3611 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 84.6% (3654 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 86.2% (3724 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 87.1% (3764 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 87.9% (3798 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 88.7% (3832 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 89.7% (3875 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 90.7% (3920 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 91.7% (3961 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 92.7% (4005 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 93.8% (4052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 94.9% (4099 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 96.8% (4183 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=806 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 80.6; 3476 / 4321 (P = 80.44%) round 40]               
[00:00:00] Finding cutoff p=795 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=795 1.2% (54 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 2.5% (110 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 3.1% (132 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 4.6% (199 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 5.2% (223 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 6.3% (271 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 7.5% (322 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 8.5% (368 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 9.8% (423 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 10.6% (458 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 12.0% (519 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 13.9% (602 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 14.9% (644 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 17.2% (745 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 19.9% (858 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 22.1% (954 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 23.7% (1025 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 24.9% (1077 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 25.7% (1111 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 26.6% (1150 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 28.1% (1215 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 28.9% (1250 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 29.9% (1293 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 30.7% (1328 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 32.3% (1397 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 33.2% (1434 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 34.3% (1481 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 35.1% (1518 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 35.8% (1548 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 36.9% (1596 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 37.8% (1635 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 39.1% (1691 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 39.9% (1723 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 40.9% (1766 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 42.0% (1813 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 43.6% (1882 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 44.4% (1917 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 45.1% (1947 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 46.0% (1986 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 47.4% (2048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 47.9% (2068 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 49.3% (2130 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 50.3% (2173 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 51.1% (2209 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 52.4% (2263 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 53.3% (2304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 54.4% (2352 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 55.1% (2381 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 56.2% (2428 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 57.0% (2465 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 58.2% (2516 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 59.9% (2590 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 60.6% (2618 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 61.4% (2655 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 62.2% (2688 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 63.3% (2737 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 64.2% (2776 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 65.2% (2816 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 66.2% (2860 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 67.2% (2905 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 68.2% (2949 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 69.8% (3017 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 70.9% (3065 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 71.4% (3086 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 72.5% (3133 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 73.4% (3172 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 74.5% (3218 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 75.9% (3278 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 76.6% (3310 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 77.6% (3354 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 78.4% (3389 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 79.8% (3448 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 80.6% (3483 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 82.0% (3543 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 83.0% (3587 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 83.7% (3618 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 84.5% (3652 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 85.7% (3703 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 86.8% (3752 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 87.7% (3791 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 88.8% (3835 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 89.7% (3874 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 90.8% (3922 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 91.8% (3965 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 92.8% (4010 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 93.8% (4052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 94.8% (4098 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 96.8% (4184 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 97.9% (4229 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=795 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 79.5; 3553 / 4321 (P = 82.23%) round 41]               
[00:00:00] Finding cutoff p=785 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=785 1.2% (51 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 2.7% (118 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 3.4% (145 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 4.8% (208 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 5.9% (254 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 6.7% (291 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 7.6% (330 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 8.8% (381 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 9.5% (410 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 10.6% (457 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 11.8% (509 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 12.8% (555 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 14.3% (619 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 15.6% (672 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 17.9% (775 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 20.4% (883 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 22.4% (968 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 23.8% (1029 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 25.1% (1085 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 25.9% (1117 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 26.8% (1156 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 28.7% (1238 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 30.2% (1307 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 31.0% (1341 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 32.4% (1401 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 33.3% (1437 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 34.0% (1470 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 34.8% (1502 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 35.8% (1545 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 37.1% (1604 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 38.5% (1664 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 38.9% (1680 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 39.8% (1718 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 41.3% (1785 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 42.8% (1851 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 43.8% (1892 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 45.7% (1974 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 46.4% (2006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 47.2% (2038 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 48.7% (2106 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 49.6% (2142 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 50.4% (2177 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 51.4% (2221 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 52.0% (2245 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 53.1% (2295 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 54.0% (2333 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 55.5% (2396 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 56.2% (2429 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 57.9% (2503 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 59.2% (2556 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 60.1% (2596 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 61.2% (2644 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 62.1% (2684 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 63.4% (2738 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 64.3% (2779 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 65.4% (2825 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 66.2% (2860 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 67.3% (2906 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 68.8% (2973 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 69.6% (3009 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 70.4% (3040 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 71.3% (3083 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 72.4% (3128 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 73.5% (3174 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 74.4% (3214 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 75.6% (3265 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 76.6% (3310 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 78.1% (3376 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 78.7% (3399 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 79.5% (3436 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 80.6% (3482 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 81.9% (3539 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 82.5% (3566 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 84.0% (3630 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 85.2% (3682 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 85.5% (3696 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 86.7% (3745 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 87.6% (3784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 88.8% (3838 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 89.7% (3875 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 90.7% (3920 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 91.8% (3968 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 92.7% (4006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 94.8% (4095 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 96.7% (4180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 97.8% (4225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=785 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 78.5; 3620 / 4321 (P = 83.78%) round 42]               
[00:00:00] Finding cutoff p=775 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=775 1.3% (55 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 2.6% (113 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 3.2% (138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 4.4% (192 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 5.4% (233 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 6.2% (267 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 7.6% (330 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 8.5% (368 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 9.6% (414 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 10.9% (472 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 12.1% (523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 13.4% (581 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 14.6% (631 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 17.0% (734 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 19.6% (847 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 21.7% (937 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 22.7% (983 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 24.9% (1077 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 26.0% (1123 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 26.9% (1163 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 28.5% (1232 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 30.4% (1314 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 31.2% (1350 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 32.6% (1410 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 34.2% (1479 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 34.9% (1507 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 35.7% (1541 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 36.9% (1593 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 38.5% (1665 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 39.6% (1709 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 40.5% (1752 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 41.7% (1800 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 43.0% (1858 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 44.2% (1910 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 44.9% (1942 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 46.2% (1996 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 47.0% (2029 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 48.4% (2092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 49.3% (2129 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 50.2% (2170 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 51.3% (2216 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 52.6% (2271 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 53.2% (2297 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 54.0% (2333 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 55.1% (2379 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 56.2% (2430 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 57.3% (2478 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 58.5% (2529 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 59.7% (2579 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 60.3% (2605 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 61.5% (2659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 62.3% (2693 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 63.2% (2731 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 64.4% (2781 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 65.4% (2825 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 66.3% (2863 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 68.0% (2939 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 68.8% (2975 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 69.4% (2998 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 70.5% (3048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 71.5% (3088 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 72.5% (3132 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 73.3% (3168 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 74.5% (3218 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 75.4% (3260 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 76.6% (3309 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 77.6% (3353 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 78.5% (3391 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 79.8% (3448 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 80.8% (3493 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 81.6% (3528 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 82.6% (3568 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 83.9% (3625 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 85.0% (3672 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 85.9% (3711 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 87.2% (3768 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 87.6% (3784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 88.6% (3830 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 89.6% (3873 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 90.8% (3925 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 92.0% (3976 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 92.7% (4006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 93.8% (4055 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 94.7% (4092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 95.7% (4137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 96.7% (4180 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=775 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 77.5; 3687 / 4321 (P = 85.33%) round 43]               
[00:00:00] Finding cutoff p=765 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=765 1.0% (45 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 2.4% (102 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 3.9% (167 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 4.5% (196 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 5.3% (231 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 6.1% (265 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 7.1% (308 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 8.4% (362 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 10.1% (435 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 11.0% (474 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 12.7% (549 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 13.7% (593 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 16.2% (701 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 18.1% (782 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 20.1% (867 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 21.3% (919 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 22.8% (986 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 24.8% (1073 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 25.7% (1112 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 27.8% (1201 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 29.0% (1254 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 30.1% (1302 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 30.9% (1334 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 32.2% (1393 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 33.3% (1437 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 34.1% (1474 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 34.8% (1503 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 35.9% (1552 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 36.7% (1587 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 38.4% (1658 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 39.2% (1695 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 39.9% (1724 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 41.1% (1777 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 42.9% (1852 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 44.1% (1905 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 45.3% (1959 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 46.0% (1987 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 47.5% (2053 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 48.3% (2088 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 49.3% (2130 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 50.4% (2177 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 51.0% (2205 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 52.0% (2249 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 53.3% (2301 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 54.5% (2356 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 55.1% (2379 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 56.9% (2460 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 57.7% (2493 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 58.3% (2518 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 59.5% (2569 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 60.2% (2600 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 61.2% (2645 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 62.3% (2691 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 63.4% (2741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 64.5% (2788 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 65.3% (2820 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 66.5% (2872 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 67.7% (2926 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 68.5% (2962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 69.4% (2999 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 70.5% (3045 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 71.7% (3098 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 72.4% (3127 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 73.6% (3182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 74.5% (3220 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 75.7% (3269 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 77.3% (3341 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 77.9% (3364 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 78.5% (3392 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 79.6% (3441 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 80.8% (3490 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 81.5% (3523 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 83.4% (3604 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 83.8% (3621 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 84.8% (3665 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 85.6% (3698 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 86.7% (3746 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 88.0% (3801 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 88.7% (3832 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 89.7% (3876 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 90.6% (3916 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 91.7% (3963 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 92.7% (4006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 93.8% (4052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 94.7% (4092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 96.0% (4148 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=765 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 76.5; 3752 / 4321 (P = 86.83%) round 44]               
[00:00:00] Finding cutoff p=754 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=754 2.0% (85 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 2.8% (119 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 3.9% (168 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 4.4% (190 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 5.3% (227 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 6.2% (267 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 7.3% (316 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 8.3% (359 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 9.6% (416 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 10.6% (459 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 12.6% (546 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 13.7% (593 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 16.0% (692 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 18.4% (797 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 20.6% (890 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 22.3% (964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 23.7% (1025 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 24.8% (1072 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 26.1% (1127 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 27.7% (1198 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 29.0% (1254 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 29.8% (1286 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 30.9% (1337 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 32.1% (1385 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 33.3% (1440 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 34.0% (1467 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 35.1% (1516 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 35.7% (1544 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 37.2% (1606 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 37.7% (1629 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 39.0% (1685 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 40.3% (1743 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 41.1% (1775 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 41.9% (1810 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 43.6% (1883 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 44.4% (1917 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 44.9% (1940 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 46.1% (1994 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 47.3% (2042 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 48.2% (2082 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 49.0% (2119 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 50.5% (2181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 51.1% (2209 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 52.0% (2245 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 53.5% (2312 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 54.0% (2335 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 56.0% (2419 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 56.8% (2456 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 57.4% (2482 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 58.1% (2510 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 59.2% (2556 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 60.1% (2596 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 61.3% (2649 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 62.4% (2698 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 63.4% (2740 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 64.2% (2776 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 65.2% (2819 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 66.8% (2887 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 67.5% (2916 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 68.4% (2955 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 69.3% (2994 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 70.5% (3047 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 71.4% (3086 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 72.4% (3130 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 73.3% (3169 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 74.7% (3226 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 75.9% (3280 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 76.5% (3304 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 77.7% (3358 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 78.5% (3390 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 79.4% (3433 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 80.4% (3476 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 82.0% (3545 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 83.2% (3595 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 83.6% (3611 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 84.6% (3654 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 85.6% (3700 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 86.7% (3747 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 87.6% (3786 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 88.7% (3834 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 89.9% (3884 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 91.0% (3931 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 91.7% (3962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 92.7% (4007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 94.7% (4093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 95.7% (4136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 96.8% (4184 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=754 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 75.4; 3799 / 4321 (P = 87.92%) round 45]               
[00:00:00] Finding cutoff p=743 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=743 2.2% (94 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 3.7% (162 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 4.6% (197 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 5.2% (224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 6.2% (269 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 7.7% (332 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 8.9% (384 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 9.7% (420 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 11.2% (485 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 12.3% (531 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 14.3% (617 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 16.2% (702 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 18.7% (807 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 20.1% (867 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 21.7% (939 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 23.2% (1004 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 24.2% (1044 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 26.0% (1122 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 26.7% (1154 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 27.6% (1193 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 29.2% (1261 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 30.1% (1302 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 30.9% (1335 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 31.8% (1372 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 32.7% (1413 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 33.7% (1455 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 35.0% (1512 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 36.0% (1555 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 37.2% (1609 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 38.7% (1674 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 40.1% (1734 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 41.4% (1788 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 41.9% (1809 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 43.6% (1886 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 44.5% (1921 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 45.6% (1971 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 46.8% (2024 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 48.6% (2101 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 49.2% (2126 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 49.9% (2157 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 51.7% (2234 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 52.8% (2282 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 53.2% (2300 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 54.8% (2367 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 55.8% (2413 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 56.5% (2440 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 57.1% (2468 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 58.3% (2520 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 59.3% (2561 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 60.3% (2606 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 61.3% (2650 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 62.5% (2699 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 63.3% (2736 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 64.2% (2774 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 65.8% (2845 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 66.7% (2883 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 67.3% (2908 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 68.5% (2959 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 69.5% (3003 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 70.3% (3038 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 71.4% (3086 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 72.6% (3139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 73.5% (3176 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 75.4% (3256 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 76.6% (3310 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 77.5% (3348 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 78.5% (3392 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 79.7% (3444 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 80.5% (3477 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 81.7% (3530 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 83.1% (3591 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 83.5% (3608 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 84.7% (3659 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 85.8% (3706 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 86.6% (3741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 87.6% (3784 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 88.8% (3838 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 89.8% (3881 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 90.9% (3928 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 91.7% (3961 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 92.7% (4004 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 93.7% (4050 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 94.7% (4092 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 95.9% (4145 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 97.8% (4225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=743 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 74.3; 3849 / 4321 (P = 89.08%) round 46]               
[00:00:00] Finding cutoff p=732 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=732 1.0% (45 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 2.7% (117 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 3.1% (136 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 4.6% (197 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 5.1% (221 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 6.3% (273 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 7.2% (310 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 8.5% (367 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 9.7% (420 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 10.3% (447 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 12.1% (521 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 13.2% (570 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 15.6% (676 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 18.1% (781 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 20.8% (899 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 21.6% (932 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 22.6% (976 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 23.6% (1020 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 24.5% (1060 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 26.6% (1148 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 27.7% (1199 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 29.5% (1275 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 30.0% (1297 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 31.2% (1347 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 32.0% (1383 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 32.9% (1421 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 33.9% (1464 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 35.0% (1511 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 35.8% (1548 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 37.0% (1597 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 38.3% (1656 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 38.8% (1678 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 39.8% (1718 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 41.4% (1789 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 42.0% (1816 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 42.8% (1851 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 43.9% (1897 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 44.9% (1940 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 46.0% (1989 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 47.2% (2038 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 48.6% (2098 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 49.2% (2125 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 50.0% (2162 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 51.4% (2222 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 52.3% (2259 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 54.0% (2334 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 55.1% (2383 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 56.1% (2423 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 57.0% (2465 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 58.1% (2511 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 59.2% (2556 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 60.2% (2601 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 61.2% (2644 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 62.3% (2693 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 63.9% (2761 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 65.0% (2810 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 65.3% (2822 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 66.2% (2861 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 67.3% (2906 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 68.4% (2956 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 69.5% (3002 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 70.3% (3036 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 71.4% (3085 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 73.1% (3159 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 73.6% (3181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 74.5% (3217 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 75.4% (3258 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 76.6% (3309 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 77.6% (3352 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 78.5% (3392 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 79.5% (3434 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 80.5% (3478 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 81.6% (3525 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 82.5% (3564 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 83.7% (3617 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 84.5% (3652 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 85.6% (3699 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 86.7% (3746 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 87.9% (3796 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 88.8% (3837 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 89.7% (3877 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 90.9% (3928 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 91.7% (3964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 92.7% (4004 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 93.9% (4057 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 94.9% (4102 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 96.9% (4186 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 97.8% (4224 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=732 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 73.2; 3895 / 4321 (P = 90.14%) round 47]               
[00:00:00] Finding cutoff p=721 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=721 1.9% (83 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 2.7% (118 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 3.7% (162 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 4.4% (192 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 5.1% (221 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 6.1% (264 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 7.4% (319 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 8.6% (373 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 9.4% (407 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 10.7% (464 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 12.0% (518 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 14.5% (627 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 16.8% (724 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 18.5% (800 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 20.0% (864 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 21.5% (928 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 23.6% (1019 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 24.5% (1060 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 26.3% (1137 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 26.9% (1164 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 27.8% (1203 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 29.3% (1268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 30.5% (1320 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 31.8% (1372 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 32.7% (1415 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 33.6% (1454 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 35.5% (1535 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 36.1% (1558 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 36.7% (1587 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 38.4% (1661 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 39.2% (1693 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 39.9% (1723 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 41.6% (1798 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 42.3% (1828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 43.0% (1858 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 44.2% (1912 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 45.3% (1958 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 46.7% (2019 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 47.7% (2061 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 48.4% (2090 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 49.7% (2146 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 50.2% (2171 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 51.4% (2219 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 53.0% (2290 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 54.0% (2334 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 55.3% (2391 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 56.5% (2440 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 57.1% (2466 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 58.1% (2511 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 59.1% (2554 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 60.3% (2605 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 61.1% (2640 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 62.2% (2688 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 63.2% (2731 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 64.2% (2775 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 65.8% (2845 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 66.6% (2879 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 67.4% (2911 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 68.5% (2958 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 69.5% (3002 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 70.4% (3042 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 71.6% (3094 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 72.4% (3127 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 73.4% (3171 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 75.1% (3245 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 75.6% (3266 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 76.4% (3302 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 77.5% (3348 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 78.9% (3410 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 79.5% (3434 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 80.5% (3478 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 81.5% (3520 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 82.8% (3578 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 83.7% (3615 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 84.6% (3655 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 85.6% (3699 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 86.7% (3745 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 87.6% (3785 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 88.6% (3828 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 89.7% (3878 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 91.0% (3930 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 91.7% (3963 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 92.7% (4006 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 93.8% (4052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 94.7% (4093 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 95.8% (4138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 97.8% (4225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=721 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 72.1; 3947 / 4321 (P = 91.34%) round 48]               
[00:00:00] Finding cutoff p=712 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=712 2.3% (100 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 3.8% (163 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 4.6% (200 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 5.2% (225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 6.2% (266 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 7.4% (319 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 8.6% (372 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 9.3% (400 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 10.6% (460 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 11.7% (507 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 14.2% (613 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 16.1% (695 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 18.8% (813 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 20.2% (872 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 22.3% (965 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 23.4% (1010 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 24.5% (1058 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 26.2% (1132 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 26.9% (1162 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 27.8% (1203 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 28.6% (1235 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 30.0% (1296 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 30.8% (1329 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 31.9% (1380 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 32.8% (1418 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 33.7% (1456 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 35.4% (1531 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 36.1% (1558 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 36.7% (1584 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 38.5% (1662 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 39.3% (1698 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 39.7% (1717 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 41.5% (1794 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 42.3% (1826 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 43.0% (1856 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 44.1% (1904 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 45.0% (1943 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 46.2% (1998 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 47.3% (2042 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 47.9% (2071 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 49.2% (2126 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 50.4% (2176 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 51.7% (2232 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 52.3% (2261 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 53.1% (2293 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 54.4% (2350 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 55.5% (2397 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 56.1% (2423 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 57.3% (2474 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 58.2% (2515 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 59.2% (2556 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 60.1% (2598 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 61.2% (2643 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 62.3% (2692 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 63.2% (2733 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 64.4% (2783 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 65.3% (2822 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 66.2% (2862 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 67.3% (2906 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 68.3% (2952 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 69.3% (2995 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 70.8% (3059 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 71.9% (3107 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 72.4% (3129 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 73.7% (3183 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 75.0% (3239 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 75.8% (3275 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 76.8% (3317 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 77.7% (3358 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 78.5% (3391 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 79.4% (3432 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 80.5% (3480 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 81.5% (3520 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 82.6% (3571 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 84.1% (3636 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 84.6% (3654 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 85.7% (3704 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 86.6% (3741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 87.6% (3785 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 88.7% (3833 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 90.0% (3889 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 90.7% (3919 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 91.7% (3964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 92.7% (4007 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 93.7% (4048 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 94.8% (4098 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 95.8% (4138 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 96.8% (4181 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 97.8% (4227 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=712 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 71.2; 3983 / 4321 (P = 92.18%) round 49]               
[00:00:00] Finding cutoff p=701 [91.2Gb / 503.5Gb]                                                     
[00:00:00] ... finding cutoff p=701 2.0% (87 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 2.9% (127 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 4.4% (188 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 5.3% (228 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 6.4% (278 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 7.2% (312 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 8.5% (366 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 9.3% (403 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 11.2% (483 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 12.4% (537 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 14.9% (642 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 17.1% (741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 18.9% (817 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 20.4% (880 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 21.9% (948 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 23.9% (1034 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 25.1% (1083 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 26.7% (1153 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 28.1% (1215 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 28.6% (1234 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 29.9% (1292 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 30.7% (1326 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 31.8% (1376 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 32.6% (1408 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 34.9% (1506 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 35.7% (1544 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 36.7% (1586 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 37.8% (1634 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 38.8% (1676 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 40.4% (1745 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 41.0% (1772 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 42.8% (1850 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 44.7% (1932 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 45.8% (1977 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 46.6% (2014 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 47.9% (2070 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 49.1% (2120 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 50.5% (2184 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 51.2% (2212 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 52.0% (2246 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 53.3% (2301 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 54.4% (2352 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 55.1% (2381 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 56.2% (2427 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 57.1% (2466 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 58.3% (2519 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 59.4% (2567 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 60.2% (2602 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 61.2% (2645 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 62.1% (2684 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 63.4% (2741 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 64.3% (2780 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 65.3% (2820 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 66.2% (2862 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 67.3% (2907 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 68.5% (2962 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 69.3% (2996 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 70.4% (3043 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 71.4% (3086 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 73.0% (3153 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 73.8% (3188 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 74.5% (3219 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 75.6% (3266 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 76.5% (3307 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 77.7% (3356 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 78.5% (3391 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 80.1% (3461 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 81.1% (3504 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 81.5% (3521 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 82.9% (3584 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 83.8% (3619 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 84.6% (3655 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 85.7% (3701 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 86.6% (3740 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 87.7% (3790 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 89.0% (3844 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 89.9% (3883 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 90.7% (3918 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 91.7% (3964 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 92.8% (4012 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 93.8% (4052 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 94.9% (4100 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 95.8% (4139 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 96.8% (4182 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 97.8% (4225 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 98.8% (4268 of 4321), ETA 0:00:00                              
[00:00:00] ... finding cutoff p=701 99.8% (4312 of 4321), ETA 0:00:00                              
[00:00:00] Preparing TNF Graph Building [pTNF = 70.1; 4018 / 4321 (P = 92.99%) round 50]               
[00:00:00] Finished Preparing TNF Graph Building [pTNF = 69.20] [91.0Gb / 503.5Gb]                                            
[00:00:00] Starting Building TNF Graph. CacheSize=512 TILE=103 nobs=4321 maxEdges=200
[00:00:00] Building TNF Graph 38.1% (1648 of 4321), ETA 0:00:00     [91.0Gb / 503.5Gb]                           
[00:00:00] Building TNF Graph 76.3% (3296 of 4321), ETA 0:00:00     [91.0Gb / 503.5Gb]                           
[00:00:00] Finished Building TNF Graph (173821 edges) [91.0Gb / 503.5Gb]                                          
[00:00:00] Cleaned up after Building TNF Graph (173821 edges) [91.0Gb / 503.5Gb]                                          
[00:00:00] Cleaned up TNF matrix of large contigs [91.0Gb / 503.5Gb]                                             
[00:00:00] Applying coverage correlations to TNF graph with 173821 edges
[00:00:00] Allocated memory for graph edges [91.0Gb / 503.5Gb]
[00:00:00] ... calculating abundance dist 1.0% (1751 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 2.0% (3478 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 3.0% (5224 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 4.0% (6966 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 5.0% (8708 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 6.0% (10447 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 7.0% (12178 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 8.0% (13917 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 9.0% (15661 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 10.0% (17394 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 11.0% (19137 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 12.0% (20880 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 13.0% (22613 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 14.0% (24358 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 15.0% (26089 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 16.0% (27838 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 17.0% (29577 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 18.0% (31314 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 19.0% (33046 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 20.0% (34782 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 21.0% (36526 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 22.0% (38266 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 23.0% (40011 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 24.0% (41744 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 25.0% (43479 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 26.0% (45217 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 27.0% (46955 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 28.0% (48704 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 29.0% (50436 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 30.0% (52170 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 31.0% (53936 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 32.0% (55649 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 33.0% (57399 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 34.0% (59136 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 35.0% (60873 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 36.0% (62604 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 37.0% (64346 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 38.0% (66083 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 39.0% (67823 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 40.0% (69565 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 41.0% (71307 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 42.0% (73045 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 43.0% (74789 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 44.0% (76519 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 45.0% (78260 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 46.0% (80003 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 47.0% (81738 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 48.0% (83479 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 49.0% (85223 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 50.0% (86956 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 51.0% (88697 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 52.0% (90432 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 53.0% (92167 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 54.0% (93918 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 55.0% (95649 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 56.0% (97395 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 57.0% (99132 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 58.0% (100869 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 59.0% (102614 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 60.0% (104355 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 61.0% (106086 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 62.0% (107821 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 63.0% (109563 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 64.0% (111296 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 65.0% (113036 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 66.0% (114785 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 67.0% (116522 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 68.0% (118274 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 69.0% (120000 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 70.0% (121733 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 71.0% (123474 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 72.0% (125215 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 73.0% (126956 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 74.0% (128700 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 75.0% (130429 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 76.0% (132166 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 77.0% (133904 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 78.0% (135648 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 79.0% (137385 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 80.0% (139120 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 81.0% (140867 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 82.0% (142607 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 83.0% (144346 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 84.0% (146085 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 85.0% (147830 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 86.0% (149571 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 87.0% (151304 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 88.0% (153042 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 89.0% (154779 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 90.0% (156515 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 91.0% (158259 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 92.0% (159994 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 93.0% (161731 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 94.0% (163470 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 95.1% (165218 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 96.1% (166957 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 97.0% (168692 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 98.0% (170423 of 173821), ETA 0:00:00                              
[00:00:00] ... calculating abundance dist 99.0% (172167 of 173821), ETA 0:00:00                              
[00:00:00] Calculating geometric means [91.0Gb / 503.5Gb]
[00:00:00] Traversing graph with 4321 nodes and 173821 edges [91.0Gb / 503.5Gb]
[00:00:00] Building SCR Graph and Binning (411 vertices and 715 edges) [P = 9.50%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 1.0% (1739 of 173821), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (821 vertices and 2546 edges) [P = 19.00%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 2.0% (3478 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 3.0% (5217 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 4.0% (6956 of 173821), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1233 vertices and 5498 edges) [P = 28.50%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 5.0% (8695 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 6.0% (10434 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 7.0% (12173 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 8.0% (13912 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 9.0% (15651 of 173821), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (1642 vertices and 9023 edges) [P = 38.00%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 10.0% (17390 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 11.0% (19129 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 12.0% (20868 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 13.0% (22607 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 14.0% (24346 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 15.0% (26085 of 173821), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (2053 vertices and 12905 edges) [P = 47.50%; 91.0Gb / 503.5Gb]                           
[00:00:00] ... traversing graph 16.0% (27824 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 17.0% (29563 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 18.0% (31302 of 173821), ETA 0:00:00                               
[00:00:00] ... traversing graph 19.0% (33041 of 173821), ETA 0:00:00                               
[00:00:00] Building SCR Graph and Binning (2330 vertices and 16622 edges) [P = 57.00%; 91.0Gb / 503.5Gb]                           
[00:00:01] Finished Traversing graph [91.0Gb / 503.5Gb]                                       
[00:00:01] Dissolved 2580 small clusters leaving 2120 leftover contigs to be re-merged into larger clusters
[00:00:01] Rescuing singleton large contigs                                   
[00:00:01] There are 5 bins already
[00:00:01] Outputting bins
[00:00:01] Writing cluster stats to: 03bins/metabat2/1507993/1507993.bin.BinInfo.txt
[00:00:01] 64.63% (13823631 bases) of large (>=1500) and 0.00% (0 bases) of small (<1500) contigs were binned.
5 bins (13823631 bases in total) formed.
[00:00:01] Finished
MetaBAT2 generated 5 bins for 1507993
MetaBAT2 binning completed for 1507993
Binning completed for sample: 1507993
