INFO:    Converting OCI blobs to SIF format
INFO:    Starting build...
Copying blob sha256:0e6dd20ee818794f2f70b3dad6316630256644ecd340435fb273da623245ca78
Copying blob sha256:59856638ac9f32d4caa0f5761b2597fe251642786fdfe1b917ddbb074b890c29
Copying blob sha256:a9dde5e2a643eca8fde0eed52f4aed31f3ecd9c1b2f24d5e3729cd8d2ae68177
Copying blob sha256:9ff7e2e5f967fb9c4e8099e63508ab0dddebe3f820d08ca7fd568431b0d10c0e
Copying blob sha256:6f317d6d954b9a59c54b2cb09e1f30cd3e872796e431cd2ceac5ed570beb2939
Copying blob sha256:675cac559d077f6708a9931d90e801a5834a419b80aedf8fa01499be09d0e08d
time="2025-04-28T16:12:24-07:00" level=error msg="Rolling back transaction: cannot rollback - no transaction is active"
Copying blob sha256:374c558e71daf84e430cf7bbb4af95cec0a3d2984eff3a27858ee0d03be6049d
Copying blob sha256:0df3c64bb19acab06b69728650bd3c3ed7193efe01f7027a81c44e6d9910bc81
Copying blob sha256:e936d7784cf988634bb8c3f94b8d895416e17be11ea0540abca85d225043da4d
Copying blob sha256:4dfd8d164cc5809f0fb8556fb27c931ef5c319353819af28a0b4df9301e54f8d
Copying blob sha256:473490461998ab1efd3fb4c270d5d051d2bb65aca7c361efacb342dda4b5fef3
Copying blob sha256:8f5e491552e6efef12e515fdeb085d6cc85925d6167f91d1aa3cfb727c557914
Copying blob sha256:a66ab3a674d95f89aeb3259740e46128cec4f2d406cc1f631d14b5806483aac5
time="2025-04-28T16:12:28-07:00" level=error msg="Rolling back transaction: sql: transaction has already been committed or rolled back"
Copying blob sha256:18f922275a6f0d7dde75027a285dd23437bd26de80f7e66a025a49739b63db79
Copying blob sha256:c8e106a5860b6452d83af4d44183b408fabcfbf2c91043e0442c0308cdcf5757
Copying config sha256:a24bd272b49d6e66ade4d8447b73ca8c034ff1b0bc6e96b143a1991869e81582
time="2025-04-28T16:12:33-07:00" level=warning msg="Compressor for blob with digest sha256:a24bd272b49d6e66ade4d8447b73ca8c034ff1b0bc6e96b143a1991869e81582 previously recorded as gzip, now uncompressed"
Writing manifest to image destination
2025/04/28 16:12:40  info unpack layer: sha256:9ff7e2e5f967fb9c4e8099e63508ab0dddebe3f820d08ca7fd568431b0d10c0e
2025/04/28 16:12:40  warn rootless{dev/agpgart} creating empty file in place of device 10:175
2025/04/28 16:12:40  warn rootless{dev/audio} creating empty file in place of device 14:4
2025/04/28 16:12:40  warn rootless{dev/audio1} creating empty file in place of device 14:20
2025/04/28 16:12:40  warn rootless{dev/audio2} creating empty file in place of device 14:36
2025/04/28 16:12:40  warn rootless{dev/audio3} creating empty file in place of device 14:52
2025/04/28 16:12:40  warn rootless{dev/audioctl} creating empty file in place of device 14:7
2025/04/28 16:12:40  warn rootless{dev/console} creating empty file in place of device 5:1
2025/04/28 16:12:40  warn rootless{dev/dsp} creating empty file in place of device 14:3
2025/04/28 16:12:40  warn rootless{dev/dsp1} creating empty file in place of device 14:19
2025/04/28 16:12:40  warn rootless{dev/dsp2} creating empty file in place of device 14:35
2025/04/28 16:12:40  warn rootless{dev/dsp3} creating empty file in place of device 14:51
2025/04/28 16:12:40  warn rootless{dev/full} creating empty file in place of device 1:7
2025/04/28 16:12:40  warn rootless{dev/kmem} creating empty file in place of device 1:2
2025/04/28 16:12:40  warn rootless{dev/loop0} creating empty file in place of device 7:0
2025/04/28 16:12:40  warn rootless{dev/loop1} creating empty file in place of device 7:1
2025/04/28 16:12:40  warn rootless{dev/loop2} creating empty file in place of device 7:2
2025/04/28 16:12:40  warn rootless{dev/loop3} creating empty file in place of device 7:3
2025/04/28 16:12:40  warn rootless{dev/loop4} creating empty file in place of device 7:4
2025/04/28 16:12:40  warn rootless{dev/loop5} creating empty file in place of device 7:5
2025/04/28 16:12:40  warn rootless{dev/loop6} creating empty file in place of device 7:6
2025/04/28 16:12:40  warn rootless{dev/loop7} creating empty file in place of device 7:7
2025/04/28 16:12:40  warn rootless{dev/mem} creating empty file in place of device 1:1
2025/04/28 16:12:40  warn rootless{dev/midi0} creating empty file in place of device 35:0
2025/04/28 16:12:40  warn rootless{dev/midi00} creating empty file in place of device 14:2
2025/04/28 16:12:40  warn rootless{dev/midi01} creating empty file in place of device 14:18
2025/04/28 16:12:40  warn rootless{dev/midi02} creating empty file in place of device 14:34
2025/04/28 16:12:40  warn rootless{dev/midi03} creating empty file in place of device 14:50
2025/04/28 16:12:40  warn rootless{dev/midi1} creating empty file in place of device 35:1
2025/04/28 16:12:40  warn rootless{dev/midi2} creating empty file in place of device 35:2
2025/04/28 16:12:40  warn rootless{dev/midi3} creating empty file in place of device 35:3
2025/04/28 16:12:40  warn rootless{dev/mixer} creating empty file in place of device 14:0
2025/04/28 16:12:40  warn rootless{dev/mixer1} creating empty file in place of device 14:16
2025/04/28 16:12:40  warn rootless{dev/mixer2} creating empty file in place of device 14:32
2025/04/28 16:12:40  warn rootless{dev/mixer3} creating empty file in place of device 14:48
2025/04/28 16:12:40  warn rootless{dev/mpu401data} creating empty file in place of device 31:0
2025/04/28 16:12:40  warn rootless{dev/mpu401stat} creating empty file in place of device 31:1
2025/04/28 16:12:40  warn rootless{dev/null} creating empty file in place of device 1:3
2025/04/28 16:12:40  warn rootless{dev/port} creating empty file in place of device 1:4
2025/04/28 16:12:40  warn rootless{dev/ram0} creating empty file in place of device 1:0
2025/04/28 16:12:40  warn rootless{dev/ram1} creating empty file in place of device 1:1
2025/04/28 16:12:40  warn rootless{dev/ram10} creating empty file in place of device 1:10
2025/04/28 16:12:40  warn rootless{dev/ram11} creating empty file in place of device 1:11
2025/04/28 16:12:40  warn rootless{dev/ram12} creating empty file in place of device 1:12
2025/04/28 16:12:40  warn rootless{dev/ram13} creating empty file in place of device 1:13
2025/04/28 16:12:40  warn rootless{dev/ram14} creating empty file in place of device 1:14
2025/04/28 16:12:40  warn rootless{dev/ram15} creating empty file in place of device 1:15
2025/04/28 16:12:40  warn rootless{dev/ram16} creating empty file in place of device 1:16
2025/04/28 16:12:40  warn rootless{dev/ram2} creating empty file in place of device 1:2
2025/04/28 16:12:40  warn rootless{dev/ram3} creating empty file in place of device 1:3
2025/04/28 16:12:40  warn rootless{dev/ram4} creating empty file in place of device 1:4
2025/04/28 16:12:40  warn rootless{dev/ram5} creating empty file in place of device 1:5
2025/04/28 16:12:40  warn rootless{dev/ram6} creating empty file in place of device 1:6
2025/04/28 16:12:40  warn rootless{dev/ram7} creating empty file in place of device 1:7
2025/04/28 16:12:40  warn rootless{dev/ram8} creating empty file in place of device 1:8
2025/04/28 16:12:40  warn rootless{dev/ram9} creating empty file in place of device 1:9
2025/04/28 16:12:40  warn rootless{dev/random} creating empty file in place of device 1:8
2025/04/28 16:12:40  warn rootless{dev/rmidi0} creating empty file in place of device 35:64
2025/04/28 16:12:40  warn rootless{dev/rmidi1} creating empty file in place of device 35:65
2025/04/28 16:12:40  warn rootless{dev/rmidi2} creating empty file in place of device 35:66
2025/04/28 16:12:40  warn rootless{dev/rmidi3} creating empty file in place of device 35:67
2025/04/28 16:12:40  warn rootless{dev/sequencer} creating empty file in place of device 14:1
2025/04/28 16:12:40  warn rootless{dev/smpte0} creating empty file in place of device 35:128
2025/04/28 16:12:40  warn rootless{dev/smpte1} creating empty file in place of device 35:129
2025/04/28 16:12:40  warn rootless{dev/smpte2} creating empty file in place of device 35:130
2025/04/28 16:12:40  warn rootless{dev/smpte3} creating empty file in place of device 35:131
2025/04/28 16:12:40  warn rootless{dev/sndstat} creating empty file in place of device 14:6
2025/04/28 16:12:40  warn rootless{dev/tty} creating empty file in place of device 5:0
2025/04/28 16:12:40  warn rootless{dev/tty0} creating empty file in place of device 4:0
2025/04/28 16:12:40  warn rootless{dev/tty1} creating empty file in place of device 4:1
2025/04/28 16:12:40  warn rootless{dev/tty2} creating empty file in place of device 4:2
2025/04/28 16:12:40  warn rootless{dev/tty3} creating empty file in place of device 4:3
2025/04/28 16:12:40  warn rootless{dev/tty4} creating empty file in place of device 4:4
2025/04/28 16:12:40  warn rootless{dev/tty5} creating empty file in place of device 4:5
2025/04/28 16:12:40  warn rootless{dev/tty6} creating empty file in place of device 4:6
2025/04/28 16:12:40  warn rootless{dev/tty7} creating empty file in place of device 4:7
2025/04/28 16:12:40  warn rootless{dev/tty8} creating empty file in place of device 4:8
2025/04/28 16:12:40  warn rootless{dev/tty9} creating empty file in place of device 4:9
2025/04/28 16:12:40  warn rootless{dev/urandom} creating empty file in place of device 1:9
2025/04/28 16:12:40  warn rootless{dev/zero} creating empty file in place of device 1:5
2025/04/28 16:12:41  info unpack layer: sha256:59856638ac9f32d4caa0f5761b2597fe251642786fdfe1b917ddbb074b890c29
2025/04/28 16:12:41  info unpack layer: sha256:6f317d6d954b9a59c54b2cb09e1f30cd3e872796e431cd2ceac5ed570beb2939
2025/04/28 16:12:41  info unpack layer: sha256:a9dde5e2a643eca8fde0eed52f4aed31f3ecd9c1b2f24d5e3729cd8d2ae68177
2025/04/28 16:12:41  info unpack layer: sha256:675cac559d077f6708a9931d90e801a5834a419b80aedf8fa01499be09d0e08d
2025/04/28 16:12:41  info unpack layer: sha256:0e6dd20ee818794f2f70b3dad6316630256644ecd340435fb273da623245ca78
2025/04/28 16:12:43  info unpack layer: sha256:374c558e71daf84e430cf7bbb4af95cec0a3d2984eff3a27858ee0d03be6049d
2025/04/28 16:12:44  info unpack layer: sha256:0df3c64bb19acab06b69728650bd3c3ed7193efe01f7027a81c44e6d9910bc81
2025/04/28 16:12:44  info unpack layer: sha256:e936d7784cf988634bb8c3f94b8d895416e17be11ea0540abca85d225043da4d
2025/04/28 16:12:44  info unpack layer: sha256:4dfd8d164cc5809f0fb8556fb27c931ef5c319353819af28a0b4df9301e54f8d
2025/04/28 16:12:45  info unpack layer: sha256:473490461998ab1efd3fb4c270d5d051d2bb65aca7c361efacb342dda4b5fef3
2025/04/28 16:12:45  info unpack layer: sha256:8f5e491552e6efef12e515fdeb085d6cc85925d6167f91d1aa3cfb727c557914
2025/04/28 16:12:45  info unpack layer: sha256:a66ab3a674d95f89aeb3259740e46128cec4f2d406cc1f631d14b5806483aac5
2025/04/28 16:12:45  info unpack layer: sha256:18f922275a6f0d7dde75027a285dd23437bd26de80f7e66a025a49739b63db79
2025/04/28 16:12:45  warn rootless{opt/conda/bin/python} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/bin/python2} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/lib/libffi.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/lib/libffi.so.6} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/lib/libpython2.7.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/lib/libsqlite3.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/lib/libsqlite3.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/lib/pkgconfig/python.pc} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/lib/pkgconfig/python2.pc} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/pkgs/libffi-3.2.1-1/lib/libffi.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/pkgs/libffi-3.2.1-1/lib/libffi.so.6} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/pkgs/python-2.7.13-0/bin/python} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/pkgs/python-2.7.13-0/bin/python2} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/pkgs/python-2.7.13-0/lib/libpython2.7.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/pkgs/python-2.7.13-0/lib/pkgconfig/python.pc} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:45  warn rootless{opt/conda/pkgs/python-2.7.13-0/lib/pkgconfig/python2.pc} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/pkgs/python-2.7.13-0/share/man/man1/python.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/pkgs/python-2.7.13-0/share/man/man1/python2.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/pkgs/sqlite-3.13.0-0/lib/libsqlite3.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/pkgs/sqlite-3.13.0-0/lib/libsqlite3.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/share/man/man1/python.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/share/man/man1/python2.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  info unpack layer: sha256:c8e106a5860b6452d83af4d44183b408fabcfbf2c91043e0442c0308cdcf5757
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libasan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libasan.so.5} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libatomic.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libatomic.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libgomp.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libgomp.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libitm.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libitm.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/liblsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/liblsan.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libquadmath.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libquadmath.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libtsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libtsan.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libubsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libubsan.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libz.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:46  warn rootless{opt/conda/lib/libz.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libasan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libasan.so.5} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libatomic.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libatomic.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libgomp.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libgomp.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libitm.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libitm.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/liblsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/liblsan.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libquadmath.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libquadmath.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libtsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libtsan.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libubsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/lib/libubsan.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libasan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libasan.so.5} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libasan.so.5.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libatomic.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libatomic.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libatomic.so.1.2.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libgomp.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libgomp.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libgomp.so.1.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libitm.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libitm.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libitm.so.1.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/liblsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/liblsan.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/liblsan.so.0.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libquadmath.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libquadmath.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libquadmath.so.0.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libtsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libtsan.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libtsan.so.0.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libubsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libubsan.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:47  warn rootless{opt/conda/pkgs/libgcc-ng-9.1.0-hdf63c60_0/x86_64-conda_cos6-linux-gnu/sysroot/lib/libubsan.so.1.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/pkgs/zlib-1.2.11-h7b6447c_3/lib/libz.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/pkgs/zlib-1.2.11-h7b6447c_3/lib/libz.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libasan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libasan.so.5} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libasan.so.5.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libatomic.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libatomic.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libatomic.so.1.2.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libgomp.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libgomp.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libgomp.so.1.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libitm.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libitm.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libitm.so.1.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/liblsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/liblsan.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/liblsan.so.0.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libquadmath.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libquadmath.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libquadmath.so.0.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libtsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libtsan.so.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libtsan.so.0.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libubsan.so} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libubsan.so.1} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
2025/04/28 16:12:48  warn rootless{opt/conda/x86_64-conda_cos6-linux-gnu/sysroot/lib/libubsan.so.1.0.0} ignoring (usually) harmless EPERM on setxattr "user.rootlesscontainers"
INFO:    Creating SIF file...
INFO:    Using cached SIF image
mv: cannot stat 'samtools_v1.9-4-deb_cv1.sif': No such file or directory
[bwa_index] Pack FASTA... 0.06 sec
[bwa_index] Construct BWT for the packed sequence...
[bwa_index] 1.87 seconds elapse.
[bwa_index] Update BWT... 0.04 sec
[bwa_index] Pack forward-only FASTA... 0.03 sec
[bwa_index] Construct SA from BWT and Occ... 0.57 sec
[main] Version: 0.7.17-r1188
[main] CMD: /opt/conda/bin/bwa index 02mapping/1507990/temp/scaffolds.fasta
[main] Real time: 2.915 sec; CPU: 2.589 sec
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 1071224 sequences (160000212 bp)...
[M::process] read 1073424 sequences (160000298 bp)...
[M::mem_process_seqs] Processed 1071224 reads in 80.824 CPU sec, 6.150 real sec
[M::process] read 1071160 sequences (160000175 bp)...
[M::mem_process_seqs] Processed 1073424 reads in 74.658 CPU sec, 5.373 real sec
[M::process] read 1071574 sequences (160000025 bp)...
[M::mem_process_seqs] Processed 1071160 reads in 59.904 CPU sec, 4.358 real sec
[M::process] read 1070514 sequences (160000062 bp)...
[M::mem_process_seqs] Processed 1071574 reads in 66.347 CPU sec, 4.345 real sec
[M::process] read 1070610 sequences (160000278 bp)...
[M::mem_process_seqs] Processed 1070514 reads in 59.903 CPU sec, 4.153 real sec
[M::process] read 1070974 sequences (160000188 bp)...
[M::mem_process_seqs] Processed 1070610 reads in 60.559 CPU sec, 4.042 real sec
[M::mem_process_seqs] Processed 1070974 reads in 60.535 CPU sec, 4.117 real sec
[M::process] read 1072486 sequences (160000199 bp)...
[M::process] read 931814 sequences (136780252 bp)...
slurmstepd: error: *** JOB 17160541 ON n0043.dori0 CANCELLED AT 2025-04-28T16:13:50 ***
