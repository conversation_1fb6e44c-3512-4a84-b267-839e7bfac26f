#!/usr/bin/env python3
"""
Script to calculate N50 for each sample's contigs separately and create a CSV with treatment groups.
"""

import sys
from Bio import SeqIO
import csv
from collections import defaultdict

def calculate_n50_for_lengths(contig_lengths):
    """
    Calculate N50 from a list of contig lengths.
    
    Args:
        contig_lengths (list): List of contig lengths
        
    Returns:
        int: N50 value
    """
    if not contig_lengths:
        return 0
    
    # Sort lengths in descending order
    sorted_lengths = sorted(contig_lengths, reverse=True)
    total_length = sum(sorted_lengths)
    
    # Calculate N50
    cumulative_length = 0
    for length in sorted_lengths:
        cumulative_length += length
        if cumulative_length >= total_length * 0.5:
            return length
    
    return 0

def parse_fasta_by_sample(fasta_file):
    """
    Parse FASTA file and group contigs by sample ID.
    
    Args:
        fasta_file (str): Path to the FASTA file
        
    Returns:
        dict: Dictionary with sample IDs as keys and lists of contig lengths as values
    """
    sample_contigs = defaultdict(list)
    
    with open(fasta_file, 'r') as handle:
        for record in SeqIO.parse(handle, "fasta"):
            # Extract sample ID from header (everything before the first dot)
            sample_id = record.id.split('.')[0]
            contig_length = len(record.seq)
            sample_contigs[sample_id].append(contig_length)
    
    return sample_contigs

def assign_treatment(sample_id):
    """
    Assign treatment based on sample ID.
    
    Args:
        sample_id (str): Sample ID
        
    Returns:
        str: Treatment type ('MDA' or 'PTA')
    """
    mda_samples = ['1507990', '1507992', '1507993']
    return 'MDA' if sample_id in mda_samples else 'PTA'

def main():
    fasta_file = "manual_skani_analysis/discosea_contigs_only.fasta"
    output_file = "discosea_n50_by_treatment.csv"
    
    try:
        # Parse FASTA file by sample
        print("Parsing FASTA file by sample...")
        sample_contigs = parse_fasta_by_sample(fasta_file)
        
        # Calculate N50 for each sample
        results = []
        
        for sample_id, contig_lengths in sample_contigs.items():
            n50 = calculate_n50_for_lengths(contig_lengths)
            treatment = assign_treatment(sample_id)
            total_contigs = len(contig_lengths)
            total_length = sum(contig_lengths)
            mean_length = total_length / total_contigs if total_contigs > 0 else 0
            max_length = max(contig_lengths) if contig_lengths else 0
            min_length = min(contig_lengths) if contig_lengths else 0
            
            results.append({
                'sample_id': sample_id,
                'treatment': treatment,
                'n50': n50,
                'total_contigs': total_contigs,
                'total_length': total_length,
                'mean_length': round(mean_length, 1),
                'max_length': max_length,
                'min_length': min_length
            })
            
            print(f"Sample {sample_id} ({treatment}): N50 = {n50:,} bp, {total_contigs:,} contigs, {total_length:,} bp total")
        
        # Sort results by treatment and sample_id
        results.sort(key=lambda x: (x['treatment'], x['sample_id']))

        # Save to CSV
        with open(output_file, 'w', newline='') as csvfile:
            fieldnames = ['sample_id', 'treatment', 'n50', 'total_contigs', 'total_length',
                         'mean_length', 'max_length', 'min_length']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(results)

        print(f"\nResults saved to {output_file}")

        # Calculate summary statistics by treatment
        mda_n50s = [r['n50'] for r in results if r['treatment'] == 'MDA']
        pta_n50s = [r['n50'] for r in results if r['treatment'] == 'PTA']

        print(f"\nSummary by treatment:")
        print(f"MDA samples: {len(mda_n50s)} samples")
        if mda_n50s:
            print(f"  N50 range: {min(mda_n50s):,} - {max(mda_n50s):,} bp")
            print(f"  N50 mean: {sum(mda_n50s)/len(mda_n50s):,.1f} bp")

        print(f"PTA samples: {len(pta_n50s)} samples")
        if pta_n50s:
            print(f"  N50 range: {min(pta_n50s):,} - {max(pta_n50s):,} bp")
            print(f"  N50 mean: {sum(pta_n50s)/len(pta_n50s):,.1f} bp")

        # Display the full table
        print(f"\nFull results table:")
        print(f"{'Sample ID':<10} {'Treatment':<10} {'N50 (bp)':<12} {'Contigs':<10} {'Total Length (bp)':<18}")
        print("-" * 70)
        for result in results:
            print(f"{result['sample_id']:<10} {result['treatment']:<10} {result['n50']:<12,} "
                  f"{result['total_contigs']:<10,} {result['total_length']:<18,}")
        
    except FileNotFoundError:
        print(f"Error: File '{fasta_file}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
