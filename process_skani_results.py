#!/usr/bin/env python3

import pandas as pd
import os
import glob

def process_skani_results():
    """Process skani results and create a clean summary"""
    
    # Directory containing skani results
    results_dir = "skani_acanthamoeba_mapping"
    
    # Find all result files
    result_files = glob.glob(f"{results_dir}/*_vs_acanthamoeba.txt")
    
    # Initialize list to store results
    all_results = []
    
    for file_path in result_files:
        # Extract sample and bin info from filename
        filename = os.path.basename(file_path)
        # Format: 1507990_1507990.bin.1_vs_acanthamoeba.txt
        parts = filename.replace("_vs_acanthamoeba.txt", "").split("_")
        sample = parts[0]
        bin_name = "_".join(parts[1:])  # Handle cases like 1507990.bin.1
        
        try:
            # Read the result file
            df = pd.read_csv(file_path, sep='\t')
            
            if len(df) > 0:
                row = df.iloc[0]  # Take first (and usually only) row
                
                # Extract key information
                result = {
                    'Sample': sample,
                    'Bin': bin_name,
                    'ANI': round(float(row['ANI']), 2),
                    'Aligned_fraction_query': round(float(row['Align_fraction_query']), 4),
                    'Aligned_fraction_reference': round(float(row['Align_fraction_ref']), 4),
                    'Ref_name': row['Ref_name'].split()[0] if pd.notna(row['Ref_name']) else 'N/A',
                    'Query_name': row['Query_name'].split()[0] if pd.notna(row['Query_name']) else 'N/A',
                    'Num_ref_contigs': int(row['Num_ref_contigs']),
                    'Num_query_contigs': int(row['Num_query_contigs']),
                    'Total_bases_covered': int(row['Total_bases_covered'])
                }
                
                all_results.append(result)
                
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            continue
    
    # Create DataFrame
    results_df = pd.DataFrame(all_results)
    
    # Sort by sample and ANI (descending)
    results_df = results_df.sort_values(['Sample', 'ANI'], ascending=[True, False])
    
    # Save clean summary
    output_file = f"{results_dir}/skani_clean_summary.tsv"
    results_df.to_csv(output_file, sep='\t', index=False)
    print(f"Clean summary saved to: {output_file}")
    
    # Create high ANI summary (>80%)
    high_ani_df = results_df[results_df['ANI'] > 80.0]
    high_ani_file = f"{results_dir}/skani_high_ani_clean.tsv"
    high_ani_df.to_csv(high_ani_file, sep='\t', index=False)
    print(f"High ANI summary (>80%) saved to: {high_ani_file}")
    
    # Print summary statistics
    print(f"\nSummary Statistics:")
    print(f"Total comparisons: {len(results_df)}")
    print(f"High ANI matches (>80%): {len(high_ani_df)}")
    print(f"Very high ANI matches (>90%): {len(results_df[results_df['ANI'] > 90.0])}")
    print(f"Extremely high ANI matches (>95%): {len(results_df[results_df['ANI'] > 95.0])}")
    
    # Show top matches
    print(f"\nTop 10 ANI matches:")
    print(results_df.head(10)[['Sample', 'Bin', 'ANI', 'Aligned_fraction_query', 'Aligned_fraction_reference']].to_string(index=False))
    
    return results_df

if __name__ == "__main__":
    process_skani_results()
