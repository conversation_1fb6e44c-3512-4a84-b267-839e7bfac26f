#!/bin/bash

# Script to calculate coverage and average depth from BBMap SAM files
# Output directory for processed files
mkdir -p coverage_analysis

echo "Processing BBMap SAM files to calculate coverage and depth statistics..."
echo "Sample,Coverage_Percent,Average_Depth,Total_Reads,Mapped_Reads,Mapping_Rate" > coverage_analysis/coverage_summary.csv

for sam_file in acanthamoeba-bbmap-output/*.sam; do
    sample=$(basename "$sam_file" .sam)
    echo "Processing $sample..."
    
    # Convert SAM to BAM and sort
    echo "  Converting SAM to BAM and sorting..."
    samtools view -bS "$sam_file" | samtools sort -o "coverage_analysis/${sample}.sorted.bam"
    
    # Index the BAM file
    echo "  Indexing BAM file..."
    samtools index "coverage_analysis/${sample}.sorted.bam"
    
    # Get coverage statistics using samtools depth
    echo "  Calculating depth..."
    samtools depth "coverage_analysis/${sample}.sorted.bam" > "coverage_analysis/${sample}.depth.txt"
    
    # Get mapping statistics
    echo "  Getting mapping statistics..."
    samtools flagstat "coverage_analysis/${sample}.sorted.bam" > "coverage_analysis/${sample}.flagstat.txt"
    
    # Calculate coverage percentage and average depth
    echo "  Calculating coverage and depth metrics..."
    
    # Get total reference length (sum of all contigs)
    total_bases=$(samtools view -H "coverage_analysis/${sample}.sorted.bam" | grep "^@SQ" | awk '{sum += $3} END {print sum}' | sed 's/LN://')
    
    # Count positions with coverage > 0
    covered_bases=$(awk '$3 > 0' "coverage_analysis/${sample}.depth.txt" | wc -l)
    
    # Calculate coverage percentage
    coverage_percent=$(echo "scale=2; $covered_bases * 100 / $total_bases" | bc -l)
    
    # Calculate average depth
    avg_depth=$(awk '{sum += $3} END {if(NR>0) print sum/NR; else print 0}' "coverage_analysis/${sample}.depth.txt")
    
    # Get mapping statistics from flagstat
    total_reads=$(grep "in total" "coverage_analysis/${sample}.flagstat.txt" | awk '{print $1}')
    mapped_reads=$(grep "mapped" "coverage_analysis/${sample}.flagstat.txt" | head -1 | awk '{print $1}')
    mapping_rate=$(echo "scale=2; $mapped_reads * 100 / $total_reads" | bc -l)
    
    # Output to CSV
    echo "$sample,$coverage_percent,$avg_depth,$total_reads,$mapped_reads,$mapping_rate" >> coverage_analysis/coverage_summary.csv
    
    echo "  Completed $sample: Coverage = ${coverage_percent}%, Average Depth = ${avg_depth}"
done

echo ""
echo "Analysis complete! Results saved in coverage_analysis/"
echo "Summary file: coverage_analysis/coverage_summary.csv"
echo ""
echo "Coverage Summary:"
echo "================="
cat coverage_analysis/coverage_summary.csv 