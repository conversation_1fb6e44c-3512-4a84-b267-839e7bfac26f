{"cells": [{"cell_type": "markdown", "id": "ae6e04df", "metadata": {}, "source": ["Using skani to map acanthamoeba castellani genome to discosea contigs"]}, {"cell_type": "code", "execution_count": 7, "id": "71a990c3", "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import subprocess\n", "\n", "#file paths\n", "BINS_DIR = '/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/03bins/metabat2_fixed/'\n", "TAX_DIR = '/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/06taxonomy/eukulele_batches/'\n", "ACANTHAMEOBA_GENOME = '/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/acanthamoeba_castellani_genome.fna'\n", "OUTPUT_DIR = './manual_skani_analysis'\n", "THREADS = 4"]}, {"cell_type": "code", "execution_count": null, "id": "57ac53bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Checking 18 taxonomy files for Discosea bins...\n", "Found 18 Discosea bins\n", "Extracting contigs from 18 Discosea bins...\n", "Extracted 15834 contigs from Discosea bins\n", "Combined contigs saved to: manual_skani_analysis/discosea_contigs_only.fasta\n", "Extracted 15834 individual contigs\n", "Skani analysis completed successfully\n", "Successfully loaded DataFrame with shape: (40428, 7)\n", "Column names: ['Ref_file', 'Query_file', 'ANI', 'Align_fraction_ref', 'Align_fraction_query', 'Ref_name', 'Query_name']\n", "Error loading skani results: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'float'\n", "Trying alternative parsing...\n", "Alternative parsing successful! Loaded 40428 comparison results\n", "Summary table saved to: manual_skani_analysis/summary_table.tsv\n", "Contig-level search analysis completed!\n"]}], "source": ["def extract_discosea_contigs(bins_dir, discosea_bins, output_dir):\n", "    \"\"\"Extract individual contigs from Discosea bins\"\"\"\n", "    \n", "    combined_contigs_file = Path(output_dir) / 'discosea_contigs_only.fasta'\n", "    contig_count = 0\n", "    \n", "    print(f\"Extracting contigs from {len(discosea_bins)} Discosea bins...\")\n", "    \n", "    with open(combined_contigs_file, 'w') as outfile:\n", "        for binname in discosea_bins:\n", "            # Get the job ID and construct path to bin file\n", "            job_id = binname.split('.')[0]\n", "            bin_file_path = Path(bins_dir) / job_id / f\"{binname}.fa\"\n", "            \n", "            if bin_file_path.exists():                \n", "                # Read the bin file and extract individual contigs\n", "                with open(bin_file_path, 'r') as infile:\n", "                    current_contig = \"\"\n", "                    current_header = \"\"\n", "                    \n", "                    for line in infile:\n", "                        if line.startswith('>'):\n", "                            # Write previous contig if it exists\n", "                            if current_header and current_contig:\n", "                                # Add bin name to contig header for tracking\n", "                                outfile.write(f\">{binname}_{current_header[1:].strip()}\\n\")\n", "                                outfile.write(current_contig)\n", "                                if not current_contig.endswith('\\n'):\n", "                                    outfile.write('\\n')\n", "                                contig_count += 1\n", "                            \n", "                            # Start new contig\n", "                            current_header = line.strip()\n", "                            current_contig = \"\"\n", "                        else:\n", "                            current_contig += line\n", "                    \n", "                    # Write the last contig\n", "                    if current_header and current_contig:\n", "                        outfile.write(f\">{binname}_{current_header[1:].strip()}\\n\")\n", "                        outfile.write(current_contig)\n", "                        if not current_contig.endswith('\\n'):  # FIXED: Ensure sequence ends with newline\n", "                            outfile.write('\\n')\n", "                        contig_count += 1\n", "            else:\n", "                print(f\"Warning: Bin file not found: {bin_file_path}\")\n", "    \n", "    print(f\"Extracted {contig_count} contigs from Discosea bins\")\n", "    print(f\"Combined contigs saved to: {combined_contigs_file}\")\n", "    \n", "    return str(combined_contigs_file), contig_count\n", "\n", "def identify_discosea_bins(taxonomy_dir):\n", "    \"\"\"Identify bins classified as Discosea\"\"\"\n", "    discosea_bins = []\n", "    taxonomy_files = list(Path(taxonomy_dir).rglob('*estimated-taxonomy.out'))\n", "\n", "    print(f\"Checking {len(taxonomy_files)} taxonomy files for Discosea bins...\")\n", "\n", "    for taxfile in taxonomy_files:\n", "        bin_name = taxfile.name.replace('.proteins-estimated-taxonomy.out', '') #gets file name w/o extension\n", "        try:\n", "            with open(taxfile, 'r') as f:\n", "                taxonomy = f.read().lower()\n", "                if 'discosea' in taxonomy:\n", "                    discosea_bins.append(bin_name)\n", "        except Exception as e:\n", "            print(f\"Warning: Could not read {taxfile}: {e}\")\n", "    \n", "    return discosea_bins\n", "\n", "def find_bin_files(bins_dir, discosea_bins):\n", "    \"\"\"Find bin files for Discosea bins\"\"\"\n", "    binfiles = []\n", "    extension = '.fa'\n", "\n", "    print(f\"Searching for {len(discosea_bins)} Discosea bins...\")\n", "    \n", "    bins_path = Path(bins_dir)\n", "    \n", "    for binname in discosea_bins:\n", "        # Extract job ID from bin name (e.g., \"1507990.bin.1\" -> \"1507990\")\n", "        job_id = binname.split('.')[0]\n", "        \n", "        # Look for the bin file in the job ID subdirectory\n", "        bin_file_path = bins_path / job_id / f\"{binname}{extension}\"\n", "        \n", "        print(f\"Looking for: {bin_file_path}\")\n", "        \n", "        if bin_file_path.exists():\n", "            binfiles.append(str(bin_file_path))\n", "        else:\n", "            print(f\"✗ Warning: Bin file for {binname} not found at {bin_file_path}\")\n", "    \n", "    print(f\"Found {len(binfiles)} bin files total\")\n", "    return binfiles\n", "\n", "def run_skani_analysis(acanthamoeba_genome, discosea_contigs_file, output_dir, threads=4):\n", "    \"\"\"Run skani dist analysis\"\"\"\n", "\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    skani_output = Path(output_dir) / 'skani_contigs_results.txt'\n", "    \n", "    skani_path = \"/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/skani-env/bin/skani\"\n", "    \n", "    cmd = [skani_path, 'dist', \n", "           '-q', acanthamoeba_genome,\n", "           '-r', discosea_contigs_file,\n", "           '-o', str(skani_output),\n", "           '-t', str(threads),\n", "           '--qi',\n", "           '--ri'\n", "           ]\n", "    \n", "    try:\n", "        result = subprocess.run(cmd, capture_output=True, text=True, check=True)\n", "        print(\"Skani analysis completed successfully\")\n", "        return str(skani_output)\n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"Error running skani: {e}\")\n", "        print(f\"stdout: {e.stdout}\")\n", "        print(f\"stderr: {e.stderr}\")\n", "        return None                  \n", "\n", "def load_and_process_results(skani_output_file):\n", "    \"\"\"Load and process skani results\"\"\"\n", "    if not os.path.exists(skani_output_file):\n", "        print(f\"Error: Skani output file {skani_output_file} not found\")\n", "        return None\n", "    \n", "    try:\n", "        df = pd.read_csv(skani_output_file, sep='\\t', header=0)\n", "        \n", "        print(f\"Successfully loaded DataFrame with shape: {df.shape}\")\n", "        print(\"Column names:\", df.columns.tolist())\n", "        \n", "        # Keep only the essential columns for analysis\n", "        essential_cols = ['Ref_file', 'Query_file', 'ANI', 'Align_fraction_ref', 'Align_fraction_query', 'Ref_name', 'Query_name']\n", "        \n", "        # Select only the columns that exist\n", "        available_cols = [col for col in essential_cols if col in df.columns]\n", "        df = df[available_cols]\n", "        \n", "        # Extract bin names from file paths\n", "        df['Bin_Name'] = df['Ref_file'].apply(lambda x: Path(x).stem)\n", "        \n", "        print(f\"Loaded {len(df)} comparison results\")\n", "        print(\"Sample of results:\")\n", "        print(df[['Bin_Name', 'ANI', 'Align_fraction_ref', 'Align_fraction_query']].head())\n", "        \n", "        return df\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading skani results: {e}\")\n", "        \n", "        # Try alternative approach - read first 7 columns only\n", "        try:\n", "            print(\"Trying alternative parsing...\")\n", "            df = pd.read_csv(skani_output_file, sep='\\t', header=0, usecols=[0,1,2,3,4,5,6])\n", "            df.columns = ['Ref_file', 'Query_file', 'ANI', 'Align_fraction_ref', 'Align_fraction_query', 'Ref_name', 'Query_name']\n", "            \n", "            # Extract bin names from file paths\n", "            df['Bin_Name'] = df['Ref_file'].apply(lambda x: Path(x).stem)\n", "            \n", "            print(f\"Alternative parsing successful! Loaded {len(df)} comparison results\")\n", "            return df\n", "            \n", "        except Exception as e2:\n", "            print(f\"Alternative parsing also failed: {e2}\")\n", "            return None\n", "\n", "def generate_summary_table(df, output_dir):\n", "    \"\"\"Generate clean summary table\"\"\"\n", "    if df is None or df.empty:\n", "        print(\"No data to summarize\")\n", "        return\n", "    \n", "    summary_cols = ['Bin_Name', 'ANI', 'Align_fraction_ref', 'Align_fraction_query']\n", "    summary_df = df[summary_cols].copy()\n", "\n", "    summary_df = summary_df.sort_values('ANI', ascending=False)\n", "\n", "    summary_file = Path(output_dir) / 'summary_table.tsv'\n", "    summary_df.to_csv(summary_file, sep='\\t', index=False, float_format='%.3f')\n", "\n", "    print(f\"Summary table saved to: {summary_file}\")\n", "    return summary_df\n", "\n", "def main():\n", "    os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "\n", "    #1. Identify discosea bins\n", "    discosea_bins = identify_discosea_bins(TAX_DIR)\n", "    if not discosea_bins:\n", "        print(\"No Discosea bins found\")\n", "        return None\n", "    print(f\"Found {len(discosea_bins)} Discosea bins\")\n", "\n", "    #2. Extract individual contigs from Discosea bins\n", "    discosea_contigs_file, contig_count = extract_discosea_contigs(BINS_DIR, discosea_bins, OUTPUT_DIR)\n", "    if contig_count == 0:\n", "        print(\"No contigs extracted from Discosea bins\")\n", "        return None\n", "    print(f\"Extracted {contig_count} individual contigs\")\n", "\n", "    #3. Run skani search against individual contigs\n", "    skani_output = run_skani_analysis(ACANTHAMEOBA_GENOME, discosea_contigs_file, OUTPUT_DIR, THREADS)\n", "    if not skani_output:\n", "        print(\"Error running skani\")\n", "        return None\n", "\n", "    #4. Load and process results\n", "    df = load_and_process_results(skani_output)\n", "\n", "    #5. Generate summary table\n", "    summary_df = generate_summary_table(df, OUTPUT_DIR)\n", "\n", "    print(\"Contig-level search analysis completed!\")\n", "    return df\n", "\n", "if __name__ == \"__main__\":\n", "    results_df = main()\n"]}, {"cell_type": "code", "execution_count": 2, "id": "bcb2b7f0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original results: 40428 comparisons\n", "After filtering (ANI > 90% AND Align_fraction_query > 70%): 19282 comparisons\n", "Filtered results saved to: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/manual_skani_analysis/filtered_results.tsv\n"]}], "source": ["import pandas as pd\n", "from pathlib import Path\n", "\n", "def filter_skani_results(output_dir, ani_threshold=90, align_query_threshold=70):\n", "    \"\"\"Filter skani results based on ANI and alignment fraction thresholds\"\"\"\n", "    \n", "    # Load the summary table\n", "    summary_file = '/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/manual_skani_analysis/summary_table.tsv'\n", "    \n", "    \n", "    # Read the data\n", "    df = pd.read_csv(summary_file, sep='\\t')\n", "    print(f\"Original results: {len(df)} comparisons\")\n", "    \n", "    # Apply filters\n", "    filtered_df = df[\n", "        (df['ANI'] > ani_threshold) & \n", "        (df['Align_fraction_query'] > align_query_threshold)\n", "    ]\n", "    \n", "    print(f\"After filtering (ANI > {ani_threshold}% AND Align_fraction_query > {align_query_threshold}%): {len(filtered_df)} comparisons\")\n", "    \n", "    if len(filtered_df) == 0:\n", "        print(\"No results meet the filtering criteria\")\n", "        return filtered_df\n", "    \n", "    # Sort by ANI descending, then by align_fraction_query descending\n", "    filtered_df = filtered_df.sort_values(['ANI', 'Align_fraction_query'], ascending=[False, False])\n", "    \n", "    # Save filtered results\n", "    filtered_file = Path(output_dir) / 'filtered_results.tsv'\n", "    filtered_df.to_csv(filtered_file, sep='\\t', index=False, float_format='%.3f')\n", "    \n", "    print(f\"Filtered results saved to: {filtered_file}\")\n", "\n", "# Run the filtering\n", "OUTPUT_DIR = '/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/manual_skani_analysis/'\n", "filtered_results = filter_skani_results(OUTPUT_DIR, ani_threshold=90, align_query_threshold=70)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "35efccf9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully loaded DataFrame with shape: (40428, 7)\n", "Loaded 40428 comparison results\n", "Sample bin names: ['1507996.bin.1' '1507994.bin.1' '1507992.bin.4' '1507993.bin.1'\n", " '1507999.bin.1' '1507995.bin.1' '1507993.bin.3' '1507992.bin.3'\n", " '1507992.bin.2' '1507990.bin.2']\n", "Sample data:\n", "        Bin_Name    ANI  Align_fraction_ref  Align_fraction_query\n", "0  1507996.bin.1  98.34                8.55                 100.0\n", "1  1507994.bin.1  94.88               14.38                 100.0\n", "2  1507992.bin.4  94.88                7.91                 100.0\n", "3  1507993.bin.1  94.88                9.14                 100.0\n", "4  1507996.bin.1  94.88               14.12                 100.0\n", "\n", "=== Raw Results Analysis ===\n", "Total comparisons: 40428\n", "Unique bins: 18\n", "Unique contigs: 12138\n", "ANI range: 80.86% - 100.00%\n", "Query alignment range: 1.31% - 100.00%\n", "\n", "=== Filtering Results ===\n", "After filtering (ANI > 90% AND Align_fraction_query > 70%): 19282 comparisons\n", "\n", "=== Filtered Results by Bin ===\n", "Number of bins with matches: 18\n", "Top 10 bins by number of matches:\n", "Bin_Name\n", "1507995.bin.1    4059\n", "1507999.bin.1    3967\n", "1507996.bin.1    3808\n", "1507994.bin.1    3746\n", "1507993.bin.1    1445\n", "1507993.bin.3     747\n", "1507992.bin.3     518\n", "1507992.bin.4     282\n", "1507992.bin.2     145\n", "1507992.bin.5     113\n", "Name: count, dtype: int64\n", "\n", "Filtered results saved to: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/manual_skani_analysis/filtered_results_with_bins.tsv\n", "\n", "=== Summary Statistics ===\n", "ANI range: 90.03% - 100.00%\n", "Query alignment fraction range: 70.01% - 100.00%\n", "\n", "=== Top 10 Matches ===\n", "     Bin_Name                                          Ref_name   ANI  Align_fraction_ref  Align_fraction_query\n", "1507993.bin.1 1507993.bin.1_NODE_185_length_15375_cov_29.321084 100.0                6.97                 100.0\n", "1507995.bin.1 1507995.bin.1_NODE_1476_length_6490_cov_17.439627 100.0               16.50                 100.0\n", "1507995.bin.1 1507995.bin.1_NODE_462_length_12245_cov_23.413290 100.0                6.48                 100.0\n", "1507996.bin.1  1507996.bin.1_NODE_778_length_9128_cov_18.080128 100.0                8.69                 100.0\n", "1507994.bin.1 1507994.bin.1_NODE_2018_length_5166_cov_54.537468 100.0               16.86                 100.0\n", "1507996.bin.1 1507996.bin.1_NODE_151_length_17769_cov_37.201761 100.0                4.90                 100.0\n", "1507995.bin.1  1507995.bin.1_NODE_81_length_22277_cov_54.810863 100.0                3.91                 100.0\n", "1507993.bin.1 1507993.bin.1_NODE_362_length_11196_cov_24.935733 100.0                7.78                 100.0\n", "1507999.bin.1  1507999.bin.1_NODE_57_length_25224_cov_33.129763 100.0                3.45                 100.0\n", "1507994.bin.1 1507994.bin.1_NODE_2214_length_4806_cov_15.431909 100.0               24.30                 100.0\n"]}], "source": ["import pandas as pd\n", "import os\n", "\n", "def load_and_process_results_fixed(skani_output_file):\n", "    \"\"\"Load and process skani results with proper column handling\"\"\"\n", "    if not os.path.exists(skani_output_file):\n", "        print(f\"Error: Skani output file {skani_output_file} not found\")\n", "        return None\n", "    \n", "    try:\n", "        # Read the file manually to handle the extra columns\n", "        data = []\n", "        with open(skani_output_file, 'r') as f:\n", "            lines = f.readlines()\n", "            \n", "            # Skip header line\n", "            for line in lines[1:]:\n", "                if line.strip():\n", "                    parts = line.strip().split('\\t')\n", "                    if len(parts) >= 7:  # Make sure we have at least the core columns\n", "                        data.append({\n", "                            'Ref_file': parts[0],\n", "                            'Query_file': parts[1],\n", "                            'ANI': float(parts[2]),\n", "                            'Align_fraction_ref': float(parts[3]),\n", "                            'Align_fraction_query': float(parts[4]),\n", "                            'Ref_name': parts[5],\n", "                            'Query_name': parts[6] if len(parts) > 6 else ''\n", "                        })\n", "        \n", "        # Create DataFrame\n", "        df = pd.DataFrame(data)\n", "        \n", "        if df.empty:\n", "            print(\"No valid data found in skani output\")\n", "            return None\n", "            \n", "        print(f\"Successfully loaded DataFrame with shape: {df.shape}\")\n", "        \n", "        # Extract bin names from the Ref_name column\n", "        def extract_bin_name(ref_name):\n", "            if pd.isna(ref_name):\n", "                return \"Unknown\"\n", "            # Extract bin name from format: \"1507996.bin.1_NODE_782_length_9111_cov_124.522306\"\n", "            parts = str(ref_name).split('_')\n", "            if len(parts) > 0:\n", "                bin_part = parts[0]  # This should be \"1507996.bin.1\"\n", "                return bin_part\n", "            return \"Unknown\"\n", "        \n", "        df['Bin_Name'] = df['Ref_name'].apply(extract_bin_name)\n", "        \n", "        print(f\"Loaded {len(df)} comparison results\")\n", "        print(\"Sample bin names:\", df['Bin_Name'].unique()[:10])\n", "        print(\"Sample data:\")\n", "        print(df[['Bin_Name', 'ANI', 'Align_fraction_ref', 'Align_fraction_query']].head())\n", "        \n", "        return df\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading skani results: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        return None\n", "\n", "# Updated filtering function\n", "def filter_and_analyze_results_fixed(output_dir, ani_threshold=90, align_query_threshold=70):\n", "    \"\"\"Filter skani results and provide detailed analysis\"\"\"\n", "    \n", "    # Load the raw skani results\n", "    skani_file = '/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/manual_skani_analysis/skani_contigs_results.txt'\n", "    \n", "    \n", "    # Load and process the raw results\n", "    df = load_and_process_results_fixed(str(skani_file))\n", "    \n", "    if df is None or df.empty:\n", "        return None\n", "    \n", "    print(f\"\\n=== Raw Results Analysis ===\")\n", "    print(f\"Total comparisons: {len(df)}\")\n", "    print(f\"Unique bins: {df['Bin_Name'].nunique()}\")\n", "    print(f\"Unique contigs: {df['Ref_name'].nunique()}\")\n", "    print(f\"ANI range: {df['ANI'].min():.2f}% - {df['ANI'].max():.2f}%\")\n", "    print(f\"Query alignment range: {df['Align_fraction_query'].min():.2f}% - {df['Align_fraction_query'].max():.2f}%\")\n", "    \n", "    # Apply filters\n", "    filtered_df = df[\n", "        (df['ANI'] > ani_threshold) & \n", "        (df['Align_fraction_query'] > align_query_threshold)\n", "    ]\n", "    \n", "    print(f\"\\n=== Filtering Results ===\")\n", "    print(f\"After filtering (ANI > {ani_threshold}% AND Align_fraction_query > {align_query_threshold}%): {len(filtered_df)} comparisons\")\n", "    \n", "    if len(filtered_df) == 0:\n", "        print(\"No results meet the filtering criteria\")\n", "        return filtered_df\n", "    \n", "    # Analyze filtered results by bin\n", "    print(f\"\\n=== Filtered Results by Bin ===\")\n", "    bin_counts = filtered_df['Bin_Name'].value_counts()\n", "    print(f\"Number of bins with matches: {len(bin_counts)}\")\n", "    print(\"Top 10 bins by number of matches:\")\n", "    print(bin_counts.head(10))\n", "    \n", "    # Sort by ANI descending, then by align_fraction_query descending\n", "    filtered_df = filtered_df.sort_values(['ANI', 'Align_fraction_query'], ascending=[False, False])\n", "    \n", "    # Save filtered results\n", "    filtered_file = Path(output_dir) / 'filtered_results_with_bins.tsv'\n", "    filtered_df.to_csv(filtered_file, sep='\\t', index=False, float_format='%.3f')\n", "    \n", "    print(f\"\\nFiltered results saved to: {filtered_file}\")\n", "    \n", "    # Show summary statistics\n", "    print(f\"\\n=== Summary Statistics ===\")\n", "    print(f\"ANI range: {filtered_df['ANI'].min():.2f}% - {filtered_df['ANI'].max():.2f}%\")\n", "    print(f\"Query alignment fraction range: {filtered_df['Align_fraction_query'].min():.2f}% - {filtered_df['Align_fraction_query'].max():.2f}%\")\n", "    \n", "    # Show top matches\n", "    print(f\"\\n=== Top 10 Matches ===\")\n", "    print(filtered_df[['Bin_Name', 'Ref_name', 'ANI', 'Align_fraction_ref', 'Align_fraction_query']].head(10).to_string(index=False))\n", "    \n", "    return filtered_df\n", "\n", "# Run the corrected analysis\n", "OUTPUT_DIR = '/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/manual_skani_analysis/'\n", "results = filter_and_analyze_results_fixed(OUTPUT_DIR, ani_threshold=90, align_query_threshold=70)"]}, {"cell_type": "code", "execution_count": 7, "id": "f49776c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error: Skani output file manual_skani_analysis/skani_contigs_results.txt not found\n", "=== Comparison Analysis ===\n"]}, {"ename": "TypeError", "evalue": "object of type 'NoneType' has no len()", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[7], line 37\u001b[0m\n\u001b[1;32m     34\u001b[0m     \u001b[38;5;28mprint\u001b[39m(example_matches[[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mQuery_name\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mANI\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAlign_fraction_query\u001b[39m\u001b[38;5;124m'\u001b[39m]]\u001b[38;5;241m.\u001b[39mhead())\n\u001b[1;32m     36\u001b[0m OUTPUT_DIR \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m./manual_skani_analysis\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m---> 37\u001b[0m \u001b[43manalyze_comparison_counts\u001b[49m\u001b[43m(\u001b[49m\u001b[43mOUTPUT_DIR\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[7], line 11\u001b[0m, in \u001b[0;36manalyze_comparison_counts\u001b[0;34m(output_dir)\u001b[0m\n\u001b[1;32m      8\u001b[0m df \u001b[38;5;241m=\u001b[39m load_and_process_results_fixed(\u001b[38;5;28mstr\u001b[39m(skani_file))\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m=== Comparison Analysis ===\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 11\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTotal comparisons: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mdf\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     12\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnique contigs (Ref_name): \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mRef_name\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mnunique()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnique query sequences (Query_name): \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mQuery_name\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mnunique()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mTypeError\u001b[0m: object of type 'NoneType' has no len()"]}], "source": ["# Debug: Check what's causing the high number of comparisons\n", "from pathlib import Path\n", "\n", "def analyze_comparison_counts(output_dir):\n", "    skani_file = Path(output_dir) / 'skani_contigs_results.txt'\n", "    \n", "    # Load the results\n", "    df = load_and_process_results_fixed(str(skani_file))\n", "    \n", "    print(\"=== Comparison Analysis ===\")\n", "    print(f\"Total comparisons: {len(df)}\")\n", "    print(f\"Unique contigs (Ref_name): {df['Ref_name'].nunique()}\")\n", "    print(f\"Unique query sequences (Query_name): {df['Query_name'].nunique()}\")\n", "    \n", "    # Check how many Acanthamoeba scaffolds/sequences\n", "    print(f\"\\nUnique Acanthamoeba sequences being matched:\")\n", "    query_counts = df['Query_name'].value_counts()\n", "    print(f\"Number of different Acanthamoeba sequences: {len(query_counts)}\")\n", "    print(\"Top 10 Acanthamoeba sequences by match count:\")\n", "    print(query_counts.head(10))\n", "    \n", "    # Check if contigs are matching multiple Acanthamoeba sequences\n", "    print(f\"\\nContigs matching multiple Acanthamoeba sequences:\")\n", "    contig_match_counts = df.groupby('Ref_name')['Query_name'].nunique().sort_values(ascending=False)\n", "    print(\"Top 10 contigs by number of Acanthamoeba sequences matched:\")\n", "    print(contig_match_counts.head(10))\n", "    \n", "    # Show some examples\n", "    print(f\"\\nExample of one contig's matches:\")\n", "    example_contig = df['Ref_name'].iloc[0]\n", "    example_matches = df[df['Ref_name'] == example_contig]\n", "    print(f\"Contig: {example_contig}\")\n", "    print(f\"Number of matches: {len(example_matches)}\")\n", "    print(example_matches[['Query_name', 'ANI', 'Align_fraction_query']].head())\n", "\n", "OUTPUT_DIR = './manual_skani_analysis'\n", "analyze_comparison_counts(OUTPUT_DIR)"]}, {"cell_type": "code", "execution_count": 5, "id": "efb7263f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import pandas as pd\n", "\n", "df = pd.read_csv('final_filtered_matches.csv')\n", "\n", "# Add Treatment column based on Bin_Name\n", "MDA_samples = ['1507990', '1507992', '1507993']\n", "def get_treatment(bin_name):\n", "    sample_id = str(bin_name).split('.')[0]\n", "    return 'MDA' if sample_id in MDA_samples else 'PTA'\n", "df['Treatment'] = df['Bin_Name'].apply(get_treatment)\n", "\n", "plt.figure(figsize=(14, 10))\n", "bins = 50\n", "\n", "plt.subplot(1, 2, 1)\n", "sns.histplot(data=df[df['Treatment'] == 'PTA'], x='ANI', bins=bins, color='orange', alpha=0.5, edgecolor='black', label='PTA')\n", "sns.histplot(data=df[df['Treatment'] == 'MDA'], x='ANI', bins=bins, color='grey', alpha=0.5, edgecolor='black', label='MDA')\n", "plt.xlabel('ANI (%)')\n", "plt.ylabel('Number of Contigs')\n", "plt.title('Distribution of ANI Values by Treatment')\n", "# plt.xlim(df['ANI'].min(), df['ANI'].max())\n", "plt.legend()\n", "\n", "plt.subplot(1, 2, 2)\n", "sns.histplot(data=df[df['Treatment'] == 'PTA'], x='Align_fraction_ref', bins=bins, color='orange', alpha=0.5, edgecolor='black', label='PTA')\n", "sns.histplot(data=df[df['Treatment'] == 'MDA'], x='Align_fraction_ref', bins=bins, color='grey', alpha=0.5, edgecolor='black', label='MDA')\n", "plt.xlabel('Reference Alignment Fraction (%)')\n", "plt.ylabel('Number of Contigs')\n", "plt.title('Reference Alignment Coverage by Treatment')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 10, "id": "63cf7eaf", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAArIAAAHqCAYAAAD4TK2HAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAACVcUlEQVR4nOzdd1RUx9sH8O9SdlkQUKqACFhAY++CxoJIUWwx9gaWaBKixhJbNNaoMZYkxhKDqKhg7wVRgonRKLHFFisKKIgYAUHqMu8fvuzPlY7Auvr9nHPPCffOzH3uQOBxdmauRAghQERERESkYbTUHQARERERUWkwkSUiIiIijcREloiIiIg0EhNZIiIiItJITGSJiIiISCMxkSUiIiIijcREloiIiIg0EhNZIiIiItJITGSJiIiISCMxkaX31oYNGyCRSCCRSBAeHp7nuhACtWrVgkQiQYcOHSo8vtJq2rQpJBIJvv/++3yv5z63np4eHjx4kOd6hw4dUL9+fZVz9vb28Pb2Lpd4S8Le3l75PZNIJDAwMEDTpk2xcuVKlPYlhZmZmRgzZgysrKygra2Nxo0bl23QGignJweBgYFwc3ODmZkZdHV1YWFhAW9vbxw4cAA5OTnqDlGjVNT/P0+fPsW0adPwwQcfwMDAAMbGxqhTpw6GDBmCf/75p9zv/ybCw8ML/F1MVBgmsvTeMzQ0hL+/f57zJ0+exN27d2FoaKiGqErn0qVLuHjxIgDk+0yvysjIwNdff10RYZWpNm3a4MyZMzhz5gwCAwOhr6+PL774AgsXLixVe6tXr8batWsxY8YMnDp1CoGBgWUcsWZJT09Hly5dMGzYMFhYWGD16tUICwvDmjVrYG1tjT59+uDAgQPqDpNek5KSgtatW2PDhg0YOXIk9u/fjy1btuCTTz5BZGQkLl26pO4QicqFjroDIFK3fv36YcuWLfj5559hZGSkPO/v7w9nZ2ckJyerMbqS+fXXXwEAXbt2xaFDh3D69Gm4uLjkW9bT0xNbt27FpEmT0KhRo4oM841UrlwZrVu3Vn7t5uaG6tWrY+3atZg+fXqJ27t69Srkcjn8/PzKLMYXL15AX1+/zNqrSBMmTEBISAg2btyIoUOHqlz76KOPMHnyZKSlpakpuoIJIZCeng65XK7uUNRix44duHPnDsLCwtCxY0eVaxMmTOAoOr2zOCJL770BAwYAAIKCgpTnkpKSsGvXLgwfPjzfOpmZmZg/fz7q1KkDmUwGc3Nz+Pr64smTJyrltm3bBnd3d1hZWUEul6Nu3bqYOnUqUlNTVcr5+PigUqVKuHPnDrp06YJKlSrB1tYWEydOREZGRrGeIz09HVu3bkWzZs2wfPlyAMD69esLLP/VV1/B1NQUU6ZMKVb7RenZsyfs7Ozy/YPZqlUrNG3aVPn1jh070KpVKxgbG0NfXx81atQosK+LYmRkBEdHRzx+/FjlfHG+RxKJBL/++ivS0tKU0xU2bNgA4GVitGrVKjRu3BhyuRxVqlTBxx9/jHv37qncJ3cqxu+//w4XFxfo6+srnyU5ORmTJk2Cg4MDpFIpbGxsMH78+Dzff4lEAj8/PwQGBqJu3brQ19dHo0aNcPDgwTzP+++//2LAgAGwtLSETCZD9erVMXToUJWfk7i4OIwePRrVqlWDVCqFg4MD5syZg+zs7EL7Mi4uDr/++is8PDzyJLG5ateujYYNGyq/joqKwuDBg2FhYQGZTIa6deti6dKlyp+DrKwsWFhYYMiQIXnaSkxMhFwux4QJE5TnStpna9asQd26dSGTybBx40YAwJw5c9CqVSuYmJjAyMgITZs2hb+/f57pJxkZGZg4cSKqVq0KfX19tGvXDufPn4e9vT18fHzy9E1p+vRVe/bsQcOGDaGnp4caNWrgxx9/VF5LSUlB5cqVMXr06Dz17t+/D21tbSxZsqTAtp8+fQoAsLKyyve6ltb//tzfuXMHvr6+qF27NvT19WFjY4Nu3brhypUrKnVyP+7funUrpkyZAisrK1SqVAndunXD48eP8fz5c3zyyScwMzODmZkZfH19kZKSotJG7vdp7dq1cHR0hEwmwwcffIDg4OCiOwzA33//je7du8PExAR6enpo0qQJtm/fXqy69J4QRO+pgIAAAUBERESIIUOGiJYtWyqvrV69WhgYGIjk5GRRr1490b59e+U1hUIhPD09hYGBgZgzZ44IDQ0Vv/76q7CxsREffPCBePHihbLsvHnzxPLly8WhQ4dEeHi4WLNmjXBwcBAdO3ZUiWXYsGFCKpWKunXriu+//14cP35czJo1S0gkEjFnzpxiPc+WLVsEAPHzzz8LIYRo27atqFSpknj+/HmBz/3DDz8IAOLEiRPK6+3btxf16tVTqWNnZye6du1a6P337dsnAIjQ0FCV8zdu3BAAxI8//iiEEOL06dNCIpGI/v37i8OHD4uwsDAREBAghgwZUuQz5hdHVlaWqFq1qmjQoIHyXHG/R2fOnBFdunQRcrlcnDlzRpw5c0bEx8cLIYQYNWqU0NXVFRMnThRHjx4VW7duFXXq1BGWlpYiLi5Opb9MTEyEra2t+Omnn8Rvv/0mTp48KVJTU0Xjxo2FmZmZWLZsmTh+/Lj44YcfhLGxsXB1dRU5OTnKNgAIe3t70bJlS7F9+3Zx+PBh0aFDB6GjoyPu3r2rLHfp0iVRqVIlYW9vL9asWSNOnDghNm/eLPr27SuSk5OFEELExsYKW1tbYWdnJ9auXSuOHz8u5s2bJ2QymfDx8Sm0f7du3SoAiNWrVxf5vRBCiPj4eGFjYyPMzc3FmjVrxNGjR4Wfn58AID799FNluS+//FLI5XKRlJSkUn/VqlUCgPjnn3+EEKLEfWZjYyMaNmwotm7dKsLCwsTVq1eFEEL4+PgIf39/ERoaKkJDQ8W8efOEXC7P8//SgAEDhJaWlpg6dao4duyYWLFihbC1tRXGxsZi2LBhynJv0qdCvPy5tbGxEdWrVxfr168Xhw8fFoMGDRIAxJIlS1T6ycDAQCQmJqrUnzx5stDT0xMJCQkF3uPUqVMCgGjRooXYs2dPoWVPnjwpJk6cKHbu3ClOnjwp9uzZI3r27Cnkcrn4999/leV+++03AUDY2dkJHx8fcfToUbFmzRpRqVIl0bFjR9G5c2cxadIkcezYMbF48WKhra0tvvjiC5V7ARC2trbigw8+EEFBQWL//v3C09NTABA7duzIc6/ffvtNeS4sLExIpVLx4Ycfim3btomjR48KHx8fAUAEBAQU1e30nmAiS++tVxO63F+iuX8IW7RoofwD9XoiGxQUJACIXbt2qbQXEREhAIhVq1ble7+cnByRlZUlTp48KQCIy5cvK68NGzZMABDbt29XqdOlSxfh5ORUrOdxdXUVenp64tmzZyrP5+/vX+BzZ2RkiBo1aojmzZsrk4TSJrJZWVnC0tJSDBw4UOX8V199JaRSqfIP6/fffy8A5PljXRx2dnaiS5cuIisrS2RlZYkHDx4oE86DBw8qy5XkezRs2DBhYGCgUu7MmTMCgFi6dKnK+ejoaCGXy8VXX32lPNe+ffs8/xgQQoiFCxcKLS0tERERoXJ+586dAoA4fPiw8hwAYWlpqUxGhRAiLi5OaGlpiYULFyrPubq6isqVKyuT7fyMHj1aVKpUSTx48EDlfG6/X7t2rcC6ixYtEgDE0aNHCyzzqqlTpwoA4uzZsyrnP/30UyGRSMTNmzeFEEL8888/AoD45ZdfVMq1bNlSNGvWTPl1SfvM2NhY/Pfff4XGqFAoRFZWlpg7d64wNTVV/pxfu3ZNABBTpkxRKZ/7s/NqIvsmfSrEy59biUQiLl26pHK+c+fOwsjISKSmpgohhLh7967Q0tISy5cvV5ZJS0sTpqamwtfXt9B7CCHE3LlzhVQqFQAEAOHg4CDGjBmj8rsmP9nZ2SIzM1PUrl1bfPnll8rzub8Xu3XrplJ+/PjxAoAYO3asyvmePXsKExMTlXMAhFwuV/nHX3Z2tqhTp46oVatWnnu9msjWqVNHNGnSRGRlZam06e3tLaysrIRCoSi8Q+i9wKkFRADat2+PmjVrYv369bhy5QoiIiIK/Kj74MGDqFy5Mrp164bs7Gzl0bhxY1StWlVl1e29e/cwcOBAVK1aFdra2tDV1UX79u0BADdu3FBpVyKRoFu3birnGjZsmO/OAq+LjIzEb7/9ho8++giVK1cGAPTp0weGhoaFTi+QSqWYP38+/v777zf+uE5HRweDBw/G7t27kZSUBABQKBQIDAxEjx49YGpqCgBo0aIFAKBv377Yvn07Hj58WKL7HD58GLq6utDV1YWdnR3WrVuHn376CV27dlWWKcn3KD8HDx6ERCLB4MGDVepXrVoVjRo1ylO/SpUqcHV1zdNG/fr10bhxY5U2PDw88l2d3bFjR5WFhZaWlrCwsFB+/1+8eIGTJ0+ib9++MDc3LzT2jh07wtraWuW+Xl5eAF4uYiwrYWFh+OCDD9CyZUuV8z4+PhBCICwsDADQoEEDNGvWDAEBAcoyN27cwLlz51T+Pytpn7m6uqJKlSr5xuXm5gZjY2Pl/3ezZs3C06dPER8fD+B//dC3b1+Vuh9//DF0dFSXj5RFn9arVy/PXPSBAwciOTkZFy5cAADUqFED3t7eWLVqlXIaxNatW/H06dNizeGeOXMmoqKisH79eowePRqVKlXCmjVr0KxZM5WpU9nZ2fj222/xwQcfQCqVQkdHB1KpFLdv387zewlAnh0X6tatCwAq/8/lnv/vv//yTC/o1KkTLC0tlV9ra2ujX79+uHPnDmJiYvJ9ljt37uDff//FoEGDlDHnHl26dEFsbCxu3rxZZJ/Qu4+JLBFeJpG+vr7YvHkz1qxZA0dHR3z44Yf5ln38+DESExMhlUqVCVXuERcXh4SEBAAv57x9+OGHOHv2LObPn4/w8HBERERg9+7dAJBnwYy+vj709PRUzslkMqSnpxcZ//r16yGEwMcff4zExEQkJiYiKysL3bt3x59//ol///23wLr9+/dH06ZNMWPGDGRlZRV5r8IMHz4c6enpyvlvISEhiI2Nha+vr7JMu3btsHfvXmRnZ2Po0KGoVq0a6tevr/KHtjBt27ZFREQE/vrrLwQGBsLe3h5+fn44deqUskxxv0cFefz4MYQQsLS0zFP/r7/+ylM/v3mJjx8/xj///JOnvqGhIYQQedrITfRfJZPJlD8nz549g0KhQLVq1YqM/cCBA3nuW69ePQAo9NmrV68O4OU/jIrj6dOn+T67tbW18nqu4cOH48yZM8qfxYCAAMhkMuUc9dzYS9Jn+d373LlzcHd3BwCsW7cOf/75JyIiIjBjxgwA//v/Lje2VxMs4OU/yF7/XrxJn+aqWrVqgede7adx48bh9u3bCA0NBQD8/PPPcHZ2VpljXhhLS0v4+vpizZo1+Oeff3Dy5ElIpVKMGzdOWWbChAmYOXMmevbsiQMHDuDs2bOIiIhAo0aN8l3IZ2JiovK1VCot9Pzrv7OK++yvyp3zPmnSpDz9/tlnnwEoXr/Tu4+7FhD9Px8fH8yaNQtr1qzBggULCixnZmYGU1NTHD16NN/ruaNqYWFhePToEcLDw5WjsMDLBS5lKScnR7lA6aOPPsq3zPr16/Hdd9/le00ikWDx4sXo3LkzfvnllzeKJXd0LiAgAKNHj0ZAQACsra2ViUWuHj16oEePHsjIyMBff/2FhQsXYuDAgbC3t4ezs3Oh9zA2Nkbz5s0BvFxE1qpVKzRq1AifffYZLl26BC0trWJ/jwpiZmYGiUSCP/74AzKZLM/1189JJJJ825DL5QWOiJuZmRUaw+tMTEygra1d4AjWq+02bNiwwJ/h3CQzPx07doSuri727t2LMWPGFBmTqakpYmNj85x/9OiRMpZcAwYMwIQJE7BhwwYsWLAAgYGB6Nmzp8qIakn7LL9+Dw4Ohq6uLg4ePKjyD8O9e/fmiR14mTDZ2Ngoz2dnZ+dJrt6kT3PFxcUVeO7VxNnV1RX169fHypUrUalSJVy4cAGbN28usv2CtGvXDu7u7ti7dy/i4+NhYWGBzZs3Y+jQofj2229VyiYkJCg/0SlLxX32V+V+r6dNm1bg7zUnJ6cyipA0GRNZov9nY2ODyZMn499//8WwYcMKLOft7Y3g4GAoFAq0atWqwHK5f2RfT3rWrl1bNgH/v5CQEMTExODzzz/Hxx9/nOe6n58fNm3ahG+//TbPR6a53Nzc0LlzZ8ydOxe2trZvFI+vry8+/fRTnDp1CgcOHMCECROgra2db1mZTIb27dujcuXKCAkJwcWLF4tMZF9Xu3ZtfPXVV5gzZw62bduGAQMGFPt7VBBvb28sWrQIDx8+zPPRc0na+Pbbb2FqagoHB4dStfEquVyO9u3bY8eOHViwYEGBibC3tzcOHz6MmjVr5vuxe2GqVq2KkSNHYvXq1di0aVO+OxfcvXsXqampaNiwITp16oSFCxfiwoULKiOGmzZtgkQiUdkGqkqVKujZsyc2bdoEZ2dnxMXF5Zm+UxZ9JpFIoKOjo/Izl5aWlmd/4Hbt2gF4ubPIq7Hv3Lkzz04Eb9Knua5du4bLly+rTC/YunUrDA0N84y2jh07FmPGjEFSUhIsLS3Rp0+fItt//PgxzM3NVXYnAF5O77l9+zb09fWVSapEIsnze+nQoUN4+PAhatWqVarnK8yJEyfw+PFj5ei3QqHAtm3bULNmzQI/YXByckLt2rVx+fLlPAk30auYyBK9YtGiRUWW6d+/P7Zs2YIuXbpg3LhxaNmyJXR1dRETE4PffvsNPXr0QK9eveDi4oIqVapgzJgx+Oabb6Crq4stW7bg8uXLZRqzv78/dHR0MH369HxHhkaPHo2xY8fi0KFD6NGjR4HtLF68GM2aNUN8fLzyI9PSyB15GzBgADIyMvJsYzRr1izExMSgU6dOqFatGhITE/HDDz+ozB8uqUmTJmHNmjWYM2cO+vbtW+zvUUHatGmDTz75BL6+vvj777/Rrl07GBgYIDY2FqdOnUKDBg3w6aefFhrT+PHjsWvXLrRr1w5ffvklGjZsiJycHERFReHYsWOYOHFiiZPsZcuWoW3btmjVqhWmTp2KWrVq4fHjx9i/fz/Wrl0LQ0NDzJ07F6GhoXBxccHYsWPh5OSE9PR03L9/H4cPH8aaNWsKnZ6wbNky3Lt3Dz4+PggJCUGvXr1gaWmJhIQEhIaGIiAgAMHBwWjYsCG+/PJLbNq0CV27dsXcuXNhZ2eHQ4cOYdWqVfj000/h6Oio0vbw4cOxbds2+Pn5oVq1anBzcyvzPuvatSuWLVuGgQMH4pNPPsHTp0/x/fff50nc6tWrhwEDBmDp0qXQ1taGq6srrl27hqVLl8LY2FglIXzTPgVejtp2794ds2fPhpWVFTZv3ozQ0FAsXrw4z57DgwcPxrRp0/D777/j66+/Vn5kX5jAwECsXbsWAwcORIsWLWBsbIyYmBj8+uuvuHbtGmbNmqVsx9vbGxs2bECdOnXQsGFDnD9/HkuWLCnyGUrLzMwMrq6umDlzJgwMDLBq1Sr8+++/RW7BtXbtWnh5ecHDwwM+Pj6wsbHBf//9hxs3buDChQvYsWNHucRLGkadK82I1OnV1fuFeX3XAiFertD//vvvRaNGjYSenp6oVKmSqFOnjhg9erS4ffu2stzp06eFs7Oz0NfXF+bm5mLkyJHiwoULebaPyW/lvBBCfPPNN6Kw/02fPHkipFKp6NmzZ4Flnj17JuRyuXLlcWHPPXDgQAGgVLsW5NdOmzZt8lw7ePCg8PLyEjY2NkIqlQoLCwvRpUsX8ccffxTZbmFx/PzzzwKA2LhxoxCi+N+jgvpeCCHWr18vWrVqJQwMDIRcLhc1a9YUQ4cOFX///beyTH67PORKSUkRX3/9tXBychJSqVQYGxuLBg0aiC+//FJlFTcA8fnnn+f7vK+unhdCiOvXr4s+ffoIU1NTIZVKRfXq1YWPj49IT09Xlnny5IkYO3ascHBwELq6usLExEQ0a9ZMzJgxQ6SkpOQb66uys7PFxo0bhaurqzAxMRE6OjrC3NxceHl5ia1bt6qsFn/w4IEYOHCgMDU1Fbq6usLJyUksWbIk3xXlCoVC2NraCgBixowZ5dJnQrz8vjk5OQmZTCZq1KghFi5cKPz9/QUAERkZqSyXnp4uJkyYICwsLISenp5o3bq1OHPmjDA2NlZZvf+mfZr7c7tz505Rr149IZVKhb29vVi2bFmBdXx8fISOjo6IiYkptO1c169fFxMnThTNmzcX5ubmQkdHR1SpUkW0b99eBAYGqpR99uyZGDFihLCwsBD6+vqibdu24o8//hDt27dX+V2Xu5PAq9tkCVHw75Dc31dPnjxRnsv9Pq1atUrUrFlT6Orqijp16ogtW7ao1M1v1wIhhLh8+bLo27evsLCwELq6uqJq1arC1dVVrFmzplj9Qu8+iRClfEE5ERHRO+b06dNo06YNtmzZgoEDB6olhszMTNjb26Nt27Yav/m/RCLB559/jpUrV6o7FHpHcWoBERG9l0JDQ3HmzBk0a9YMcrkcly9fxqJFi1C7du0CFxiVpydPnuDmzZsICAjA48ePMXXq1AqPgUjTMJElIqL3kpGREY4dO4YVK1bg+fPnMDMzg5eXFxYuXJhnK7yKcOjQIfj6+sLKygqrVq0q9pZbRO8zTi0gIiIiIo3EFyIQERERkUZiIktEREREGomJLBERERFpJC72ykdOTg4ePXoEQ0PDfF+BSERERETlQwiB58+fw9raOs/b6l7HRDYfjx49euPXdBIRERFR6UVHRxf5xjkmsvkwNDQE8LIDjYyM1BwNERER0fsjOTkZtra2ynysMExk85E7ncDIyIiJLBEREZEaFGd6Jxd7EREREZFGYiJLRERERBqJiSwRERERaSQmsm9AoVAgPT2dRymOnJwcdX/7iIjoLfT777+jW7dusLa2hkQiwd69e1Wu+/j4QCKRqBytW7dWKZORkYEvvvgCZmZmMDAwQPfu3RETE6O8Hh4enqeN3CMiIkJZ7sSJE3BxcYGhoSGsrKwwZcoUZGdnq9xr+/btaNy4MfT19WFnZ4clS5aoXC/oXv/++28Z9dj7jYu9SkEIgbi4OCQmJqo7FI2lpaUFBwcHSKVSdYdCRERvkdTUVDRq1Ai+vr7o3bt3vmU8PT0REBCg/Pr1vyXjx4/HgQMHEBwcDFNTU0ycOBHe3t44f/48tLW14eLigtjYWJU6M2fOxPHjx9G8eXMAwD///IMuXbpgxowZ2LRpEx4+fIgxY8ZAoVDg+++/BwAcOXIEgwYNwk8//QR3d3fcuHEDI0eOhFwuh5+fn0r7N2/eVFlAbm5uXvpOIiWJEEKoO4i3TXJyMoyNjZGUlJTvrgWxsbFITEyEhYUF9PX1+dKEEsp94YSuri6qV6/O/iMionxJJBLs2bMHPXv2VJ7z8fFBYmJinpHaXElJSTA3N0dgYCD69esH4H/7wx8+fBgeHh556mRlZaFatWrw8/PDzJkzAQDTp09HaGioygjt3r17MWDAAMTHx8PQ0BADBw5EVlYWduzYoSyzYsUKLF26FFFRUZBIJAgPD0fHjh3x7NkzVK5c+c075T1QVB72Ko7IlpBCoVAmsaampuoOR2OZm5vj0aNHyM7Ohq6urrrDISIiDRIeHg4LCwtUrlwZ7du3x4IFC2BhYQEAOH/+PLKysuDu7q4sb21tjfr16+P06dP5JrL79+9HQkICfHx8lOcyMjKgp6enUk4ulyM9PR3nz59Hhw4dkJGRAX19/TxlYmJi8ODBA9jb2yvPN2nSBOnp6fjggw/w9ddfo2PHjmXQE8Q5siWUlZUFAHl+cKlkcj8GUigUao6EiIg0iZeXF7Zs2YKwsDAsXboUERERcHV1RUZGBgAgLi4OUqkUVapUUalnaWmJuLi4fNv09/eHh4eHyls9PTw8cPr0aQQFBUGhUODhw4eYP38+ACinJXh4eGD37t04ceIEcnJycOvWLaxYsUKljJWVFX755Rfs2rULu3fvhpOTEzp16oQ1a9Zg8+bNWLduHXbs2IFHjx6VaT+9LzgiW0r8OPzNsP+IiKg0cqcLAED9+vXRvHlz2NnZ4dChQ/joo48KrCeEyPdvT0xMDEJCQrB9+3aV8+7u7liyZAnGjBmDIUOGQCaTYebMmTh16hS0tbUBAKNGjcLdu3fh7e2NrKwsGBkZYdy4cZg9e7ayjJOTE5ycnAC8nP/bsGFD6BsY4NNPP4WBmTW0dGXISk1CVmoyuvfogXFjv0D79u3fuJ/eFxyRJSIiIo1lZWUFOzs73L59GwBQtWpVZGZm4tmzZyrl4uPjYWlpmad+QEAATE1N0b179zzXJkyYgMTERERFRSEhIQE9evQAADg4OAB4OSizePFipKSk4MGDB4iLi0PLli0BQGVaAfDytfdNW7TEl1NnItukBnQqW8FsxC8wGfoTLMZshFm/+ThxMwGundzw5ZcTuLtPMTGRJSIiIo319OlTREdHw8rKCgDQrFkz6OrqIjQ0VFkmNjYWV69ehYuLi0pdIQQCAgIwdOjQAtdrSCQSWFtbQy6XIygoCLa2tmjatKlKGW1tbdjY2EAqlSIoKAjOzs7KObvAyyS6Tbv2iM2Uw9x3FbT1KkHbyFzlHnq29VHFezLM+3+L1ev8Mf7LL9+4b94HTGTLUEF70uUer04iL0uzZ89G48aNy6Xt0sjdM4/bkxERUUmlpKTg0qVLuHTpEgAgMjISly5dQlRUFFJSUjBp0iScOXMG9+/fR3h4OLp16wYzMzP06tULAGBsbIwRI0Zg4sSJOHHiBC5evIjBgwejQYMGcHNzU7lXWFgYIiMjMWLEiHxjWbJkCa5cuYJr165h3rx5WLRoEX788UfltIGEhASsWbMG//77Ly5duoRx48Zhx44dynmywMtdDLp6d8PTDC0YtBmMpL+248Wt0zBq6p3vPfWqfYAqvWZh9Zq12L9//xv25ruPc2TL0Kt70m3btg2zZs3CzZs3lefkcrlK+aysLK7YJyIiesXff/+tsqJ/woQJAIBhw4Zh9erVuHLlCjZt2oTExERYWVmhY8eO2LZtGwwNDZV1li9fDh0dHfTt2xdpaWno1KkTNmzYoExAc/n7+8PFxQV169bNN5YjR45gwYIFyMjIQKNGjbBv3z54eXmplNm4cSMmTZoEIQScnZ0RHh6unF4AvFx89nfEOUBbB+nbZ0LXrDosPv4G8potCuwDmU0d6NfvhKUrfsh3ygP9D0dky1DVqlWVh7GxMSQSifLr9PR0VK5cGdu3b0eHDh2gp6eHzZs3A3g5P6du3brQ09NDnTp1sGrVKpV2p0yZAkdHR+jr66NGjRqYOXOmcveEDRs2YM6cObh8+bJy5HfDhg0AXo4Qr127Ft7e3tDX10fdunVx5swZ3LlzBx06dICBgQGcnZ1x9+5dlfsdOHAAzZo1g56eHmrUqIE5c+aovMlEIpHg119/Ra9evaCvr4/atWsr/9V4//595S+gKlWqlOtINBERvXs6dOgAIUSeY8OGDZDL5QgJCUF8fDwyMzPx4MEDbNiwQWW3AQDQ09PDTz/9hKdPn+LFixc4cOBAnjIAsHXrVvz5558FxhIWFobExESkpaXhr7/+ypPEmpmZ4cyZM0hJSUFqaiqOHz+OVq1aqZSRSCQwrOYEu0l7YTsuGFUHfVdoEpvLoHEX/P5bmMqAGOXFRLaCTZkyBWPHjsWNGzfg4eGBdevWYcaMGViwYAFu3LiBb7/9FjNnzsTGjRuVdQwNDbFhwwZcv34dP/zwA9atW4fly5cDeLl6c+LEiahXrx5iY2MRGxursqJz3rx5GDp0KC5duoQ6depg4MCBGD16NKZNm4a///4bAFTePhISEoLBgwdj7NixuH79OtauXYsNGzZgwYIFKs8xZ84c9O3bV/nmk0GDBuG///6Dra0tdu3aBeDlW0xiY2Pxww8/lFt/EhERvc327D8IHcd2Ja4nNbeHoXUtHDlypByiencwka1g48ePx0cffQQHBwdYW1tj3rx5WLp0qfLcRx99hC+//BJr165V1vn666/h4uICe3t7dOvWDRMnTlRuEyKXy1GpUiXo6OgoR39fncLg6+uLvn37wtHREVOmTMH9+/cxaNAgeHh4oG7duhg3bhzCw8OV5RcsWICpU6di2LBhqFGjBjp37ox58+apxAO8fLPKgAEDUKtWLXz77bdITU3FuXPnoK2tDRMTEwCAhYWFcnSaiIjoffTs2TNoV6pSdMF8aBlUxn///VfGEb1bOEe2guW+wxkAnjx5gujoaIwYMQKjRo1Sns/OzlZJ/nbu3IkVK1bgzp07SElJQXZ2dpGvbMvVsGFD5X/nbjvSoEEDlXPp6elITk6GkZERzp8/j4iICJURWIVCgfT0dLx48UL5IohX2zUwMIChoSHi4+OL2w1ERETvBR0dXWQososumB9FtvIFQpQ/JrIVzMDAQPnfuXvErVu3Ls+cmtwJ6X/99Rf69++POXPmwMPDA8bGxggODsbSpUuLdb9XF5PlbgSd37ncWHJycjBnzpx8N5V+9VV9ry9Sk0gk3POOiIjoNQ72drgSfw+Aa4nqCUUWMhKiYGdnVz6BvSOYyKqRpaUlbGxscO/ePQwaNCjfMn/++Sfs7OwwY8YM5bkHDx6olJFKpWX2qtemTZvi5s2bqFWrVqnb4OtniYiIXho9agRGfeqHnA+HQEtXVux6L279Ba2cLOW2YpQ/JrJqNnv2bIwdOxZGRkbw8vJCRkYG/v77bzx79gwTJkxArVq1EBUVheDgYLRo0QKHDh3Cnj17VNqwt7dX7rNXrVo1GBoaQiYr/v8sr5o1axa8vb1ha2uLPn36QEtLC//88w+uXLmifMd0Uezs7CCRSHDw4EF06dJFOY+XiIjofdOnTx/4jR2P1OvhMGzkUaw6QghkXD4EX59h/PtZBC72UrORI0fi119/xYYNG9CgQQO0b98eGzZsUL7+rkePHvjyyy/h5+eHxo0b4/Tp05g5c6ZKG71794anpyc6duwIc3NzBAUFlToeDw8PHDx4EKGhoWjRogVat26NZcuWleijDRsbG8yZMwdTp06FpaWlyq4IRERE7xM9PT3M+no6kn/7FRmxt4ssL4RA8h+bkPNfFCbw7V5FkgghhLqDeNskJyfD2NgYSUlJeRZVpaenIzIyEg4ODipzRqlk2I9ERPS+EELg088+x/oNm2Ds/jn0ndpAoqWdp5ziRRKS/9iMjH/DcfTIYXTo0KHig30LFJaHvY5TC4iIiIjKkUQiwepVP6O6bTXMnTcfqac2QlbfAzLrOpDoSKFIS0bGrVNI/fcUqtnaYucfv6vsckQF49QCIiIionImkUgwffp0xMU+wqJvpsM8/m/8t3sO4jZPQmrIcrS1M8CRQwdx7/YtJrElwBFZIiIiogpSuXJljB07FmPHjgXwcttLLS2OK5YWe46IiIhITZjEvhn2HhERERFpJCayRERERKSR1JrIPn/+HOPHj4ednR3kcjlcXFwQERGhvO7j4wOJRKJytG7dutjtBwcHQyKRoGfPnuUQPRERERGpk1oXe40cORJXr15FYGAgrK2tsXnzZri5ueH69euwsbEBAHh6eiIgIEBZJ/f1p0V58OABJk2ahA8//LBcYn9TCQkJCAgIwIVz5/A8MRGGlSujacuW8PX1hZmZmbrDIyIiInrrqW1ENi0tDbt27cJ3332Hdu3aoVatWpg9ezYcHBywevVqZTmZTIaqVasqDxMTkyLbVigUGDRoEObMmYMaNWqU52OUWFRUFIYOHAgbKyusmzkTZjt3otHx4zDbuRO/zJyJalZWGDpoEKKiotQdKhEREdFbTW2JbHZ2NhQKRZ63Osnlcpw6dUr5dXh4OCwsLODo6IhRo0YhPj6+yLbnzp0Lc3NzjBgxoszjfhOXL19GqyZNELVjBw5mZ+PfjAz8BGABgJ8A3MzIwIHsbERt345WTZrg8uXLao6YiIiI6O2ltkTW0NAQzs7OmDdvHh49egSFQoHNmzfj7NmziI2NBQB4eXlhy5YtCAsLw9KlSxEREQFXV1dkZGQU2O6ff/4Jf39/rFu3rtixZGRkIDk5WeUoa1FRUfB0dYVnYiKOZ2ejM/J2vhaAzgCOZ2fDIzERnq6uZToymzvneMyYMXmuffbZZ5BIJPDx8VEpK5FIoKurC0tLS3Tu3Bnr169HTk5Ovu27u7tDW1sbf/31V5nFTERERFQQtS72CgwMhBACNjY2kMlk+PHHHzFw4EBoa798/3C/fv3QtWtX1K9fH926dcORI0dw69YtHDp0KN/2nj9/jsGDB2PdunUlmme6cOFCGBsbKw9bW9syeb5XfT11KpySk7EuJ6fIick6AH7NyYFTcjK+njq1TOOwtbVFcHAw0tLSlOfS09MRFBSE6tWrq5T19PREbGws7t+/jyNHjqBjx44YN24cvL29kZ2drVI2KioKZ86cgZ+fH/z9/cs0ZiIiIqL8qDWRrVmzJk6ePImUlBRER0fj3LlzyMrKgoODQ77lraysYGdnh9u3b+d7/e7du7h//z66desGHR0d6OjoYNOmTdi/fz90dHRw9+7dfOtNmzYNSUlJyiM6OrrMnhF4ubBr244dmJGdXezVdToApmdnY/uOHUhISCizWJo2bYrq1atj9+7dynO7d++Gra0tmjRpolI2d36yjY0NmjZtiunTp2Pfvn04cuQINmzYoFI2ICAA3t7e+PTTT7Ft2zakpqaWWcxERESl9fvvv6Nbt26wtraGRCLB3r17Va4XZ4ekjIwMfPHFFzAzM4OBgQG6d++OmJgY5fXw8PA8beQer+7GdOLECbi4uMDQ0BBWVlaYMmVKnoGh7du3o3HjxtDX14ednR2WLFmS55l+/vln1K1bF3K5HE5OTti0aVMZ9JRmeiv2kTUwMICVlRWePXuGkJAQ9OjRI99yT58+RXR0NKysrPK9XqdOHVy5cgWXLl1SHt27d0fHjh1x6dKlAkdaZTIZjIyMVI6yFBAQADttbXQqYT03ALba2iq7NpQFX19flTbXr1+P4cOHF6uuq6srGjVqpJIICyEQEBCAwYMHo06dOnB0dMT27dvLNGYiIqLSSE1NRaNGjbBy5coCy+R+Apl7HD58WOX6+PHjsWfPHgQHB+PUqVNISUmBt7c3FAoFAMDFxUWlfmxsLEaOHAl7e3s0b94cAPDPP/+gS5cu8PT0xMWLFxEcHIz9+/dj6iufvB45cgSDBg3CmDFjcPXqVaxatQrLli1TiX316tWYNm0aZs+ejWvXrmHOnDn4/PPPceDAgbLsNs0h1Ojo0aPiyJEj4t69e+LYsWOiUaNGomXLliIzM1M8f/5cTJw4UZw+fVpERkaK3377TTg7OwsbGxuRnJysbGPIkCFi6tSpBd5j2LBhokePHiWKKykpSQAQSUlJea6lpaWJ69evi7S0tGK31//jj4UfIEQpDj9ADOjTp0TxFyS3L548eSJkMpmIjIwU9+/fF3p6euLJkyeiR48eYtiwYSpl89OvXz9Rt25d5dfHjh0T5ubmIisrSwghxPLly0WbNm0KjaU0/UhERPQmAIg9e/aonCsqT0hMTBS6uroiODhYee7hw4dCS0tLHD16NN86mZmZwsLCQsydO1d5btq0aaJ58+Yq5fbs2SP09PSUec2AAQPExx9/rFJm+fLlolq1aiInJ0cIIYSzs7OYNGmSSplx48YV+XdXkxSWh71OrSOySUlJ+Pzzz1GnTh0MHToUbdu2xbFjx6CrqwttbW1cuXIFPXr0gKOjI4YNGwZHR0ecOXMGhoaGyjaioqKUi8PeVs8TE1HaMV5DAMnPnpVlODAzM0PXrl2xceNGBAQEoGvXriWaUyyEgEQiUX7t7++Pfv36QUfn5cSJAQMG4OzZs7h582aZxk1ERFQeCtsh6fz588jKyoK7u7vynLW1NerXr4/Tp0/n297+/fuRkJCgXEANvJyekN9OTenp6Th//nyhZWJiYvDgwYNCy+ROz3zfqPWFCH379kXfvn3zvSaXyxESElJkG+Hh4YVef30upzoYVq6M0u6D8ByAUZUqZRkOAGD48OHw8/MD8HKuTUncuHFDOY/5v//+w969e5GVlaWy/69CocD69euxePHisguaiIiojHl5eaFPnz6ws7NDZGQkZs6cCVdXV5w/fx4ymQxxcXGQSqWo8trfYktLS8TFxeXbpr+/Pzw8PFSmNHp4eGDFihUICgpC3759ERcXh/nz5wOAckDOw8MDX375JXx8fNCxY0fcuXMHK1asUJaxt7eHh4cHfv31V/Ts2RNNmzbF+fPn4e/vj6ysLHz11VeQy+UwMzND9+7dUatWrXLosbfLWzFH9l3XtGVLhMhkyH/TqoLlADgqk6FJixZlHpOnpycyMzORmZkJDw+PYtcLCwvDlStX0Lt3bwDAli1bUK1aNVy+fFllbvKKFSuwcePGPJPYiYiI3iYl3SEp1+ufTuaKiYlBSEhInr3s3d3dsWTJEowZMwYymQyOjo7o2rUrACh3axo1ahT8/Pzg7e0NqVSK1q1bo3///iplZs6cCS8vL7Ru3Rq6urpo1649nv73HwDAf184Vu0Kw+ylq1G7dm106uyOI0eOvFkHveWYyFYAX19fRCkUOFHCescBRCsU8PX1LfOYtLW1cePGDdy4cUP5P8frMjIyEBcXh4cPH+LChQv49ttv0aNHD3h7e2Po0KEAXv6r8+OPP0b9+vVVjuHDhyMxMbHIXwRERERvk9d3SKpatSoyMzPx7LVpfvHx8bC0tMxTPyAgAKampujevXueaxMmTEBiYiKioqKQkJCgXNye+ymnRCLB4sWLkZKSggcPHiAuLg4tW7YEANjb2wN4+Yn1+vXrERQUBC1tbWhb14V+Qw9IpHJU6TMXlXvNhMmQFbDy+QF/J2ijW/fumD5jBoQQZdZHbxMmshXAzMwMffv0wQIdHRR3fDIbwLc6Oujbt2+J5q+WRFE7NBw9ehRWVlawt7eHp6cnfvvtN/z444/Yt28ftLW1cf78eVy+fFk5OvsqQ0NDuLu7c09ZIiLSKK/vkNSsWTPo6uoiNDRUWSY2NhZXr16Fi4uLSl3x/7v4DB06FLq6uvm2L5FIYG1tDblcjqCgINja2qJp06YqZbS1tWFjYwOpVIqgoCA4OzvDwsJCeT00NBT9BwxEpTZDYPrxHGQlREFesyUkkv+ldVLLmqji4Qezj+fi+2UrMG/evDfum7eRRLyrKfobSE5OhrGxMZKSkvIkeunp6YiMjISDg0OeydaFiYqKQqsmTeCRmIhfi3gpQjaAkVpaOFalCv66cCHPiwreBaXtRyIiopJISUnBnTt3AABNmjTBsmXL0LFjR5iYmMDExASzZ89G7969YWVlhfv372P69OmIiorCjRs3lIvLP/30Uxw8eBAbNmyAiYkJJk2ahKdPn+L8+fMqn2qeOHECbm5uuH79OurWrZsnliVLlsDT0xNaWlrYvXs35s2bh+3bt6Nnz54AXu47v3PnTnTo0AHp6ekICAjAL7/8gpMnTypHZq9evYpWzi7Qrv0hDOp3QnLEXqTfvwQrnxXQMc47QgwAaffO48nO2bh48SIaNWpUlt1bLgrLw17HEdkKUr16dRwNC0NI5cpw09HBMSDPnNkcAMcAuOnoIKRyZRwNC3snk1giIqKK8vfff6NJkybKl/5MmDABTZo0waxZs4q9Q9Ly5cvRs2dP9O3bF23atIG+vj4OHDiQZ2qev78/XFxc8k1igZf7xH744Ydo3rw5Dh06hH379imT2FwbN25E8+bN0aZNG1y7dg3h4eHKJBYADh8+jLQXqUi5chyPt82EUGSh6uAlBSaxACCv0QyVajTByp9XlbT73nockc1HeYzI5oqKisLXU6di+44dsNXWhmdGBgzxcneCozIZohUK9O3bF/MXLnynk1iOyBIREZVcy9YuuJljiSquI0tU78WtM3h+dDniYh/B2Ni4nKIrGxyRfYtVr14dm7ZuRUxsLD6ZNw9P+/TBP25ueNqnDz6ZNw8xsbHYtGXLO53EEhERUcllZmYi4uwZ6Du1KXFdea2WyMpW4O+//y6HyNRHrfvIvs/MzMwwefJkdYdBREREGiIpKQkAoCUv+WuWJFrakOpXUrbxruCILBEREZEG0NfXBwCI7MxS1VdkZUAul5dlSGrHRJaIiIhIA+jr68O8qhUyHt4ocd3MJ/eR+SIFtWvXLofI1IeJLBEREZEGkEgk+HzMaGReOVriFxy8uHQEbdt1eOdeW8s5smqSkJCAgIAAnPv7PJKSkmFsbISWzZvB19e33F6AQERERJpt5MiRmDtvHvTvX4TcoWnRFQAoUp/hxfXfMH7zpnKOruJxRLaCRUVFYdDgIbCytsGcJT8i5E4qziYbIuROKmYv+QHWNtUwaPAQREVFqTtUIiIiesvY2Njgy/FfIvHQ98h88qDI8jkZqUjcOx9NGjdSvhL3XcIR2Qp0+fJldOrsgQwDS5j0mgk9+0Yqr5MTIgfp9y9j/587ENK8JU6EhmjEGziIiIio4nz33WI8iovFruCpMOwwEgZ1P4RER6pSRgiB9Kh/kBr+K+zMKuHwwQPQ0Xn30j6OyFaQqKgodOrsgUyrBjDpMw9yhyYqSSwASCRakDs0gUmfeciyaoBOnT3KdGTWx8cHEokEEokEurq6qFGjBiZNmoTJkycrzxd03L9/HwBw+vRpaGtrw9PTs8ziIiIiouLT0tLC5k2bMGv6FGT+uQGP1w7Hs9/WI+XKcaRc+w1JZ7bj6YbP8XTnbPTs3BZn/jwFU1NTdYddLpjIVpBp02cgw8ASVTy+gERLu9CyEi1tVPb4AhkGlpg2fUaZxuHp6YnY2Fjcu3cP8+fPx6pVq5CQkIDY2FjlUa1aNcydO1flnK2tLQBg/fr1+OKLL3Dq1ClOfyAiIlITLS0tzJgxA49jH2LNT8vRQPYUBjcOQHZ5J6onX8X0caPxMCYamzdtKvLtWJrs3RtjfgslJCRg+/btMOk1s8gkNpdESxv6LT/Gjh0L8MOK5WW2AEwmk6Fq1aoAgIEDB+K3337DwYMHERAQoCyjra0NQ0NDZblcqamp2L59OyIiIhAXF4cNGzZg1qxZZRIXERERlZxcLoePjw98fHzUHYpacES2AgQEBEBW2QJ69iWb76pn3xi6xmYqSWZZk8vlyMrKKlbZbdu2wcnJCU5OThg8eDACAgJKvP0HERERUVlhIlsBzv19Hlq2jfPMiS2KRKIFbdsm+Pv8hfKJ69w5bN26FZ06dSpWeX9/fwwePBjAyykKKSkpOHHiRLnERkRE9Db7/fff0a1bN1hbW0MikWDv3r0q119dl5J7tG7dWqVMRkYGvvjiC5iZmcHAwADdu3dHTEyM8np4eHiBa1ciIiKU5U6cOAEXFxcYGhrCysoKU6ZMQXZ2tsq9tm/fjsaNG0NfXx92dnZYsmRJgc/2559/QkdHB40bNy59B1UQJrIVICkpGRKZfqnqSqRyPEssu/ciHzx4EJUqVYKenh6cnZ3Rrl07/PTTT0XWu3nzJs6dO4f+/fsDAHR0dNCvXz+sX7++zGIjIiLSFKmpqWjUqBFWrlxZYJncdSm5x+HDh1Wujx8/Hnv27EFwcDBOnTqFlJQUeHt7Q6FQAABcXFxU6sfGxmLkyJGwt7dH8+bNAQD//PMPunTpAk9PT1y8eBHBwcHYv38/pk6dqrzPkSNHMGjQIIwZMwZXr17FqlWrsGzZsnxjT0pKwtChQ4s9yKVunCNbAYyNjSCepJaqrshMQ5XKxmUWS8eOHbF69Wro6urC2toaurq6xarn7++P7Oxs2NjY/C82IaCrq4tnz56hSpUqZRYjERHR287LywteXl6Flnl1XcrrkpKS4O/vj8DAQLi5uQEANm/eDFtbWxw/fhweHh6QSqUq9bOysrB//374+flBIpEAAIKDg9GwYUPlmpVatWph4cKFGDBgAL755hsYGhoiMDAQPXv2xJgxYwAANWrUwJQpU7B48WJ8/vnnyrYAYPTo0Rg4cCC0tbXzjDK/jTgiWwFaNm+GnOhLECKnRPWEyIEi+iKaNyvemzuKw8DAALVq1YKdnV2xk9js7Gxs2rQJS5cuxaVLl5TH5cuXYWdnhy1btpRZfERERO+K8PBwWFhYwNHREaNGjUJ8fLzy2vnz55GVlQV3d3flOWtra9SvXx+nT5/Ot739+/cjISFBZWFXRkYG9PT0VMrJ5XKkp6fj/PnzhZaJiYnBgwf/e6lCQEAA7t69i2+++abUz1zRmMhWAF9fX2QmPUH6/cslqpd+/xKykhLg6+tbTpEVz8GDB/Hs2TOMGDEC9evXVzk+/vhj+Pv7qzU+IiKit42Xlxe2bNmCsLAwLF26FBEREXB1dUVGRgYAIC4uDlKpNM8nmpaWloiLi8u3TX9/f3h4eCi3xAQADw8PnD59GkFBQVAoFHj48CHmz58PAIiNjVWW2b17N06cOIGcnBzcunULK1asUClz+/ZtTJ06FVu2bMnz4oSsrCw8f/78rVzgzUS2ApiZmaFPnz54cW4HRI6iWHVEjgIvzu1Enz59ymzrrdLy9/eHm5sbjI3zTnHo3bs3Ll26hAsXymdBGhERkSbq168funbtivr166Nbt244cuQIbt26hUOHDhVaTwih8lF/rpiYGISEhGDEiBEq593d3bFkyRKMGTMGMpkMjo6O6Nq1K4CX22kCwKhRo+Dn5wdvb29IpVK0bt1aueZFW1sbCoUCAwcOxJw5c+Do6AgAeP78OR4/fgwrG1tIpVIYGRlBR1cXHTu5Ye/evXkWk6mLRLyN6bWaJScnw9jYGElJSXk2EU5PT0dkZCQcHBzyDNMXJioqCk2bt0SWVQNULuKlCCJHgcSQnyCNu4LzEedQvXr1Uj/L26q0/UhERPS2kUgk2LNnD3r27Floudq1a2PkyJGYMmUKwsLC0KlTJ/z3338qo7KNGjVCz549MWfOHJW68+bNw08//YSHDx/mOzVQCIHY2FhUqVIF9+/fxwcffIBz586hRYsWyjIKhQJxcXEwNzfHiRMn0KVLFzx+/Fg5Mpyb+Obk5Pxv9FUiQRW3T6Fn+wFyXiQj7eYfSLseDjMzU+zasR3Ozs6l7LWCFZaHvY4jshWkevXqOBEaAt3YK/hvx0ykRV7IM2dWiBykRV7AfztmQjf2Ck6EHnsnk1giIqL3zdOnTxEdHQ0rKysAQLNmzaCrq4vQ0FBlmdjYWFy9ehUuLi4qdYUQCAgIwNChQwtc3yKRSGBtbQ25XI6goCDY2tqiaVPVNTba2tqwsbGBVCpFUFAQnJ2dYWFhASMjI1y5cgUXLlxAJ7fO0NGrBHnNFtAxsYGV70+oVN8VUnN76Nk1RBX3z1H10w14YdUEHTp2VPs2nNy1oAI1atQIF/4+h2nTZ2DHjgXQNTaDtm0TSKRyiMw0KKIvIispAX369MHCbxcwiSUiInpLpaSk4M6dO8qvIyMjcenSJZiYmMDExASzZ89G7969YWVlhfv372P69OkwMzNDr169AADGxsYYMWIEJk6cCFNTU5iYmGDSpElo0KCBcheDXGFhYYiMjMwzrSDXkiVL4OnpCS0tLezevRuLFi3C9u3blSOsCQkJ2LlzJzp06ID09HQEBARgx44dOHnyJICXr7utX78+Zs6ahfBTp2E+6Duk3vgd2c8TIDW3z3M/LZkBKncaDYlUH9179sKVy5dQo0aNsujWEuOIbAWrXr06tmwOxKOHMZg9eRy8aldCK+MUeNWuhNmTx+HRwxhs2RzIJJaIiOgt9vfff6NJkyZo0qQJAGDChAlo0qQJZs2aBW1tbVy5cgU9evSAo6Mjhg0bBkdHR5w5cwaGhobKNpYvX46ePXuib9++aNOmDfT19XHgwAFlAprL398fLi4uqFu3br6xHDlyBB9++CGaN2+OQ4cOYd++fXmmOWzcuBHNmzdHmzZtcO3aNYSHh6Nly5bK68+fP8fSpctg1OlT6JraojiM2g6Gtml1/PDDD8UqXx44RzYf5TFHllSxH4mIiN4eq1evxsRps2A+6ldItIv/gX3q9ZNIC1+Lx7GPYGBgUCaxcI4sERERERXbuvUbIK3nVqIkFgD0nVyggDb2799fTpEVjolsKeXklOzlBqSKHwQQERG9PaKjo6Gbz3zYoki0dSEztUFMTEzZB1UMXOxVQlKpFFpaWnj06BHMzc0hlUrz3e+NCiaEwJMnTyCRSIr9djEiIiIqP9nZWdDVKuX4ppY2srKyyjagYmIiW0JaWlpwcHBAbGwsHj16pO5wNJZEIkG1atXyTGgnIiKiileliikSnz8tcT0hBLJTnsLU1LQcoioaE9lSkEqlqF69OrKzs6FQFO9NXaRKV1eXSSwREdFbos9HPbA6aD/QrFuJ6mU+uokXCY/QpUuXcoqscExkSyn3Y3F+NE5ERESabsyYMViyZAkMYm9DZlW72PVeXD6CLl29YWtbvC27yhoXexERERG95xwcHNDFuxueh61FTlZ6seqkRV5AyvWTmPjl+PINrhBMZImIiIgIG9b7w0yahcQ985CTnlJo2bR75/Fs30LM/HoGOnbsWEER5sVEloiIiIhgZmaG03/8DttKAvH+o5EYHoCsxDjldZGjwIs7Z/Fs12w82Tkbs76ejm+++UaNEXOOLBERERH9P2tra5w/dxbbt2/Hih9X4tIvoyAzrAItHSmyXiRDghwMGjgIX2z/Rfl6XnXiK2rzUZJXoxERERG9q65cuYK7d+8iLS0NVapUQevWrVG5cuVyvWdJ8jCOyBIRERFRvho0aIAGDRqoO4wCcY4sEREREWkkJrJEREREpJGYyBIRERGRRmIiS0RE74zff/8d3bp1g7W1NSQSCfbu3aty3cfHBxKJROVo3bq1SpmMjAx88cUXMDMzg4GBAbp3746YmBjl9fDw8Dxt5B4RERHKcidOnICLiwsMDQ1hZWWFKVOmIDs7W+Ve27dvR+PGjaGvrw87OzssWbJE5fru3bvRuXNnmJubw8jICM7OzggJCSmj3iLSfExkiYjonZGamopGjRph5cqVBZbx9PREbGys8jh8+LDK9fHjx2PPnj0IDg7GqVOnkJKSAm9vbygUCgCAi4uLSv3Y2FiMHDkS9vb2aN68OQDgn3/+QZcuXeDp6YmLFy8iODgY+/fvx9SpU5X3OXLkCAYNGoQxY8bg6tWrWLVqFZYtW6YS+++//47OnTvj8OHDOH/+PDp27Ihu3brh4sWLZdltRBqL22/lg9tvERFpPolEgj179qBnz57Kcz4+PkhMTMwzUpsrKSkJ5ubmCAwMRL9+/QAAjx49gq2tLQ4fPgwPD488dbKyslCtWjX4+flh5syZAIDp06cjNDRUZYR27969GDBgAOLj42FoaIiBAwciKysLO3bsUJZZsWIFli5diqioKEgkknxjrFevHvr164dZs2aVtEuINEJJ8jCOyBIR0XslPDwcFhYWcHR0xKhRoxAfH6+8dv78eWRlZcHd3V15ztraGvXr18fp06fzbW///v1ISEiAj4+P8lxGRgb09PRUysnlcqSnp+P8+fOFlomJicGDBw/yvVdOTg6eP38OExOTEj0z0buKiSwREb03vLy8sGXLFoSFhWHp0qWIiIiAq6srMjIyAABxcXGQSqWoUqWKSj1LS0vExcXl1yT8/f3h4eEBW1tb5TkPDw+cPn0aQUFBUCgUePjwIebPnw8AiI2NVZbZvXs3Tpw4gZycHNy6dQsrVqxQKfO6pUuXIjU1FfXq1cP69evxww8/YP369ZxqQO8tJrJERPTe6NevH7p27Yr69eujW7duOHLkCG7duoVDhw4VWk8Ike9H/TExMQgJCcGIESNUzru7u2PJkiUYM2YMZDIZHB0d0bVrVwCAtrY2AGDUqFHw8/ODt7c3pFIpWrdujf79+6uUedXmzZvx9ddfw9LEBK6urvjOzw9bpk3Dd59/jmZNm6JV48bYuHEjsrKyStU3muhdW9yXG8+MGTNgZ2cHmUyGmjVrYv369WXQW+8mJrJERPTesrKygp2dHW7fvg0AqFq1KjIzM/Hs2TOVcvHx8bC0tMxTPyAgAKampujevXueaxMmTEBiYiKioqKQkJCAHj16AAAcHBwAvJzDu3jxYqSkpODBgweIi4tDy5YtAQD29vZ57jNs2DDoKhToeucObgP4Ny0N59LS8G96Ou4A6HD5MiaMGgWPjh2RmJj4Zh2jId61xX0A0LdvX5w4cQL+/v64efMmgoKCUKdOnbLqsnePoDySkpIEAJGUlKTuUIiIqJQAiD179hRaJiEhQchkMrFx40YhhBCJiYlCV1dXbNu2TVnm0aNHQktLSxw9elSlbk5OjnBwcBATJ04sVjwzZ84Utra2Ijs7u8AyQ4YMEc7OzirnNm7cKLS0tIStjo6IAoQo5HgIiEZSqWjTooVIS0srVlzvivy+38OGDRM9evQosE7u9zs4OFh57uHDh/l+v3NlZmYKCwsLMXfuXOW5adOmiebNm6uU27Nnj9DT0xPJyclCCCEGDBggPv74Y5Uyy5cvF9WqVRM5OTlCCCGOHDkijI2NxdOnT4t83ndZSfIwjsgSEdE7IyUlBZcuXcKlS5cAAJGRkbh06RKioqKQkpKCSZMm4cyZM7h//z7Cw8PRrVs3mJmZoVevXgAAY2NjjBgxAhMnTsSJEydw8eJFDB48GA0aNICbm5vKvcLCwhAZGZlnWkGuJUuW4MqVK7h27RrmzZuHRYsW4ccff1ROG0hISMCaNWvw77//4tKlSxg3bhx27NihnCcLAEFBQfD19YWRRIID2dnQBRAHIKmA57cGcCwzEw8vX8bcOXNK35HvEE1a3Ld//340b94c3333HWxsbODo6IhJkyYhLS3tjfrgnVYBibXG4YgsEZFm+u233wSAPMewYcPEixcvhLu7uzA3Nxe6urqievXqYtiwYSIqKkqljbS0NOHn5ydMTEyEXC4X3t7eecoI8XKEzcXFpcBYOnbsKIyNjYWenp5o1aqVOHz4sMr1J0+eiNatWwsDAwOhr68vOnXqJP766y+VMu3atcv/eYoYmQ0AhJmR0Xs1Kot8RmSDg4PFwYMHxZUrV8T+/ftFo0aNRL169UR6eroQQogtW7YIqVSap63OnTuLTz75JN/7eHl5CS8vL5VzISEhQktLS2zdulVkZ2eLmJgY0bZtWwFAbN26VQghxNq1a4W+vr44fvy4UCgU4ubNm6JOnToCgDh9+rQQQggPDw8hk8lE165dxdmzZ8WhQ4eEnZ2d8PX1FVlZWSIpKUkoFIo37aq3XknyMCay+WAiS0REb4Nt27YJIx0dkVJE4vr68QIQVXR1RWBgoLofocLkl8i+7tGjR0JXV1fs2rVLCFFwIuvm5iZGjx6d53x0dLTQ0tISO3fuzHNt6dKlwsjISGhrawt9fX2xcOFCAUA5TSUnJ0d89dVXQk9PT2hra4sqVaqI2bNnCwDi7NmzQoiXCbSenp5ITEwUQrz8x86QIUNU/hEj0dISrV3aiuDgYJGRkVGiPtIUnFpARET0Djh6+DB6KhQwKGE9OYDeWVk4WsRuDO+bt31xn5WVFWxsbGBkZIR58+bB2qYadh48BgAw6zEVVsNXwqL/t7iRaYqhw0fCulp1hIaGlk3naCgmskREb+hd2wLo1KlTaNOmDUxNTSGXy1GnTh0sX768jHqLSuK/+HhULeULOKsCePbkSdkGpOGePn2K6OhoWFlZAQCaNWsGXV1dlWQwNjYWV69ehYuLi0pdIQQCAgIwdOhQ6Orq5tu+RCKBtbU15HI5goKCYGtri6ZNm6qU0dbWho2NDaRSKYKCguDs7AwLCwsAQJs2bfDo0SOM+mQ05n27GMZdJsCgwwhAogV5zRaQmttDz7Y+KncaBcvRG6Co3QFeXl2we/fusuwmjaKj7gCIiDRd7hZAvr6+6N27d75lPD09ERAQoPxaKpWqXB8/fjwOHDiA4OBgmJqaYuLEifD29sb58+ehra2t3ALoVTNnzsTx48fzbAE0Y8YMbNq0CQ8fPsSYMWOgUCjw/fffA/jfFkA//fQT3N3dcePGDYwcORJyuRx+fn4AAAMDA/j5+aFhw4YwMDDAqVOnMHr0aBgYGOCTTz4ps36josnkcmSUsm46ANlri4veNSkpKbhz547y69zFfSYmJjAxMcHs2bPRu3dvWFlZ4f79+5g+fXqBi/tMTU1hYmKCSZMmlXpxn6enJ7S0tLB7924sWrQI27dvV1nct3PnTnTo0AHp6ekICAjAjh07cPLkSWUbAwcOxJQpU7B+vT9MvSdBW98YT4/8iEoN3KClK1O5n5ZUD8YfDoFEzwj9Bw5ExNmzaNSoUZn0q0Yp/5kOmodzZImotPAObAGUn169eonBgwcXeJ3Kx5QpU0RbqbRE82NzD1epVHw5fry6H6FcvWuL+zIzM0VlE1OhY2IjJDoyoW1oJgxb9BS2E3YJuykHCzwMHVuJAQMHvUFPvl1KkodxRJaIqALkbgFUuXJltG/fHgsWLFB+nFjUFkAeHh552ivNFkAdOnRARkYG9PX185TJ3QLo9Y34AeDixYs4ffq08hWrVHF8fX2xePFiXAVQvwT1/gUQlpmJHwoYPXxXdOjQAaKQqRchISFFtqGnp4effvoJP/30U6Hltm7dWuj1sLCwQq+bmZnhzJkzhZbZu3cvUl+kw+rTNdCSygst+yp5Y2/s2DEPP6xYDnNz82LXexdwjiwRUTnz8vLCli1bEBYWhqVLlyIiIgKurq7IyHj5oXFcXBykUimqVKmiUs/S0hJxcXH5tunv7w8PDw/Y2toqz3l4eOD06dMICgqCQqHAw4cPlcln7rQEDw8P7N69GydOnEBOTg5u3bql3Lf09akL1apVg0wmQ/PmzfH555+jbdu2WLduHb7//nusWrUKYWFhyMnJKZM+ovw5OTmh04cfYkU+r8ctzI8SCdq1aoX69UuS/pK6/bo+APK67UuUxAKAnn0jyCpbYNu2beUU2dtLrYns8+fPMX78eNjZ2UEul8PFxUVl0UJxFki8bvfu3WjevDkqV64MAwMDNG7cGIGBgeX9KEREBerXrx+6du2K+vXro1u3bjhy5Ahu3bqFQ0WsKBdCQJJPAhMTE4OQkJA8c/Xc3d2xZMkSjBkzBjKZDI6OjujatSsAKOfpjRo1Cn5+fvD29oZUKkXr1q3Rv39/lTK5/vjjD0REROCTTz7B3LlzUbduXSwdOxZ7Zs3CukmT4NW5M5zs7bFs2TIkJyeXun+ocN8sWIBNWlr4tZjlNwFYJ5Hgm2+/Lc+wqBzcfxAFHXP7EteTSLSgbWqL6Ojosg/qLafWRHbkyJEIDQ1FYGAgrly5And3d7i5ueHhw4fKMkW9I/l1JiYmmDFjBs6cOYN//vkHvr6+8PX1LdbHC0REFeFt3wIol7W1NRYvWIDAX39FU4UC9gD+TU/Hn2lpuJiWhoc5OfgkOho/TpuG1k2b4v79+2/WMZSvDz/8EP4BAfhUSwvfoOC3eiUDmAdguJYW1q5bB1dX14oLkspEdnY2oKVddMH8SLSRlZVVtgFpALUlsmlpadi1axe+++47tGvXDrVq1cLs2bPh4OCA1atXK8vJZDJUrVpVeZiYmBTabocOHdCrVy/UrVsXNWvWxLhx49CwYUOcOnWqvB+JiKhY3vYtgAAgJycHPoMH44+9e3E2OxveQuD1mYhmACYDuJqZCfsHD+Dati0eP35c6n6hgg0ZMgR79+/HegsL2OjoYLSWFvYBCAewH8BnWlqw0dHBGjMz7Nq9G8OHD1dvwFQqZqZmUDx/WrrKL/6Dqalp2QakAdS22Cs7OxsKhSLfhQmvJp2FLZAoihACYWFhuHnzJhYvXlxguYyMDOVcNQD8iIyISuRd2wLo559/xu3bt3Fw715sz87GOQDfA/iigOevBGB3djY6xcfDb/Ro7HhtH10qG127dkXkw4c4dOgQVq1YgZEXLiD5xQsY6eujcePG2DhuHLp37w4dHa7j1lR9evfEN4tXQLTpD4mk+GONWf89RPKD6+jWrVs5Rvd2kojClvuVMxcXF0ilUmzduhWWlpYICgrC0KFDUbt2bdy8eRPbtm1DpUqVYGdnh8jISMycORPZ2dk4f/48ZDJZge0mJSXBxsYGGRkZ0NbWxqpVqwr91+ns2bMxZ86cfNsxMjIqk2clondXeHg4OnbsmOf8sGHDsHr1avTs2RMXL15EYmIirKys0LFjR8ybN09loVZ6ejomT56MrVu3Ii0tDZ06dcKqVatUygAv95l88OAB/vzzz3xjcXV1xYULF5CRkYFGjRrhm2++gZeXl/J6QkICunXrhitXrkAIAWdnZyxYsACtWrVSlvnxxx8x5auvoMjIgBxATQCjAIxG4R/jnQbQTksL9x88QLVq1YruOCJS8fTpU1hZ26BKj+mQ12hW7HrPwtahjvYTnD2T/+8FTZOcnAxjY+Ni5WFqTWTv3r2L4cOH4/fff4e2tjaaNm0KR0dHXLhwAdevX89TPjY2FnZ2dggODsZHH31UYLs5OTm4d+8eUlJScOLECcybNw979+5Fhw4d8i2f34isra0tE1kiei+dO3cOzq1b454QsCtBPQGgiUyGHlOm5Ds4QERF8x0+AtsPn4Bp/8XQ0qtUZPmMRzeRsG0Gtm7ehD59+lRAhOWvJImsWhd71axZEydPnkRKSgqio6Nx7tw5ZGVlKRclvO71BRIF0dLSQq1atdC4cWNMnDgRH3/8MRYuXFhgeZlMBiMjI5WDiOh9dezYMbhIpSVKYgFAAmBARgaO7dtXHmERvRd+/GEFalhWxrNd30CR+qzQsukx1/Fs9xx8MmoEPv744wqK8O3yVuwja2BgACsrKzx79gwhISHKVbave32BRHEJIVRGXImIqGDPnj2DpUJRqroW/1+fiErH0NAQ4WEnUM/WFPH+Y5B44hdkPf3ftlpC5CAt8iKe7fsW8UFT8emoEfjpxx/z3arvfaDWGeEhISEQQsDJyQl37tzB5MmT4eTkBF9fX6SkpBS5QAIAhg4dChsbG+WI68KFC9G8eXPUrFkTmZmZOHz4MDZt2qSyEwIRERVMJpMhXat04xzp/1+fiErP1NQUf5wMx549e7D8h5/w16+fQs+wCrSlMmS9eI6c7Cz06dMHX6z/Hs7OzuoOV63UmsgmJSVh2rRpiImJgYmJCXr37o0FCxZAV1cX2dnZuHLlCjZt2qSyQGLbtm0wNDRUthEVFQWtV37hpqam4rPPPkNMTAzkcjnq1KmDzZs3o1+/fup4RCIijVOjRg1sBJCNkv+R+FtLCzVq1y6HqIjeL7q6uujbty/69u2Lf//9Fzdv3kRqaiqMjY3RsmXL9+5VtAVR62Kvt1VJJhkTEb1rkpOTYW1hgcCMDPQqurjSMwA22trYvncvvL29yys8InrHacxiLyIievsYGRlhyLBhWKGri5wS1PsFgIW5ucp2X0RE5YmJLBER5THpq6/wj1SKqRJJnjd65ecYgJlaWpj33XfKly8QEZU3JrJERJRHzZo1se/wYayWyTBCSwuPCiiXBmAlgG5aWpg1ezaGDBlSgVES0fuO77EjIqJ8tWvXDr+fPo1h/fuj+u3b6Kmtjd7Z2TAF8ALAKQDrdXWhra+PVUuXFvjaXCKi8sJEloiICtSkSRNc/vdf/Pnnn1j900+YHBaGpJQU6OvpoY6TE1aOHYvevXtzyy0iUgvuWpAP7lpAREREpB7ctYCIiIiI3nlMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhII+mUtmJ0dDTu37+PFy9ewNzcHPXq1YNMJivL2IiIiIiIClSiRPbBgwdYs2YNgoKCEB0dDSGE8ppUKsWHH36ITz75BL1794aWFgd7iYiIiKj8FDvbHDduHBo0aIDbt29j7ty5uHbtGpKSkpCZmYm4uDgcPnwYbdu2xcyZM9GwYUNERESUZ9xERERE9J4r9oisVCrF3bt3YW5unueahYUFXF1d4erqim+++QaHDx/GgwcP0KJFizINloiIiIgol0S8Oj+AAADJyckwNjZGUlISjIyM1B0OERER0XujJHlYqRd75UpISMDZs2ehUCjQokULWFlZvWmTRERERERFeqNEdteuXRgxYgQcHR2RlZWFmzdv4ueff4avr29ZxUdERERElK8SbS2QkpKi8vWcOXNw7tw5nDt3DhcvXsSOHTswY8aMMg2QiIiIiCg/JUpkmzVrhn379im/1tHRQXx8vPLrx48fQyqVll10REREREQFKNFir/v37+Ozzz6DTCbDzz//jLt376J///5QKBTIzs6GlpYWNmzYgC5dupRnzOWOi72IiIiI1KPcFnvZ29vj8OHD2Lp1K9q3b49x48bhzp07uHPnDhQKBerUqQM9Pb03Cp6IiIiIqDhK9fqtgQMHKufFdujQATk5OWjcuDGTWCIiIiKqMCXeteDIkSO4fv06GjVqBH9/f4SHh2PgwIHo0qUL5s6dC7lcXh5xEhERERGpKNGI7FdffQUfHx9ERERg9OjRmDdvHjp06ICLFy9CJpOhcePGOHLkSHnFSkRERESkVKLFXmZmZggJCUGzZs3w33//oXXr1rh165by+rVr1zB69GicOnWqXIKtKFzsRURERKQeJcnDSjQiq6+vj8jISABAdHR0njmx9erV0/gkloiIiIg0Q4kS2YULF2Lo0KGwtrZG+/btMW/evDe6+fPnzzF+/HjY2dlBLpfDxcUFERERyus+Pj6QSCQqR+vWrQttc926dfjwww9RpUoVVKlSBW5ubjh37twbxUlEREREb58SLfYaNGgQPD09ce/ePdSuXRuVK1d+o5uPHDkSV69eRWBgIKytrbF582a4ubnh+vXrsLGxAQB4enoiICBAWaeoFy6Eh4djwIABcHFxgZ6eHr777ju4u7vj2rVryjaJiIiISPOVaI5sWUpLS4OhoSH27duHrl27Ks83btwY3t7emD9/Pnx8fJCYmIi9e/eW+j4KhQJVqlTBypUrMXTo0GLV4RxZIiIiIvUolzmyY8aMQXR0dLHKbtu2DVu2bCm0THZ2NhQKRZ55tnK5XGWebXh4OCwsLODo6IhRo0apvBK3OF68eIGsrCyYmJgUWCYjIwPJyckqBxERERG93Yo9tcDc3Bz169eHi4sLunfvjubNm8Pa2hp6enp49uwZrl+/jlOnTiE4OBg2Njb45ZdfCm3P0NAQzs7OmDdvHurWrQtLS0sEBQXh7NmzqF27NgDAy8sLffr0gZ2dHSIjIzFz5ky4urri/PnzkMlkxYp76tSpsLGxgZubW4FlFi5ciDlz5hS3K4iIiIjoLVCiqQXx8fHw9/dHcHAwrl69qnLN0NAQbm5u+OSTT+Du7l6s9u7evYvhw4fj999/h7a2Npo2bQpHR0dcuHAB169fz1M+NjYWdnZ2CA4OxkcffVRk+9999x0WLVqE8PBwNGzYsMByGRkZyMjIUH6dnJwMW1tbTi0gIiIiqmAlmVpQ6jmyiYmJePDgAdLS0mBmZoaaNWtCIpGUKuDU1FQkJyfDysoK/fr1Q0pKCg4dOpRv2dq1a2PkyJGYMmVKoW1+//33mD9/Po4fP47mzZuXKB7OkSUiIiJSj5LkYSV+RW2uypUrv/GuBbkMDAxgYGCAZ8+eISQkBN99912+5Z4+fYro6GhYWVkV2t6SJUswf/58hISElDiJJSIiIiLNUKJ9ZMtaSEgIjh49isjISISGhqJjx45wcnKCr68vUlJSMGnSJJw5cwb3799HeHg4unXrBjMzM/Tq1UvZxtChQzFt2jTl19999x2+/vprrF+/Hvb29oiLi0NcXBxSUlLU8YhEREREVE7UmsgmJSXh888/R506dTB06FC0bdsWx44dg66uLrS1tXHlyhX06NEDjo6OGDZsGBwdHXHmzBkYGhoq24iKikJsbKzy61WrViEzMxMff/wxrKyslMf333+vjkckIiIionKitn1k32acI0tERESkHuWyjywRERER0duEiSwRERERaaQS7VrQpEmTYm2xdeHChVIHRERERERUHCVKZHv27FlOYRARERERlQwXe+WDi72IiIiI1IOLvYiIiIjonVeiqQUdO3Ysco6sRCLBiRMn3igoIiIiIqKilCiRbdy4cYHXkpOTERQUhIyMjDeNiYiIiIioSCVKZJcvX57nXHZ2Nn7++WcsWLAANjY2mDdvXpkFR0RERERUkBIlsq/bsmULZs2ahbS0NMyePRuffPIJdHTeqEkiIiIiomIpVdZ59OhRTJ06FZGRkZg0aRImTJgAAwODso6NiIiIiKhAJUpkz507hylTpuCvv/7CmDFjcPz4cZiZmZVXbEREREREBSrRPrJaWlqQy+UYPXo07O3tCyw3duzYsohNbbiPLBEREZF6lCQPK1Eia29vX6ztt+7du1fcJt9KTGSJiIiI1KMkeViJphbcv3//TeIiIiIiIiozZfpmr6dPn2LFihVl2SQRERERUb7eOJEVQiAkJAR9+/aFtbU1FixYUBZxEREREREVqtSJ7P379zFr1izY2dmhS5cu0NPTw6FDhxAXF1eW8RERERER5atEiWxGRgaCgoLQqVMn1K1bF1evXsWyZcugpaWFqVOnws3NDdra2uUVKxERERGRUokWe9nY2OCDDz7A4MGDsXPnTlSpUgUAMGDAgHIJjoiIiIioICUakVUoFJBIJJBIJBx5JSIiIiK1KlEiGxsbi08++QRBQUGoWrUqevfujT179hS5tywRERERUVkrUSKrp6eHQYMGISwsDFeuXEHdunUxduxYZGdnY8GCBQgNDYVCoSivWImIiIiIlEq9a0HNmjUxf/58PHjwAIcOHUJGRga8vb1haWlZlvEREREREeWrRIu98qOlpQUvLy94eXnhyZMnCAwMLIu4iIiIiIgKJRFCCHUH8bYpyTt+iYiIiKjslCQPK9NX1BIRERERVRQmskRERESkkZjIEhEREZFGYiJLRERERBqpVLsWKBQKbNiwASdOnEB8fDxycnJUroeFhZVJcEREREREBSlVIjtu3Dhs2LABXbt2Rf369flmLyIiIiKqcKVKZIODg7F9+3Z06dKlrOMhIiIiIiqWUs2RlUqlqFWrVlnHQkRERERUbKVKZCdOnIgffvgBfJcCEREREalLqaYWnDp1Cr/99huOHDmCevXqQVdXV+X67t27yyQ4IiIiIqKClCqRrVy5Mnr16lXWsRARERERFVupEtmAgICyjoOIiIiIqET4QgQiIiIi0kilGpEFgJ07d2L79u2IiopCZmamyrULFy68cWBERERERIUp1Yjsjz/+CF9fX1hYWODixYto2bIlTE1Nce/ePXh5eZV1jEREREREeZQqkV21ahV++eUXrFy5ElKpFF999RVCQ0MxduxYJCUllXWMRERERER5lCqRjYqKgouLCwBALpfj+fPnAIAhQ4YgKCio7KIjIiIiIipAqRLZqlWr4unTpwAAOzs7/PXXXwCAyMhIviSBiIiIiCpEqRJZV1dXHDhwAAAwYsQIfPnll+jcuTP69evH/WWJiIiIqEJIRCmGUHNycpCTkwMdnZebHmzfvh2nTp1CrVq1MGbMGEil0jIPtCIlJyfD2NgYSUlJMDIyUnc4RERERO+NkuRhpUpk33VMZImIiIjUoyR5WKlfiPDHH39g8ODBcHZ2xsOHDwEAgYGBOHXqVGmbJCIiIiIqtlIlsrt27YKHhwfkcjkuXryIjIwMAMDz58/x7bfflmmARERERET5KVUiO3/+fKxZswbr1q2Drq6u8ryLiwvf6kVEREREFaJUiezNmzfRrl27POeNjIyQmJj4pjERERERERWpVImslZUV7ty5k+f8qVOnUKNGjTcOioiIiIioKKVKZEePHo1x48bh7NmzkEgkePToEbZs2YJJkybhs88+K+sYiYiIiIjy0ClNpa+++gpJSUno2LEj0tPT0a5dO8hkMkyaNAl+fn5lHSMRERERUR5vtI/sixcvcP36deTk5OCDDz5ApUqVyjI2teE+skRERETqUZI8rFQjsrn09fXRvHnzN2mCiIiIiKhUSpTIDh8+vFjl1q9fX6pgiIiIiIiKq0SJ7IYNG2BnZ4cmTZqAb7YlIiIiInUqUSI7ZswYBAcH4969exg+fDgGDx4MExOT8oqNiIiIiKhAJdp+a9WqVYiNjcWUKVNw4MAB2Nraom/fvggJCeEILRERERFVqBLvIyuTyTBgwACEhobi+vXrqFevHj777DPY2dkhJSWlRG09f/4c48ePh52dHeRyOVxcXBAREaG87uPjA4lEonK0bt260DavXbuG3r17w97eHhKJBCtWrCjpIxIRERGRBijVCxFy5SaXQgjk5OSUuP7IkSMRGhqKwMBAXLlyBe7u7nBzc8PDhw+VZTw9PREbG6s8Dh8+XGibL168QI0aNbBo0SJUrVq1xDERERERkWYocSKbkZGBoKAgdO7cGU5OTrhy5QpWrlyJqKioEu0jm5aWhl27duG7775Du3btUKtWLcyePRsODg5YvXq1spxMJkPVqlWVR1Fzclu0aIElS5agf//+kMlkJX08IiIiItIQJVrs9dlnnyE4OBjVq1eHr68vgoODYWpqWqobZ2dnQ6FQQE9PT+W8XC7HqVOnlF+Hh4fDwsIClStXRvv27bFgwQJYWFiU6p4FycjIQEZGhvLr5OTkMm2fiIiIiMpeiRLZNWvWoHr16nBwcMDJkydx8uTJfMvt3r27yLYMDQ3h7OyMefPmoW7durC0tERQUBDOnj2L2rVrAwC8vLzQp08f2NnZITIyEjNnzoSrqyvOnz9fpqOtCxcuxJw5c8qsPSIiIiIqfyVKZIcOHQqJRFJmNw8MDMTw4cNhY2MDbW1tNG3aFAMHDsSFCxcAAP369VOWrV+/Ppo3bw47OzscOnQIH330UZnFMW3aNEyYMEH5dXJyMmxtbcusfSIiIiIqeyV+IUJZqlmzJk6ePInU1FQkJyfDysoK/fr1g4ODQ77lraysYGdnh9u3b5dpHDKZjPNpiYiIiDTMG+1aUFYMDAxgZWWFZ8+eISQkBD169Mi33NOnTxEdHQ0rK6sKjpCIiIiI3jYlGpEta7kvUnBycsKdO3cwefJkODk5wdfXFykpKZg9ezZ69+4NKysr3L9/H9OnT4eZmRl69eqlbGPo0KGwsbHBwoULAQCZmZm4fv268r8fPnyIS5cuoVKlSqhVq5ZanpOIiIiIyp5aE9mkpCRMmzYNMTExMDExQe/evbFgwQLo6uoiOzsbV65cwaZNm5CYmAgrKyt07NgR27Ztg6GhobKNqKgoaGn9b2D50aNHaNKkifLr77//Ht9//z3at2+P8PDwinw8IiIiIipHEsF3y+aRnJwMY2NjJCUlwcjISN3hEBEREb03SpKHvRVzZImIiIiISoqJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZiIktEREREGomJLBERERFpJCayRERERKSRmMgSERERkUZSayL7/PlzjB8/HnZ2dpDL5XBxcUFERITyuo+PDyQSicrRunXrItvdtWsXPvjgA8hkMnzwwQfYs2dPeT4GEREREamBWhPZkSNHIjQ0FIGBgbhy5Qrc3d3h5uaGhw8fKst4enoiNjZWeRw+fLjQNs+cOYN+/fphyJAhuHz5MoYMGYK+ffvi7Nmz5f04RERERFSBJEIIoY4bp6WlwdDQEPv27UPXrl2V5xs3bgxvb2/Mnz8fPj4+SExMxN69e4vdbr9+/ZCcnIwjR44oz3l6eqJKlSoICgoqVhvJyckwNjZGUlISjIyMin1vIiIiInozJcnD1DYim52dDYVCAT09PZXzcrkcp06dUn4dHh4OCwsLODo6YtSoUYiPjy+03TNnzsDd3V3lnIeHB06fPl1gnYyMDCQnJ6scRERERPR2U1sia2hoCGdnZ8ybNw+PHj2CQqHA5s2bcfbsWcTGxgIAvLy8sGXLFoSFhWHp0qWIiIiAq6srMjIyCmw3Li4OlpaWKucsLS0RFxdXYJ2FCxfC2NhYedja2pbNQxIRERFRuVHrHNnAwEAIIWBjYwOZTIYff/wRAwcOhLa2NoCX0wS6du2K+vXro1u3bjhy5Ahu3bqFQ4cOFdquRCJR+VoIkefcq6ZNm4akpCTlER0d/eYPR0RERETlSkedN69ZsyZOnjyJ1NRUJCcnw8rKCv369YODg0O+5a2srGBnZ4fbt28X2GbVqlXzjL7Gx8fnGaV9lUwmg0wmK91DEBEREZFavBX7yBoYGMDKygrPnj1DSEgIevTokW+5p0+fIjo6GlZWVgW25ezsjNDQUJVzx44dg4uLS5nGTERERETqpdYR2ZCQEAgh4OTkhDt37mDy5MlwcnKCr68vUlJSMHv2bPTu3RtWVla4f/8+pk+fDjMzM/Tq1UvZxtChQ2FjY4OFCxcCAMaNG4d27dph8eLF6NGjB/bt24fjx4+rLCAjIiIiIs2n1hHZpKQkfP7556hTpw6GDh2Ktm3b4tixY9DV1YW2tjauXLmCHj16wNHREcOGDYOjoyPOnDkDQ0NDZRtRUVHKxWEA4OLiguDgYAQEBKBhw4bYsGEDtm3bhlatWqnjEYmIiIionKhtH9m3GfeRJSIiIlIPjdhHloiIiIjoTTCRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khMZImIiIhIIzGRJSIiIiKNxESWiIiIiDQSE1kiIiIi0khvZSL7+++/o1u3brC2toZEIsHevXtVrvv4+EAikagcrVu3VimTkZGBL774AmZmZjAwMED37t0RExOjvB4eHp6njdzj/PnzynInTpyAi4sLDA0NYWVlhSlTpiA7O1vlXtu3b0fjxo2hr68POzs7LFmyJM8znTx5Es2aNYOenh5q1KiBNWvWlEFPEREREb2/3spENjU1FY0aNcLKlSsLLOPp6YnY2FjlcfjwYZXr48ePx549exAcHIxTp04hJSUF3t7eUCgUAAAXFxeV+rGxsRg5ciTs7e3RtGlTAMDVq1fRpUsXeHp64uLFiwgODsb+/fsxdepU5X2OHDmCQYMGYcyYMbh69SpWrVqFZcuWqcQeGRmJLl264MMPP8TFixcxffp0jB07Frt27SrLbiMiIiJ6r0iEEELdQRRGIpFgz5496Nmzp/Kcj48PEhMT84zU5kpKSoK5uTkCAwPRr18/AMCjR49ga2uLw4cPw8PDI0+drKwsVKtWDX5+fhg3bhyMjY0xYcIE/P7774iIiFCW27t3LwYMGID4+HgYGhpi4MCByMrKwo4dO5RlVqxYgaVLlyIqKgoSiQRTpkzB/v37cePGDWWZMWPG4PLlyzhz5swb9hARERHRuyM5ORnGxsZISkqCkZFRoWXfyhHZ4ggPD4eFhQUcHR0xatQoxMfHK6+dP38eWVlZcHd3V56ztrZG/fr1cfr06Xzb279/PxISEuDj46M8l5mZCT09PZVycrkc6enpyukHGRkZ+ZaJiYnBgwcPAABnzpxRiQUAPDw88PfffyMrK6vkD09EREREmpnIenl5YcuWLQgLC8PSpUsREREBV1dXZGRkAADi4uIglUpRpUoVlXqWlpaIi4vLt01/f394eHjA1tZWec7V1RWnT59GUFAQFAoFHj58iPnz5wMAYmNjAbxMSHfv3o0TJ04gJycHt27dwooVK1TKxMXFwdLSMk8s2dnZSEhIePMOISIiInoPaWQi269fP3Tt2hX169dHt27dcOTIEdy6dQuHDh0qtJ4QAhKJJM/5mJgYhISEYMSIESrnO3XqhCVLlmDMmDGQyWRwdHRE165dAQDa2toAgFGjRsHPzw/e3t6QSqVo3bo1+vfvr1IGQJ77Pn78GADQtkULVDYwQGUDA9Sxt8c333yDhw8flrBHiIiIiN4/GpnIvs7Kygp2dna4ffs2AKBq1arIzMzEs2fPVMrFx8fnGRkFgICAAJiamqJ79+55rk2YMAGJiYmIiopCQkICevToAQBwcHAA8DJBXbx4MVJSUvDgwQPExcWhZcuWAAB7e3tlPLkjwSkpKRjSvz8+7t0bADD24UMEvXiBrS9e4PMHD7B38WLYVa+OYQMH4sWLF2XQO0RERETvpncikX369Cmio6NhZWUFAGjWrBl0dXURGhqqLBMbG4urV6/CxcVFpa4QAgEBARg6dCh0dXXzbV8ikcDa2hpyuRxBQUGwtbVV7myQS1tbGzY2NpBKpQgKCoKzszMsLCwAAM7OzggNDUVSUhI6tmmD83v2oLsQaA1gHAAvAF0AfAHgUkYGwnNy8NeuXXBt2xbPnz8vo14iIiIierfoqDuA/KSkpODOnTvKryMjI3Hp0iWYmJjAxMQEs2fPRu/evWFlZYX79+9j+vTpMDMzQ69evQAAxsbGGDFiBCZOnAhTU1OYmJhg0qRJaNCgAdzc3FTuFRYWhsjIyDzTCnItWbIEnp6e0NLSwu7du7Fo0SJs375dOW0gISEBO3fuRIcOHZCeno6AgADs2LEDJ0+eVLYxZswYrFy5Eo3q14fs8WP4ZWVhIoCgfO4nAdAWwOnMTLS/dg0DPv4YB44ezXdKBBEREdF7TbyFfvvtNwEgzzFs2DDx4sUL4e7uLszNzYWurq6oXr26GDZsmIiKilJpIy0tTfj5+QkTExMhl8uFt7d3njJCCDFgwADh4uKici4pKUkAEElJSaJjx47C2NhY6OnpiVatWonDhw+rlH3y5Ilo3bq1MDAwEPr6+qJTp07ir7/+ynOfFStWCABCCgh7QKwGhCjiuAsIbYlE/Pnnn2XQq0RERERvv1fzsKK89fvIqkNJ9i8rrn69e0Oyfz+CX3srWFF66+hA3rs3NgcHl0kcRERERG+z92IfWU3y7Nkz7N67F2NKmMQCwKfZ2dixaxeSk5PLITIiIiIizaXWRPb58+cYP3487OzsIJfL4eLiovIWrVeNHj0aEolEuUdrQbKysjB37lzUrFkTenp6aNSoEY4ePVoO0RdfdHQ0snNy0LwUdZsByMzO5pZcRERERK9RayI7cuRIhIaGIjAwEFeuXIG7uzvc3NzyJG179+7F2bNnYW1tXWSbX3/9NdauXYuffvoJ169fx5gxY9CrVy9cvHixvB6jSLkvapCVom7uO8Ny2yAiIiKil9SWyKalpWHXrl347rvv0K5dO9SqVQuzZ8+Gg4MDVq9erSz38OFD+Pn5YcuWLQVuj/WqwMBATJ8+HV26dEGNGjXw6aefwsPDA0uXLi3PxylU7hvG4osol5/cOq+/pYyIiIjofae2RDY7OxsKhQJ6enoq5+VyOU6dOgUAyMnJwZAhQzB58mTUq1evWO1mZGQU2mZBdZKTk1WOslSjRg3UsLFBaZZrBQFwsrdH9erVyzQmIiIiIk2ntkTW0NAQzs7OmDdvHh49egSFQoHNmzfj7NmziI2NBQAsXrwYOjo6GDt2bLHb9fDwwLJly3D79m3k5OQgNDQU+/btU7aZn4ULF8LY2Fh52NravvHzvUpLSwufjh+P1TIZckpQTwFgjUyGT8eP5z6yRERERK9R6xzZwMBACCFgY2MDmUyGH3/8EQMHDoS2tjbOnz+PH374ARs2bChREvfDDz+gdu3aqFOnDqRSKfz8/ODr66t8gUF+pk2bhqSkJOURHR1dFo+nwtfXF7EAfixBnWUAnkokGDZsWJnHQ0RERKTp1JrI1qxZEydPnkRKSgqio6Nx7tw5ZGVlwcHBAX/88Qfi4+NRvXp16OjoQEdHBw8ePMDEiRNhb29fYJvm5ubYu3cvUlNT8eDBA/z777+oVKkSHBwcCqwjk8lgZGSkcpQ1U1NTBG7diklaWlhbjPI/A5iupYUt27ahcuXKZR4PERERkaZ7K/aRNTAwgJWVFZ49e4aQkBD06NEDQ4YMwT///INLly4pD2tra0yePBkhISFFtqmnpwcbGxtkZ2dj165d6NGjRwU8SeE++ugjbN6yBWN1dNBZKsVeAK/uLJsNYDcAN6kUE3V0sDU4GN27d1dLrERERERvOx113jwkJARCCDg5OeHOnTuYPHkynJyc4OvrC11dXZiamqqU19XVRdWqVeHk5KQ8N3ToUNjY2GDhwoUAgLNnz+Lhw4do3LgxHj58iNmzZyMnJwdfffVVhT5bQfr374+GDRvipxUrMHjTJhgIATttbQgADxQKpGlpYciwYfhp3DjUrVtX3eESERERvbXUmsgmJSVh2rRpiImJgYmJCXr37o0FCxYUa5utXFFRUdDS+t/Acnp6Or7++mvcu3cPlSpVQpcuXRAYGPhWfTz/wQcfYPUvv2Dx99/j8OHDePz4MQDA0tISXbp0KZepDURERETvGokQQqg7iLdNSd7xS0RERERlpyR52FsxR5aIiIiIqKSYyBIRERGRRmIiS0REREQaiYksEREREWkkJrJEREREpJGYyBIRERGRRmIiS0REREQaiYksEREREWkktb7Z622V+46I5ORkNUdCRERE9H7Jzb+K884uJrL5eP78OQDA1tZWzZEQERERvZ+eP38OY2PjQsvwFbX5yMnJwaNHj2BoaAiJRFKu90pOToatrS2io6P5OtzXsG/yx34pGPumYOybgrFv8sd+KRj7pmBl0TdCCDx//hzW1tbQ0ip8FixHZPOhpaWFatWqVeg9jYyM+D9DAdg3+WO/FIx9UzD2TcHYN/ljvxSMfVOwN+2bokZic3GxFxERERFpJCayRERERKSRmMiqmUwmwzfffAOZTKbuUN467Jv8sV8Kxr4pGPumYOyb/LFfCsa+KVhF9w0XexERERGRRuKILBERERFpJCayRERERKSRmMgSERERkUZiIlsBfv/9d3Tr1g3W1taQSCTYu3evyvXHjx/Dx8cH1tbW0NfXh6enJ27fvq2eYCvYwoUL0aJFCxgaGsLCwgI9e/bEzZs3VcoIITB79mxYW1tDLpejQ4cOuHbtmpoirhjF6Zfdu3fDw8MDZmZmkEgkuHTpknqCrWBF9U1WVhamTJmCBg0awMDAANbW1hg6dCgePXqkxqgrRnF+bmbPno06derAwMAAVapUgZubG86ePaumiCtOcfrmVaNHj4ZEIsGKFSsqLkg1KE6/+Pj4QCKRqBytW7dWU8QVp7g/Mzdu3ED37t1hbGwMQ0NDtG7dGlFRUWqIuOIUp29e/5nJPZYsWVKmsTCRrQCpqalo1KgRVq5cmeeaEAI9e/bEvXv3sG/fPly8eBF2dnZwc3NDamqqGqKtWCdPnsTnn3+Ov/76C6GhocjOzoa7u7vKs3/33XdYtmwZVq5ciYiICFStWhWdO3dWvkr4XVScfklNTUWbNm2waNEiNUZa8YrqmxcvXuDChQuYOXMmLly4gN27d+PWrVvo3r27miMvf8X5uXF0dMTKlStx5coVnDp1Cvb29nB3d8eTJ0/UGHn5K07f5Nq7dy/Onj0La2trNURasYrbL56enoiNjVUehw8fVlPEFac4fXP37l20bdsWderUQXh4OC5fvoyZM2dCT09PjZGXv+L0zas/L7GxsVi/fj0kEgl69+5dtsEIqlAAxJ49e5Rf37x5UwAQV69eVZ7Lzs4WJiYmYt26dWqIUL3i4+MFAHHy5EkhhBA5OTmiatWqYtGiRcoy6enpwtjYWKxZs0ZdYVa41/vlVZGRkQKAuHjxYsUH9hYorG9ynTt3TgAQDx48qMDI1K84fZOUlCQAiOPHj1dgZOpXUN/ExMQIGxsbcfXqVWFnZyeWL1+ungDVJL9+GTZsmOjRo4f6gnpL5Nc3/fr1E4MHD1ZjVG+H4vyu6dGjh3B1dS3ze3NEVs0yMjIAQOVfb9ra2pBKpTh16pS6wlKbpKQkAICJiQkAIDIyEnFxcXB3d1eWkclkaN++PU6fPq2WGNXh9X6h/ylO3yQlJUEikaBy5coVFNXboai+yczMxC+//AJjY2M0atSoIkNTu/z6JicnB0OGDMHkyZNRr149dYWmVgX9zISHh8PCwgKOjo4YNWoU4uPj1RGeWr3eNzk5OTh06BAcHR3h4eEBCwsLtGrVKs/0wfdBUb9rHj9+jEOHDmHEiBFlfm8msmpWp04d2NnZYdq0aXj27BkyMzOxaNEixMXFITY2Vt3hVSghBCZMmIC2bduifv36AIC4uDgAgKWlpUpZS0tL5bV3XX79Qi8Vp2/S09MxdepUDBw48L16J3phfXPw4EFUqlQJenp6WL58OUJDQ2FmZqamSCteQX2zePFi6OjoYOzYsWqMTn0K6hcvLy9s2bIFYWFhWLp0KSIiIuDq6qociHkf5Nc38fHxSElJwaJFi+Dp6Yljx46hV69e+Oijj3Dy5Ek1R1xxivN7eOPGjTA0NMRHH31U5vfXKfMWqUR0dXWxa9cujBgxAiYmJtDW1oabmxu8vLzUHVqF8/Pzwz///JPvSLREIlH5WgiR59y7qrB+ed8V1TdZWVno378/cnJysGrVqgqOTr0K65uOHTvi0qVLSEhIwLp169C3b1+cPXsWFhYWaoi04uXXN+fPn8cPP/yACxcuvDe/W15X0M9Mv379lP9dv359NG/eHHZ2djh06FC5JCZvo/z6JicnBwDQo0cPfPnllwCAxo0b4/Tp01izZg3at2+vllgrWnH+Rq1fvx6DBg0ql7nDHJF9CzRr1gyXLl1CYmIiYmNjcfToUTx9+hQODg7qDq3CfPHFF9i/fz9+++03VKtWTXm+atWqAJBn9DU+Pv7/2rv3oKiuOw7g35U3uEsaILAqL6UioFIQk4IF4qPgQIsBIsQaxYgSAwZL8VGVBIztaIimWrUk1g2QWGO0EmMDGQbk4UYcEVweQVQgPHysRXE0Isjz1z8sd1wXZDUCWfP7zNwZ99xzz/7uuQf3x9l7D2qztM+igfqFDd43XV1dCAsLQ319PXJycn5Ws7GD9Y2JiQkcHBzw61//GjKZDLq6upDJZCMQ6fAbqG/kcjmam5thY2MDXV1d6OrqorGxEfHx8bCzsxu5gIfJ4/xfI5VKYWtr+7NZXWegvjE3N4euri6cnZ1V6js5OT3zqxb00WTcyOVyXLhwAcuWLRuSGDiR/QkxNTWFhYUFampqUFJSgnnz5o10SEOOiLBy5UpkZGQgLy9PLXm3t7eHlZUVcnJyhLLOzk4UFhbCy8truMMdNoP1y8+ZJn3Tl8TW1NQgNzcXZmZmIxDp8HvScUNEz/zXxIP1zaJFi1BRUYGysjJhGzNmDNasWYPs7OwRinroPcmYaWlpwaVLlyCVSochwpEzWN/o6+tj+vTpastOXbx4Eba2tsMZ6rB7nHEjk8kwbdq0obsP/6k/PsbU3LlzhxQKBSkUCgJAH374ISkUCuEJ6kOHDlF+fj7V1dXR0aNHydbWlkJCQkY46uHx1ltvkampKRUUFJBSqRS2trY2oc7WrVvJ1NSUMjIyqLKykhYsWEBSqZR++OGHEYx8aGnSLy0tLaRQKCgzM5MA0MGDB0mhUJBSqRzByIfeYH3T1dVFQUFBNG7cOCorK1Op09HRMcLRD63B+qa1tZXWr19Pp06dooaGBiotLaXIyEgyMDBQWTnlWaTJz9TDfg6rFgzWL3fu3KH4+HgqKiqi+vp6ys/PJ09PTxo7duwz/X8wkWZjJiMjg/T09Gjv3r1UU1NDu3btIh0dHZLL5SMY+dDT9Ofp9u3bZGxsTCkpKUMWCyeywyA/P58AqG0RERFERLRz504aN24c6enpkY2NDSUkJDzzH7h9+usXAJSamirU6e3tpcTERLKysiIDAwPy8fGhysrKkQt6GGjSL6mpqf3WSUxMHLG4h8NgfdO3HFl/W35+/ojGPtQG65v29nYKDg6mMWPGkL6+PkmlUgoKCqLi4uKRDXwYaPIz9bCfQyI7WL+0tbWRn58fWVhYCJ9RERER1NTUNLKBDwNNx4xMJiMHBwcyNDQkV1dXOnr06MgEPIw07ZuPP/6YjIyM6NatW0MWi+j/ATHGGGOMMaZV+B5ZxhhjjDGmlTiRZYwxxhhjWokTWcYYY4wxppU4kWWMMcYYY1qJE1nGGGOMMaaVOJFljDHGGGNaiRNZxhhjjDGmlTiRZYwxxhhjWokTWcbYT15bWxtCQ0MhkUggEolw69atkQ5pRCxZsgSvvPKK8Prll1/GH//4xxGL5+euoaEBIpEIZWVlQ/5enZ2dcHBwwMmTJ5+4jY6ODtjY2KC0tPQpRsbYyOJEljEttGTJEohEIqxYsUJtX3R0NEQiEZYsWTL8gT0kLS0NIpFI2CwtLfH73/8eVVVVj9VOeno65HI5ioqKoFQqYWpqOkQRj7yoqCjo6Ojg4MGDg9bNyMjA5s2bhyGq4ZGUlIRf/epXGtV7cFz1bbm5uUMW28O/RACAtbU1lEolJk+ePGTv22fv3r2wtbXFjBkzANxPShctWgSJRAJHR0fk5eWp1E9OTsbbb7+tUmZgYIDVq1dj3bp1Qx4vY8OFE1nGtJS1tTUOHjyI9vZ2oezevXv4/PPPYWNjM4KRqZJIJFAqlbh69SoyMzNx9+5dBAYGorOzU+M26urq4OTkhMmTJ8PKygoikeix4+jp6UFvb+9jHzec2tra8MUXX2DNmjWQyWSD1n/++echFouHIbKfHhcXFyiVSpXNx8dHrd7jjLPHpaOjAysrK+jq6g7Ze/TZtWsXli1bJrzeu3cvSktLcerUKSxfvhwLFixA31+cr6+vx759+/DXv/5VrZ2FCxdCLpejurp6yGNmbDhwIsuYlnJ3d4eNjQ0yMjKEsoyMDFhbW8PNzU2lLhEhOTkZ48ePh5GREVxdXfHvf/9b2N/T04PIyEjY29vDyMgIjo6O2Llzp0obfTNS27Ztg1QqhZmZGWJiYtDV1fXIOEUiEaysrCCVSuHh4YG4uDg0NjbiwoULQp2ioiL4+PjAyMgI1tbWiI2Nxd27dwHc//p8+/btOHHiBEQiEV5++WUA9xOUtWvXYuzYsTAxMcFLL72EgoICoc20tDQ899xz+Prrr+Hs7AwDAwM0NjZqfFx2djacnJwwevRozJ07F0qlUuW8PvnkE7i4uMDAwABSqRQrV64U9t2+fRtRUVF44YUXIJFIMGvWLJSXlz+ynwDg8OHDcHZ2xvr163Hy5Ek0NDQ8sv7DtxYolUoEBgbCyMgI9vb2OHDgAOzs7LBjxw6V67Fv3z4EBwfD2NgYv/zlL3Hs2DFhf0FBAUQiEbKzs+Hm5gYjIyPMmjULzc3N+Oabb+Dk5ASJRIIFCxagra1NOG6wMdbX7vHjx+Hh4QFjY2N4eXkJ4yAtLQ2bNm1CeXm5MMOalpY24Lnr6urCyspKZdPX1xfG6ZYtWzBmzBhMnDgRALB//354eHhALBbDysoKf/jDH9Dc3KzSZlVVFQIDAyGRSCAWi+Ht7Y26ujokJSUhPT0dX331lRBbQUFBv7cWFBYW4sUXXxTGxZ///Gd0d3erXLPY2FisXbsWzz//PKysrJCUlPTI63z27FnU1tYiMDBQKKuurkZQUBBcXFwQExOD5uZm3LhxAwDw1ltv4f3334dEIlFry8zMDF5eXvj8888f+Z6MaQtOZBnTYm+88QZSU1OF15988gmWLl2qVi8hIQGpqalISUlBVVUV4uLi8Prrr6OwsBAA0Nvbi3HjxuHQoUM4d+4c3n33XWzYsAGHDh1SaSc/Px91dXXIz89Heno60tLSHplsPOzWrVs4cOAAAEBPTw8AUFlZCX9/f4SEhKCiogJffPEFvv32WyExzMjIwPLly+Hp6QmlUikk7m+88QZOnjyJgwcPoqKiAvPnz8fcuXNRU1MjvF9bWxu2bNmCffv2oaqqCi+88ILGx23btg2fffYZTpw4gaamJqxevVrYn5KSgpiYGERFRaGyshLHjh2Dg4MDgPsJXWBgIK5du4asrCyUlpbC3d0ds2fPxs2bNx/ZPzKZDK+//jpMTU0REBCgcm01sXjxYly9ehUFBQU4cuQI9u7dq5asAcCmTZsQFhaGiooKBAQEYOHChWqxJSUlYffu3SgqKsKlS5cQFhaGHTt24MCBA8jMzEROTg527dol1B9sjPXZuHEjtm/fjpKSEujq6grjNTw8HPHx8SozreHh4Y91/n2OHz+O6upq5OTk4OuvvwZw/xefzZs3o7y8HEePHkV9fb3K7TdXrlyBj48PDA0NkZeXh9LSUixduhTd3d1YvXo1wsLChF9olEolvLy81N73ypUrCAgIwPTp01FeXo6UlBTIZDL85S9/UamXnp4OExMTnD59GsnJyXjvvfeQk5Mz4PmcOHECEydOVElMXV1d8e2336K9vR3Z2dmQSqUwNzfH/v37YWhoiODg4AHbe/HFFyGXyzXtTsZ+2ogxpnUiIiJo3rx5dP36dTIwMKD6+npqaGggQ0NDun79Os2bN48iIiKIiKi1tZUMDQ2pqKhIpY3IyEhasGDBgO8RHR1NoaGhKu9pa2tL3d3dQtn8+fMpPDx8wDZSU1MJAJmYmJCxsTEBIAAUFBQk1Fm0aBFFRUWpHCeXy2nUqFHU3t5ORESrVq0iX19fYX9tbS2JRCK6cuWKynGzZ8+m9evXq7x3WVnZEx1XW1sr7N+zZw9ZWloKr8eMGUMbN27s95yPHz9OEomE7t27p1I+YcIE+vjjj/vvKCK6ePEi6enp0fXr14mI6MsvvyRra2vq6ekR6vRd9z6+vr60atUqIiKqrq4mAHTmzBlhf01NDQGgv/3tb0IZAEpISBBet7a2kkgkom+++YaIiPLz8wkA5ebmCnW2bNlCAKiurk4oe/PNN8nf319oY7Ax1l+7mZmZBEC4zomJieTq6jpgH/VJTEykUaNGkYmJibBNnz5d6CNLS0vq6Oh4ZBvFxcUEgO7cuUNEROvXryd7e3vq7Ozst/7DfU9EVF9fTwBIoVAQEdGGDRvI0dGRent7hTp79uyh0aNHC9fR19eXfvOb36i0M336dFq3bt2Asa5atYpmzZqlUtbZ2UnR0dFkZ2dHHh4eJJfLqaWlhcaPH0+NjY20ceNGmjBhAvn5+dHly5dVjt25cyfZ2dkN3DmMaZGhv7GHMTZkzM3NERgYiPT0dGEm0NzcXKXOuXPncO/ePfz2t79VKe/s7FS5BeGjjz7Cvn370NjYiPb2dnR2dqo9eOPi4gIdHR3htVQqRWVl5SNjFIvFOHv2LLq7u1FYWIgPPvgAH330kbC/tLQUtbW1+Ne//iWUERF6e3tRX18PJycntTbPnj0LIhK+Nu7T0dEBMzMz4bW+vj6mTp362McZGxtjwoQJKufZN7PZ3NyMq1evYvbs2f2eb2lpKVpbW1XaA4D29nbU1dX1ewxwfzbW399fuH4BAQGIjIxEbm4u/Pz8Bjyuz4ULF6Crqwt3d3ehzMHBAb/4xS/U6j7YJyYmJhCLxWoztw/WsbS0hLGxMcaPH69SVlxcDEDzMfZwu1KpFMD9Pn3c+7odHR1VbokwMDAQ/j1lyhTo6+ur1FcoFEhKSkJZWRlu3rwp3C/d1NQEZ2dnlJWVwdvbW/im4ElUV1fD09NT5R7uGTNmoLW1FZcvXxbO8cE+AFTHV3/a29thaGioUqanp4c9e/aolC1ZsgSxsbEoKyvD0aNHUV5ejuTkZMTGxuLIkSNCPSMjI5XbQhjTZpzIMqblli5dKnwN//AHGwDhAzszMxNjx45V2df34X/o0CHExcVh+/bt8PT0hFgsxgcffIDTp0+r1H/4Q14kEg36ANWoUaOEr90nTZqEa9euITw8HCdOnBDie/PNNxEbG6t27EDJTW9vL3R0dFBaWqqSWAPA6NGjhX8bGRmpJBWaHtffedL/H6QxMjJ65Pn29vZCKpWq3Hfb57nnnuv3mJ6eHnz66ae4du2ayoNDPT09kMlkGiWyffFpUq7JdXywjkgkeuQxmoyxgdp98PjHoa+vL4yrh5mYmKi8vnv3Lvz8/ODn54f9+/fDwsICTU1N8Pf3Fx4GG+y6aoKI1B5E7Ov/B8sf9+fI3Nx80F8Y8/LycO7cOchkMqxZswYBAQEwMTFBWFgYdu/erVL35s2bsLCw0OicGPup40SWMS03d+5c4cPY399fbX/fg05NTU3w9fXttw25XA4vLy9ER0cLZY+aPfwx4uLi8OGHH+LLL79EcHAw3N3dUVVVNWBS0h83Nzf09PSgubkZ3t7eQ37cg8RiMezs7HD8+HHMnDlTbb+7u7uQkNrZ2WnUZlZWFu7cuQOFQqGSYJ8/fx4LFy5ES0uL2gzvwyZNmoTu7m4oFApMmzYNAFBbWzssa+5qMsY0oa+vj56enqcY2X3nz5/HjRs3sHXrVlhbWwMASkpKVOpMnToV6enp6Orq6ndWVpPYnJ2dceTIEZWEtqioCGKxWC3Bfxxubm5ISUnpN1EG7q9WEhMTgwMHDkBHRwc9PT1CAt3V1aUW93fffac2U86YtuKHvRjTcjo6OqiurkZ1dbXaLCNwP/FavXo14uLikJ6ejrq6OigUCuzZswfp6ekA7n8FXVJSguzsbFy8eBHvvPMOzpw5MyTxSiQSLFu2DImJiSAirFu3DqdOnUJMTAzKyspQU1ODY8eOqa2B+aCJEydi4cKFWLx4MTIyMlBfX48zZ87g/fffR1ZW1lM/7mFJSUnYvn07/v73v6OmpgZnz54VHnyaM2cOPD098corryA7OxsNDQ0oKipCQkKCWvLURyaTITAwEK6urpg8ebKwhYaGwsLCAvv37x80pkmTJmHOnDmIiopCcXExFAoFoqKi1Galh4ImY0wTdnZ2qK+vR1lZGW7cuIGOjo6nEp+NjQ309fWxa9cufP/99zh27Jja+rsrV67EDz/8gNdeew0lJSWoqanBZ599JqyqYGdnh4qKCly4cAE3btzod7WO6OhoXLp0CW+//TbOnz+Pr776ComJifjTn/6EUaOe/ON25syZuHv37oDrL7/33nsIDAwUktMZM2YgIyMDFRUV2L17t7D2bB+5XK7RLD9j2oATWcaeARKJpN+ldvps3rwZ7777LrZs2QInJyf4+/vjP//5D+zt7QEAK1asQEhICMLDw/HSSy+hpaVFZXb2aVu1ahWqq6tx+PBhTJ06FYWFhaipqYG3tzfc3NzwzjvvCPdPDiQ1NRWLFy9GfHw8HB0dERQUhNOnTwszbk/7uAdFRERgx44d+Mc//gEXFxf87ne/E1Y9EIlEyMrKgo+PD5YuXYqJEyfitddeQ0NDAywtLdXa+u9//4vMzEyEhoaq7ROJRAgJCdFoTVkA+PTTT2FpaQkfHx8EBwdj+fLlEIvFavdXDoXBxpgmQkNDMXfuXMycORMWFhZPbYkoCwsLpKWlCcubbd26Fdu2bVOpY2Zmhry8PLS2tsLX1xfTpk3DP//5T2F2dvny5XB0dISHhwcsLCz6/QtbY8eORVZWFoqLi+Hq6ooVK1YgMjISCQkJPyp+MzMzhISEqNxH3ue7777D4cOHsWnTJqHs1VdfRWBgILy9vVFRUaGylN6pU6dw+/ZtvPrqqz8qJsZ+KkQ00I1VjDHGtNrly5dhbW2N3NzcAR9OY9qhsrISc+bMQW1t7Y/6Ixjz58+Hm5sbNmzY8BSjY2zk8D2yjDH2jOibUZwyZQqUSiXWrl0LOzu7fv/iFdMuU6ZMQXJyMhoaGjBlypQnaqOjowOurq6Ii4t7ytExNnJ4RpYxxp4R2dnZiI+Px/fffw+xWAwvLy/s2LEDtra2Ix0aY4wNCU5kGWOMMcaYVuKHvRhjjDHGmFbiRJYxxhhjjGklTmQZY4wxxphW4kSWMcYYY4xpJU5kGWOMMcaYVuJEljHGGGOMaSVOZBljjDHGmFbiRJYxxhhjjGklTmQZY4wxxphW+h8aP+wbjZAcigAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 700x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Extract sample ID from Bin_Name\n", "df['SampleID'] = df['Bin_Name'].apply(lambda x: str(x).split('.')[0])\n", "\n", "# Group by SampleID and Treatment, calculate mean ANI and mean reference coverage\n", "grouped = df.groupby(['SampleID', 'Treatment']).agg({\n", "    'ANI': 'mean',\n", "    'Align_fraction_ref': 'mean'\n", "}).reset_index()\n", "\n", "# Plot\n", "plt.figure(figsize=(7, 5))\n", "sns.scatterplot(\n", "    data=grouped,\n", "    x='Align_fraction_ref',\n", "    y='ANI',\n", "    hue='Treatment',\n", "    palette={'PTA': 'C0', 'MDA': 'red'},\n", "    s=120,\n", "    edgecolor='black'\n", ")\n", "for _, row in grouped.iterrows():\n", "    plt.text(row['Align_fraction_ref'], row['ANI'], row['SampleID'], fontsize=10, ha='right', va='bottom')\n", "\n", "plt.xlabel('Mean Reference Alignment Fraction (%)')\n", "plt.ylabel('Mean ANI (%)')\n", "plt.title('Mean ANI vs Reference Coverage by Sam<PERSON>')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "fbf8b318", "metadata": {}, "source": ["Strict filtering to reduce num of comparisons:\n", "- Maintains biological relevance (keeps high-quality matches)\n", "- Removes technical artifacts (over-represented housekeeping genes)\n", "- Eliminates redundancy (one match per bacterial contig)\n", "- Preserves the strongest signals (highest ANI matches)\n", "- This should give you a much more manageable dataset that focuses on the most biologically meaningful bacterial-Acanthamoeba relationships while eliminating the multiplicative noise."]}, {"cell_type": "code", "execution_count": 8, "id": "35395168", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final filtered dataset: 5,428\n", "\n", "Final dataset characteristics\n", "Unique bacterial contigs: 4,950\n", "Unique Acanthamoeba sequences: 3,675\n", "Average ANI: 97.37%\n", "Average reference coverage: 23.77%\n"]}], "source": ["step1 = df[(df['ANI'] > 95) & (df['Align_fraction_query'] > 70)]\n", "\n", "acan_counts = step1['Query_name'].value_counts()\n", "threshold = acan_counts.quantile(.90)\n", "step2 = step1[~step1['Query_name'].isin(acan_counts[acan_counts > threshold].index)]\n", "\n", "max_ani_per_contig = step2.groupby('Ref_name')['ANI'].transform('max')\n", "final_filtered = step2[step2['ANI'] == max_ani_per_contig]\n", "\n", "print(f\"Final filtered dataset: {len(final_filtered):,}\")\n", "print(f\"\\nFinal dataset characteristics\")\n", "print(f\"Unique bacterial contigs: {final_filtered['Ref_name'].nunique():,}\")\n", "print(f\"Unique Acanthamoeba sequences: {final_filtered['Query_name'].nunique():,}\")\n", "print(f\"Average ANI: {final_filtered['ANI'].mean():.2f}%\")\n", "print(f\"Average reference coverage: {final_filtered['Align_fraction_ref'].mean():.2f}%\")"]}, {"cell_type": "code", "execution_count": 11, "id": "0181db78", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved 5,428 filtered matches to 'final_filtered_matches.csv'\n"]}], "source": ["# Save the filtered comparison results\n", "final_filtered.to_csv('final_filtered_matches.csv', index=False)\n", "print(f\"Saved {len(final_filtered):,} filtered matches to 'final_filtered_matches.csv'\")"]}, {"cell_type": "code", "execution_count": 15, "id": "2cedae04", "metadata": {}, "outputs": [], "source": ["def extract_contig_id(full_name):\n", "    # Extract NODE_X from the full name\n", "    try:\n", "        # Split by underscore and find the NODE part\n", "        parts = full_name.split('_')\n", "        for i, part in enumerate(parts):\n", "            if part == 'NODE':\n", "                return f\"{part}_{parts[i+1]}\"  # Returns \"NODE_782\"\n", "    except:\n", "        return full_name  # Return original if parsing fails\n", "    return full_name\n", "\n", "simplified_output = final_filtered.copy()\n", "\n", "# Create the new column structure\n", "output_df = pd.DataFrame({\n", "    'Sampleid': simplified_output['Bin_Name'],\n", "    'contigid': simplified_output['Ref_name'].apply(extract_contig_id),\n", "    'Align_fraction_query[%]': simplified_output['Align_fraction_query'],\n", "    'ANI[%]': simplified_output['ANI']\n", "})\n", "\n", "# Save the simplified version\n", "output_df.to_csv('final_filtered_matches_simplified.csv', index=False)"]}, {"cell_type": "code", "execution_count": 9, "id": "b8a711c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully loaded DataFrame with shape: (40428, 7)\n", "Loaded 40428 comparison results\n", "Sample bin names: ['1507996.bin.1' '1507994.bin.1' '1507992.bin.4' '1507993.bin.1'\n", " '1507999.bin.1' '1507995.bin.1' '1507993.bin.3' '1507992.bin.3'\n", " '1507992.bin.2' '1507990.bin.2']\n", "Sample data:\n", "        Bin_Name    ANI  Align_fraction_ref  Align_fraction_query\n", "0  1507996.bin.1  98.34                8.55                 100.0\n", "1  1507994.bin.1  94.88               14.38                 100.0\n", "2  1507992.bin.4  94.88                7.91                 100.0\n", "3  1507993.bin.1  94.88                9.14                 100.0\n", "4  1507996.bin.1  94.88               14.12                 100.0\n", "\n", "BEFORE FILTERING\n", "Total comparisons: 40,428\n", "Unique contigs: 12,138\n", "Unique Acanthamoeba sequences: 6,119\n", "Average ANI: 95.35%\n", "\n", "AFTER FILTERING\n", "Total comparisons: 5,428\n", "Unique contigs: 4,950\n", "Unique Acanthamoeba sequences: 3,675\n", "Average ANI: 97.37%\n"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "OUTPUT_DIR = './manual_skani_analysis'\n", "\n", "skani_file = Path(OUTPUT_DIR) / 'skani_contigs_results.txt'\n", "df = load_and_process_results_fixed(str(skani_file))\n", "\n", "#before filtering analysis\n", "print(\"\\nBEFORE FILTERING\")\n", "print(f\"Total comparisons: {len(df):,}\")\n", "print(f\"Unique contigs: {df['Ref_name'].nunique():,}\")\n", "print(f\"Unique Acanthamoeba sequences: {df['Query_name'].nunique():,}\")\n", "print(f\"Average ANI: {df['ANI'].mean():.2f}%\")\n", "\n", "#after filtering analysis\n", "print(\"\\nAFTER FILTERING\")\n", "print(f\"Total comparisons: {len(final_filtered):,}\")\n", "print(f\"Unique contigs: {final_filtered['Ref_name'].nunique():,}\")\n", "print(f\"Unique Acanthamoeba sequences: {final_filtered['Query_name'].nunique():,}\")\n", "print(f\"Average ANI: {final_filtered['ANI'].mean():.2f}%\")"]}, {"cell_type": "code", "execution_count": 14, "id": "32907718", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from Bio import SeqIO\n", "import os\n", "\n", "BINS_DIRECTORY = \"/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/03bins/metabat2_fixed/\"\n", "ASSEMBLY_FILE = \"/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/\"\n", "OUTPUT_DIR = \"/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/manual_skani_analysis/\"\n", "\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "\n", "def get_contig_ids_from_bins(bins_directory):\n", "    binned_contigs = {}\n", "    all_binned_contigs = set()\n", "\n", "    bin_files = []\n", "\n", "    for root, dirs, files in os.walk(bins_directory):\n", "        for file in files:\n", "            if file.endswith(\".fa\"):\n", "                full_path = os.path.join(root, file)\n", "                rel_path = os.path.relpath(full_path, bins_directory)\n", "                bin_files.append((full_path, rel_path))\n", "\n", "    print(f\"Found {len(bin_files)} bin files\")\n", "\n", "    for bin_path, rel_path in bin_files:\n", "        bin_name = os.path.splitext(rel_path)[0]\n", "\n", "        contgis_in_bins = []\n", "\n", "        try:\n", "            for record in SeqIO.parse(bin_path, \"fasta\"):\n", "                contig_id = record.id\n", "                contgis_in_bins.append(contig_id)\n", "                all_binned_contigs.add(contig_id)\n", "\n", "            binned_contigs[bin_name] = contgis_in_bins\n", "\n", "        except Exception as e:\n", "            print(f\"Error reading {rel_path}: {e}\")\n", "\n", "    return binned_contigs, all_binned_contigs"]}, {"cell_type": "code", "execution_count": 22, "id": "e3be6873", "metadata": {}, "outputs": [], "source": ["import os\n", "import gzip\n", "\n", "def get_contig_ids_from_assembly(assembly_directory):\n", "    assembly_contigs = set()\n", "    contig_lengths = {}\n", "\n", "    # Look for scaffolds.fasta files only\n", "    assembly_files = []\n", "    for root, dirs, files in os.walk(assembly_directory):\n", "        for file in files:\n", "            if file in ['scaffolds.fasta', 'scaffolds.fasta.gz']:\n", "                full_path = os.path.join(root, file)\n", "                assembly_files.append(full_path)\n", "    \n", "    print(f\"Found {len(assembly_files)} scaffold files\")\n", "    \n", "    for assembly_file in assembly_files:\n", "        try:\n", "            if assembly_file.endswith('.gz'):\n", "                with gzip.open(assembly_file, 'rt') as handle:\n", "                    for record in SeqIO.parse(handle, \"fasta\"):\n", "                        contig_id = record.id\n", "                        assembly_contigs.add(contig_id)\n", "                        contig_lengths[contig_id] = len(record.seq)\n", "            else:\n", "                for record in SeqIO.parse(assembly_file, \"fasta\"):\n", "                    contig_id = record.id\n", "                    assembly_contigs.add(contig_id)\n", "                    contig_lengths[contig_id] = len(record.seq)\n", "\n", "        except Exception as e:\n", "            print(f\"Error parsing {assembly_file}: {e}\")\n", "    \n", "    print(f\"Total contigs found: {len(assembly_contigs)}\")\n", "    return assembly_contigs, contig_lengths"]}, {"cell_type": "code", "execution_count": 23, "id": "b6ccb02b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 18 bin files\n", "Found 14 scaffold files\n", "Total contigs found: 250791\n", "Unbinned contigs: 234,957\n", "Contigs in bins but not in assembly: 15834\n"]}], "source": ["binned_contigs, all_binned_contigs = get_contig_ids_from_bins(BINS_DIRECTORY)\n", "assembly_contigs, contig_lengths = get_contig_ids_from_assembly(ASSEMBLY_FILE)\n", "\n", "unbinned_contigs = assembly_contigs - all_binned_contigs\n", "missing_from_assembly = all_binned_contigs - unbinned_contigs\n", "\n", "print(f\"Unbinned contigs: {len(unbinned_contigs):,}\")\n", "print(f\"Contigs in bins but not in assembly: {len(missing_from_assembly)}\")"]}, {"cell_type": "code", "execution_count": 24, "id": "57087180", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Category  Num_contigs  Total_length_bp  Mean_length_bp\n", "  Binned        15834        116597278     7363.728559\n", "Unbinned       234957        130834235      556.843316\n", "   Total       250791        247431513      986.604436\n", "\n", "Files saved:\n", "  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/manual_skani_analysis//binned_vs_unbinned_summary.csv\n", "  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/manual_skani_analysis//unbinned_contig_ids.txt\n"]}], "source": ["import numpy as np\n", "\n", "binned_lengths = [contig_lengths.get(contig, 0) for contig in all_binned_contigs]\n", "unbinned_lengths = [contig_lengths.get(contig, 0) for contig in unbinned_contigs]\n", "\n", "total_contigs = len(assembly_contigs)\n", "total_length = sum(contig_lengths.values())\n", "\n", "comparison_stats = pd.DataFrame({\n", "    'Category': ['Binned', 'Unbinned', 'Total'],\n", "    'Num_contigs': [len(all_binned_contigs), len(unbinned_contigs), total_contigs],\n", "    'Total_length_bp': [sum(binned_lengths), sum(unbinned_lengths), total_length],\n", "    'Mean_length_bp': [np.mean(binned_lengths), np.mean(unbinned_lengths), np.mean(list(contig_lengths.values()))],\n", "})\n", "\n", "print(comparison_stats.to_string(index=False))\n", "comparison_stats.to_csv(f'{OUTPUT_DIR}/binned_vs_unbinned_summary.csv', index=False)\n", "\n", "# Save unbinned contig IDs\n", "with open(f'{OUTPUT_DIR}/unbinned_contig_ids.txt', 'w') as f:\n", "    for contig in sorted(unbinned_contigs):\n", "        f.write(f\"{contig}\\n\")\n", "\n", "print(f\"\\nFiles saved:\")\n", "print(f\"  - {OUTPUT_DIR}/binned_vs_unbinned_summary.csv\")\n", "print(f\"  - {OUTPUT_DIR}/unbinned_contig_ids.txt\")"]}, {"cell_type": "code", "execution_count": 5, "id": "3ba90c07", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample contig IDs from contig_lengths:\n", "['CDFL01000001.1', 'CDFL01000002.1', 'CDFL01000003.1', 'CDFL01000004.1', 'CDFL01000005.1']\n", "\n", "Sample contig IDs from all_binned_contigs:\n", "['NODE_501_length_11120_cov_36.519205', 'NODE_2217_length_4858_cov_59.228191', 'NODE_1620_length_6127_cov_42.864295', 'NODE_361_length_12754_cov_17.428616', 'NODE_548_length_10788_cov_24.309606']\n", "\n", "Binned contigs found in assembly: 0/15834\n", "'NODE_501_length_11120_cov_36.519205' not found. Similar keys: []\n", "'NODE_2217_length_4858_cov_59.228191' not found. Similar keys: []\n", "'NODE_1620_length_6127_cov_42.864295' not found. Similar keys: []\n", "'NODE_361_length_12754_cov_17.428616' not found. Similar keys: []\n", "'NODE_548_length_10788_cov_24.309606' not found. Similar keys: []\n"]}], "source": ["# Debug: Check for mismatches\n", "print(\"Sample contig IDs from contig_lengths:\")\n", "print(list(contig_lengths.keys())[:5])\n", "print(\"\\nSample contig IDs from all_binned_contigs:\")\n", "print(list(all_binned_contigs)[:5])\n", "\n", "# Check how many binned contigs are found in contig_lengths\n", "found_binned = sum(1 for contig in all_binned_contigs if contig in contig_lengths)\n", "print(f\"\\nBinned contigs found in assembly: {found_binned}/{len(all_binned_contigs)}\")\n", "\n", "# Check for partial matches (in case of header formatting issues)\n", "sample_binned = list(all_binned_contigs)[:5]\n", "for contig in sample_binned:\n", "    if contig not in contig_lengths:\n", "        # Look for similar keys\n", "        similar = [k for k in list(contig_lengths.keys())[:10] if contig in k or k in contig]\n", "        print(f\"'{contig}' not found. Similar keys: {similar}\")"]}, {"cell_type": "code", "execution_count": null, "id": "cd7bfc87", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}