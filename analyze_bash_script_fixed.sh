#!/bin/bash

echo "=== ACANTHAMOEBA CASTELLANII ANALYSIS SUMMARY ==="
echo

# Create temporary combined file
temp_file=$(mktemp)
find acantheamoeba_mapping_results -name "*.txt" -not -name "filtered_*" -not -name "all_*" -exec awk 'NR>1 {print FILENAME, $0}' {} \; > "$temp_file"

# Overall statistics from combined data
awk '{
    total++
    ani_sum += $4
    if ($4 > 95) high_conf++
    if ($4 > 90) good_match++
    if ($6 > 0.5) good_coverage++
}
END {
    print "OVERALL STATISTICS:"
    print "Total bins analyzed:", total
    print "Average ANI:", sprintf("%.1f%%", ani_sum/total)
    print "High confidence A. castellanii (ANI >95%):", high_conf
    print "Likely A. castellanii (ANI >90%):", good_match
    print "Bins with good coverage (>50%):", good_coverage
    print ""
}' "$temp_file"

# List high-confidence matches
echo "HIGH CONFIDENCE A. CASTELLANII BINS (ANI >95%):"
awk '$4 > 95 {
    gsub(/.*\//, "", $1)
    gsub(/_results\.txt/, "", $1)
    printf "%-30s ANI: %5.1f%% | Query coverage: %5.1f%%\n", $1, $4, $6*100
}' "$temp_file" | sort -k3 -nr

echo
echo "LIKELY A. CASTELLANII BINS (ANI 90-95%):"
awk '$4 > 90 && $4 <= 95 {
    gsub(/.*\//, "", $1)
    gsub(/_results\.txt/, "", $1)
    printf "%-30s ANI: %5.1f%% | Query coverage: %5.1f%%\n", $1, $4, $6
}' "$temp_file" | sort -k3 -nr

# Clean up
rm "$temp_file"

echo
echo "=== ANALYSIS COMPLETE ==="