#!/usr/bin/env python3
"""
<PERSON>ript to calculate N50 of contigs from a FASTA file.
N50 is the length of the shortest contig at 50% of the total genome length.
"""

import sys
from Bio import SeqIO
import argparse

def calculate_n50(fasta_file):
    """
    Calculate N50 from a FASTA file.
    
    Args:
        fasta_file (str): Path to the FASTA file
        
    Returns:
        tuple: (n50_value, total_length, num_contigs, contig_lengths)
    """
    contig_lengths = []
    
    # Read sequences and get their lengths
    with open(fasta_file, 'r') as handle:
        for record in SeqIO.parse(handle, "fasta"):
            contig_lengths.append(len(record.seq))
    
    # Sort lengths in descending order
    contig_lengths.sort(reverse=True)
    
    total_length = sum(contig_lengths)
    num_contigs = len(contig_lengths)
    
    # Calculate N50
    cumulative_length = 0
    n50 = 0
    
    for length in contig_lengths:
        cumulative_length += length
        if cumulative_length >= total_length * 0.5:
            n50 = length
            break
    
    return n50, total_length, num_contigs, contig_lengths

def calculate_additional_stats(contig_lengths):
    """Calculate additional assembly statistics."""
    if not contig_lengths:
        return {}
    
    total_length = sum(contig_lengths)
    num_contigs = len(contig_lengths)
    
    # Calculate N90, N75, N25, N10
    stats = {}
    for percentile in [10, 25, 50, 75, 90]:
        cumulative_length = 0
        for length in contig_lengths:
            cumulative_length += length
            if cumulative_length >= total_length * (percentile / 100):
                stats[f'N{percentile}'] = length
                break
    
    stats['min_length'] = min(contig_lengths)
    stats['max_length'] = max(contig_lengths)
    stats['mean_length'] = total_length / num_contigs
    stats['median_length'] = contig_lengths[num_contigs // 2] if num_contigs > 0 else 0
    
    return stats

def main():
    parser = argparse.ArgumentParser(description='Calculate N50 and other assembly statistics from a FASTA file')
    parser.add_argument('fasta_file', help='Path to the FASTA file')
    parser.add_argument('--detailed', '-d', action='store_true', help='Show detailed statistics')
    
    args = parser.parse_args()
    
    try:
        n50, total_length, num_contigs, contig_lengths = calculate_n50(args.fasta_file)
        
        print(f"Assembly Statistics for: {args.fasta_file}")
        print("=" * 50)
        print(f"Total contigs: {num_contigs:,}")
        print(f"Total length: {total_length:,} bp")
        print(f"N50: {n50:,} bp")
        
        if args.detailed:
            additional_stats = calculate_additional_stats(contig_lengths)
            print(f"\nDetailed Statistics:")
            print(f"N10: {additional_stats['N10']:,} bp")
            print(f"N25: {additional_stats['N25']:,} bp")
            print(f"N50: {additional_stats['N50']:,} bp")
            print(f"N75: {additional_stats['N75']:,} bp")
            print(f"N90: {additional_stats['N90']:,} bp")
            print(f"Min length: {additional_stats['min_length']:,} bp")
            print(f"Max length: {additional_stats['max_length']:,} bp")
            print(f"Mean length: {additional_stats['mean_length']:,.1f} bp")
            print(f"Median length: {additional_stats['median_length']:,} bp")
            
            # Show length distribution
            print(f"\nLength Distribution:")
            ranges = [(0, 1000), (1000, 5000), (5000, 10000), (10000, 50000), (50000, float('inf'))]
            for min_len, max_len in ranges:
                if max_len == float('inf'):
                    count = sum(1 for l in contig_lengths if l >= min_len)
                    print(f"  >= {min_len:,} bp: {count:,} contigs")
                else:
                    count = sum(1 for l in contig_lengths if min_len <= l < max_len)
                    print(f"  {min_len:,}-{max_len:,} bp: {count:,} contigs")
        
    except FileNotFoundError:
        print(f"Error: File '{args.fasta_file}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
