mode    moleculo

simp
{
    ; enable advanced ec removal algo
    topology_simplif_enabled false 

    ; tip clipper:
    tc
    {
        ; rctc: tip_cov < rctc * not_tip_cov
        ; tc_lb: max_tip_length = max((min(k, read_length / 2) * tc_lb), read_length);
        condition               "{ tc_lb 2.5, cb 3, rctc 10000 } { tc_lb 4.5, mmm 2 }"
    }

    ; bulge remover:
    br
    {
        enabled true
        max_coverage            3
        max_relative_coverage       100000.     ; bulge_cov < this * not_bulge_cov
    }
    
    ; erroneous connections remover:
    ec
    {
       ; ec_lb: max_ec_length = k + ec_lb
       ; icb: iterative coverage bound
       ; condition               "{ ec_lb 30, icb 20.0 }"
       condition               "{ ec_lb 30, icb 3.1 }"
    }
    
    ; relative coverage erroneous component remover:
    rcc
    {
        enabled true
        coverage_gap    20.
        max_length_coeff    2.0
        max_length_with_tips_coeff   3.0
        max_vertex_cnt      30
        max_ec_length_coefficient   30
        max_coverage_coeff  5.0
    }
    
    ; complex bulge remover
    cbr
    {
        enabled true
        pics_enabled 0
        folder  complex_br_components 
        max_relative_length 5.
        max_length_difference   5
    }

    ; hidden ec remover
    her
    {
        enabled                     true
        uniqueness_length           1500
        unreliability_threshold     0.2
        relative_threshold          5     
    }

    init_clean
    {
        activation_cov  -1.
        ier
        {
            enabled                     false
        }

        tip_condition   ""
        ec_condition    ""
    }
}

pe {
params {
    normalize_weight        true

    overlap_removal {
        enabled true
        cut_all true
    }

    scaffolding_mode old

    ; extension selection
    extension_options
    {
        single_threshold           0.3
        weight_threshold           0.6
    }

    scaffolder {
        short_overlap     10
        use_la_gap_joiner false
    }
}
}

sc_cor
{
    scaffolds_file scaffolds.fasta
    output_unfilled true
    max_insert 100
    max_cut_length 50
}
