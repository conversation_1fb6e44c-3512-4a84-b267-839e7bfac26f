mode rna

preserve_raw_paired_index true
min_edge_length_for_is_count 500

calculate_coverage_for_each_lib true
strand_specificity {
    ss_enabled false
    antisense false
}

ss_coverage_splitter {
    enabled           true
    bin_size          50   
    min_edge_len      200
    min_edge_coverage 5
    min_flanking_coverage 2
    coverage_margin   5
}

pacbio_processor
{
    internal_length_cutoff 100
;align and traverse.
    compression_cutoff 0.6
    path_limit_stretching 1.3
    path_limit_pressing 0.7
    max_path_in_dijkstra 5000
    max_vertex_in_dijkstra 1000
    rna_filtering   true

;gap_closer
    long_seq_limit 100
    enable_gap_closing false
    enable_fl_gap_closing true
    pacbio_min_gap_quantity 2
    contigs_min_gap_quantity 1
    max_contigs_gap_length 10000
}

contig_output {
    scaffolds_name  transcripts
    ; none  --- do not output broken scaffolds | break_gaps --- break only by N steches | break_all --- break all with overlap < k
    output_broken_scaffolds     none
}

simp
{
    ;all topology based erroneous connection removers are off
    topology_simplif_enabled false

    tc
    {
        ; rctc: tip_cov < rctc * not_tip_cov
        ; tc_lb: max_tip_length = max((min(k, read_length / 2) * tc_lb), read_length);
        condition  "{ mmm 3 tc_lb 4, cb 100000, rctc 0.5 } { tc_lb 2, cb 1, rctc 10000 }"
    }

    dead_end
    {
        enabled true
        condition  "{ tc_lb 3.5, cb 2 }"
    }

    ; bulge remover:
    br
    {
        enabled true
        max_additive_length_coefficient 100
        max_coverage            1000000.0
        max_relative_coverage       100000.0 ; bulge_cov < this * not_bulge_cov
    }

    ; erroneous connections remover:
    ec
    { 
       ; ec_lb: max_ec_length = k + ec_lb
       ; icb: iterative coverage bound
       ; to_ec_lb: max_ec_length = 2*tip_length(to_ec_lb) - 1
       ; nbr: use not bulge erroneous connections remover 
       ; condition               "{ ec_lb 9, icb 40.0, nbr }"
       condition               "{ ec_lb 30, icb 200, rcec_cb 1.0 }"
    }

    ; relative coverage erroneous connections remover:
    rcec
    {
        rcec_lb 30
        rcec_cb 1.0
        enabled true
    }

    rcc
    {
        enabled true
        coverage_gap    20.
    }

    ; hidden ec remover
    her
    {
        ; TODO NB config also used in special rna mode version (always enabled)
        enabled                     false
        uniqueness_length           1500
        unreliability_threshold     0.2
        relative_threshold          5
    }

    ier
    {
        enabled                       true
        use_rl_for_max_length         true ; max_length will be taken max with read_length
        use_rl_for_max_length_any_cov false ; use_rl_for_max_length_any_cov will be taken max with read_length
        max_length                    80
        max_coverage                  2
        max_length_any_cov            0
        rl_threshold_increase         2 ; add this value to read length if used, i.e. flags above are set
    }

}

; disable filtering in rna mode
de
{
    raw_filter_threshold	0
}

pe {
debug_output    true

params {
    multi_path_extend   true

    scaffolding_mode old

    overlap_removal {
        enabled false
        end_start_only  true
        cut_all true
    }

    extension_options
    {
        single_threshold           0.05
    }

    scaffolder {
        cutoff        1
        hard_cutoff   5
        rel_cov_cutoff    0.1
        cluster_info false
        min_overlap_for_rna_scaffolding 8
    }

    path_cleaning_presets "default soft hard"
    ; All length cutoffs presented in nucleotides
    ; So edges less than or equal to (relative cutoff * RL - K) or (absolute cutoff - K) will be deleted
    path_cleaning
    {
        enabled true
        min_length  110
        isolated_min_length 130
        isolated_min_cov 4
        min_length_for_low_covered 140
        rel_cutoff 1.3
        rel_isolated_cutoff 1.5
        rel_low_covered_cutoff 1.6
        min_coverage 2
    }

    ; All length cutoffs presented in nucleotides
    hard_path_cleaning
    {
        enabled true
        min_length  130
        isolated_min_length 180
        isolated_min_cov 8
        min_length_for_low_covered 180
        rel_cutoff 1.5
        rel_isolated_cutoff 2.0
        rel_low_covered_cutoff 2.0
        min_coverage 3
    }

    ; All length cutoffs presented in nucleotides
    soft_path_cleaning
    {
        enabled true
        min_length  85
        isolated_min_length 100
        isolated_min_cov 2
        min_length_for_low_covered 130
        rel_cutoff 1.05
        rel_isolated_cutoff 1.2
        rel_low_covered_cutoff 1.5
        min_coverage 1
    }

    use_coordinated_coverage false
    coordinated_coverage {
       max_edge_length_repeat 1000
       delta                  0.5
       min_path_len           300
    }

    simple_coverage_resolver {
        enabled true
        coverage_margin 2  
        min_upper_coverage 2
        max_coverage_variation 10
    }
}
}
