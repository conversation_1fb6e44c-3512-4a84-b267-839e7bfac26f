Command line: /usr/local/bin/spades.py	-s	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507996.anqdpht.fastq.gz	--sc	-o	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996	-t	16	-m	128	

System information:
  SPAdes version: 3.15.5
  Python version: 3.10.6
  OS: Linux-4.18.0-553.5.1.el8_10.x86_64-x86_64-with-glibc2.28

Output dir: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996
Mode: read error correction and assembling
Debug mode is turned OFF

Dataset parameters:
  Single-cell mode
  Reads:
    Library number: 1, library type: single
      left reads: not specified
      right reads: not specified
      interlaced reads: not specified
      single reads: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507996.anqdpht.fastq.gz']
      merged reads: not specified
Read error correction parameters:
  Iterations: 1
  PHRED offset will be auto-detected
  Corrected reads will be compressed
Assembly parameters:
  k: [21, 33, 55]
  Repeat resolution is enabled
  Mismatch careful mode is turned OFF
  MismatchCorrector will be SKIPPED
  Coverage cutoff is turned OFF
Other parameters:
  Dir for temp files: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/tmp
  Threads: 16
  Memory limit (in Gb): 128


======= SPAdes pipeline started. Log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/spades.log

/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507996.anqdpht.fastq.gz: max reads length: 150

Reads length: 150


===== Before start started. 


===== Read error correction started. 


===== Read error correction started. 


== Running: /usr/local/bin/spades-hammer /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/corrected/configs/config.info

  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  75)   Starting BayesHammer, built from N/A, git revision N/A
  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  76)   Loading config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/corrected/configs/config.info
  0:00:00.004     1M / 33M   INFO    General                 (main.cpp                  :  78)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.004     1M / 33M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.004     1M / 33M   INFO    General                 (main.cpp                  :  86)   Trying to determine PHRED offset
  0:00:00.004     1M / 33M   INFO    General                 (main.cpp                  :  92)   Determined value is 33
  0:00:00.004     1M / 33M   INFO    General                 (hammer_tools.cpp          :  38)   Hamming graph threshold tau=1, k=21, subkmer positions = [ 0 10 ]
  0:00:00.004     1M / 33M   INFO    General                 (main.cpp                  : 113)   Size of aux. kmer data 24 bytes
     === ITERATION 0 begins ===
  0:00:00.010     1M / 33M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 16 files using 16 threads. This might take a while.
  0:00:00.014     1M / 33M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.014     1M / 33M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66666 Gb
  0:00:00.014     1M / 33M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 4194304
  0:00:00.054  9217M / 9217M INFO   K-mer Splitting          (kmer_data.cpp             :  97)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507996.anqdpht.fastq.gz
  0:00:11.931  9217M / 14G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 3560539 reads
  0:00:24.674  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 7202194 reads
  0:00:39.859  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 10878365 reads
  0:00:55.578  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 14510461 reads
  0:01:10.925  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 18095880 reads
  0:01:25.050  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 20320346 reads
  0:01:25.050  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 112)   Total 20320346 reads processed
  0:01:25.051     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:36.785     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 279932700 kmers in total.
  0:01:36.804     1M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:44.098   211M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 279932700 kmers, 202197192 bytes occupied (5.77845 bits per kmer).
  0:01:44.099   211M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 354)   Arranging kmers in hash map order
  0:01:54.946  4483M / 15G   INFO    General                 (main.cpp                  : 148)   Clustering Hamming graph.
  0:03:53.196  4483M / 15G   INFO    General                 (main.cpp                  : 155)   Extracting clusters:
  0:03:53.196  4483M / 15G   INFO    General                 (concurrent_dsu.cpp        :  18)   Connecting to root
  0:03:53.603  4483M / 15G   INFO    General                 (concurrent_dsu.cpp        :  34)   Calculating counts
  0:04:15.621  8835M / 15G   INFO    General                 (concurrent_dsu.cpp        :  63)   Writing down entries
  0:04:46.703  4483M / 15G   INFO    General                 (main.cpp                  : 167)   Clustering done. Total clusters: 138186214
  0:04:46.707  2347M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 371)   Collecting K-mer information, this takes a while.
  0:04:47.330  8755M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 377)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507996.anqdpht.fastq.gz
  0:06:23.158  8755M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 384)   Collection done, postprocessing.
  0:06:23.551  8755M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 397)   There are 279932700 kmers in total. Among them 165293678 (59.0476%) are singletons.
  0:06:23.551  8755M / 15G   INFO    General                 (main.cpp                  : 173)   Subclustering Hamming graph
  0:09:01.075  8756M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 650)   Subclustering done. Total 8589 non-read kmers were generated.
  0:09:01.076  8756M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 651)   Subclustering statistics:
  0:09:01.076  8756M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 652)     Total singleton hamming clusters: 91413170. Among them 42431502 (46.4173%) are good
  0:09:01.076  8756M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 653)     Total singleton subclusters: 5093344. Among them 5086199 (99.8597%) are good
  0:09:01.076  8756M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 654)     Total non-singleton subcluster centers: 49820895. Among them 36645251 (73.554%) are good
  0:09:01.076  8756M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 655)     Average size of non-trivial subcluster: 3.78451 kmers
  0:09:01.076  8756M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 656)     Average number of sub-clusters per non-singleton cluster: 1.17406
  0:09:01.076  8756M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 657)     Total solid k-mers: 84162952
  0:09:01.076  8756M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 658)     Substitution probabilities: [4,4]((0.932636,0.0306298,0.0313714,0.0053626),(0.0257796,0.936255,0.00877464,0.0291907),(0.0283083,0.00851802,0.936648,0.0265259),(0.00518842,0.0315488,0.0305046,0.932758))
  0:09:01.218  8756M / 15G   INFO    General                 (main.cpp                  : 178)   Finished clustering.
  0:09:01.218  8756M / 15G   INFO    General                 (main.cpp                  : 197)   Starting solid k-mers expansion in 16 threads.
  0:09:46.691  8756M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 0 produced 11760120 new k-mers.
  0:10:32.049  8756M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 1 produced 858399 new k-mers.
  0:11:17.331  8756M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 2 produced 46782 new k-mers.
  0:12:02.694  8756M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 3 produced 4051 new k-mers.
  0:12:48.124  8756M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 4 produced 667 new k-mers.
  0:13:33.442  8756M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 5 produced 130 new k-mers.
  0:14:18.757  8756M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 6 produced 0 new k-mers.
  0:14:18.758  8756M / 15G   INFO    General                 (main.cpp                  : 222)   Solid k-mers finalized
  0:14:18.758  8756M / 15G   INFO    General                 (hammer_tools.cpp          : 222)   Starting read correction in 16 threads.
  0:14:18.758  8756M / 15G   INFO    General                 (hammer_tools.cpp          : 266)   Correcting single reads: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507996.anqdpht.fastq.gz
  0:14:21.320  9533M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 0 of 1600000 reads.
  0:14:24.392  9655M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 0
  0:14:25.575  9655M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 0
  0:14:27.775  9685M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 1 of 1600000 reads.
  0:14:29.884  9685M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 1
  0:14:31.032  9685M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 1
  0:14:33.209  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 2 of 1600000 reads.
  0:14:35.393  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 2
  0:14:36.559  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 2
  0:14:38.744  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 3 of 1600000 reads.
  0:14:41.717  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 3
  0:14:42.880  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 3
  0:14:45.167  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 4 of 1600000 reads.
  0:14:50.415  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 4
  0:14:51.565  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 4
  0:14:53.854  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 5 of 1600000 reads.
  0:14:56.376  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 5
  0:14:57.536  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 5
  0:14:59.838  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 6 of 1600000 reads.
  0:15:02.488  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 6
  0:15:03.632  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 6
  0:15:05.931  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 7 of 1600000 reads.
  0:15:08.238  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 7
  0:15:09.386  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 7
  0:15:11.685  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 8 of 1600000 reads.
  0:15:14.001  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 8
  0:15:15.244  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 8
  0:15:17.544  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 9 of 1600000 reads.
  0:15:20.261  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 9
  0:15:21.489  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 9
  0:15:23.784  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 10 of 1600000 reads.
  0:15:26.825  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 10
  0:15:27.976  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 10
  0:15:30.309  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 11 of 1600000 reads.
  0:15:38.285  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 11
  0:15:39.439  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 11
  0:15:41.237  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 12 of 1120346 reads.
  0:15:55.431  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 12
  0:15:56.233  9687M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 12
  0:16:06.044  8756M / 15G   INFO    General                 (hammer_tools.cpp          : 276)   Correction done. Changed 5868376 bases in 2607086 reads.
  0:16:06.051  8756M / 15G   INFO    General                 (hammer_tools.cpp          : 277)   Failed to correct 0 bases out of 2994253362.
  0:16:06.063     1M / 15G   INFO    General                 (main.cpp                  : 255)   Saving corrected dataset description to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/corrected/corrected.yaml
  0:16:06.140     1M / 15G   INFO    General                 (main.cpp                  : 262)   All done. Exiting.

===== Read error correction finished. 


===== corrected reads compression started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/compress_all.py --input_file /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/corrected/corrected.yaml --ext_python_modules_home /usr/local/share/spades --max_threads 16 --output_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/corrected --gzip_output

== Compressing corrected reads (with gzip)
== Files to compress: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/corrected/1507996.anqdpht.fastq.00.0_0.cor.fastq']
== Files compression is finished
== Dataset yaml file is updated

===== corrected reads compression finished. 


===== Read error correction finished. 


===== Assembling started. 


===== K21 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K21/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K21/configs/mda_mode.info

  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K21/configs/config.info
  0:00:00.002     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K21/configs/mda_mode.info
  0:00:00.002     1M / 36M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.002     1M / 36M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.002     1M / 36M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.002     1M / 36M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/dataset.info) with K=21
  0:00:00.002     1M / 36M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.002     1M / 36M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.002     1M / 36M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.002     1M / 36M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.003     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.004     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.006     1M / 36M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.007     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.015     1M / 36M   INFO    General                 (read_converter.cpp        :  72)   Converting reads to binary format for library #0 (takes a while)
  0:00:00.015     1M / 36M   INFO    General                 (read_converter.cpp        :  73)   Converting paired reads
  0:00:00.024    23M / 36M   INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:00.024     1M / 36M   INFO    General                 (read_converter.cpp        :  86)   Converting single reads
  0:00:00.286    33M / 44M   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:00:00.299    36M / 50M   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:00:00.318    41M / 62M   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:00:00.441    35M / 86M   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:00:00.600    40M / 109M  INFO    General                 (binary_converter.cpp      :  96)   262144 reads processed
  0:00:01.011    34M / 142M  INFO    General                 (binary_converter.cpp      :  96)   524288 reads processed
  0:00:01.720    38M / 207M  INFO    General                 (binary_converter.cpp      :  96)   1048576 reads processed
  0:00:03.225    45M / 245M  INFO    General                 (binary_converter.cpp      :  96)   2097152 reads processed
  0:00:06.419    45M / 322M  INFO    General                 (binary_converter.cpp      :  96)   4194304 reads processed
  0:00:12.682    44M / 339M  INFO    General                 (binary_converter.cpp      :  96)   8388608 reads processed
  0:00:25.706    42M / 355M  INFO    General                 (binary_converter.cpp      :  96)   16777216 reads processed
  0:00:31.821    31M / 367M  INFO    General                 (binary_converter.cpp      : 111)   20295612 reads written
  0:00:31.822    21M / 367M  INFO    General                 (read_converter.cpp        :  92)   Converting merged reads
  0:00:31.848    31M / 367M  INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:32.442     1M / 367M  INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:32.634     1M / 367M  INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:32.635     1M / 367M  INFO    General                 (construction.cpp          : 159)   Average read length 147.397
  0:00:32.635     1M / 367M  INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:32.635     1M / 367M  INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:32.652     1M / 367M  INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:32.652     1M / 367M  INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:32.652     1M / 367M  INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:00:39.193  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 10771730 reads
  0:00:44.209  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 21703192 reads
  0:00:56.119  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 32484958 reads
  0:01:01.329  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 38606913 reads
  0:01:07.620  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 39779222 reads
  0:01:11.624  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 40247589 reads
  0:01:14.085  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 40591224 reads
  0:01:14.088     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 40591224 reads
  0:01:14.101     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:18.959     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 77639075 kmers in total.
  0:01:18.970     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:01:19.498     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:01:19.503     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:01:19.526     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:01:19.537     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:01:19.537     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:01:30.763  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 77639075 kmers
  0:01:30.764  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 77639075 kmers.
  0:01:30.765     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:35.870     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 75759208 kmers in total.
  0:01:35.880     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:37.366    59M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 75759208 kmers, 54836672 bytes occupied (5.79063 bits per kmer).
  0:01:37.374    59M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:40.328   136M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:42.848   136M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:42.896   135M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:01:42.896   135M / 11G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:01:42.896   135M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:01:45.504   213M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 669906
  0:01:45.571   213M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 677640
  0:01:45.594   135M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   10466088 22-mers were removed by early tip clipper
  0:01:45.599   135M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:45.802   135M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:48.713   317M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 4827534 sequences extracted
  0:01:50.014   317M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:50.621   317M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 4 loops collected
  0:01:50.747   317M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 9655076 edges to create
  0:01:50.748   691M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:54.166   839M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:54.224   839M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:54.251   871M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 2947671 vertices to create
  0:01:54.251  1144M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:56.823   890M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:56.824   890M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:57.394   948M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 77639075 kmers, 56195888 bytes occupied (5.79047 bits per kmer).
  0:01:57.421  1249M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:02:17.823  1249M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:02:20.026  1249M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 9653728 edges
  0:02:21.032   832M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:02:22.151   833M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 16
  0:02:22.151   833M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 38.0107
  0:02:22.151   833M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 417
  0:02:22.169   832M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 38.0107
  0:02:22.170   832M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:02:22.170   832M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:02:22.171   832M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:02:22.172   832M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:02:22.172   832M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:02:22.233   832M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 0 times
  0:02:22.233   832M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:02:22.233   832M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:02:22.233   832M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:22.233   832M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:02:22.233   832M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:22.405   832M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 17150 times
  0:02:22.406   832M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.231   858M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 220208 times
  0:02:41.238   858M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:48.542   953M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1207246 times
  0:02:48.557   953M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:02:48.557   953M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:48.902   892M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 9050 times
  0:02:48.903   892M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:54.400   851M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 61550 times
  0:02:54.410   851M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:55.177   849M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 130376 times
  0:02:55.188   849M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:02:55.188   849M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:55.277   845M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 112 times
  0:02:55.277   845M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:56.692   830M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 14063 times
  0:02:56.695   830M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:56.952   829M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 43076 times
  0:02:56.958   829M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:02:56.958   829M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:56.987   827M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 18 times
  0:02:56.987   827M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:57.585   824M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 4199 times
  0:02:57.586   824M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:57.735   825M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 25928 times
  0:02:57.738   825M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:02:57.738   825M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:57.754   824M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 12 times
  0:02:57.754   824M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:58.551   821M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2025 times
  0:02:58.551   821M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:58.655   822M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 18261 times
  0:02:58.657   822M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:02:58.657   822M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:58.668   821M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:58.668   821M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:59.283   820M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1178 times
  0:02:59.283   820M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:59.360   820M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 12329 times
  0:02:59.361   820M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:02:59.361   820M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:59.368   820M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:59.368   820M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:59.946   819M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 717 times
  0:02:59.947   819M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:00.011   819M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 10390 times
  0:03:00.012   819M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:03:00.012   819M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:00.017   819M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:00.017   819M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:00.549   818M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 504 times
  0:03:00.549   818M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:00.602   818M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 8113 times
  0:03:00.603   818M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:03:00.603   818M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:00.606   818M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 4 times
  0:03:00.606   818M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:01.002   818M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 327 times
  0:03:01.003   818M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:01.049   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 6521 times
  0:03:01.050   813M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:03:01.050   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:01.053   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 4 times
  0:03:01.053   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:01.350   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 298 times
  0:03:01.350   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:01.383   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 5568 times
  0:03:01.384   813M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:03:01.384   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:01.397   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 104 times
  0:03:01.397   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:01.957   814M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 602 times
  0:03:01.957   814M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:01.970   745M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2 times
  0:03:01.970   745M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:03:01.970   745M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:01.971   745M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:01.971   745M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:01.971   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1 times
  0:03:01.972   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:01.972   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:03:01.972   741M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 13
  0:03:01.972   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:01.972   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:01.972   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:01.972   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:01.972   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:01.972   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:03:01.990   739M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:03:01.991   739M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:03:01.991   739M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:03:01.991   739M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:03:01.991   739M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:03:01.991   739M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:03:01.991   739M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:02.149   738M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 3601 times
  0:03:02.150   738M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:02.500   738M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 570 times
  0:03:02.501   738M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:02.511   738M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:02.512   738M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:02.523   738M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 19 times
  0:03:02.523   738M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:02.989   740M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 38 times
  0:03:02.989   740M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:03.634   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1096 times
  0:03:03.635   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:03.725   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 38 times
  0:03:03.725   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:04.039   739M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 7 times
  0:03:04.039   739M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:04.050   739M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:03:04.050   739M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:04.061   739M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:04.061   739M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:04.515   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 5 times
  0:03:04.516   741M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:05.139   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 2 times
  0:03:05.139   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:05.269   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:03:05.270   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:05.585   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:03:05.586   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:05.586   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:03:05.586   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:05.586   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:05.586   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:05.586   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:05.586   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:05.586   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:03:05.586   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:05.720   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:03:05.720   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:06.035   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:03:06.036   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:06.036   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:06.036   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:06.036   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:06.036   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:06.036   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:06.036   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:06.037   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:03:06.037   743M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:03:06.097   743M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:03:06.204   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:03:06.216   743M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 1981 times
  0:03:06.236   738M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:03:06.236   738M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 63.4541
  0:03:06.253   738M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 39274814
  0:03:06.280   738M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 561
  0:03:06.281   738M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 529494
  0:03:06.281   738M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 324402
  0:03:06.281   738M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:03:06.284   738M / 11G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K21/simplified_contigs
  0:03:06.295   741M / 11G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:03:06.299   741M / 11G   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:03:06.317   741M / 11G   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:03:06.331   741M / 11G   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:03:06.360   741M / 11G   INFO    General                 (binary_converter.cpp      :  96)   262144 reads processed
  0:03:06.370   741M / 11G   INFO    General                 (binary_converter.cpp      : 111)   264861 reads written
  0:03:06.443   738M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:03:06.577     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 3 minutes 6 seconds

===== K21 finished. 


===== K33 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K33/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K33/configs/mda_mode.info

  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K33/configs/config.info
  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K33/configs/mda_mode.info
  0:00:00.000     1M / 36M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/dataset.info) with K=33
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 36M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.004     1M / 36M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.005     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.005     1M / 36M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K21/simplified_contigs
  0:00:00.092     1M / 36M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.092     1M / 36M   INFO    General                 (construction.cpp          : 159)   Average read length 147.397
  0:00:00.092     1M / 36M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.092     1M / 36M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.097     1M / 36M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.097     1M / 36M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.097     1M / 36M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:08.861  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 5832555 reads
  0:00:14.709  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 12132964 reads
  0:00:20.187  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 18278121 reads
  0:00:25.614  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 24248953 reads
  0:00:31.586  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 30378490 reads
  0:00:38.914  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 36290732 reads
  0:00:45.276  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 38940383 reads
  0:00:48.366  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 39754303 reads
  0:00:50.779  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 40161515 reads
  0:00:52.539  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 40483964 reads
  0:00:54.476  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 40956066 reads
  0:00:56.655     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 41120946 reads
  0:00:56.669     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:02.005     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 96647342 kmers in total.
  0:01:02.005     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:01:02.492     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:01:02.492     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:01:02.508     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:01:02.508     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:01:02.508     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:01:14.692  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 70166177 kmers
  0:01:20.106  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 96647390 kmers
  0:01:20.106  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 96647390 kmers.
  0:01:20.107     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:25.298     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 95275831 kmers in total.
  0:01:25.308     2M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:27.660    76M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 95275831 kmers, 68932616 bytes occupied (5.78805 bits per kmer).
  0:01:27.660    76M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:31.338   168M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:34.836   168M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:34.953   168M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:01:34.953   168M / 11G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:01:34.953   168M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:01:38.336   247M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 946872
  0:01:38.375   247M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 959998
  0:01:38.411   168M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   17920943 34-mers were removed by early tip clipper
  0:01:38.411   168M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:38.573   168M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:42.174   351M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 3874439 sequences extracted
  0:01:43.883   351M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:44.586   351M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 5 loops collected
  0:01:44.698   351M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 7748888 edges to create
  0:01:44.698   652M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:47.434   772M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:47.481   772M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:47.497   804M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 2502933 vertices to create
  0:01:47.497  1037M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:49.677   826M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:49.677   826M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:50.437   901M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 96647342 kmers, 69922976 bytes occupied (5.78789 bits per kmer).
  0:01:50.472  1273M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:02:10.026  1273M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:02:13.385  1273M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 7748119 edges
  0:02:15.084   719M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:02:15.921   720M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 13
  0:02:15.933   720M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 29.2229
  0:02:15.938   720M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 339
  0:02:15.960   719M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 29.2229
  0:02:15.965   719M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:02:15.976   719M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:02:15.987   719M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:02:15.997   719M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:02:16.008   719M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:02:16.074   719M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 68 times
  0:02:16.084   719M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:02:16.094   719M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:02:16.105   719M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:16.116   719M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:02:16.127   719M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:16.370   718M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 39981 times
  0:02:16.376   718M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:25.796   723M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 197286 times
  0:02:25.801   723M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:31.557   758M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 877240 times
  0:02:31.587   758M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:02:31.598   758M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:31.904   740M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 11272 times
  0:02:31.909   740M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:35.967   690M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 53406 times
  0:02:35.976   690M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:36.874   678M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 126876 times
  0:02:36.887   678M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:02:36.887   678M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:36.953   669M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 203 times
  0:02:36.953   669M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:38.369   665M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 17874 times
  0:02:38.372   665M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:38.576   663M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 27760 times
  0:02:38.581   663M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:02:38.581   663M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:38.606   660M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 29 times
  0:02:38.606   660M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:39.240   657M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 4637 times
  0:02:39.241   657M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:39.334   658M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 13098 times
  0:02:39.336   658M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:02:39.336   658M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:39.348   656M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 12 times
  0:02:39.349   656M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:39.731   654M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1836 times
  0:02:39.732   654M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:39.792   654M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 8212 times
  0:02:39.793   654M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:02:39.793   654M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:39.801   654M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 9 times
  0:02:39.801   654M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.040   653M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 966 times
  0:02:40.040   653M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:40.080   652M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 5516 times
  0:02:40.081   652M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:02:40.081   652M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:40.086   652M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5 times
  0:02:40.086   652M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.222   652M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 538 times
  0:02:40.222   652M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:40.254   652M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 4207 times
  0:02:40.254   652M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:02:40.254   652M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:40.257   652M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 4 times
  0:02:40.257   652M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.352   651M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 388 times
  0:02:40.352   651M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:40.378   651M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3217 times
  0:02:40.378   651M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:02:40.378   651M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:40.380   651M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3 times
  0:02:40.380   651M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.446   651M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 238 times
  0:02:40.446   651M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:40.470   650M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2571 times
  0:02:40.470   650M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:02:40.471   650M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:40.472   650M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3 times
  0:02:40.472   650M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.519   649M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 153 times
  0:02:40.519   649M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:40.534   650M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2030 times
  0:02:40.535   650M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:02:40.535   650M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:40.544   649M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 124 times
  0:02:40.544   649M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.750   650M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 337 times
  0:02:40.750   650M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:40.758   614M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:02:40.758   614M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:02:40.758   614M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:40.758   614M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:40.758   614M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.758   614M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:40.758   614M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:40.758   614M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:02:40.772   610M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:02:40.772   610M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:02:40.772   610M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:02:40.772   610M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:02:40.772   610M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:40.772   610M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:40.773   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:40.857   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 2870 times
  0:02:40.858   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:41.137   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 1084 times
  0:02:41.137   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.144   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:02:41.144   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:41.151   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 23 times
  0:02:41.151   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.353   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 29 times
  0:02:41.354   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:41.580   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 838 times
  0:02:41.580   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:41.620   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 25 times
  0:02:41.620   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:41.858   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 11 times
  0:02:41.858   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.865   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:41.865   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:41.871   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:41.871   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:42.125   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2 times
  0:02:42.125   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:42.383   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 2 times
  0:02:42.383   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:42.446   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:02:42.446   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:42.677   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 1 times
  0:02:42.677   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:42.677   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:42.677   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:42.677   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:42.677   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:42.678   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1 times
  0:02:42.678   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:42.678   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1 times
  0:02:42.678   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:42.739   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:02:42.740   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:42.967   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:02:42.968   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:42.968   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:42.968   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:42.968   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:42.968   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:42.968   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:42.968   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:42.968   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:02:42.968   609M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:02:43.048   610M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:02:43.099   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:02:43.122   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 19562 times
  0:02:43.132   608M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:02:43.133   608M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 54.1395
  0:02:43.140   608M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 41317876
  0:02:43.153   608M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 1673
  0:02:43.153   608M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 241708
  0:02:43.153   608M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 170866
  0:02:43.153   608M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:02:43.162   608M / 11G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K33/simplified_contigs
  0:02:43.181   611M / 11G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:02:43.184   611M / 11G   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:02:43.193   611M / 11G   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:02:43.227   611M / 11G   INFO    General                 (binary_converter.cpp      : 111)   120878 reads written
  0:02:43.287   608M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:02:43.351     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 2 minutes 43 seconds

===== K33 finished. 


===== K55 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/configs/mda_mode.info

  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/configs/config.info
  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/configs/mda_mode.info
  0:00:00.000     1M / 36M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/dataset.info) with K=55
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 36M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 242)   Will need read mapping, kmer mapper will be attached
  0:00:00.001     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.002     1M / 36M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.003     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.003     1M / 36M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K33/simplified_contigs
  0:00:00.139     1M / 36M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.139     1M / 36M   INFO    General                 (construction.cpp          : 159)   Average read length 147.397
  0:00:00.139     1M / 36M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.140     1M / 36M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.144     1M / 36M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.144     1M / 36M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.144     1M / 36M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:07.890  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 7573603 reads
  0:00:15.182  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 15494947 reads
  0:00:21.905  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 23384757 reads
  0:00:27.587  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 31047085 reads
  0:00:33.598  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 37726613 reads
  0:00:38.517  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 39495740 reads
  0:00:41.493  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 39996360 reads
  0:00:43.710  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 40443046 reads
  0:00:48.646  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 40832980 reads
  0:00:48.685     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 40832980 reads
  0:00:48.698     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:56.913     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 118209065 kmers in total.
  0:00:56.929     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:57.450     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:57.456     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:57.483     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:57.504     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:57.510     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:01:04.075  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 16202126 kmers
  0:01:08.476  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 35376789 kmers
  0:01:12.635  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 52029267 kmers
  0:01:17.501  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 70869367 kmers
  0:01:23.985  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 87538655 kmers
  0:01:31.756  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 106308266 kmers
  0:01:35.473  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 118209593 kmers
  0:01:35.473  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 118209593 kmers.
  0:01:35.474     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:42.776     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 117554246 kmers in total.
  0:01:42.788     2M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:46.718    88M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 117554246 kmers, 85024320 bytes occupied (5.78622 bits per kmer).
  0:01:46.718    88M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:53.424   204M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:57.828   204M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:57.976   204M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:58.886   204M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:02:04.311   448M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 4910653 sequences extracted
  0:02:06.727   448M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:02:07.589   448M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 0 loops collected
  0:02:07.760   448M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 9821306 edges to create
  0:02:07.761   829M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:02:11.184   982M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:02:11.246   982M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:02:11.284  1046M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 4255834 vertices to create
  0:02:11.284  1439M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:02:14.959  1148M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:02:14.959  1148M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:02:15.931  1234M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 118209065 kmers, 85496736 bytes occupied (5.78614 bits per kmer).
  0:02:15.972  1686M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:02:32.676  1686M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:02:36.990  1687M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 9821000 edges
  0:02:38.452   989M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:02:39.147   990M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 9
  0:02:39.147   990M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 15.8654
  0:02:39.148   990M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 231
  0:02:39.154   989M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 15.8654
  0:02:39.154   989M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:02:39.154   989M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: early_gapcloser)
  0:02:39.154   989M / 11G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:02:39.155   989M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:02:39.155   989M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:02:39.155   989M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:02:39.155   989M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:02:39.225   989M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 28 times
  0:02:39.226   989M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:02:39.226   989M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:02:39.226   989M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:39.226   989M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:02:39.226   989M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:44.333   907M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1418127 times
  0:02:44.358   907M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:01.540  1691M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 128920 times
  0:03:01.544  1691M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:04.087  1709M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 346310 times
  0:03:04.095  1709M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:03:04.096  1709M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:04.392  1689M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 21615 times
  0:03:04.396  1689M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:12.527  1863M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 31772 times
  0:03:12.529  1863M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:13.470  1853M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 122098 times
  0:03:13.477  1853M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:03:13.478  1853M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:13.552  1848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2082 times
  0:03:13.554  1848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:18.658  1968M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 20637 times
  0:03:18.660  1968M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:18.902  1967M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 27058 times
  0:03:18.905  1967M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:03:18.905  1967M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:18.933  1962M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 188 times
  0:03:18.934  1962M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:20.260  1994M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 5296 times
  0:03:20.261  1994M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:20.391  1994M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 13596 times
  0:03:20.393  1994M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:03:20.394  1994M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:20.409  1993M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 66 times
  0:03:20.410  1993M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:21.054  2007M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2616 times
  0:03:21.056  2007M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:21.116  2006M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 6527 times
  0:03:21.117  2006M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:03:21.118  2006M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:21.126  2006M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 20 times
  0:03:21.127  2006M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:21.399  2012M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1203 times
  0:03:21.400  2012M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:21.437  2012M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 4199 times
  0:03:21.438  2012M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:03:21.439  2012M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:21.444  2011M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 13 times
  0:03:21.445  2011M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:21.592  2015M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 652 times
  0:03:21.593  2015M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:21.619  2015M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2879 times
  0:03:21.621  2015M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:03:21.621  2015M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:21.626  2015M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 4 times
  0:03:21.627  2015M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:21.728  2016M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 425 times
  0:03:21.729  2016M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:21.748  2016M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2071 times
  0:03:21.749  2016M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:03:21.750  2016M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:21.753  2016M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 8 times
  0:03:21.754  2016M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:21.811  2018M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 255 times
  0:03:21.812  2018M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:21.828  2018M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1653 times
  0:03:21.829  2018M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:03:21.830  2018M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:21.832  2017M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 7 times
  0:03:21.833  2017M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:21.880  2019M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 198 times
  0:03:21.881  2019M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:21.893  2019M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1264 times
  0:03:21.894  2019M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:03:21.895  2019M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:21.918  2018M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 731 times
  0:03:21.920  2018M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:22.037  2020M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 250 times
  0:03:22.038  2020M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:22.052  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:03:22.053  2003M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:03:22.054  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:22.055  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:03:22.056  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:22.057  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1 times
  0:03:22.058  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:22.059  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:03:22.060  2003M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 13
  0:03:22.061  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:22.061  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:22.062  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:22.063  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:22.064  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:22.065  2003M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:03:22.095  2000M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: late_gapcloser)
  0:03:22.096  2000M / 11G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:03:22.097  2000M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:03:22.098  2000M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:03:22.099  2000M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:03:22.099  2000M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:03:22.100  2000M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:03:22.101  2000M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:03:22.102  2000M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:22.103  2000M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:22.103  2000M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:22.334  2000M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:22.545  2000M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:29.314  1999M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:29.580  1999M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:29.582  1999M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:29.598  1999M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 13 times
  0:03:29.599  1999M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:29.647  1999M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1617 times
  0:03:29.649  1999M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:31.138  2050M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 2650 times
  0:03:31.139  2050M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:31.154  2050M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 6 times
  0:03:31.156  2050M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:31.170  2050M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 50 times
  0:03:31.172  2050M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:31.251  2051M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 42 times
  0:03:31.252  2051M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:31.514  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 720 times
  0:03:31.516  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:31.516  2059M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:31.517  2059M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:31.709  2059M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:31.891  2059M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:36.133  2059M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:36.362  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:36.364  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:36.379  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 1 times
  0:03:36.380  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:36.407  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 11 times
  0:03:36.409  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:36.868  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 6 times
  0:03:36.870  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:36.883  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:36.884  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:36.898  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:36.899  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:36.972  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 3 times
  0:03:36.974  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:37.047  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 7 times
  0:03:37.048  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:37.049  2059M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:37.050  2059M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:37.236  2059M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:37.419  2059M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:41.342  2059M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:41.577  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:41.579  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:41.593  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:03:41.594  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:41.624  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1 times
  0:03:41.625  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:42.086  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 1 times
  0:03:42.087  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:42.088  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:42.089  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:42.090  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:42.091  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:42.092  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:42.093  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:42.094  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1 times
  0:03:42.095  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:42.096  2059M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:42.097  2059M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:42.283  2059M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:42.466  2059M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:46.365  2059M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:46.609  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:46.611  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:46.625  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:03:46.626  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:46.658  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:03:46.659  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:47.102  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:03:47.104  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:47.105  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:47.106  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:47.107  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:47.108  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:47.109  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:47.109  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:47.110  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:03:47.111  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:47.112  2059M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:47.113  2059M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:47.299  2059M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:47.478  2059M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:51.397  2059M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:51.638  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 0 times
  0:03:51.640  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:51.654  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:03:51.656  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:51.687  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:03:51.688  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:52.144  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:03:52.146  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:52.147  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:52.148  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:52.149  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:52.150  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:52.150  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:52.151  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:52.152  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:03:52.153  2059M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:03:52.226  2059M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:03:52.314  2059M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:03:52.434  2056M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 91200 times
  0:03:52.447  2055M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:03:52.448  2055M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 41.5275
  0:03:52.458  2055M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 42593880
  0:03:52.473  2055M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 2755
  0:03:52.474  2055M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 148100
  0:03:52.475  2055M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 117404
  0:03:52.476  2055M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Mismatch Correction (id: mismatch_correction)
  0:03:52.477  2055M / 11G   INFO    General                 (graph_pack.cpp            :  67)   Index refill
  0:03:52.478  2055M / 11G   INFO    General                 (edge_index.hpp            : 156)   Using small index (max_id = 9870415)
  0:03:52.561  2055M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:03:53.354  2085M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 42592951 kmers, 30764088 bytes occupied (5.77825 bits per kmer).
  0:03:53.385  2413M / 11G   INFO    General                 (edge_index_builders.hpp   : 252)   Collecting edge information from graph, this takes a while.
  0:03:53.914  2413M / 11G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 25744428 kmers to process
  0:03:57.592  2413M / 11G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:03:57.594  2413M / 11G   INFO    General                 (mismatch_correction.cpp   : 400)   Collect potential mismatches
  0:04:00.102  2430M / 11G   INFO    General                 (mismatch_correction.cpp   : 192)   Total 75700 edges (out of 148100) with 2535700 potential mismatch positions (33.4967 positions per edge)
  0:04:00.109  2430M / 11G   INFO    General                 (mismatch_correction.cpp   : 402)   Potential mismatches collected
  0:04:01.397  2432M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:04:01.477  2434M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:04:01.537  2436M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:04:01.582  2438M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:04:01.605  2441M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:04:01.669  2442M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:04:01.901  2445M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:04:02.936  2448M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:04:05.064  2457M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8400000 reads
  0:04:08.734  2462M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 16800000 reads
  0:04:18.272  2482M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 33611000 reads
  0:04:37.142  3047M / 11G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 40591224 reads processed
  0:04:39.343  2614M / 11G   INFO    General                 (mismatch_correction.cpp   : 395)   All edges processed
  0:04:39.368  2416M / 11G   INFO    General                 (mismatch_correction.cpp   : 450)   Corrected 799 nucleotides
  0:04:39.370  2416M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:04:39.371  2416M / 11G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/before_rr.fasta
  0:04:39.847  2416M / 11G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/assembly_graph_after_simplification.gfa
  0:04:40.188  2416M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Paired Information Counting (id: late_pair_info_count)
  0:04:40.341  2444M / 11G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 25778902 kmers to process
  0:04:44.124  2444M / 11G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:04:44.140  2444M / 11G   INFO    General                 (pair_info_count.cpp       : 339)   Min edge length for estimation: 2755
  0:04:44.141  2444M / 11G   INFO    General                 (pair_info_count.cpp       : 411)   Mapping single reads of library #0
  0:04:44.142  2444M / 11G   INFO    General                 (pair_info_count.cpp       :  48)   Selecting usual mapper
  0:04:45.488  2445M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:04:45.530  2446M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:04:45.537  2448M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:04:45.567  2449M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:04:45.711  2452M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:04:45.736  2453M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:04:45.932  2453M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:04:47.400  2456M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:04:49.333  2457M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8400000 reads
  0:04:53.845  2461M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 16874000 reads
  0:05:06.217  2514M / 11G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 20295612 reads processed
  0:05:06.222  2513M / 11G   INFO    General                 (pair_info_count.cpp       : 413)   Total paths obtained from single reads: 449954
  0:05:06.223  2513M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Distance Estimation (id: distance_estimation)
  0:05:06.224  2513M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Repeat Resolving (id: repeat_resolving)
  0:05:06.225  2513M / 11G   INFO    General                 (repeat_resolving.cpp      :  87)   Using Path-Extend repeat resolving
  0:05:06.229  2513M / 11G   INFO    General                 (launcher.cpp              : 600)   ExSPAnder repeat resolving tool started
  0:05:06.378  2812M / 11G   INFO    General                 (launcher.cpp              : 418)   Creating main extenders, unique edge length = 2000
  0:05:06.391  2822M / 11G   INFO    General                 (launcher.cpp              : 342)   filling path container
  0:05:07.261  4577M / 11G   INFO    General                 (extenders_logic.cpp       :  47)   resolvable_repeat_length_bound set to 10000
  0:05:09.304  4590M / 11G   INFO    General                 (extenders_logic.cpp       : 543)   Using 0 paired-end libraries
  0:05:09.310  4590M / 11G   INFO    General                 (extenders_logic.cpp       : 544)   Using 0 paired-end scaffolding libraries
  0:05:09.320  4590M / 11G   INFO    General                 (extenders_logic.cpp       : 545)   Using 1 single read library
  0:05:09.331  4590M / 11G   INFO    General                 (launcher.cpp              : 447)   Total number of extenders is 1
  0:05:09.342  4590M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 0 paths from 73341 (0%)
  0:05:10.660  4591M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 128 paths from 73341 (0%)
  0:05:11.491  4592M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 256 paths from 73341 (0%)
  0:05:13.630  4593M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 512 paths from 73341 (0%)
  0:05:15.316  4595M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 1024 paths from 73341 (1%)
  0:05:17.532  4600M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 2048 paths from 73341 (2%)
  0:05:22.151  4609M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 4096 paths from 73341 (5%)
  0:05:26.132  4622M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 7335 paths from 73341 (10%)
  0:05:27.495  4626M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 8192 paths from 73341 (11%)
  0:05:35.496  4652M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 14670 paths from 73341 (20%)
  0:05:37.580  4659M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 16384 paths from 73341 (22%)
  0:05:40.062  4685M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 22005 paths from 73341 (30%)
  0:05:42.909  4719M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 29340 paths from 73341 (40%)
  0:05:43.532  4737M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 32768 paths from 73341 (44%)
  0:05:45.002  4756M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 36675 paths from 73341 (50%)
  0:05:53.592  4792M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 44010 paths from 73341 (60%)
  0:06:08.615  4824M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 51345 paths from 73341 (70%)
  0:06:17.187  4843M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 58680 paths from 73341 (80%)
  0:06:18.236  4847M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 65536 paths from 73341 (89%)
  0:06:18.246  4847M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 66015 paths from 73341 (90%)
  0:06:18.870  4851M / 11G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:06:18.873  4851M / 11G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:06:19.005  4808M / 11G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:06:19.008  4808M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:06:19.011  4808M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:06:19.030  4808M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:06:19.033  4808M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:06:19.222  4812M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:06:19.429  4814M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:06:19.568  5034M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:06:19.815  4797M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:06:19.875  4790M / 11G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:06:19.881  4790M / 11G   INFO    General                 (launcher.cpp              : 454)   Closing gaps in paths
  0:06:19.988  4970M / 11G   INFO    General                 (launcher.cpp              : 484)   Gap closing completed
  0:06:20.015  4983M / 11G   INFO    General                 (launcher.cpp              : 302)   Traversing tandem repeats
  0:06:20.201  4984M / 11G   INFO    General                 (launcher.cpp              : 312)   Traversed 1456 loops
  0:06:20.205  4984M / 11G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:06:20.209  4984M / 11G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:06:20.236  4977M / 11G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:06:20.240  4977M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:06:20.244  4977M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:06:20.262  4977M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:06:20.266  4977M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:06:20.319  4977M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:06:20.374  4977M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:06:20.395  4999M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:06:20.432  4979M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:06:20.479  4981M / 11G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:06:20.483  4981M / 11G   INFO    General                 (launcher.cpp              : 664)   ExSPAnder repeat resolving tool finished
  0:06:20.796  2692M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:06:20.828  2692M / 11G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/before_rr.fasta
  0:06:21.242  2692M / 11G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/assembly_graph_with_scaffolds.gfa
  0:06:21.459  2692M / 11G   INFO    General                 (contig_output_stage.cpp   : 165)   Outputting FastG graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/assembly_graph.fastg
  0:06:22.311  2692M / 11G   INFO    General                 (contig_output_stage.cpp   : 196)   Breaking scaffolds
  0:06:22.592  2955M / 11G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/final_contigs.fasta
  0:06:22.789  2955M / 11G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/final_contigs.paths
  0:06:23.057  2746M / 11G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/scaffolds.fasta
  0:06:23.345  2746M / 11G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/scaffolds.paths
  0:06:23.434  2746M / 11G   INFO    General                 (contig_output_stage.cpp   : 111)   Populating GFA with scaffold paths
  0:06:23.585  2692M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:06:25.557     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 6 minutes 25 seconds

===== K55 finished. 


===== Copy files started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/copy_files.py /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/final_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/K55/final_contigs.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/contigs.paths


===== Copy files finished. 


===== Assembling finished. 


===== Breaking scaffolds started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/breaking_scaffolds_script.py --result_scaffolds_filename /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/scaffolds.fasta --misc_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/misc --threshold_for_breaking_scaffolds 3


===== Breaking scaffolds finished. 


===== Terminate started. 


===== Terminate finished. 

 * Corrected reads are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/corrected/
 * Assembled contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/contigs.fasta
 * Assembled scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/scaffolds.fasta
 * Paths in the assembly graph corresponding to the contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/contigs.paths
 * Paths in the assembly graph corresponding to the scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/scaffolds.paths
 * Assembly graph is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/assembly_graph.fastg
 * Assembly graph in GFA format is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/assembly_graph_with_scaffolds.gfa

======= SPAdes pipeline finished.

SPAdes log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507996/spades.log

Thank you for using SPAdes!
