Command line: /usr/local/bin/spades.py	-s	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507990.anqdpht.fastq.gz	--sc	-o	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990	-t	16	-m	128	

System information:
  SPAdes version: 3.15.5
  Python version: 3.10.6
  OS: Linux-4.18.0-553.5.1.el8_10.x86_64-x86_64-with-glibc2.28

Output dir: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990
Mode: read error correction and assembling
Debug mode is turned OFF

Dataset parameters:
  Single-cell mode
  Reads:
    Library number: 1, library type: single
      left reads: not specified
      right reads: not specified
      interlaced reads: not specified
      single reads: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507990.anqdpht.fastq.gz']
      merged reads: not specified
Read error correction parameters:
  Iterations: 1
  PHRED offset will be auto-detected
  Corrected reads will be compressed
Assembly parameters:
  k: [21, 33, 55]
  Repeat resolution is enabled
  Mismatch careful mode is turned OFF
  MismatchCorrector will be SKIPPED
  Coverage cutoff is turned OFF
Other parameters:
  Dir for temp files: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/tmp
  Threads: 16
  Memory limit (in Gb): 128


======= SPAdes pipeline started. Log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/spades.log

/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507990.anqdpht.fastq.gz: max reads length: 150

Reads length: 150


===== Before start started. 


===== Read error correction started. 


===== Read error correction started. 


== Running: /usr/local/bin/spades-hammer /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/corrected/configs/config.info

  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  75)   Starting BayesHammer, built from N/A, git revision N/A
  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  76)   Loading config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/corrected/configs/config.info
  0:00:00.006     1M / 33M   INFO    General                 (main.cpp                  :  78)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.006     1M / 33M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.006     1M / 33M   INFO    General                 (main.cpp                  :  86)   Trying to determine PHRED offset
  0:00:00.008     1M / 33M   INFO    General                 (main.cpp                  :  92)   Determined value is 33
  0:00:00.009     1M / 33M   INFO    General                 (hammer_tools.cpp          :  38)   Hamming graph threshold tau=1, k=21, subkmer positions = [ 0 10 ]
  0:00:00.009     1M / 33M   INFO    General                 (main.cpp                  : 113)   Size of aux. kmer data 24 bytes
     === ITERATION 0 begins ===
  0:00:00.018     1M / 33M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 16 files using 16 threads. This might take a while.
  0:00:00.028     1M / 33M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.028     1M / 33M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66666 Gb
  0:00:00.028     1M / 33M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 4194304
  0:00:00.066  9217M / 9217M INFO   K-mer Splitting          (kmer_data.cpp             :  97)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507990.anqdpht.fastq.gz
  0:00:14.458  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 3647028 reads
  0:00:31.230  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 7321040 reads
  0:00:46.116  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 9503780 reads
  0:00:46.116  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 112)   Total 9503780 reads processed
  0:00:46.117     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:54.720     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 103538888 kmers in total.
  0:00:54.769     1M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:59.971    77M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 103538888 kmers, 74794304 bytes occupied (5.77903 bits per kmer).
  0:00:59.972    77M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 354)   Arranging kmers in hash map order
  0:01:03.948  1661M / 15G   INFO    General                 (main.cpp                  : 148)   Clustering Hamming graph.
  0:01:41.590  1661M / 15G   INFO    General                 (main.cpp                  : 155)   Extracting clusters:
  0:01:41.590  1661M / 15G   INFO    General                 (concurrent_dsu.cpp        :  18)   Connecting to root
  0:01:41.739  1661M / 15G   INFO    General                 (concurrent_dsu.cpp        :  34)   Calculating counts
  0:01:47.897  2750M / 15G   INFO    General                 (concurrent_dsu.cpp        :  63)   Writing down entries
  0:02:01.834  1661M / 15G   INFO    General                 (main.cpp                  : 167)   Clustering done. Total clusters: 39382685
  0:02:01.835   869M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 371)   Collecting K-mer information, this takes a while.
  0:02:02.066  3241M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 377)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507990.anqdpht.fastq.gz
  0:02:45.179  3241M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 384)   Collection done, postprocessing.
  0:02:45.334  3241M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 397)   There are 103538888 kmers in total. Among them 67243108 (64.9448%) are singletons.
  0:02:45.335  3241M / 15G   INFO    General                 (main.cpp                  : 173)   Subclustering Hamming graph
  0:03:33.081  3241M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 650)   Subclustering done. Total 659 non-read kmers were generated.
  0:03:33.081  3241M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 651)   Subclustering statistics:
  0:03:33.081  3241M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 652)     Total singleton hamming clusters: 30348298. Among them 12595320 (41.5026%) are good
  0:03:33.081  3241M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 653)     Total singleton subclusters: 134100. Among them 133490 (99.5451%) are good
  0:03:33.081  3241M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 654)     Total non-singleton subcluster centers: 9700682. Among them 7515033 (77.4691%) are good
  0:03:33.082  3241M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 655)     Average size of non-trivial subcluster: 7.54554 kmers
  0:03:33.082  3241M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 656)     Average number of sub-clusters per non-singleton cluster: 1.08859
  0:03:33.082  3241M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 657)     Total solid k-mers: 20243843
  0:03:33.082  3241M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 658)     Substitution probabilities: [4,4]((0.93482,0.0322982,0.0305149,0.00236645),(0.0255558,0.939226,0.00630245,0.0289161),(0.0283625,0.0061663,0.939438,0.0260328),(0.00221707,0.0303232,0.0320235,0.935436))
  0:03:33.137  3241M / 15G   INFO    General                 (main.cpp                  : 178)   Finished clustering.
  0:03:33.137  3241M / 15G   INFO    General                 (main.cpp                  : 197)   Starting solid k-mers expansion in 16 threads.
  0:03:54.673  3241M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 0 produced 2465130 new k-mers.
  0:04:16.143  3241M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 1 produced 261259 new k-mers.
  0:04:37.417  3241M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 2 produced 9686 new k-mers.
  0:04:58.694  3241M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 3 produced 589 new k-mers.
  0:05:19.886  3241M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 4 produced 49 new k-mers.
  0:05:41.070  3241M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 5 produced 0 new k-mers.
  0:05:41.070  3241M / 15G   INFO    General                 (main.cpp                  : 222)   Solid k-mers finalized
  0:05:41.070  3241M / 15G   INFO    General                 (hammer_tools.cpp          : 222)   Starting read correction in 16 threads.
  0:05:41.070  3241M / 15G   INFO    General                 (hammer_tools.cpp          : 266)   Correcting single reads: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507990.anqdpht.fastq.gz
  0:05:43.371  4022M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 0 of 1600000 reads.
  0:05:46.141  4144M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 0
  0:05:47.334  4144M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 0
  0:05:49.596  4169M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 1 of 1600000 reads.
  0:05:52.263  4169M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 1
  0:05:53.413  4169M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 1
  0:05:55.656  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 2 of 1600000 reads.
  0:05:57.661  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 2
  0:05:58.807  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 2
  0:06:01.055  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 3 of 1600000 reads.
  0:06:02.993  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 3
  0:06:04.137  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 3
  0:06:06.419  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 4 of 1600000 reads.
  0:06:08.679  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 4
  0:06:09.829  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 4
  0:06:12.166  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 5 of 1503780 reads.
  0:06:21.716  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 5
  0:06:22.795  4170M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 5
  0:06:26.143  3241M / 15G   INFO    General                 (hammer_tools.cpp          : 276)   Correction done. Changed 3392342 bases in 1648397 reads.
  0:06:26.150  3241M / 15G   INFO    General                 (hammer_tools.cpp          : 277)   Failed to correct 0 bases out of 1416781689.
  0:06:26.157     1M / 15G   INFO    General                 (main.cpp                  : 255)   Saving corrected dataset description to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/corrected/corrected.yaml
  0:06:26.162     1M / 15G   INFO    General                 (main.cpp                  : 262)   All done. Exiting.

===== Read error correction finished. 


===== corrected reads compression started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/compress_all.py --input_file /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/corrected/corrected.yaml --ext_python_modules_home /usr/local/share/spades --max_threads 16 --output_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/corrected --gzip_output

== Compressing corrected reads (with gzip)
== Files to compress: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/corrected/1507990.anqdpht.fastq.00.0_0.cor.fastq']
== Files compression is finished
== Dataset yaml file is updated

===== corrected reads compression finished. 


===== Read error correction finished. 


===== Assembling started. 


===== K21 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K21/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K21/configs/mda_mode.info

  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K21/configs/config.info
  0:00:00.008     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K21/configs/mda_mode.info
  0:00:00.009     1M / 36M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.009     1M / 36M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.009     1M / 36M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.009     1M / 36M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/dataset.info) with K=21
  0:00:00.009     1M / 36M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.009     1M / 36M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.009     1M / 36M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.009     1M / 36M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.014     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.014     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.019     1M / 36M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.022     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.032     1M / 36M   INFO    General                 (read_converter.cpp        :  72)   Converting reads to binary format for library #0 (takes a while)
  0:00:00.033     1M / 36M   INFO    General                 (read_converter.cpp        :  73)   Converting paired reads
  0:00:00.044    23M / 36M   INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:00.044     1M / 36M   INFO    General                 (read_converter.cpp        :  86)   Converting single reads
  0:00:00.248    33M / 44M   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:00:00.263    36M / 52M   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:00:00.281    41M / 63M   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:00:00.403    35M / 92M   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:00:00.572    40M / 116M  INFO    General                 (binary_converter.cpp      :  96)   262144 reads processed
  0:00:00.992    34M / 147M  INFO    General                 (binary_converter.cpp      :  96)   524288 reads processed
  0:00:01.930    38M / 181M  INFO    General                 (binary_converter.cpp      :  96)   1048576 reads processed
  0:00:03.444    46M / 221M  INFO    General                 (binary_converter.cpp      :  96)   2097152 reads processed
  0:00:06.835    45M / 266M  INFO    General                 (binary_converter.cpp      :  96)   4194304 reads processed
  0:00:13.178    44M / 315M  INFO    General                 (binary_converter.cpp      :  96)   8388608 reads processed
  0:00:15.113    31M / 333M  INFO    General                 (binary_converter.cpp      : 111)   9497001 reads written
  0:00:15.114    21M / 333M  INFO    General                 (read_converter.cpp        :  92)   Converting merged reads
  0:00:15.122    31M / 333M  INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:15.501     1M / 333M  INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:15.640     1M / 333M  INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:15.640     1M / 333M  INFO    General                 (construction.cpp          : 159)   Average read length 149.103
  0:00:15.640     1M / 333M  INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:15.640     1M / 333M  INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:15.648     1M / 333M  INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:15.648     1M / 333M  INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:15.648     1M / 333M  INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:00:21.476  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 12923842 reads
  0:00:24.577  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 18848184 reads
  0:00:25.848  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 18994002 reads
  0:00:25.849     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 18994002 reads
  0:00:25.864     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:28.137     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 20289981 kmers in total.
  0:00:28.140     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:28.354     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:28.354     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:28.361     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:28.361     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:28.361     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:00:31.885  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 20289981 kmers
  0:00:31.885  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 20289981 kmers.
  0:00:31.886     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:34.007     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 19910643 kmers in total.
  0:00:34.010     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:34.170    16M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 19910643 kmers, 14500040 bytes occupied (5.82605 bits per kmer).
  0:00:34.170    16M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:00:36.042    35M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:00:36.604    35M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:00:36.619    35M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:00:36.619    35M / 11G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:00:36.619    35M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:00:37.162    55M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 235520
  0:00:37.169    55M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 240326
  0:00:37.176    35M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   3875028 22-mers were removed by early tip clipper
  0:00:37.176    35M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:00:37.333    35M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:00:37.822    74M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 1030598 sequences extracted
  0:00:38.094    73M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:00:38.370    74M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 20 loops collected
  0:00:38.397    73M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 2061236 edges to create
  0:00:38.397   154M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:00:39.128   186M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:00:39.139   186M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:00:39.146   194M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 651280 vertices to create
  0:00:39.146   254M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:00:39.706   199M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:00:39.706   199M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:39.901   213M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 20289981 kmers, 14773304 bytes occupied (5.82487 bits per kmer).
  0:00:39.909   294M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:00:48.631   294M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:00:49.003   294M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 2060782 edges
  0:00:50.175   184M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:00:50.411   185M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 27
  0:00:50.411   185M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 73.8372
  0:00:50.411   185M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 198
  0:00:50.421   184M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 73.8372
  0:00:50.421   184M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:00:50.421   184M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:00:50.422   184M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:00:50.423   184M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:00:50.423   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:00:50.435   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 0 times
  0:00:50.435   184M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:00:50.435   184M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:00:50.435   184M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:00:50.435   184M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:00:50.435   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:50.483   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 9356 times
  0:00:50.483   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:54.174   192M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 51109 times
  0:00:54.175   192M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:55.862   199M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 292307 times
  0:00:55.868   199M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:00:55.868   199M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:55.924   186M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3763 times
  0:00:55.925   186M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:56.760   179M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 12371 times
  0:00:56.762   179M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:56.828   178M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 12344 times
  0:00:56.829   178M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:00:56.829   178M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:56.835   177M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 26 times
  0:00:56.835   177M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:57.173   176M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1353 times
  0:00:57.173   176M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:57.195   176M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 4051 times
  0:00:57.195   176M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:00:57.195   176M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:57.197   176M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5 times
  0:00:57.197   176M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:57.326   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 362 times
  0:00:57.326   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:57.335   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1888 times
  0:00:57.335   175M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:00:57.336   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:57.336   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:00:57.336   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:57.410   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 162 times
  0:00:57.410   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:57.415   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1082 times
  0:00:57.415   175M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:00:57.415   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:57.416   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:00:57.416   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:57.453   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 96 times
  0:00:57.453   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:57.457   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 794 times
  0:00:57.457   175M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:00:57.457   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:57.458   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:57.458   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:57.484   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 59 times
  0:00:57.484   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:57.487   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 518 times
  0:00:57.487   174M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:00:57.487   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:57.488   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:57.488   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:57.501   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 19 times
  0:00:57.501   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:57.503   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 407 times
  0:00:57.503   174M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:00:57.503   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:57.503   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:57.503   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:57.511   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 11 times
  0:00:57.511   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:57.513   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 329 times
  0:00:57.513   174M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:00:57.513   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:57.513   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:57.513   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:57.519   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 20 times
  0:00:57.519   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:57.521   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 276 times
  0:00:57.521   174M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:00:57.521   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:57.524   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 26 times
  0:00:57.524   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:57.772   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 125 times
  0:00:57.772   175M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:57.775   166M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:00:57.775   166M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:00:57.775   166M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:57.775   166M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:57.775   166M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:57.775   166M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:00:57.775   166M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:57.775   166M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:00:57.779   165M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:00:57.779   165M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:00:57.779   165M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:00:57.779   165M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:00:57.779   165M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:00:57.779   165M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:00:57.779   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:00:57.819   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1516 times
  0:00:57.819   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:00:57.913   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 36 times
  0:00:57.913   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:57.915   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:57.915   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:00:57.917   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 7 times
  0:00:57.917   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:58.140   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 7 times
  0:00:58.140   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:00:58.391   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 126 times
  0:00:58.391   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:00:58.416   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 6 times
  0:00:58.416   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:00:58.503   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:00:58.504   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:58.506   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:58.506   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:00:58.508   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:00:58.508   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:58.724   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:00:58.724   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:00:58.964   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:00:58.964   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:00:58.994   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:00:58.994   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:00:59.084   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:00:59.084   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:59.084   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:59.084   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:00:59.084   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:00:59.084   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:59.084   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:00:59.084   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:00:59.084   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:00:59.085   165M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:00:59.096   165M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:00:59.107   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:00:59.114   165M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 8403 times
  0:00:59.116   164M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:00:59.116   164M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 135.269
  0:00:59.118   164M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 8850046
  0:00:59.122   164M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 668
  0:00:59.122   164M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 75766
  0:00:59.122   164M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 61288
  0:00:59.122   164M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:00:59.124   164M / 11G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K21/simplified_contigs
  0:00:59.139   167M / 11G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:00:59.141   167M / 11G   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:00:59.150   167M / 11G   INFO    General                 (binary_converter.cpp      : 111)   37908 reads written
  0:00:59.188   164M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:00:59.200     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 0 minutes 59 seconds

===== K21 finished. 


===== K33 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K33/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K33/configs/mda_mode.info

  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K33/configs/config.info
  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K33/configs/mda_mode.info
  0:00:00.001     1M / 36M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/dataset.info) with K=33
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 36M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.002     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.004     1M / 36M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.005     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.005     1M / 36M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K21/simplified_contigs
  0:00:00.123     1M / 36M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.123     1M / 36M   INFO    General                 (construction.cpp          : 159)   Average read length 149.103
  0:00:00.123     1M / 36M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.123     1M / 36M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.136     1M / 36M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.136     1M / 36M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.136     1M / 36M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:04.984  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 7325669 reads
  0:00:08.308  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 14189296 reads
  0:00:11.296  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 18522400 reads
  0:00:13.793  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 19069818 reads
  0:00:13.799     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 19069818 reads
  0:00:13.813     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:16.395     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 24065399 kmers in total.
  0:00:16.395     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:16.668     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:16.668     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:16.672     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:16.672     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:16.672     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:21.426  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 24065399 kmers
  0:00:21.428  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 24065399 kmers.
  0:00:21.429     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:24.322     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 23796518 kmers in total.
  0:00:24.323     2M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:25.012    20M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 23796518 kmers, 17306504 bytes occupied (5.81816 bits per kmer).
  0:00:25.013    20M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:00:26.890    43M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:00:27.588    43M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:00:27.619    43M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:00:27.619    43M / 11G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:00:27.619    43M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:00:28.193    63M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 293787
  0:00:28.202    63M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 300367
  0:00:28.215    43M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   5387776 34-mers were removed by early tip clipper
  0:00:28.215    43M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:00:28.361    43M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:00:28.949    83M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 835395 sequences extracted
  0:00:29.257    83M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:00:29.536    84M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 21 loops collected
  0:00:29.566    83M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 1670832 edges to create
  0:00:29.566   152M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:00:30.149   178M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:00:30.158   178M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:00:30.163   186M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 566535 vertices to create
  0:00:30.163   242M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:00:30.666   195M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:00:30.666   195M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:30.849   214M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 24065399 kmers, 17499696 bytes occupied (5.81738 bits per kmer).
  0:00:30.858   306M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:00:39.175   306M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:00:39.636   306M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 1670627 edges
  0:00:40.566   169M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:00:40.726   169M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 22
  0:00:40.727   169M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 58.7053
  0:00:40.727   169M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 155
  0:00:40.737   169M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 58.7053
  0:00:40.737   169M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:00:40.737   169M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:00:40.737   169M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:00:40.737   169M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:00:40.737   169M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:00:40.747   169M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 5 times
  0:00:40.747   169M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:00:40.747   169M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:00:40.747   169M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:00:40.747   169M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:00:40.747   169M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:40.809   168M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 16419 times
  0:00:40.810   168M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:43.225   182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 44897 times
  0:00:43.226   182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:44.540   176M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 211356 times
  0:00:44.544   176M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:00:44.544   176M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:44.593   167M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3194 times
  0:00:44.594   167M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.141   158M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 14516 times
  0:00:45.144   158M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:45.201   157M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 8697 times
  0:00:45.202   157M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:00:45.202   157M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.206   156M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 18 times
  0:00:45.206   156M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.276   155M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1661 times
  0:00:45.277   155M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:45.291   155M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2007 times
  0:00:45.291   155M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:00:45.291   155M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.292   155M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 4 times
  0:00:45.292   155M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.311   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 336 times
  0:00:45.311   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:45.317   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 817 times
  0:00:45.317   154M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:00:45.317   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.317   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:00:45.317   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.324   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 140 times
  0:00:45.324   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:45.329   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 508 times
  0:00:45.329   154M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:00:45.329   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.329   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:00:45.329   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.334   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 57 times
  0:00:45.334   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:45.336   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 279 times
  0:00:45.336   154M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:00:45.336   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.336   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:45.336   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.339   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 26 times
  0:00:45.339   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:45.341   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 240 times
  0:00:45.341   154M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:00:45.341   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.341   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:45.341   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.345   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 28 times
  0:00:45.345   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:45.346   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 165 times
  0:00:45.346   154M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:00:45.346   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.346   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:45.346   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.347   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 17 times
  0:00:45.347   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:45.349   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 128 times
  0:00:45.349   154M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:00:45.349   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.349   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:45.349   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.349   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 7 times
  0:00:45.349   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:45.350   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 118 times
  0:00:45.350   154M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:00:45.351   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.353   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 23 times
  0:00:45.353   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.385   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 53 times
  0:00:45.385   154M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:45.387   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:00:45.387   145M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:00:45.387   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.387   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:45.387   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.387   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:00:45.387   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:00:45.387   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:00:45.391   145M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:00:45.391   145M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:00:45.391   145M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:00:45.391   145M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:00:45.391   145M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:00:45.391   145M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:00:45.391   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:00:45.405   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 600 times
  0:00:45.405   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:00:45.505   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 42 times
  0:00:45.505   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.507   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:00:45.507   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:00:45.509   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 16 times
  0:00:45.509   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.535   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1 times
  0:00:45.536   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:00:45.564   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 70 times
  0:00:45.564   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:00:45.573   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 3 times
  0:00:45.574   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:00:45.668   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:00:45.668   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.670   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:00:45.670   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:00:45.672   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:00:45.672   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.698   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:00:45.698   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:00:45.725   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:00:45.725   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:00:45.734   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:00:45.734   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:00:45.832   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:00:45.832   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:45.832   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:00:45.832   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:00:45.832   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:00:45.832   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:00:45.832   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:00:45.832   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:00:45.832   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:00:45.832   145M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:00:45.853   145M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:00:45.862   145M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:00:45.873   144M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 14873 times
  0:00:45.874   144M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:00:45.875   144M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 118.332
  0:00:45.876   144M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 9126217
  0:00:45.878   144M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 1457
  0:00:45.878   144M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 36199
  0:00:45.878   144M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 42804
  0:00:45.878   144M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:00:45.883   144M / 11G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K33/simplified_contigs
  0:00:45.910   147M / 11G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:00:45.916   147M / 11G   INFO    General                 (binary_converter.cpp      : 111)   18102 reads written
  0:00:45.951   144M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:00:45.958     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 0 minutes 45 seconds

===== K33 finished. 


===== K55 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/configs/mda_mode.info

  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/configs/config.info
  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/configs/mda_mode.info
  0:00:00.000     1M / 36M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.000     1M / 36M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/dataset.info) with K=55
  0:00:00.001     1M / 36M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 36M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 36M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 36M   INFO    General                 (pipeline.cpp              : 242)   Will need read mapping, kmer mapper will be attached
  0:00:00.001     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.003     1M / 36M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.003     1M / 36M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.003     1M / 36M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K33/simplified_contigs
  0:00:00.106     1M / 36M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.106     1M / 36M   INFO    General                 (construction.cpp          : 159)   Average read length 149.103
  0:00:00.106     1M / 36M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.106     1M / 36M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.112     1M / 36M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.112     1M / 36M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.112     1M / 36M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:06.423  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 9160636 reads
  0:00:11.230  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 17859362 reads
  0:00:14.206  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 18977279 reads
  0:00:16.184  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 19030206 reads
  0:00:16.191     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 19030206 reads
  0:00:16.204     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:20.281     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 28354112 kmers in total.
  0:00:20.285     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:20.756     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:20.756     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:20.775     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:20.775     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:20.775     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:29.498  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 28354112 kmers
  0:00:29.499  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 28354112 kmers.
  0:00:29.500     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:33.852     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 28220928 kmers in total.
  0:00:33.852     2M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:34.942    24M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 28220928 kmers, 20502800 bytes occupied (5.81208 bits per kmer).
  0:00:34.943    24M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:00:39.148    50M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:00:40.009    50M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:00:40.042    50M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:00:41.054    50M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:00:41.870   114M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 1284254 sequences extracted
  0:00:42.300   114M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:00:43.097   114M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 1 loops collected
  0:00:43.160   114M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 2568510 edges to create
  0:00:43.160   214M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:00:44.017   254M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:00:44.048   254M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:00:44.059   270M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 1151071 vertices to create
  0:00:44.059   379M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:00:45.057   303M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:00:45.057   303M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:45.374   325M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 28354112 kmers, 20598464 bytes occupied (5.81177 bits per kmer).
  0:00:45.385   437M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:00:52.819   437M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:00:53.566   437M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 2568454 edges
  0:00:55.111   265M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:00:55.287   266M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 14
  0:00:55.287   266M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 31.5198
  0:00:55.287   266M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 126
  0:00:55.295   265M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 31.5198
  0:00:55.295   265M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:00:55.295   265M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: early_gapcloser)
  0:00:55.295   265M / 11G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:00:55.295   265M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:00:55.295   265M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:00:55.295   265M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:00:55.295   265M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:00:55.315   265M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 1 times
  0:00:55.315   265M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:00:55.315   265M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:00:55.315   265M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:00:55.315   265M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:00:55.315   265M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:56.727   243M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 406176 times
  0:00:56.731   243M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:00.453   412M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 31103 times
  0:01:00.454   412M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:01.198   414M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 106889 times
  0:01:01.201   414M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:01:01.201   414M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:01.257   410M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5222 times
  0:01:01.258   410M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:03.499   481M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 14919 times
  0:01:03.500   481M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:03.583   480M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 10594 times
  0:01:03.584   480M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:01:03.584   480M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:03.589   478M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 123 times
  0:01:03.590   478M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:03.918   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2532 times
  0:01:03.918   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:03.939   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2430 times
  0:01:03.940   490M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:01:03.940   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:03.941   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 14 times
  0:01:03.941   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:04.008   493M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 595 times
  0:01:04.009   493M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:04.019   493M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 923 times
  0:01:04.019   493M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:01:04.019   493M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:04.020   493M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5 times
  0:01:04.020   493M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:04.039   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 186 times
  0:01:04.039   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:04.044   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 448 times
  0:01:04.044   494M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:01:04.044   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:04.045   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:04.045   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:04.055   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 93 times
  0:01:04.055   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:04.059   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 248 times
  0:01:04.059   494M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:01:04.059   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:04.059   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:01:04.059   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:04.064   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 42 times
  0:01:04.064   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:04.066   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 178 times
  0:01:04.066   494M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:01:04.067   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:04.067   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:04.067   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:04.070   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 26 times
  0:01:04.070   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:04.072   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 133 times
  0:01:04.072   494M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:01:04.072   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:04.072   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:04.072   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:04.074   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 12 times
  0:01:04.074   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:04.075   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 103 times
  0:01:04.075   494M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:01:04.075   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:04.075   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:04.075   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:04.077   495M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 12 times
  0:01:04.077   495M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:04.078   495M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 82 times
  0:01:04.078   495M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:01:04.078   495M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:04.084   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 110 times
  0:01:04.084   494M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:04.093   495M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 20 times
  0:01:04.093   495M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:04.097   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:04.097   490M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:01:04.097   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:04.097   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:04.097   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:04.097   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:04.098   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:04.098   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:04.098   490M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 13
  0:01:04.098   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:04.098   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:04.098   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:04.098   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:04.098   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:04.099   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:04.106   490M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: late_gapcloser)
  0:01:04.106   490M / 11G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:01:04.106   490M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:01:04.107   490M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:01:04.107   490M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:01:04.107   490M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:01:04.107   490M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:04.107   490M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:04.107   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:04.107   490M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:04.107   490M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:04.155   490M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:04.188   490M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:04.443   490M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:04.475   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:01:04.476   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:04.480   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 6 times
  0:01:04.480   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:04.486   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 146 times
  0:01:04.486   490M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:04.678   491M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 80 times
  0:01:04.679   491M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:04.682   491M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:04.683   491M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:04.686   491M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 21 times
  0:01:04.686   491M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:04.691   491M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 3 times
  0:01:04.691   491M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:04.701   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 26 times
  0:01:04.701   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:04.701   492M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:04.701   492M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:04.735   492M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:04.765   492M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:04.824   492M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:04.851   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:01:04.851   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:04.855   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:01:04.855   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:04.860   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:04.860   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:05.015   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:05.016   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:05.019   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:05.019   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:05.022   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:05.023   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:05.026   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:05.027   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:05.031   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1 times
  0:01:05.031   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:05.031   492M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:05.031   492M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:05.057   492M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:05.083   492M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:05.123   492M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:05.148   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:01:05.149   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:05.152   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:01:05.152   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:05.157   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:05.157   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:05.317   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:05.317   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:05.317   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:05.317   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:05.317   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:05.317   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:05.318   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:05.318   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:05.318   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:05.318   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:05.318   492M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:05.318   492M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:05.344   492M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:05.369   492M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:05.408   492M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:05.433   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 0 times
  0:01:05.434   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:05.437   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:01:05.437   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:05.442   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:05.442   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:05.596   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:05.596   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:05.597   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:05.597   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:05.597   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:05.597   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:05.597   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:05.597   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:05.597   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:05.598   492M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:01:05.611   492M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:01:05.624   492M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:01:05.651   491M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 33479 times
  0:01:05.653   490M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:01:05.653   490M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 96.637
  0:01:05.655   490M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 8945633
  0:01:05.657   490M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 2935
  0:01:05.657   490M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 21519
  0:01:05.657   490M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 33418
  0:01:05.658   490M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Mismatch Correction (id: mismatch_correction)
  0:01:05.658   490M / 11G   INFO    General                 (graph_pack.cpp            :  67)   Index refill
  0:01:05.658   490M / 11G   INFO    General                 (edge_index.hpp            : 156)   Using small index (max_id = 2581355)
  0:01:05.679   490M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:05.889   497M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 8944544 kmers, 6461064 bytes occupied (5.77878 bits per kmer).
  0:01:05.896   569M / 11G   INFO    General                 (edge_index_builders.hpp   : 252)   Collecting edge information from graph, this takes a while.
  0:01:06.038   569M / 11G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 5538672 kmers to process
  0:01:06.763   569M / 11G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:01:06.763   569M / 11G   INFO    General                 (mismatch_correction.cpp   : 400)   Collect potential mismatches
  0:01:07.188   571M / 11G   INFO    General                 (mismatch_correction.cpp   : 192)   Total 5140 edges (out of 21519) with 319948 potential mismatch positions (62.2467 positions per edge)
  0:01:07.190   571M / 11G   INFO    General                 (mismatch_correction.cpp   : 402)   Potential mismatches collected
  0:01:08.134   572M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:01:08.174   573M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:01:08.175   575M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:01:08.176   576M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:01:08.217   577M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:01:08.225   578M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:01:08.303   580M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:01:09.174   581M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:01:10.550   583M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8400000 reads
  0:01:13.433   607M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 16909600 reads
  0:01:20.747   707M / 11G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 18994002 reads processed
  0:01:20.857   595M / 11G   INFO    General                 (mismatch_correction.cpp   : 395)   All edges processed
  0:01:20.861   569M / 11G   INFO    General                 (mismatch_correction.cpp   : 450)   Corrected 30 nucleotides
  0:01:20.861   569M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:01:20.861   569M / 11G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/before_rr.fasta
  0:01:21.202   569M / 11G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/assembly_graph_after_simplification.gfa
  0:01:21.265   569M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Paired Information Counting (id: late_pair_info_count)
  0:01:21.289   573M / 11G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 5540540 kmers to process
  0:01:22.063   573M / 11G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:01:22.067   573M / 11G   INFO    General                 (pair_info_count.cpp       : 339)   Min edge length for estimation: 2935
  0:01:22.067   573M / 11G   INFO    General                 (pair_info_count.cpp       : 411)   Mapping single reads of library #0
  0:01:22.067   573M / 11G   INFO    General                 (pair_info_count.cpp       :  48)   Selecting usual mapper
  0:01:22.740   573M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:01:22.751   573M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:01:22.789   573M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:01:22.811   574M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:01:22.821   574M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:01:22.822   574M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:01:22.897   574M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:01:23.529   574M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:01:26.174   578M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 9303600 reads
  0:01:28.200   580M / 11G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 9497001 reads processed
  0:01:28.202   580M / 11G   INFO    General                 (pair_info_count.cpp       : 413)   Total paths obtained from single reads: 39858
  0:01:28.202   580M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Distance Estimation (id: distance_estimation)
  0:01:28.202   580M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Repeat Resolving (id: repeat_resolving)
  0:01:28.202   580M / 11G   INFO    General                 (repeat_resolving.cpp      :  87)   Using Path-Extend repeat resolving
  0:01:28.203   580M / 11G   INFO    General                 (launcher.cpp              : 600)   ExSPAnder repeat resolving tool started
  0:01:28.223   623M / 11G   INFO    General                 (launcher.cpp              : 418)   Creating main extenders, unique edge length = 2000
  0:01:28.223   624M / 11G   INFO    General                 (launcher.cpp              : 342)   filling path container
  0:01:28.296   732M / 11G   INFO    General                 (extenders_logic.cpp       :  47)   resolvable_repeat_length_bound set to 10000
  0:01:28.386   734M / 11G   INFO    General                 (extenders_logic.cpp       : 543)   Using 0 paired-end libraries
  0:01:28.387   734M / 11G   INFO    General                 (extenders_logic.cpp       : 544)   Using 0 paired-end scaffolding libraries
  0:01:28.387   734M / 11G   INFO    General                 (extenders_logic.cpp       : 545)   Using 1 single read library
  0:01:28.387   734M / 11G   INFO    General                 (launcher.cpp              : 447)   Total number of extenders is 1
  0:01:28.387   734M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 0 paths from 10638 (0%)
  0:01:28.627   735M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 128 paths from 10638 (1%)
  0:01:28.878   735M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 256 paths from 10638 (2%)
  0:01:29.004   736M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 512 paths from 10638 (4%)
  0:01:29.381   739M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 1024 paths from 10638 (9%)
  0:01:29.383   739M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 1064 paths from 10638 (10%)
  0:01:29.899   743M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 2048 paths from 10638 (19%)
  0:01:29.923   743M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 2128 paths from 10638 (20%)
  0:01:30.282   748M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 3192 paths from 10638 (30%)
  0:01:30.584   751M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 4096 paths from 10638 (38%)
  0:01:30.585   752M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 4256 paths from 10638 (40%)
  0:01:30.776   756M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 5320 paths from 10638 (50%)
  0:01:30.799   760M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 6384 paths from 10638 (60%)
  0:01:30.820   764M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 7448 paths from 10638 (70%)
  0:01:30.827   767M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 8192 paths from 10638 (77%)
  0:01:30.830   769M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 8512 paths from 10638 (80%)
  0:01:30.884   773M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 9576 paths from 10638 (90%)
  0:01:30.983   774M / 11G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:01:30.984   774M / 11G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:01:30.990   772M / 11G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:01:30.990   772M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:01:30.990   772M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:01:30.994   772M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:01:30.994   772M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:01:31.002   772M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:01:31.010   772M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:01:31.014   780M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:01:31.020   772M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:01:31.025   773M / 11G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:01:31.025   773M / 11G   INFO    General                 (launcher.cpp              : 454)   Closing gaps in paths
  0:01:31.037   810M / 11G   INFO    General                 (launcher.cpp              : 484)   Gap closing completed
  0:01:31.040   812M / 11G   INFO    General                 (launcher.cpp              : 302)   Traversing tandem repeats
  0:01:31.063   812M / 11G   INFO    General                 (launcher.cpp              : 312)   Traversed 11 loops
  0:01:31.064   812M / 11G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:01:31.064   812M / 11G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:01:31.066   811M / 11G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:01:31.066   811M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:01:31.067   811M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:01:31.068   811M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:01:31.068   811M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:01:31.073   811M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:01:31.077   811M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:01:31.078   812M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:01:31.080   811M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:01:31.084   812M / 11G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:01:31.084   812M / 11G   INFO    General                 (launcher.cpp              : 664)   ExSPAnder repeat resolving tool finished
  0:01:31.112   618M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:01:31.113   618M / 11G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/before_rr.fasta
  0:01:31.226   618M / 11G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/assembly_graph_with_scaffolds.gfa
  0:01:31.267   618M / 11G   INFO    General                 (contig_output_stage.cpp   : 165)   Outputting FastG graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/assembly_graph.fastg
  0:01:31.445   618M / 11G   INFO    General                 (contig_output_stage.cpp   : 196)   Breaking scaffolds
  0:01:31.484   668M / 11G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/final_contigs.fasta
  0:01:31.537   668M / 11G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/final_contigs.paths
  0:01:31.595   629M / 11G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/scaffolds.fasta
  0:01:31.668   629M / 11G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/scaffolds.paths
  0:01:31.695   629M / 11G   INFO    General                 (contig_output_stage.cpp   : 111)   Populating GFA with scaffold paths
  0:01:31.927   618M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:01:32.320     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 1 minutes 32 seconds

===== K55 finished. 


===== Copy files started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/copy_files.py /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/final_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/K55/final_contigs.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/contigs.paths


===== Copy files finished. 


===== Assembling finished. 


===== Breaking scaffolds started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/breaking_scaffolds_script.py --result_scaffolds_filename /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/scaffolds.fasta --misc_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/misc --threshold_for_breaking_scaffolds 3


===== Breaking scaffolds finished. 


===== Terminate started. 


===== Terminate finished. 

 * Corrected reads are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/corrected/
 * Assembled contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/contigs.fasta
 * Assembled scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/scaffolds.fasta
 * Paths in the assembly graph corresponding to the contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/contigs.paths
 * Paths in the assembly graph corresponding to the scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/scaffolds.paths
 * Assembly graph is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/assembly_graph.fastg
 * Assembly graph in GFA format is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/assembly_graph_with_scaffolds.gfa

======= SPAdes pipeline finished.

SPAdes log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507990/spades.log

Thank you for using SPAdes!
