- STAGE: Before start
  args: []
  config_dir: ''
  del_after: []
  output_files: []
  path: 'true'
  short_name: before_start
- STAGE: Read error correction
  args: []
  config_dir: ''
  del_after: []
  output_files: []
  path: 'true'
  short_name: ec_start
- STAGE: Read error correction
  args:
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/corrected/configs/config.info
  config_dir: corrected
  del_after:
  - tmp/hammer_s26e_13h
  output_files:
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/corrected/corrected.yaml
  path: /usr/local/bin/spades-hammer
  short_name: ec_runtool
- STAGE: corrected reads compression
  args:
  - /usr/local/share/spades/spades_pipeline/scripts/compress_all.py
  - --input_file
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/corrected/corrected.yaml
  - --ext_python_modules_home
  - /usr/local/share/spades
  - --max_threads
  - '16'
  - --output_dir
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/corrected
  - --gzip_output
  config_dir: ''
  del_after: []
  output_files: []
  path: /usr/local/bin/python
  short_name: ec_compress
- STAGE: Read error correction
  args: []
  config_dir: ''
  del_after: []
  output_files: []
  path: 'true'
  short_name: ec_finish
- STAGE: Assembling
  args: []
  config_dir: ''
  del_after: []
  output_files: []
  path: 'true'
  short_name: as_start
- STAGE: K21
  args:
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K21/configs/config.info
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K21/configs/mda_mode.info
  config_dir: K21
  del_after: []
  output_files: []
  path: /usr/local/bin/spades-core
  short_name: k21
- STAGE: K33
  args:
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K33/configs/config.info
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K33/configs/mda_mode.info
  config_dir: K33
  del_after: []
  output_files: []
  path: /usr/local/bin/spades-core
  short_name: k33
- STAGE: K55
  args:
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/configs/config.info
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/configs/mda_mode.info
  config_dir: K55
  del_after: []
  output_files: []
  path: /usr/local/bin/spades-core
  short_name: k55
- STAGE: Copy files
  args:
  - /usr/local/share/spades/spades_pipeline/scripts/copy_files.py
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/before_rr.fasta
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/before_rr.fasta
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/assembly_graph_after_simplification.gfa
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/assembly_graph_after_simplification.gfa
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/final_contigs.fasta
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/contigs.fasta
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/first_pe_contigs.fasta
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/first_pe_contigs.fasta
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/strain_graph.gfa
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/strain_graph.gfa
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/scaffolds.fasta
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/scaffolds.fasta
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/scaffolds.paths
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/scaffolds.paths
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/assembly_graph_with_scaffolds.gfa
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/assembly_graph_with_scaffolds.gfa
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/assembly_graph.fastg
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/assembly_graph.fastg
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/final_contigs.paths
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/contigs.paths
  config_dir: ''
  del_after:
  - .bin_reads
  - tmp/spades_9lzgipxn
  output_files: []
  path: /usr/local/bin/python
  short_name: copy_files
- STAGE: Assembling
  args: []
  config_dir: ''
  del_after: []
  output_files: []
  path: 'true'
  short_name: as_finish
- STAGE: Breaking scaffolds
  args:
  - /usr/local/share/spades/spades_pipeline/scripts/breaking_scaffolds_script.py
  - --result_scaffolds_filename
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/scaffolds.fasta
  - --misc_dir
  - /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/misc
  - --threshold_for_breaking_scaffolds
  - '3'
  config_dir: ''
  del_after: []
  output_files: []
  path: /usr/local/bin/python
  short_name: bs
- STAGE: Terminate
  args: []
  config_dir: ''
  del_after:
  - configs
  output_files: []
  path: 'true'
  short_name: terminate
