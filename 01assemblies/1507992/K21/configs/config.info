; input options:

#include "simplification.info"
#include "construction.info"
#include "distance_estimation.info"
#include "detail_info_printer.info"
#include "tsa.info"
#include "pe_params.info"

K 21
;FIXME introduce isolate mode
mode base

dataset /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/dataset.info
log_filename    log.properties

output_base /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992
tmp_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/tmp/spades_9lzgipxn

main_iteration false
; iterative mode switcher, activates additional contigs usage
use_additional_contigs false
additional_contigs	tmp_contigs.fasta
load_from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K21/saves

; Multithreading options
temp_bin_reads_dir	.bin_reads/
max_threads 16
max_memory 128
buffer_size     512; in Megabytes

entry_point read_conversion
;entry_point construction
;entry_point simplification
;entry_point hybrid_aligning
;entry_point late_pair_info_count
;entry_point distance_estimation
;entry_point repeat_resolving

checkpoints none
developer_mode false
scaffold_correction_mode false

; enabled (1) or disabled (0) repeat resolution (former "paired_mode")
rr_enable false
; 0 for graph N50
min_edge_length_for_is_count 0

;preserve raw paired index after distance estimation
preserve_raw_paired_index false

; two-step pipeline
two_step_rr false
; enables/disables usage of intermediate contigs in two-step pipeline
use_intermediate_contigs false

;use single reads for rr (all | only_single_libs | none )
single_reads_rr only_single_libs

; The following parameters are used ONLY if developer_mode is true

; whether to output dot-files with pictures of graphs - ONLY in developer mode
output_pictures true

; whether to output resulting contigs after intermediate stages - ONLY in developer mode
output_nonfinal_contigs true

; whether to compute number of paths statistics   - ONLY in developer mode
compute_paths_number false

; End of developer_mode parameters

;if true simple mismatches are corrected
correct_mismatches false

; set it true to get statistics, such as false positive/negative, perfect match, etc.
paired_info_statistics false

; set it true to get statistics for pair information (over gaps), such as false positive/negative, perfect match, etc.
paired_info_scaffolder false

;the only option left from repeat resolving
max_repeat_length 8000

; repeat resolving mode (none path_extend)
resolving_mode path_extend

use_scaffolder  true

avoid_rc_connections true

calculate_coverage_for_each_lib false
strand_specificity {
    ss_enabled false
    antisense false
}

contig_output {
    contigs_name    final_contigs
    scaffolds_name  scaffolds
    ; none  --- do not output broken scaffolds | break_gaps --- break only by N steches | break_all --- break all with overlap < k
    output_broken_scaffolds     break_gaps
}

;position handling

pos
{
    max_mapping_gap 0 ; in terms of K+1 mers value will be K + max_mapping_gap
    max_gap_diff 0
	contigs_for_threading ./data/debruijn/contigs.fasta
    contigs_to_analyze ./data/debruijn/contigs.fasta
	late_threading true
	careful_labeling true

}

gap_closer_enable false

gap_closer
{
    minimal_intersection	10

    ;before_raw_simplify and before_simplify are mutually exclusive
    before_raw_simplify    		true
    before_simplify		false
    after_simplify 		true
    weight_threshold		2.0
    max_dist_to_tip		5000
}

kmer_coverage_model {
    probability_threshold 0.05
    strong_probability_threshold 0.999
    use_coverage_threshold false
    coverage_threshold 10.0
}

; low covered edges remover
lcer
{
    lcer_enabled                     false
    lcer_coverage_threshold          0.0
}

pacbio_processor
{
    internal_length_cutoff 200
;align and traverse.
    compression_cutoff 0.6
    path_limit_stretching 1.3
    path_limit_pressing 0.7
    max_path_in_dijkstra 15000
    max_vertex_in_dijkstra 2000
    rna_filtering   false

;gap_closer
    long_seq_limit 400
    enable_gap_closing true
    pacbio_min_gap_quantity 2
    contigs_min_gap_quantity 1
    max_contigs_gap_length 10000
}

;TODO move out!
graph_read_corr
{
	enable false
	output_dir corrected_contigs/
	binary true
}

bwa_aligner
{
    debug false
    min_contig_len 0
}

;flanking coverage range
flanking_range 55
series_analysis ""
save_gp false

ss_coverage_splitter {
    enabled           false
    bin_size          50
    min_edge_len      200
    min_edge_coverage 5
    min_flanking_coverage 2
    coverage_margin   5
}

time_tracer {
  time_tracer_enabled false
  granularity 500
}

hybrid_aligner {
  trusted_aligner {
        long_read_threshold       1000
        long_read_fuzzy_coverage  0.95
        short_read_fuzzy_coverage 0.90
  }
}
