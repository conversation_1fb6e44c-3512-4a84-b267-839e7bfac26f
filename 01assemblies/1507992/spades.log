Command line: /usr/local/bin/spades.py	-s	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507992.anqdpht.fastq.gz	--sc	-o	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992	-t	16	-m	128	

System information:
  SPAdes version: 3.15.5
  Python version: 3.10.6
  OS: Linux-4.18.0-553.5.1.el8_10.x86_64-x86_64-with-glibc2.28

Output dir: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992
Mode: read error correction and assembling
Debug mode is turned OFF

Dataset parameters:
  Single-cell mode
  Reads:
    Library number: 1, library type: single
      left reads: not specified
      right reads: not specified
      interlaced reads: not specified
      single reads: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507992.anqdpht.fastq.gz']
      merged reads: not specified
Read error correction parameters:
  Iterations: 1
  PHRED offset will be auto-detected
  Corrected reads will be compressed
Assembly parameters:
  k: [21, 33, 55]
  Repeat resolution is enabled
  Mismatch careful mode is turned OFF
  MismatchCorrector will be SKIPPED
  Coverage cutoff is turned OFF
Other parameters:
  Dir for temp files: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/tmp
  Threads: 16
  Memory limit (in Gb): 128


======= SPAdes pipeline started. Log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/spades.log

/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507992.anqdpht.fastq.gz: max reads length: 150

Reads length: 150


===== Before start started. 


===== Read error correction started. 


===== Read error correction started. 


== Running: /usr/local/bin/spades-hammer /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/corrected/configs/config.info

  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  75)   Starting BayesHammer, built from N/A, git revision N/A
  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  76)   Loading config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/corrected/configs/config.info
  0:00:00.002     1M / 33M   INFO    General                 (main.cpp                  :  78)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.002     1M / 33M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.002     1M / 33M   INFO    General                 (main.cpp                  :  86)   Trying to determine PHRED offset
  0:00:00.003     1M / 33M   INFO    General                 (main.cpp                  :  92)   Determined value is 33
  0:00:00.003     1M / 33M   INFO    General                 (hammer_tools.cpp          :  38)   Hamming graph threshold tau=1, k=21, subkmer positions = [ 0 10 ]
  0:00:00.003     1M / 33M   INFO    General                 (main.cpp                  : 113)   Size of aux. kmer data 24 bytes
     === ITERATION 0 begins ===
  0:00:00.027     1M / 33M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 16 files using 16 threads. This might take a while.
  0:00:00.039     1M / 33M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.039     1M / 33M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66666 Gb
  0:00:00.039     1M / 33M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 4194304
  0:00:00.077  9217M / 9217M INFO   K-mer Splitting          (kmer_data.cpp             :  97)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507992.anqdpht.fastq.gz
  0:00:14.328  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 3664738 reads
  0:00:31.968  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 7364637 reads
  0:00:50.897  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 10322542 reads
  0:00:50.897  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 112)   Total 10322542 reads processed
  0:00:50.898     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:59.762     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 134626828 kmers in total.
  0:00:59.785     1M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:02.729   102M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 134626828 kmers, 97247576 bytes occupied (5.77879 bits per kmer).
  0:01:02.735   102M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 354)   Arranging kmers in hash map order
  0:01:07.784  2158M / 15G   INFO    General                 (main.cpp                  : 148)   Clustering Hamming graph.
  0:01:59.546  2158M / 15G   INFO    General                 (main.cpp                  : 155)   Extracting clusters:
  0:01:59.546  2158M / 15G   INFO    General                 (concurrent_dsu.cpp        :  18)   Connecting to root
  0:01:59.738  2158M / 15G   INFO    General                 (concurrent_dsu.cpp        :  34)   Calculating counts
  0:02:09.391  4334M / 15G   INFO    General                 (concurrent_dsu.cpp        :  63)   Writing down entries
  0:02:25.364  2158M / 15G   INFO    General                 (main.cpp                  : 167)   Clustering done. Total clusters: 68651681
  0:02:25.366  1130M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 371)   Collecting K-mer information, this takes a while.
  0:02:25.666  4214M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 377)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507992.anqdpht.fastq.gz
  0:03:15.353  4214M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 384)   Collection done, postprocessing.
  0:03:15.555  4214M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 397)   There are 134626828 kmers in total. Among them 77498004 (57.5651%) are singletons.
  0:03:15.556  4214M / 15G   INFO    General                 (main.cpp                  : 173)   Subclustering Hamming graph
  0:04:09.123  4214M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 650)   Subclustering done. Total 1492 non-read kmers were generated.
  0:04:09.125  4214M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 651)   Subclustering statistics:
  0:04:09.125  4214M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 652)     Total singleton hamming clusters: 51553162. Among them 30593220 (59.3431%) are good
  0:04:09.125  4214M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 653)     Total singleton subclusters: 585507. Among them 584842 (99.8864%) are good
  0:04:09.125  4214M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 654)     Total non-singleton subcluster centers: 17868843. Among them 13226185 (74.0181%) are good
  0:04:09.125  4214M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 655)     Average size of non-trivial subcluster: 4.64944 kmers
  0:04:09.125  4214M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 656)     Average number of sub-clusters per non-singleton cluster: 1.0793
  0:04:09.125  4214M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 657)     Total solid k-mers: 44404247
  0:04:09.125  4214M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 658)     Substitution probabilities: [4,4]((0.93643,0.0300508,0.0304418,0.00307786),(0.0233969,0.940392,0.00702246,0.029189),(0.0284269,0.00683549,0.940716,0.0240214),(0.00302835,0.0304599,0.0298938,0.936618))
  0:04:09.208  4214M / 15G   INFO    General                 (main.cpp                  : 178)   Finished clustering.
  0:04:09.208  4214M / 15G   INFO    General                 (main.cpp                  : 197)   Starting solid k-mers expansion in 16 threads.
  0:04:32.311  4214M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 0 produced 3160177 new k-mers.
  0:04:55.262  4214M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 1 produced 238313 new k-mers.
  0:05:18.220  4214M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 2 produced 9920 new k-mers.
  0:05:41.128  4214M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 3 produced 863 new k-mers.
  0:06:04.115  4214M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 4 produced 89 new k-mers.
  0:06:27.366  4214M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 5 produced 33 new k-mers.
  0:06:50.458  4214M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 6 produced 86 new k-mers.
  0:07:13.707  4214M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 7 produced 0 new k-mers.
  0:07:13.707  4214M / 15G   INFO    General                 (main.cpp                  : 222)   Solid k-mers finalized
  0:07:13.707  4214M / 15G   INFO    General                 (hammer_tools.cpp          : 222)   Starting read correction in 16 threads.
  0:07:13.707  4214M / 15G   INFO    General                 (hammer_tools.cpp          : 266)   Correcting single reads: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507992.anqdpht.fastq.gz
  0:07:15.988  4995M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 0 of 1600000 reads.
  0:07:18.513  5117M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 0
  0:07:19.709  5117M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 0
  0:07:21.935  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 1 of 1600000 reads.
  0:07:24.209  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 1
  0:07:25.673  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 1
  0:07:27.911  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 2 of 1600000 reads.
  0:07:30.161  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 2
  0:07:31.612  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 2
  0:07:33.842  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 3 of 1600000 reads.
  0:07:35.931  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 3
  0:07:37.345  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 3
  0:07:39.618  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 4 of 1600000 reads.
  0:07:41.991  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 4
  0:07:43.400  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 4
  0:07:45.746  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 5 of 1600000 reads.
  0:07:49.208  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 5
  0:07:50.610  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 5
  0:07:51.790  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 6 of 722542 reads.
  0:07:58.520  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 6
  0:07:59.152  5139M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 6
  0:08:02.935  4214M / 15G   INFO    General                 (hammer_tools.cpp          : 276)   Correction done. Changed 3099723 bases in 1499301 reads.
  0:08:02.946  4214M / 15G   INFO    General                 (hammer_tools.cpp          : 277)   Failed to correct 0 bases out of 1539345668.
  0:08:02.957     1M / 15G   INFO    General                 (main.cpp                  : 255)   Saving corrected dataset description to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/corrected/corrected.yaml
  0:08:03.016     1M / 15G   INFO    General                 (main.cpp                  : 262)   All done. Exiting.

===== Read error correction finished. 


===== corrected reads compression started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/compress_all.py --input_file /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/corrected/corrected.yaml --ext_python_modules_home /usr/local/share/spades --max_threads 16 --output_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/corrected --gzip_output

== Compressing corrected reads (with gzip)
== Files to compress: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/corrected/1507992.anqdpht.fastq.00.0_0.cor.fastq']
== Files compression is finished
== Dataset yaml file is updated

===== corrected reads compression finished. 


===== Read error correction finished. 


===== Assembling started. 


===== K21 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K21/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K21/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K21/configs/config.info
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K21/configs/mda_mode.info
  0:00:00.002     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/dataset.info) with K=21
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.002     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.002     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.002     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.003     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.003     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.005     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.007     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.015     1M / 35M   INFO    General                 (read_converter.cpp        :  72)   Converting reads to binary format for library #0 (takes a while)
  0:00:00.015     1M / 35M   INFO    General                 (read_converter.cpp        :  73)   Converting paired reads
  0:00:00.021    23M / 35M   INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:00.021     1M / 35M   INFO    General                 (read_converter.cpp        :  86)   Converting single reads
  0:00:00.229    33M / 44M   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:00:00.244    36M / 52M   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:00:00.262    41M / 62M   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:00:00.379    35M / 92M   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:00:00.544    40M / 110M  INFO    General                 (binary_converter.cpp      :  96)   262144 reads processed
  0:00:00.953    34M / 146M  INFO    General                 (binary_converter.cpp      :  96)   524288 reads processed
  0:00:01.674    38M / 201M  INFO    General                 (binary_converter.cpp      :  96)   1048576 reads processed
  0:00:03.163    46M / 239M  INFO    General                 (binary_converter.cpp      :  96)   2097152 reads processed
  0:00:06.325    45M / 272M  INFO    General                 (binary_converter.cpp      :  96)   4194304 reads processed
  0:00:12.672    44M / 302M  INFO    General                 (binary_converter.cpp      :  96)   8388608 reads processed
  0:00:16.045    31M / 306M  INFO    General                 (binary_converter.cpp      : 111)   10314965 reads written
  0:00:16.049    21M / 306M  INFO    General                 (read_converter.cpp        :  92)   Converting merged reads
  0:00:16.070    31M / 306M  INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:16.496     1M / 306M  INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:16.632     1M / 306M  INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:16.643     1M / 306M  INFO    General                 (construction.cpp          : 159)   Average read length 149.152
  0:00:16.643     1M / 306M  INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:16.645     1M / 306M  INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:16.665     1M / 306M  INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:16.665     1M / 306M  INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:16.665     1M / 306M  INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:00:23.913  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 12125202 reads
  0:00:29.180  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 20270290 reads
  0:00:31.941  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 20629930 reads
  0:00:31.952     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 20629930 reads
  0:00:31.966     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:36.529     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 35148977 kmers in total.
  0:00:36.540     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:37.070     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:37.070     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:37.076     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:37.076     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:37.076     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:00:46.098  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 35148977 kmers
  0:00:46.108  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 35148977 kmers.
  0:00:46.120     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:51.176     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 34703410 kmers in total.
  0:00:51.187     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:52.572    27M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 34703410 kmers, 25185984 bytes occupied (5.806 bits per kmer).
  0:00:52.581    27M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:00:55.659    63M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:00:56.709    63M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:00:56.731    63M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:00:56.731    63M / 11G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:00:56.731    63M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:00:57.666    83M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 264012
  0:00:57.686    83M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 267924
  0:00:57.696    63M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   4963380 22-mers were removed by early tip clipper
  0:00:57.705    63M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:00:58.038    63M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:00:59.092   113M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 1253717 sequences extracted
  0:00:59.590   113M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:00.250   113M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 25 loops collected
  0:01:00.284   113M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 2507484 edges to create
  0:01:00.284   214M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:01.109   254M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:01.155   254M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:01.163   262M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 808175 vertices to create
  0:01:01.174   338M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:01.884   271M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:01.884   271M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:02.429   296M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 35148977 kmers, 25506992 bytes occupied (5.80546 bits per kmer).
  0:01:02.451   433M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:01:12.696   433M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:01:13.449   433M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 2506891 edges
  0:01:14.375   234M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:01:14.626   234M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 18
  0:01:14.626   234M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 43.5725
  0:01:14.626   234M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 284
  0:01:14.635   234M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 43.5725
  0:01:14.635   234M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:01:14.635   234M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:01:14.636   234M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:01:14.637   234M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:01:14.637   234M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:01:14.652   234M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 0 times
  0:01:14.652   234M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:01:14.652   234M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:01:14.652   234M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:14.652   234M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:01:14.652   234M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:14.708   233M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 10696 times
  0:01:14.708   233M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:19.054   244M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 69546 times
  0:01:19.055   244M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:20.793   261M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 301765 times
  0:01:20.796   261M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:01:20.796   261M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:20.873   248M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5011 times
  0:01:20.874   248M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:21.852   237M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 11211 times
  0:01:21.856   237M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:22.007   238M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 26225 times
  0:01:22.009   238M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:01:22.009   238M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:22.024   236M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 42 times
  0:01:22.024   236M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:22.822   232M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1711 times
  0:01:22.823   232M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:22.873   232M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 9540 times
  0:01:22.874   232M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:01:22.874   232M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:22.878   232M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:01:22.878   232M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:23.214   231M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 612 times
  0:01:23.214   231M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:23.239   231M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 4639 times
  0:01:23.239   231M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:01:23.239   231M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:23.241   231M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3 times
  0:01:23.241   231M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:23.396   231M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 281 times
  0:01:23.396   231M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:23.414   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3136 times
  0:01:23.415   230M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:01:23.415   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:23.416   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:23.416   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:23.515   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 161 times
  0:01:23.515   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:23.527   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2417 times
  0:01:23.527   230M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:01:23.527   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:23.528   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:01:23.528   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:23.607   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 131 times
  0:01:23.607   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:23.616   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1695 times
  0:01:23.616   230M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:01:23.616   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:23.617   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:23.617   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:23.653   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 86 times
  0:01:23.653   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:23.660   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1308 times
  0:01:23.660   230M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:01:23.660   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:23.660   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:01:23.660   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:23.690   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 58 times
  0:01:23.691   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:23.696   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1114 times
  0:01:23.697   229M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:01:23.697   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:23.697   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:23.697   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:23.722   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 53 times
  0:01:23.722   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:23.727   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 894 times
  0:01:23.727   229M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:01:23.727   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:23.732   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 34 times
  0:01:23.732   229M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:24.255   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 151 times
  0:01:24.255   230M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:24.260   213M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:24.260   213M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:01:24.260   213M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:24.260   213M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:24.260   213M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:24.260   213M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:24.260   213M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:24.260   213M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:24.268   210M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:01:24.268   210M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:01:24.268   210M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:01:24.268   210M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:01:24.268   210M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:24.268   211M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:24.268   211M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:24.355   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 3077 times
  0:01:24.355   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:24.555   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 166 times
  0:01:24.556   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:24.559   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:24.559   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:24.563   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 30 times
  0:01:24.563   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:25.108   211M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 15 times
  0:01:25.108   211M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:25.658   211M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 308 times
  0:01:25.658   211M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:25.705   211M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 10 times
  0:01:25.705   211M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:25.891   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 4 times
  0:01:25.892   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:25.895   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:25.895   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:25.899   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:25.899   210M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:26.397   211M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 3 times
  0:01:26.397   211M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:26.957   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 2 times
  0:01:26.963   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:27.043   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:27.054   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:27.243   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:27.243   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:27.244   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:27.244   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:27.244   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:27.244   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:27.244   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:27.244   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:27.244   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:27.244   212M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:01:27.262   212M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:01:27.298   212M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:01:27.314   211M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 13554 times
  0:01:27.319   210M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:01:27.319   210M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 63.3727
  0:01:27.322   210M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 20465386
  0:01:27.331   210M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 596
  0:01:27.331   210M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 187891
  0:01:27.331   210M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 146152
  0:01:27.331   210M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:01:27.336   210M / 11G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K21/simplified_contigs
  0:01:27.356   212M / 11G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:01:27.360   212M / 11G   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:01:27.377   212M / 11G   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:01:27.390   212M / 11G   INFO    General                 (binary_converter.cpp      : 111)   94000 reads written
  0:01:27.459   210M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:01:27.501     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 1 minutes 27 seconds

===== K21 finished. 


===== K33 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K33/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K33/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K33/configs/config.info
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K33/configs/mda_mode.info
  0:00:00.001     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/dataset.info) with K=33
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.003     1M / 35M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.003     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.003     1M / 35M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K21/simplified_contigs
  0:00:00.153     1M / 35M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.153     1M / 35M   INFO    General                 (construction.cpp          : 159)   Average read length 149.152
  0:00:00.153     1M / 35M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.153     1M / 35M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.172     1M / 35M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.172     1M / 35M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.172     1M / 35M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:08.066  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 6684759 reads
  0:00:14.021  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 13161955 reads
  0:00:20.405  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 19347218 reads
  0:00:25.344  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 20734018 reads
  0:00:28.753  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 20817930 reads
  0:00:28.828     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 20817930 reads
  0:00:28.842     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:39.260     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 39122901 kmers in total.
  0:00:39.271     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:40.775     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:40.781     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:40.808     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:40.816     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:40.816     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:55.534  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 39122901 kmers
  0:00:55.551  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 39122901 kmers.
  0:00:55.690     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:02.900     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 38866827 kmers in total.
  0:01:02.905     2M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:05.659    31M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 38866827 kmers, 28192128 bytes occupied (5.80282 bits per kmer).
  0:01:05.667    31M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:12.806    71M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:14.149    71M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:14.201    70M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:01:14.202    70M / 11G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:01:14.203    70M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:01:15.638    99M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 323058
  0:01:15.676    99M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 328431
  0:01:15.701    70M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   6361977 34-mers were removed by early tip clipper
  0:01:15.711    70M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:16.105    71M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:17.331   116M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 891197 sequences extracted
  0:01:17.865   116M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:18.461   116M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 27 loops collected
  0:01:18.500   116M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 1782448 edges to create
  0:01:18.500   188M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:19.128   216M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:19.138   216M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:19.143   224M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 635150 vertices to create
  0:01:19.143   285M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:19.692   235M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:19.692   235M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:20.739   264M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 39122901 kmers, 28376456 bytes occupied (5.80253 bits per kmer).
  0:01:20.768   416M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:01:30.490   416M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:01:31.404   416M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 1782188 edges
  0:01:33.474   186M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:01:33.633   186M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 15
  0:01:33.633   186M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 36.3336
  0:01:33.633   186M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 168
  0:01:33.639   186M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 36.3336
  0:01:33.639   186M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:01:33.639   186M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:01:33.639   186M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:01:33.639   186M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:01:33.639   186M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:01:33.650   186M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 21 times
  0:01:33.650   186M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:01:33.650   186M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:01:33.650   186M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:33.650   186M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:01:33.650   186M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:33.721   185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 17716 times
  0:01:33.722   185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:35.955   191M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 61165 times
  0:01:35.956   191M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:37.132   193M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 182021 times
  0:01:37.135   193M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:01:37.135   193M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:37.189   185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3622 times
  0:01:37.190   185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:37.741   177M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 11262 times
  0:01:37.743   177M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:37.878   177M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 17647 times
  0:01:37.880   177M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:01:37.880   177M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:37.887   176M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 42 times
  0:01:37.887   176M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:38.087   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2064 times
  0:01:38.087   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:38.116   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3902 times
  0:01:38.116   174M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:01:38.116   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:38.118   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:01:38.119   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:38.170   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 585 times
  0:01:38.170   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:38.184   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1979 times
  0:01:38.185   173M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:01:38.185   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:38.185   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3 times
  0:01:38.186   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:38.217   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 267 times
  0:01:38.217   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:38.226   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1095 times
  0:01:38.226   173M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:01:38.226   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:38.226   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:38.226   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:38.240   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 126 times
  0:01:38.240   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:38.246   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 753 times
  0:01:38.246   172M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:01:38.246   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:38.246   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:38.246   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:38.254   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 76 times
  0:01:38.254   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:38.259   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 521 times
  0:01:38.260   172M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:01:38.260   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:38.260   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:38.260   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:38.265   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 46 times
  0:01:38.265   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:38.269   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 452 times
  0:01:38.269   172M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:01:38.269   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:38.269   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:38.269   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:38.273   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 47 times
  0:01:38.273   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:38.276   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 342 times
  0:01:38.276   172M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:01:38.276   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:38.276   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:38.276   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:38.280   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 35 times
  0:01:38.281   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:38.283   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 298 times
  0:01:38.283   172M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:01:38.283   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:38.287   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 46 times
  0:01:38.287   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:38.371   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 96 times
  0:01:38.371   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:38.375   163M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:38.375   163M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:01:38.375   163M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:38.375   163M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:38.375   163M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:38.375   163M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:38.375   163M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:38.375   163M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:38.382   162M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:01:38.382   162M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:01:38.382   162M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:01:38.382   162M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:01:38.382   162M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:38.382   162M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:38.382   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:38.412   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1333 times
  0:01:38.412   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:38.603   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 154 times
  0:01:38.604   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:38.607   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:38.607   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:38.611   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 26 times
  0:01:38.611   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:38.686   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 13 times
  0:01:38.686   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:38.766   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 191 times
  0:01:38.766   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:38.784   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 6 times
  0:01:38.784   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:38.967   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 2 times
  0:01:38.968   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:38.971   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:38.971   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:38.975   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:38.975   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:39.048   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:39.049   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:39.124   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:39.124   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:39.144   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:39.144   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:39.332   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:39.332   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:39.332   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:39.332   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:39.332   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:39.333   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:39.333   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:39.333   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:39.333   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:39.333   162M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:01:39.372   163M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:01:39.395   163M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:01:39.416   162M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 24472 times
  0:01:39.419   161M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:01:39.419   161M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 54.767
  0:01:39.421   161M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 21400899
  0:01:39.426   161M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 1318
  0:01:39.426   161M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 90537
  0:01:39.426   161M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 102820
  0:01:39.426   161M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:01:39.442   161M / 11G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K33/simplified_contigs
  0:01:39.467   163M / 11G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:01:39.469   163M / 11G   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:01:39.485   163M / 11G   INFO    General                 (binary_converter.cpp      : 111)   45276 reads written
  0:01:39.627   161M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:01:39.644     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 1 minutes 39 seconds

===== K33 finished. 


===== K55 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/configs/config.info
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/configs/mda_mode.info
  0:00:00.000     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/dataset.info) with K=55
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 242)   Will need read mapping, kmer mapper will be attached
  0:00:00.001     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.003     1M / 35M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.003     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.003     1M / 35M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K33/simplified_contigs
  0:00:00.199     1M / 35M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.199     1M / 35M   INFO    General                 (construction.cpp          : 159)   Average read length 149.152
  0:00:00.199     1M / 35M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.199     1M / 35M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.212     1M / 35M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.212     1M / 35M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.213     1M / 35M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:08.730  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 8201670 reads
  0:00:16.542  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 16451598 reads
  0:00:28.114  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 20477227 reads
  0:00:34.732  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 20720482 reads
  0:00:34.742     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 20720482 reads
  0:00:34.756     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:41.967     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 42579751 kmers in total.
  0:00:41.978     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:42.859     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:42.865     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:42.880     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:42.897     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:42.908     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:53.673  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 42579751 kmers
  0:00:53.680  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 42579751 kmers.
  0:00:53.681     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:58.545     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 42492545 kmers in total.
  0:00:58.556     2M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:00.553    37M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 42492545 kmers, 30809648 bytes occupied (5.80048 bits per kmer).
  0:01:00.556    37M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:04.853    81M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:06.288    81M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:06.340    81M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:06.591    81M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:08.059   150M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 1318799 sequences extracted
  0:01:08.745   150M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:09.494   150M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 0 loops collected
  0:01:09.557   150M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 2637598 edges to create
  0:01:09.557   254M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:10.462   298M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:10.479   298M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:10.491   314M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 1231593 vertices to create
  0:01:10.491   431M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:11.506   350M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:11.506   350M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:11.917   386M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 42579751 kmers, 30872392 bytes occupied (5.80039 bits per kmer).
  0:01:11.932   550M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:01:20.127   550M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:01:21.473   550M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 2637539 edges
  0:01:23.910   286M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:01:24.082   286M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 11
  0:01:24.088   286M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 22.8087
  0:01:24.093   286M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 193
  0:01:24.095   286M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 22.8087
  0:01:24.095   286M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:01:24.095   286M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: early_gapcloser)
  0:01:24.095   286M / 11G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:01:24.095   286M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:01:24.095   286M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:01:24.096   286M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:01:24.096   286M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:01:24.117   286M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 5 times
  0:01:24.117   286M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:01:24.117   286M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:01:24.117   286M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:24.117   286M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:01:24.117   286M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:25.738   263M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 427248 times
  0:01:25.743   263M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:29.717   470M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 37981 times
  0:01:29.719   470M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:30.294   475M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 75053 times
  0:01:30.296   475M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:01:30.296   475M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:30.347   470M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3951 times
  0:01:30.348   470M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:31.848   515M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 9216 times
  0:01:31.848   515M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:31.974   514M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 14244 times
  0:01:31.975   514M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:01:31.975   514M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:31.981   513M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 153 times
  0:01:31.981   513M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:32.313   525M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2386 times
  0:01:32.314   525M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:32.354   525M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3875 times
  0:01:32.354   525M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:01:32.354   525M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:32.356   524M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 33 times
  0:01:32.356   524M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:32.450   528M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 762 times
  0:01:32.450   528M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:32.467   528M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1504 times
  0:01:32.468   528M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:01:32.468   528M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:32.469   528M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5 times
  0:01:32.469   528M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:32.497   529M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 248 times
  0:01:32.497   529M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:32.506   529M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 724 times
  0:01:32.506   529M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:01:32.507   529M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:32.507   529M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:32.507   529M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:32.525   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 123 times
  0:01:32.525   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:32.530   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 428 times
  0:01:32.531   530M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:01:32.531   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:32.531   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:01:32.531   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:32.541   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 77 times
  0:01:32.542   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:32.546   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 340 times
  0:01:32.546   530M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:01:32.547   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:32.547   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:01:32.547   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:32.552   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 39 times
  0:01:32.552   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:32.556   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 252 times
  0:01:32.556   530M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:01:32.556   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:32.557   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:32.557   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:32.560   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 23 times
  0:01:32.560   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:32.563   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 219 times
  0:01:32.563   530M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:01:32.563   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:32.564   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:32.564   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:32.566   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 14 times
  0:01:32.566   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:32.568   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 138 times
  0:01:32.568   530M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:01:32.568   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:32.578   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 370 times
  0:01:32.579   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:32.595   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 22 times
  0:01:32.596   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:32.601   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:32.601   526M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:01:32.601   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:32.601   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:32.602   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:32.602   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:32.602   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:32.602   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:32.613   526M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: late_gapcloser)
  0:01:32.614   526M / 11G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:01:32.614   526M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:01:32.614   526M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:01:32.614   526M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:01:32.614   526M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:01:32.614   526M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:32.615   526M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:32.615   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:32.615   526M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:32.615   526M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:32.701   526M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:32.778   526M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:33.322   526M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:33.395   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:01:33.396   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:33.402   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 13 times
  0:01:33.402   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:33.413   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 306 times
  0:01:33.413   526M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:33.751   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 217 times
  0:01:33.751   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:33.757   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 8 times
  0:01:33.758   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:33.764   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 55 times
  0:01:33.764   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:33.775   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 9 times
  0:01:33.775   530M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:33.807   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 110 times
  0:01:33.807   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:33.808   531M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:33.808   531M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:33.882   531M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:33.954   531M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:34.148   531M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:34.214   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:01:34.215   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:34.220   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:01:34.220   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:34.228   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 4 times
  0:01:34.229   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:34.483   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 3 times
  0:01:34.483   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:34.489   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:34.489   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:34.494   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:34.494   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:34.503   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:34.503   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:34.512   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:34.512   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:34.512   531M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:34.512   531M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:34.573   531M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:34.635   531M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:34.765   531M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:34.828   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:01:34.828   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:34.834   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:01:34.834   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:34.842   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:34.842   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:35.104   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:35.104   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:35.104   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:35.105   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:35.105   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:35.105   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:35.105   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:35.105   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:35.105   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:35.106   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:35.106   531M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:35.106   531M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:35.163   531M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:35.221   531M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:35.349   531M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:35.410   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 0 times
  0:01:35.410   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:35.416   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:01:35.416   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:35.424   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:35.424   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:35.677   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:35.678   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:35.678   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:35.678   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:35.678   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:35.678   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:35.679   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:35.679   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:35.679   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:35.679   531M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:01:35.704   531M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:01:35.734   531M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:01:35.785   529M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 49057 times
  0:01:35.788   529M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:01:35.788   529M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 44.4117
  0:01:35.791   529M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 21138313
  0:01:35.795   529M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 2506
  0:01:35.795   529M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 55646
  0:01:35.795   529M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 82558
  0:01:35.795   529M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Mismatch Correction (id: mismatch_correction)
  0:01:35.795   529M / 11G   INFO    General                 (graph_pack.cpp            :  67)   Index refill
  0:01:35.795   529M / 11G   INFO    General                 (edge_index.hpp            : 156)   Using small index (max_id = 2650788)
  0:01:35.841   529M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:36.292   544M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 21136041 kmers, 15266536 bytes occupied (5.77839 bits per kmer).
  0:01:36.307   709M / 11G   INFO    General                 (edge_index_builders.hpp   : 252)   Collecting edge information from graph, this takes a while.
  0:01:36.602   709M / 11G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 5931712 kmers to process
  0:01:37.377   709M / 11G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:01:37.378   709M / 11G   INFO    General                 (mismatch_correction.cpp   : 400)   Collect potential mismatches
  0:01:37.904   712M / 11G   INFO    General                 (mismatch_correction.cpp   : 192)   Total 14709 edges (out of 55646) with 538334 potential mismatch positions (36.599 positions per edge)
  0:01:37.905   712M / 11G   INFO    General                 (mismatch_correction.cpp   : 402)   Potential mismatches collected
  0:01:38.982   713M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:01:39.009   714M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:01:39.028   716M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:01:39.030   718M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:01:39.032   719M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:01:39.060   720M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:01:39.123   722M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:01:40.073   724M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:01:41.164   726M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8400000 reads
  0:01:44.560   732M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 16778800 reads
  0:01:52.135   880M / 11G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 20629930 reads processed
  0:01:52.404   753M / 11G   INFO    General                 (mismatch_correction.cpp   : 395)   All edges processed
  0:01:52.410   709M / 11G   INFO    General                 (mismatch_correction.cpp   : 450)   Corrected 73 nucleotides
  0:01:52.411   709M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:01:52.411   709M / 11G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/before_rr.fasta
  0:01:52.632   709M / 11G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/assembly_graph_after_simplification.gfa
  0:01:52.816   709M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Paired Information Counting (id: late_pair_info_count)
  0:01:52.869   719M / 11G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 5936062 kmers to process
  0:01:53.686   719M / 11G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:01:53.691   719M / 11G   INFO    General                 (pair_info_count.cpp       : 339)   Min edge length for estimation: 2506
  0:01:53.692   719M / 11G   INFO    General                 (pair_info_count.cpp       : 411)   Mapping single reads of library #0
  0:01:53.692   719M / 11G   INFO    General                 (pair_info_count.cpp       :  48)   Selecting usual mapper
  0:01:54.405   719M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:01:54.434   720M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:01:54.436   720M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:01:54.439   720M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:01:54.477   721M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:01:54.484   721M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:01:54.576   722M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:01:55.256   722M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:01:56.109   723M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8534100 reads
  0:02:00.742   735M / 11G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 10314965 reads processed
  0:02:00.743   735M / 11G   INFO    General                 (pair_info_count.cpp       : 413)   Total paths obtained from single reads: 93955
  0:02:00.743   735M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Distance Estimation (id: distance_estimation)
  0:02:00.744   735M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Repeat Resolving (id: repeat_resolving)
  0:02:00.744   735M / 11G   INFO    General                 (repeat_resolving.cpp      :  87)   Using Path-Extend repeat resolving
  0:02:00.745   735M / 11G   INFO    General                 (launcher.cpp              : 600)   ExSPAnder repeat resolving tool started
  0:02:00.782   846M / 11G   INFO    General                 (launcher.cpp              : 418)   Creating main extenders, unique edge length = 2000
  0:02:00.783   848M / 11G   INFO    General                 (launcher.cpp              : 342)   filling path container
  0:02:00.905  1092M / 11G   INFO    General                 (extenders_logic.cpp       :  47)   resolvable_repeat_length_bound set to 10000
  0:02:01.145  1096M / 11G   INFO    General                 (extenders_logic.cpp       : 543)   Using 0 paired-end libraries
  0:02:01.146  1096M / 11G   INFO    General                 (extenders_logic.cpp       : 544)   Using 0 paired-end scaffolding libraries
  0:02:01.146  1096M / 11G   INFO    General                 (extenders_logic.cpp       : 545)   Using 1 single read library
  0:02:01.147  1096M / 11G   INFO    General                 (launcher.cpp              : 447)   Total number of extenders is 1
  0:02:01.147  1096M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 0 paths from 27578 (0%)
  0:02:02.034  1097M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 128 paths from 27578 (0%)
  0:02:02.944  1098M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 256 paths from 27578 (0%)
  0:02:04.159  1099M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 512 paths from 27578 (1%)
  0:02:04.882  1101M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 1024 paths from 27578 (3%)
  0:02:06.011  1106M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 2048 paths from 27578 (7%)
  0:02:07.231  1109M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 2758 paths from 27578 (10%)
  0:02:08.627  1114M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 4096 paths from 27578 (14%)
  0:02:09.368  1120M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 5516 paths from 27578 (20%)
  0:02:10.703  1131M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 8192 paths from 27578 (29%)
  0:02:10.705  1131M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 8274 paths from 27578 (30%)
  0:02:11.974  1142M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 11032 paths from 27578 (40%)
  0:02:12.469  1153M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 13790 paths from 27578 (50%)
  0:02:12.837  1164M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 16384 paths from 27578 (59%)
  0:02:12.839  1164M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 16548 paths from 27578 (60%)
  0:02:12.906  1175M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 19306 paths from 27578 (70%)
  0:02:12.961  1187M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 22064 paths from 27578 (80%)
  0:02:13.743  1195M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 24822 paths from 27578 (90%)
  0:02:13.911  1197M / 11G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:02:13.912  1197M / 11G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:02:13.933  1191M / 11G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:02:13.933  1191M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:02:13.934  1191M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:02:13.942  1191M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:02:13.942  1191M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:02:13.974  1192M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:02:14.005  1192M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:02:14.023  1222M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:02:14.058  1190M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:02:14.079  1191M / 11G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:02:14.080  1191M / 11G   INFO    General                 (launcher.cpp              : 454)   Closing gaps in paths
  0:02:14.124  1282M / 11G   INFO    General                 (launcher.cpp              : 484)   Gap closing completed
  0:02:14.134  1285M / 11G   INFO    General                 (launcher.cpp              : 302)   Traversing tandem repeats
  0:02:14.202  1285M / 11G   INFO    General                 (launcher.cpp              : 312)   Traversed 61 loops
  0:02:14.202  1285M / 11G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:02:14.203  1285M / 11G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:02:14.210  1284M / 11G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:02:14.210  1284M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:02:14.211  1284M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:02:14.217  1284M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:02:14.218  1284M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:02:14.234  1284M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:02:14.250  1284M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:02:14.253  1287M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:02:14.261  1284M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:02:14.279  1286M / 11G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:02:14.280  1286M / 11G   INFO    General                 (launcher.cpp              : 664)   ExSPAnder repeat resolving tool finished
  0:02:14.342   826M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:02:14.344   826M / 11G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/before_rr.fasta
  0:02:14.601   826M / 11G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/assembly_graph_with_scaffolds.gfa
  0:02:14.695   826M / 11G   INFO    General                 (contig_output_stage.cpp   : 165)   Outputting FastG graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/assembly_graph.fastg
  0:02:15.183   826M / 11G   INFO    General                 (contig_output_stage.cpp   : 196)   Breaking scaffolds
  0:02:15.295   949M / 11G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/final_contigs.fasta
  0:02:15.755   949M / 11G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/final_contigs.paths
  0:02:15.904   853M / 11G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/scaffolds.fasta
  0:02:16.116   853M / 11G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/scaffolds.paths
  0:02:16.179   853M / 11G   INFO    General                 (contig_output_stage.cpp   : 111)   Populating GFA with scaffold paths
  0:02:16.293   826M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:02:16.720     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 2 minutes 16 seconds

===== K55 finished. 


===== Copy files started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/copy_files.py /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/final_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/K55/final_contigs.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/contigs.paths


===== Copy files finished. 


===== Assembling finished. 


===== Breaking scaffolds started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/breaking_scaffolds_script.py --result_scaffolds_filename /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/scaffolds.fasta --misc_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/misc --threshold_for_breaking_scaffolds 3


===== Breaking scaffolds finished. 


===== Terminate started. 


===== Terminate finished. 

 * Corrected reads are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/corrected/
 * Assembled contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/contigs.fasta
 * Assembled scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/scaffolds.fasta
 * Paths in the assembly graph corresponding to the contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/contigs.paths
 * Paths in the assembly graph corresponding to the scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/scaffolds.paths
 * Assembly graph is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/assembly_graph.fastg
 * Assembly graph in GFA format is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/assembly_graph_with_scaffolds.gfa

======= SPAdes pipeline finished.

SPAdes log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507992/spades.log

Thank you for using SPAdes!
