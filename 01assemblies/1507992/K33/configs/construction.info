; construction

construction
{
	; mode of construction: extension (construct hash map of kmers to extentions), old (construct set of k+1-mers)
	mode extension

	; enable keeping in graph perfect cycles. This slows down condensing but some plasmids can be lost if this is turned off.
	keep_perfect_loops true

	; size of buffer for each thread in MB, 0 for autodetection
	read_buffer_size 0

        ; read median coverage threshold
        read_cov_threshold 0

	early_tip_clipper
	{
		; tip clipper can be enabled only in extension mode
		enable true

		; optional parameter. By default tips of length rl-k are removed
;		length_bound 10
	}
}

