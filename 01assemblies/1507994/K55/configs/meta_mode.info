mode    meta

; two-step pipeline
two_step_rr true
min_edge_length_for_is_count 900

; enables/disables usage of intermediate contigs in two-step pipeline
use_intermediate_contigs true

;flanking coverage range
flanking_range 30

simp
{
    cycle_iter_count 3

    ; enable advanced ec removal algo
    topology_simplif_enabled false

    ; erroneous connections remover:
    ec
    {
       ; ec_lb: max_ec_length = k + ec_lb
       ; icb: iterative coverage bound
       ; condition               "{ ec_lb 30, icb 20.0 }"
       condition               "{ ec_lb 30, icb 2.5 }"
    }
    
    ; tip clipper:
    tc
    {
        ; rctc: tip_cov < rctc * not_tip_cov
        ; tc_lb: max_tip_length = max((min(k, read_length / 2) * tc_lb), read_length);
        condition               "{ rl 0.2 } { rlmk 2., rctc 2.0 }"
    }

    ; relative coverage erroneous component remover:
    rcc
    {
        enabled true
        coverage_gap    5.
        max_length_coeff    3.0
        max_length_with_tips_coeff   5.0
        max_vertex_cnt      100
        max_ec_length_coefficient   300
        max_coverage_coeff  -1.0
    }

    ; complex tip clipper
    complex_tc
    {
        enabled               true
    }
    
    ; relative edge disconnector:
    red
    {
        enabled true
        diff_mult  10.
        unconditional_diff_mult 50.
    }

    ; bulge remover:
    br
    {
        enabled true
        max_coverage			1000000.0
        max_relative_coverage		5.		; bulge_cov < this * not_bulge_cov
        max_delta			10
        max_relative_delta		0.1
        dijkstra_vertex_limit   3000
        parallel true
    }

    ; final tip clipper:
    final_tc
    {
        condition               "{ lb 500, rctc 0.4 } { lb 850, rctc 0.2 }"
    }

    ; final bulge remover:
    final_br
    {
        enabled true
        main_iteration_only true
        max_bulge_length_coefficient    30.     ; max_bulge_length = max_bulge_length_coefficient * k
        max_coverage            1000000.0
        max_relative_coverage       0.5     ; bulge_cov < this * not_bulge_cov
        max_delta 45
        max_relative_delta 0.1
        min_identity 0.7
    }

    ; suspecies bulge remover:
    subspecies_br
    {
        enabled false
    }
    
    ; complex bulge remover
    cbr
    {
        enabled true
    }

    ; hidden ec remover
    her
    {
        ; TODO NB config used in special meta mode version (always enabled)
        enabled                     false
        uniqueness_length           1500
        unreliability_threshold     -1.
        relative_threshold          3.     
    }

    init_clean
    {
        activation_cov  -1.
        early_it_only   false
        ier
        {
            enabled true
        }
        ;Disable if it does not help the br performance much!
        tip_condition   "{ tc_lb 3.5, cb 2.1 }"
        ;ec_condition is here only to speed-up future br on early iterations
        ec_condition    "{ ec_lb 10, cb 1.5 }"
        disconnect_flank_cov    -1.
    }

}

;TODO rename
preliminary_simp
{
    init_clean
    {
        tip_condition   "loop 2 { rlmk 1., cb 1.2, mmm 2 } { rlmk 1., cb 1.2, mmm 0.05 } { rl 0.2, cb 1.2 }"
        ec_condition    "{ ec_lb 0, cb 0.9 }"
        disconnect_flank_cov    0.8
    }

    ; bulge remover:
    br
    {
        enabled true
        max_coverage			1000000.0
        max_relative_coverage		0.5		; bulge_cov < this * not_bulge_cov
        max_delta			10
        max_relative_delta		0.1
    }

    ; Currently will not work even if enabled. Left for experiments.
    ; relative edge disconnector
    red
    {
        enabled false
        diff_mult  10.
        unconditional_diff_mult 100.
    }
}

; undo single cell config changes, enforce filtering
de
{
    raw_filter_threshold	1
    rounding_coeff              0.5 ; rounding : min(de_max_distance * rounding_coeff, rounding_thr)
    rounding_threshold          0
}

;NB decsends from sc_pe
pe {

long_reads {
    pacbio_reads {
        filtering   1.9
        weight_priority    20.0
        unique_edge_priority 10.0
        min_significant_overlap 1000
    }
}

params {
    overlap_removal {
        enabled true
        cut_all true
    }

    scaffolding_mode old_pe_2015

    normalize_weight     true
    
    ; extension selection
    extension_options
    {
        single_threshold           0.3
        weight_threshold           0.6
        priority_coeff             1.5
        max_repeat_length          1000000 
    }
    
    use_coordinated_coverage true

    coordinated_coverage
    {
       min_path_len           10000
    }

}

}

prelim_pe {
params {
    scaffolding_mode old

    overlap_removal {
        enabled false
    }

    use_coordinated_coverage false
    remove_overlaps     false
    scaffolding2015 {
        min_unique_length 100000000
    }
}
}
