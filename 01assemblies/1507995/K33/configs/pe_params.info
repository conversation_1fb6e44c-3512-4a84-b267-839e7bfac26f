pe {

; output options

debug_output    false

output {
    write_overlaped_paths   true
    write_paths             true
}

visualize {
    print_overlaped_paths   true
    print_paths             true
}

params {
    multi_path_extend   false
    ; old | 2015 | combined | old_pe_2015
    scaffolding_mode old_pe_2015
    
    overlap_removal {
        enabled true
        end_start_only  false
        cut_all false
    }

    normalize_weight     true
    
    ; extension selection
    extension_options
    {
        single_threshold           0.1
        weight_threshold           0.5
        priority_coeff             1.5
        ;TODO remove from here
        max_repeat_length          8000
    }    

    mate_pair_options
    {
        single_threshold           30
        weight_threshold           0.5
        priority_coeff             1.5
        ;TODO remove from here
        max_repeat_length          8000
    }

    scaffolder {
        enabled       true
        cutoff        2
        hard_cutoff   0
        rel_cov_cutoff    0.0
        sum_threshold 3  

        cluster_info  true
        cl_threshold  0

        fix_gaps       true
        use_la_gap_joiner true
        ;next param should be 0.51 - 1.0 if use_old_score = true and 3.0 otherwise
        min_gap_score   0.7

        max_can_overlap   1.
        short_overlap     6
        artificial_gap    10

        min_overlap_length 10
        flank_multiplication_coefficient .5
        flank_addition_coefficient 5

        var_coeff 3.0
        basic_overlap_coeff 2.0
    }

    path_cleaning_presets ""

    use_coordinated_coverage false
    coordinated_coverage
    {
       max_edge_length_repeat 300
       delta                  0.5
       min_path_len           1000
    }


    simple_coverage_resolver {
        enabled false
        coverage_margin 2
        min_upper_coverage 5
        max_coverage_variation 5
    }


    scaffolding2015 {
        ; (median * (1+variation) > unique > median * (1 - variation))
        relative_weight_cutoff 2.0

        unique_length_upper_bound 2000   ; max(unique_length_upper_bound, max_is(all libs))
        unique_length_lower_bound 500    ; max(unique_length_lower_bound, unique_length_step)
        unique_length_step 300

        graph_connectivity_max_edges 200000
    }

    scaffold_graph {
        construct    false
        output       false
        always_add   40         ; connection with read count >= always_add are always added to the graph
        never_add     5         ; connection with read count < never_add are never added to the graph
        relative_threshold 0.25 ; connection with read count >= max_read_count * relative_threshod are added to the graph if satisfy condition above, max_read_count is calculated amond all alternatives
        use_graph_connectivity false
        max_path_length 10000
    }

    genome_consistency_checker {
        max_gap 1000
        relative_max_gap 0.2
        use_main_storage true ; if set to true, next two parameters are set to min_unique_length
        unresolvable_jump 1000 ; length of unresolvable repeats
        unique_length 500  ; spelling genome in the alphabet of edges longer than this          
    }

    uniqueness_analyser {
        enabled        true
        unique_coverage_variation 0.5

        nonuniform_coverage_variation 50
        uniformity_fraction_threshold 0.8
    }

    loop_traversal
    {
        min_edge_length         1000
        max_component_size      10
        max_path_length         1000
    }
}


long_reads {
    pacbio_reads {
        filtering   2.5
        weight_priority    1.2
        unique_edge_priority 5.0
        min_significant_overlap 0
    }

    single_reads {
        filtering  1.25 
        weight_priority    5.0
        unique_edge_priority 10000.0
        min_significant_overlap 0
    }

    contigs {
        filtering   0.0
        weight_priority    1.5
        unique_edge_priority 2.0
        min_significant_overlap 0
    }

    meta_untrusted_contigs {
        filtering   0.0
        weight_priority    10000.0
        unique_edge_priority 10000.0
        min_significant_overlap 200
    }

    rna_long_reads {
        filtering   0.1
        weight_priority    1.1
        unique_edge_priority 2.0
        min_significant_overlap 0
    }


}
}
