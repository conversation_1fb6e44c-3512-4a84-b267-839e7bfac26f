; = HAMMER =
; input options: working dir, input files, offset, and possibly kmers
dataset /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/input_dataset.yaml
input_working_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/tmp/hammer_9wfigq0z
input_trim_quality			4
input_qvoffset				
output_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/corrected

; == HAMMER GENERAL ==
; general options
general_do_everything_after_first_iteration	1
general_hard_memory_limit 128
general_max_nthreads 16
general_tau			1
general_max_iterations 1
general_debug			0

; count k-mers
count_do				1
count_numfiles				16
count_merge_nthreads 16
count_split_buffer			0
count_filter_singletons                 0

; hamming graph clustering
hamming_do				1
hamming_blocksize_quadratic_threshold	50

; bayesian subclustering
bayes_do				1
bayes_nthreads 16
bayes_singleton_threshold		0.995
bayes_nonsingleton_threshold		0.9
bayes_use_hamming_dist			0
bayes_discard_only_singletons		0
bayes_debug_output			0
bayes_hammer_mode			0
bayes_write_solid_kmers			0
bayes_write_bad_kmers			0
bayes_initial_refine                    1

; iterative expansion step
expand_do				1
expand_max_iterations			25
expand_nthreads 16
expand_write_each_iteration		0
expand_write_kmers_result		0

; read correction
correct_do				1
correct_discard_bad			0
correct_use_threshold			1
correct_threshold			0.98
correct_nthreads 16
correct_readbuffer			100000
correct_stats                           1
