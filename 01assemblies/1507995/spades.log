Command line: /usr/local/bin/spades.py	-s	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507995.anqdpht.fastq.gz	--sc	-o	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995	-t	16	-m	128	

System information:
  SPAdes version: 3.15.5
  Python version: 3.10.6
  OS: Linux-4.18.0-553.5.1.el8_10.x86_64-x86_64-with-glibc2.28

Output dir: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995
Mode: read error correction and assembling
Debug mode is turned OFF

Dataset parameters:
  Single-cell mode
  Reads:
    Library number: 1, library type: single
      left reads: not specified
      right reads: not specified
      interlaced reads: not specified
      single reads: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507995.anqdpht.fastq.gz']
      merged reads: not specified
Read error correction parameters:
  Iterations: 1
  PHRED offset will be auto-detected
  Corrected reads will be compressed
Assembly parameters:
  k: [21, 33, 55]
  Repeat resolution is enabled
  Mismatch careful mode is turned OFF
  MismatchCorrector will be SKIPPED
  Coverage cutoff is turned OFF
Other parameters:
  Dir for temp files: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/tmp
  Threads: 16
  Memory limit (in Gb): 128


======= SPAdes pipeline started. Log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/spades.log

/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507995.anqdpht.fastq.gz: max reads length: 150

Reads length: 150


===== Before start started. 


===== Read error correction started. 


===== Read error correction started. 


== Running: /usr/local/bin/spades-hammer /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/corrected/configs/config.info

  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  75)   Starting BayesHammer, built from N/A, git revision N/A
  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  76)   Loading config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/corrected/configs/config.info
  0:00:00.002     1M / 33M   INFO    General                 (main.cpp                  :  78)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.002     1M / 33M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.002     1M / 33M   INFO    General                 (main.cpp                  :  86)   Trying to determine PHRED offset
  0:00:00.002     1M / 33M   INFO    General                 (main.cpp                  :  92)   Determined value is 33
  0:00:00.002     1M / 33M   INFO    General                 (hammer_tools.cpp          :  38)   Hamming graph threshold tau=1, k=21, subkmer positions = [ 0 10 ]
  0:00:00.002     1M / 33M   INFO    General                 (main.cpp                  : 113)   Size of aux. kmer data 24 bytes
     === ITERATION 0 begins ===
  0:00:00.015     1M / 33M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 16 files using 16 threads. This might take a while.
  0:00:00.019     1M / 33M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.019     1M / 33M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66666 Gb
  0:00:00.019     1M / 33M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 4194304
  0:00:00.058  9217M / 9217M INFO   K-mer Splitting          (kmer_data.cpp             :  97)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507995.anqdpht.fastq.gz
  0:00:12.325  9217M / 14G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 3573662 reads
  0:00:25.615  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 7266578 reads
  0:00:41.135  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 10937811 reads
  0:00:56.465  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 14661569 reads
  0:01:11.536  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 18345643 reads
  0:01:30.143  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 21910880 reads
  0:01:42.602  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 24005734 reads
  0:01:42.613  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 112)   Total 24005734 reads processed
  0:01:42.613     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:50.431     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 315416096 kmers in total.
  0:01:50.439     1M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:59.167   233M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 315416096 kmers, 227825504 bytes occupied (5.77841 bits per kmer).
  0:01:59.167   233M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 354)   Arranging kmers in hash map order
  0:02:11.460  5050M / 15G   INFO    General                 (main.cpp                  : 148)   Clustering Hamming graph.
  0:04:28.331  5050M / 15G   INFO    General                 (main.cpp                  : 155)   Extracting clusters:
  0:04:28.331  5050M / 15G   INFO    General                 (concurrent_dsu.cpp        :  18)   Connecting to root
  0:04:28.803  5050M / 15G   INFO    General                 (concurrent_dsu.cpp        :  34)   Calculating counts
  0:04:51.651  9402M / 15G   INFO    General                 (concurrent_dsu.cpp        :  63)   Writing down entries
  0:05:26.662  5050M / 15G   INFO    General                 (main.cpp                  : 167)   Clustering done. Total clusters: 151529415
  0:05:26.666  2641M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 371)   Collecting K-mer information, this takes a while.
  0:05:27.370    10G / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 377)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507995.anqdpht.fastq.gz
  0:07:32.351    10G / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 384)   Collection done, postprocessing.
  0:07:32.795    10G / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 397)   There are 315416096 kmers in total. Among them 192051780 (60.8884%) are singletons.
  0:07:32.801    10G / 15G   INFO    General                 (main.cpp                  : 173)   Subclustering Hamming graph
  0:11:32.244    10G / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 650)   Subclustering done. Total 11037 non-read kmers were generated.
  0:11:32.255    10G / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 651)   Subclustering statistics:
  0:11:32.255    10G / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 652)     Total singleton hamming clusters: 99913948. Among them 39915918 (39.9503%) are good
  0:11:32.256    10G / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 653)     Total singleton subclusters: 6167563. Among them 6156512 (99.8208%) are good
  0:11:32.256    10G / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 654)     Total non-singleton subcluster centers: 55317426. Among them 40619562 (73.43%) are good
  0:11:32.256    10G / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 655)     Average size of non-trivial subcluster: 3.89638 kmers
  0:11:32.256    10G / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 656)     Average number of sub-clusters per non-singleton cluster: 1.19121
  0:11:32.256    10G / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 657)     Total solid k-mers: 86691992
  0:11:32.256    10G / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 658)     Substitution probabilities: [4,4]((0.928827,0.0331043,0.0327186,0.00535021),(0.0255667,0.935889,0.00909738,0.0294468),(0.0284363,0.00881906,0.93637,0.0263751),(0.00516208,0.0330313,0.0330295,0.928777))
  0:11:32.407    10G / 15G   INFO    General                 (main.cpp                  : 178)   Finished clustering.
  0:11:32.407    10G / 15G   INFO    General                 (main.cpp                  : 197)   Starting solid k-mers expansion in 16 threads.
  0:12:25.424    10G / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 0 produced 12174879 new k-mers.
  0:13:18.496    10G / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 1 produced 864278 new k-mers.
  0:14:11.499    10G / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 2 produced 50031 new k-mers.
  0:15:05.152    10G / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 3 produced 4096 new k-mers.
  0:15:58.888    10G / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 4 produced 382 new k-mers.
  0:16:54.316    10G / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 5 produced 157 new k-mers.
  0:17:48.525    10G / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 6 produced 20 new k-mers.
  0:18:42.258    10G / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 7 produced 0 new k-mers.
  0:18:42.258    10G / 15G   INFO    General                 (main.cpp                  : 222)   Solid k-mers finalized
  0:18:42.259    10G / 15G   INFO    General                 (hammer_tools.cpp          : 222)   Starting read correction in 16 threads.
  0:18:42.259    10G / 15G   INFO    General                 (hammer_tools.cpp          : 266)   Correcting single reads: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507995.anqdpht.fastq.gz
  0:18:44.489    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 0 of 1600000 reads.
  0:18:48.082    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 0
  0:18:49.323    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 0
  0:18:51.543    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 1 of 1600000 reads.
  0:18:55.015    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 1
  0:18:56.175    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 1
  0:18:58.352    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 2 of 1600000 reads.
  0:19:01.956    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 2
  0:19:03.145    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 2
  0:19:05.396    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 3 of 1600000 reads.
  0:19:10.884    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 3
  0:19:12.066    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 3
  0:19:14.357    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 4 of 1600000 reads.
  0:19:21.231    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 4
  0:19:22.380    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 4
  0:19:24.640    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 5 of 1600000 reads.
  0:19:28.590    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 5
  0:19:29.895    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 5
  0:19:32.169    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 6 of 1600000 reads.
  0:19:36.243    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 6
  0:19:37.436    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 6
  0:19:39.697    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 7 of 1600000 reads.
  0:19:43.641    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 7
  0:19:44.818    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 7
  0:19:47.076    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 8 of 1600000 reads.
  0:19:50.257    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 8
  0:19:51.502    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 8
  0:19:53.769    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 9 of 1600000 reads.
  0:19:57.081    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 9
  0:19:58.279    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 9
  0:20:00.543    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 10 of 1600000 reads.
  0:20:03.430    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 10
  0:20:04.612    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 10
  0:20:06.873    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 11 of 1600000 reads.
  0:20:09.915    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 11
  0:20:11.067    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 11
  0:20:13.333    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 12 of 1600000 reads.
  0:20:16.845    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 12
  0:20:18.062    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 12
  0:20:20.360    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 13 of 1600000 reads.
  0:20:28.556    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 13
  0:20:29.842    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 13
  0:20:32.284    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 14 of 1600000 reads.
  0:21:00.448    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 14
  0:21:01.595    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 14
  0:21:01.606    11G / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 15 of 5734 reads.
  0:21:01.678    11G / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 15
  0:21:01.683    11G / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 15
  0:21:10.511    10G / 15G   INFO    General                 (hammer_tools.cpp          : 276)   Correction done. Changed 7098757 bases in 3047347 reads.
  0:21:10.519    10G / 15G   INFO    General                 (hammer_tools.cpp          : 277)   Failed to correct 0 bases out of 3504882753.
  0:21:10.532     1M / 15G   INFO    General                 (main.cpp                  : 255)   Saving corrected dataset description to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/corrected/corrected.yaml
  0:21:10.540     1M / 15G   INFO    General                 (main.cpp                  : 262)   All done. Exiting.

===== Read error correction finished. 


===== corrected reads compression started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/compress_all.py --input_file /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/corrected/corrected.yaml --ext_python_modules_home /usr/local/share/spades --max_threads 16 --output_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/corrected --gzip_output

== Compressing corrected reads (with gzip)
== Files to compress: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/corrected/1507995.anqdpht.fastq.00.0_0.cor.fastq']
== Files compression is finished
== Dataset yaml file is updated

===== corrected reads compression finished. 


===== Read error correction finished. 


===== Assembling started. 


===== K21 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K21/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K21/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K21/configs/config.info
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K21/configs/mda_mode.info
  0:00:00.002     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/dataset.info) with K=21
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.002     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.002     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.002     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.004     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.004     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.006     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.007     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.021     1M / 35M   INFO    General                 (read_converter.cpp        :  72)   Converting reads to binary format for library #0 (takes a while)
  0:00:00.021     1M / 35M   INFO    General                 (read_converter.cpp        :  73)   Converting paired reads
  0:00:00.028    23M / 35M   INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:00.028     1M / 35M   INFO    General                 (read_converter.cpp        :  86)   Converting single reads
  0:00:00.297    33M / 44M   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:00:00.307    36M / 50M   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:00:00.316    41M / 60M   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:00:00.450    35M / 92M   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:00:00.621    40M / 109M  INFO    General                 (binary_converter.cpp      :  96)   262144 reads processed
  0:00:01.035    34M / 135M  INFO    General                 (binary_converter.cpp      :  96)   524288 reads processed
  0:00:01.743    38M / 144M  INFO    General                 (binary_converter.cpp      :  96)   1048576 reads processed
  0:00:03.362    45M / 230M  INFO    General                 (binary_converter.cpp      :  96)   2097152 reads processed
  0:00:06.268    45M / 292M  INFO    General                 (binary_converter.cpp      :  96)   4194304 reads processed
  0:00:12.957    44M / 331M  INFO    General                 (binary_converter.cpp      :  96)   8388608 reads processed
  0:00:26.113    42M / 352M  INFO    General                 (binary_converter.cpp      :  96)   16777216 reads processed
  0:00:37.893    31M / 352M  INFO    General                 (binary_converter.cpp      : 111)   23970351 reads written
  0:00:37.899    21M / 352M  INFO    General                 (read_converter.cpp        :  92)   Converting merged reads
  0:00:37.928    31M / 352M  INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:38.992     1M / 352M  INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:39.204     1M / 352M  INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:39.204     1M / 352M  INFO    General                 (construction.cpp          : 159)   Average read length 146.055
  0:00:39.204     1M / 352M  INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:39.204     1M / 352M  INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:39.210     1M / 352M  INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:39.210     1M / 352M  INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:39.210     1M / 352M  INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:00:49.970  9601M / 9601M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 10799741 reads
  0:00:54.895  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 20939978 reads
  0:00:59.464  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 30737346 reads
  0:01:04.055  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 40649551 reads
  0:01:07.482  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 44618342 reads
  0:01:11.255  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 46276660 reads
  0:01:15.523  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 46999734 reads
  0:01:19.608  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 47573426 reads
  0:01:23.532  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 47940702 reads
  0:01:23.535     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 47940702 reads
  0:01:23.548     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:27.478     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 83187028 kmers in total.
  0:01:27.489     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:01:27.878     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:01:27.878     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:01:27.884     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:01:27.884     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:01:27.885     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:01:39.426  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 83187028 kmers
  0:01:39.437  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 83187028 kmers.
  0:01:39.449     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:43.912     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 81121021 kmers in total.
  0:01:43.921     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:46.902    60M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 81121021 kmers, 58708256 bytes occupied (5.7897 bits per kmer).
  0:01:46.904    60M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:51.359   140M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:54.248   140M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:54.305   140M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:01:54.311   140M / 11G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:01:54.316   140M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:01:57.181   220M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 787581
  0:01:57.214   220M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 798142
  0:01:57.233   140M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   12737219 22-mers were removed by early tip clipper
  0:01:57.233   140M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:57.375   140M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:02:00.465   339M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 5294132 sequences extracted
  0:02:01.866   338M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:02:02.471   339M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 6 loops collected
  0:02:02.586   338M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 10588276 edges to create
  0:02:02.586   748M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:02:06.286   912M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:02:06.349   912M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:02:06.375   944M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 3228131 vertices to create
  0:02:06.375  1246M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:02:09.106   968M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:02:09.106   968M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:02:09.724  1028M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 83187028 kmers, 60201128 bytes occupied (5.78947 bits per kmer).
  0:02:09.753  1348M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:02:32.784  1348M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:02:35.916  1348M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 10586604 edges
  0:02:37.123   945M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:02:38.429   946M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 17
  0:02:38.429   946M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 42.3332
  0:02:38.440   946M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 477
  0:02:38.470   945M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 42.3332
  0:02:38.481   945M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:02:38.487   945M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:02:38.499   945M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:02:38.504   945M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:02:38.515   945M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:02:38.596   945M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 0 times
  0:02:38.606   945M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:02:38.623   945M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:02:38.634   945M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:38.639   945M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:02:38.650   945M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:38.882   945M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 23364 times
  0:02:38.893   945M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:02.382  1025M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 250406 times
  0:03:02.387  1025M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:10.221  1062M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1316856 times
  0:03:10.238  1062M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:03:10.238  1062M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:10.609  1025M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 12267 times
  0:03:10.611  1025M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:16.261   959M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 62484 times
  0:03:16.271   959M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:17.102   961M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 141272 times
  0:03:17.116   961M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:03:17.116   961M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:17.218   950M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 132 times
  0:03:17.218   950M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:18.659   938M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 12627 times
  0:03:18.662   938M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:18.953   936M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 49029 times
  0:03:18.960   936M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:03:18.960   936M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:18.993   934M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 20 times
  0:03:18.993   934M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:19.740   932M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 4004 times
  0:03:19.741   932M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:19.912   933M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 28147 times
  0:03:19.916   933M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:03:19.916   933M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:19.935   931M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 11 times
  0:03:19.935   931M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:20.985   929M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1762 times
  0:03:20.986   929M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:21.131   930M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 21095 times
  0:03:21.134   930M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:03:21.134   930M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:21.146   929M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 10 times
  0:03:21.146   929M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:22.038   928M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 988 times
  0:03:22.038   928M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:22.137   924M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 14990 times
  0:03:22.139   924M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:03:22.139   924M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:22.146   924M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 6 times
  0:03:22.146   924M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:23.007   923M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 654 times
  0:03:23.007   923M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:23.082   924M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 12005 times
  0:03:23.084   924M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:03:23.084   924M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:23.089   923M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:03:23.089   923M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:23.795   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 441 times
  0:03:23.796   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:23.854   923M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 9513 times
  0:03:23.855   923M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:03:23.855   923M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:23.859   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 6 times
  0:03:23.859   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:24.402   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 310 times
  0:03:24.402   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:24.454   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 8305 times
  0:03:24.455   922M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:03:24.455   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:24.458   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5 times
  0:03:24.458   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:24.865   921M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 269 times
  0:03:24.865   921M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:24.906   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 6472 times
  0:03:24.906   922M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:03:24.906   922M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:24.921   921M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 169 times
  0:03:24.921   921M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:25.697   924M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 745 times
  0:03:25.698   924M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:25.710   855M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1 times
  0:03:25.710   855M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:03:25.710   855M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:25.712   855M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:25.712   855M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:25.712   855M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:25.712   855M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:25.712   855M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:03:25.732   844M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:03:25.733   844M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:03:25.733   844M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:03:25.733   844M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:03:25.733   844M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:03:25.733   844M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:03:25.733   844M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:25.897   844M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 3441 times
  0:03:25.897   844M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:26.282   844M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 757 times
  0:03:26.282   844M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:26.294   844M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:26.294   844M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:26.306   844M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 34 times
  0:03:26.306   844M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:26.940   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 45 times
  0:03:26.941   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:27.833   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1328 times
  0:03:27.834   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:27.935   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 25 times
  0:03:27.936   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:28.275   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 2 times
  0:03:28.276   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:28.287   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:28.287   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:28.299   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:28.299   846M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:28.926   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 4 times
  0:03:28.927   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:29.609   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:03:29.609   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:29.746   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:03:29.747   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:30.080   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:03:30.081   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:30.081   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:30.081   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:30.081   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:30.081   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:30.081   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:30.081   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:30.081   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:03:30.081   848M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:03:30.138   848M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:03:30.258   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:03:30.270   848M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 1624 times
  0:03:30.292   843M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:03:30.292   843M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 72.0501
  0:03:30.310   843M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 40337102
  0:03:30.342   843M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 537
  0:03:30.342   843M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 601257
  0:03:30.342   843M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 363204
  0:03:30.343   843M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:03:30.353   843M / 11G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K21/simplified_contigs
  0:03:30.362   846M / 11G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:03:30.365   846M / 11G   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:03:30.373   846M / 11G   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:03:30.390   846M / 11G   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:03:30.416   846M / 11G   INFO    General                 (binary_converter.cpp      :  96)   262144 reads processed
  0:03:30.439   846M / 11G   INFO    General                 (binary_converter.cpp      : 111)   300746 reads written
  0:03:30.500   843M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:03:30.640     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 3 minutes 30 seconds

===== K21 finished. 


===== K33 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K33/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K33/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K33/configs/config.info
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K33/configs/mda_mode.info
  0:00:00.000     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/dataset.info) with K=33
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.002     1M / 35M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.003     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.003     1M / 35M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K21/simplified_contigs
  0:00:00.102     1M / 35M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.102     1M / 35M   INFO    General                 (construction.cpp          : 159)   Average read length 146.055
  0:00:00.102     1M / 35M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.102     1M / 35M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.108     1M / 35M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.108     1M / 35M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.108     1M / 35M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:05.736  9601M / 9646M INFO    General                 (kmer_splitters.hpp        : 125)   Processed 6319243 reads
  0:00:09.261  9601M / 10G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 12022854 reads
  0:00:13.132  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 17601025 reads
  0:00:17.028  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 23460907 reads
  0:00:20.340  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 28956807 reads
  0:00:24.117  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 34554246 reads
  0:00:28.519  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 40519415 reads
  0:00:33.340  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 44276547 reads
  0:00:35.909  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 45478177 reads
  0:00:38.592  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 46540851 reads
  0:00:40.458  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 46998774 reads
  0:00:49.257     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 48542194 reads
  0:00:49.271     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:57.495     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 103680406 kmers in total.
  0:00:57.507     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:58.111     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:58.122     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:58.137     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:58.153     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:58.169     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:01:09.644  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 54676269 kmers
  0:01:16.852  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 103680486 kmers
  0:01:16.863  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 103680486 kmers.
  0:01:16.875     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:22.283     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 102234508 kmers in total.
  0:01:22.294     2M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:24.354    76M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 102234508 kmers, 73960320 bytes occupied (5.7875 bits per kmer).
  0:01:24.354    76M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:27.661   176M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:31.510   176M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:31.630   176M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:01:31.641   176M / 11G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:01:31.646   176M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:01:35.089   256M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 1079441
  0:01:35.134   256M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 1096102
  0:01:35.173   176M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   21150901 34-mers were removed by early tip clipper
  0:01:35.173   176M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:35.273   176M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:39.092   371M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 4135320 sequences extracted
  0:01:40.806   371M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:41.592   371M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 10 loops collected
  0:01:41.704   371M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 8270660 edges to create
  0:01:41.705   692M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:44.558   821M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:44.607   821M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:44.626   853M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 2689432 vertices to create
  0:01:44.626  1102M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:46.936   878M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:46.937   878M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:47.717   953M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 103680406 kmers, 75002704 bytes occupied (5.78722 bits per kmer).
  0:01:47.754  1349M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:02:09.297  1349M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:02:12.441  1349M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 8269597 edges
  0:02:14.186   795M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:02:15.052   796M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 14
  0:02:15.052   796M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 32.5112
  0:02:15.052   796M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 335
  0:02:15.072   795M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 32.5112
  0:02:15.072   795M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:02:15.072   795M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:02:15.072   795M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:02:15.072   795M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:02:15.072   795M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:02:15.123   795M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 65 times
  0:02:15.123   795M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:02:15.123   795M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:02:15.123   795M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:15.123   795M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:02:15.123   795M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:15.375   793M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 54433 times
  0:02:15.376   793M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:26.375   829M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 227132 times
  0:02:26.380   829M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:32.382   850M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 911128 times
  0:02:32.399   850M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:02:32.399   850M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:32.733   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 14653 times
  0:02:32.735   813M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:36.673   759M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 55606 times
  0:02:36.682   759M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:37.560   752M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 125145 times
  0:02:37.573   752M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:02:37.573   752M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:37.637   748M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 221 times
  0:02:37.638   748M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:38.752   735M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 15031 times
  0:02:38.755   735M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:39.025   733M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 34601 times
  0:02:39.031   733M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:02:39.031   733M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:39.062   730M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 44 times
  0:02:39.062   730M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:39.828   727M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 4364 times
  0:02:39.829   727M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:39.957   726M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 15741 times
  0:02:39.959   726M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:02:39.959   726M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:39.973   725M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 17 times
  0:02:39.973   725M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.431   724M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1716 times
  0:02:40.431   724M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:40.498   724M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 8904 times
  0:02:40.500   724M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:02:40.500   724M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:40.506   724M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:02:40.506   724M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.755   723M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 751 times
  0:02:40.755   723M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:40.811   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 6462 times
  0:02:40.812   721M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:02:40.812   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:40.817   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 6 times
  0:02:40.817   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:40.986   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 445 times
  0:02:40.987   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:41.023   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 4795 times
  0:02:41.024   721M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:02:41.024   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.027   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 4 times
  0:02:41.027   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.157   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 299 times
  0:02:41.157   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:41.188   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3987 times
  0:02:41.189   721M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:02:41.189   721M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.191   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:02:41.191   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.285   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 221 times
  0:02:41.285   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:41.310   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2987 times
  0:02:41.310   720M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:02:41.311   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.312   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3 times
  0:02:41.312   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.377   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 145 times
  0:02:41.378   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:41.398   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2443 times
  0:02:41.398   720M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:02:41.398   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.409   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 160 times
  0:02:41.409   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.647   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 413 times
  0:02:41.647   720M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:41.656   684M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1 times
  0:02:41.656   684M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:02:41.656   684M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:41.657   684M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:41.657   684M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:41.657   684M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:41.657   684M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:02:41.657   684M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:02:41.674   679M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:02:41.674   679M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:02:41.674   679M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:02:41.674   679M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:02:41.674   679M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:41.674   679M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:41.674   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:41.767   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 2758 times
  0:02:41.768   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:42.082   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 1287 times
  0:02:42.082   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:42.090   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:42.090   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:42.099   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 24 times
  0:02:42.099   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:42.296   680M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 22 times
  0:02:42.296   680M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:42.551   680M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 970 times
  0:02:42.551   680M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:42.598   680M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 21 times
  0:02:42.598   680M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:42.860   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 8 times
  0:02:42.860   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:42.868   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:42.868   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:42.876   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:42.876   679M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:43.127   680M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 3 times
  0:02:43.127   680M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:43.380   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1 times
  0:02:43.380   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:43.446   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1 times
  0:02:43.446   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:43.710   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:02:43.710   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:43.710   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:43.711   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:43.711   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:43.711   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:43.711   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:43.711   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:43.711   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1 times
  0:02:43.711   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:02:43.778   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:02:43.778   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:02:44.035   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:02:44.035   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:44.035   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:02:44.036   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:02:44.036   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:02:44.036   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:02:44.036   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:02:44.036   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:02:44.036   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:02:44.036   681M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:02:44.134   681M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:02:44.198   681M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:02:44.227   680M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 25441 times
  0:02:44.238   677M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:02:44.238   677M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 61.6095
  0:02:44.248   677M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 42286244
  0:02:44.264   677M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 1657
  0:02:44.264   677M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 264505
  0:02:44.264   677M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 181248
  0:02:44.264   677M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:02:44.272   677M / 11G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K33/simplified_contigs
  0:02:44.286   680M / 11G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:02:44.288   680M / 11G   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:02:44.295   680M / 11G   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:02:44.315   680M / 11G   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:02:44.326   680M / 11G   INFO    General                 (binary_converter.cpp      : 111)   132277 reads written
  0:02:44.384   677M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:02:44.452     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 2 minutes 44 seconds

===== K33 finished. 


===== K55 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/configs/config.info
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/configs/mda_mode.info
  0:00:00.000     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/dataset.info) with K=55
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 242)   Will need read mapping, kmer mapper will be attached
  0:00:00.001     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.004     1M / 35M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.004     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.004     1M / 35M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K33/simplified_contigs
  0:00:00.127     1M / 35M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.127     1M / 35M   INFO    General                 (construction.cpp          : 159)   Average read length 146.055
  0:00:00.127     1M / 35M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.127     1M / 35M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.132     1M / 35M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.132     1M / 35M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.133     1M / 35M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:05.983  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 8014559 reads
  0:00:10.214  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 15428333 reads
  0:00:14.122  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 23241077 reads
  0:00:18.843  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 30365532 reads
  0:00:23.600  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 37924769 reads
  0:00:29.102  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 44180490 reads
  0:00:32.674  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 45753618 reads
  0:00:35.243  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 46552026 reads
  0:00:37.158  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 46952376 reads
  0:00:38.976  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 47436433 reads
  0:00:40.906  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 47831766 reads
  0:00:43.867     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 48205256 reads
  0:00:43.880     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:52.544     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 125518194 kmers in total.
  0:00:52.550     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:53.326     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:53.337     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:53.355     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:53.364     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:53.365     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:59.108  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 12240570 kmers
  0:01:02.724  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 24788925 kmers
  0:01:06.521  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 37351835 kmers
  0:01:10.143  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 49917371 kmers
  0:01:13.530  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 62470925 kmers
  0:01:17.198  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 75025739 kmers
  0:01:20.723  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 87564706 kmers
  0:01:24.306  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 100100558 kmers
  0:01:28.089  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 112648723 kmers
  0:01:31.620  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 125205236 kmers
  0:01:32.855  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 125519074 kmers
  0:01:32.866  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 125519074 kmers.
  0:01:32.873     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:39.478     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 124884677 kmers in total.
  0:01:39.489     2M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:42.488    91M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 124884677 kmers, 90317760 bytes occupied (5.78567 bits per kmer).
  0:01:42.502    91M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:48.071   211M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:53.021   211M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:53.160   211M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:53.275   211M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:58.973   474M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 5250986 sequences extracted
  0:02:01.515   474M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:02:02.452   474M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 0 loops collected
  0:02:02.643   474M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 10501972 edges to create
  0:02:02.644   880M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:02:06.235  1044M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:02:06.301  1044M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:02:06.344  1108M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 4617469 vertices to create
  0:02:06.344  1538M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:02:10.210  1226M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:02:10.210  1226M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:02:11.259  1316M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 125518194 kmers, 90774912 bytes occupied (5.78561 bits per kmer).
  0:02:11.304  1796M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:02:29.625  1796M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:02:34.527  1796M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 10501536 edges
  0:02:36.096  1080M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:02:36.811  1081M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 10
  0:02:36.812  1081M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 17.3914
  0:02:36.812  1081M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 242
  0:02:36.824  1080M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 17.3914
  0:02:36.824  1080M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:02:36.825  1080M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: early_gapcloser)
  0:02:36.825  1080M / 11G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:02:36.825  1080M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:02:36.825  1080M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:02:36.825  1080M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:02:36.825  1080M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:02:36.904  1080M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 19 times
  0:02:36.904  1080M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:02:36.904  1080M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:02:36.904  1080M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:02:36.904  1080M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:02:36.904  1080M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:02:42.537   990M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1548932 times
  0:02:42.552   990M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:01.832  1870M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 147026 times
  0:03:01.840  1870M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:04.124  1896M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 308004 times
  0:03:04.131  1896M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:03:04.132  1896M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:04.417  1877M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 24616 times
  0:03:04.421  1877M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:11.580  2032M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 29202 times
  0:03:11.582  2032M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:12.566  2031M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 130382 times
  0:03:12.569  2031M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:03:12.569  2031M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:12.648  2026M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2564 times
  0:03:12.650  2026M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:16.993  2143M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 19411 times
  0:03:16.995  2143M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:17.359  2141M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 42017 times
  0:03:17.360  2141M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:03:17.361  2141M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:17.398  2136M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 348 times
  0:03:17.400  2136M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:18.740  2174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 6215 times
  0:03:18.742  2174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:18.886  2174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 15802 times
  0:03:18.888  2174M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:03:18.889  2174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:18.905  2173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 78 times
  0:03:18.907  2173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:19.470  2186M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2306 times
  0:03:19.471  2186M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:19.573  2185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 9979 times
  0:03:19.575  2185M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:03:19.576  2185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:19.587  2185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 43 times
  0:03:19.588  2185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:19.877  2192M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1216 times
  0:03:19.878  2192M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:19.933  2192M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 5328 times
  0:03:19.935  2192M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:03:19.936  2192M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:19.941  2191M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 32 times
  0:03:19.943  2191M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:20.078  2195M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 585 times
  0:03:20.080  2195M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:20.118  2194M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3366 times
  0:03:20.120  2194M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:03:20.121  2194M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:20.125  2194M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 13 times
  0:03:20.127  2194M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:20.204  2195M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 333 times
  0:03:20.205  2195M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:20.228  2195M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2371 times
  0:03:20.230  2195M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:03:20.231  2195M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:20.234  2195M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 7 times
  0:03:20.235  2195M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:20.284  2196M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 198 times
  0:03:20.285  2196M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:20.304  2196M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1888 times
  0:03:20.306  2196M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:03:20.307  2196M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:20.309  2196M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 7 times
  0:03:20.311  2196M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:20.348  2197M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 159 times
  0:03:20.349  2197M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:20.364  2197M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1417 times
  0:03:20.365  2197M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:03:20.367  2197M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:20.395  2197M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1022 times
  0:03:20.397  2197M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:20.518  2199M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 230 times
  0:03:20.520  2199M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:20.536  2182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:03:20.537  2182M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:03:20.538  2182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:20.540  2182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:20.541  2182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:20.542  2182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:20.543  2182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:03:20.544  2182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:03:20.580  2178M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: late_gapcloser)
  0:03:20.582  2178M / 11G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:03:20.583  2178M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:03:20.584  2178M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:03:20.585  2178M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:03:20.586  2178M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:03:20.587  2178M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:03:20.588  2178M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:03:20.589  2178M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:20.590  2178M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:20.591  2178M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:20.890  2178M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:21.161  2178M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:29.113  2178M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:29.421  2178M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:29.423  2178M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:29.441  2178M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 10 times
  0:03:29.442  2178M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:29.490  2178M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1316 times
  0:03:29.491  2178M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:31.250  2235M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 3011 times
  0:03:31.251  2235M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:31.269  2235M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 12 times
  0:03:31.271  2235M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:31.289  2235M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 72 times
  0:03:31.290  2235M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:31.390  2236M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 49 times
  0:03:31.392  2236M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:31.691  2245M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 817 times
  0:03:31.692  2245M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:31.693  2245M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:31.695  2245M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:31.937  2245M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:32.167  2245M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:37.111  2246M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:37.400  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:37.402  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:37.420  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 3 times
  0:03:37.422  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:37.451  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 12 times
  0:03:37.452  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:38.017  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 12 times
  0:03:38.018  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:38.035  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:38.037  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:38.054  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:38.055  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:38.140  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 6 times
  0:03:38.142  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:38.225  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 8 times
  0:03:38.227  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:38.228  2246M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:38.229  2246M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:38.470  2246M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:38.701  2246M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:43.109  2246M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:43.404  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:43.406  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:43.424  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 1 times
  0:03:43.426  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:43.460  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:03:43.462  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:44.045  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:03:44.047  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:44.048  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:44.050  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:44.051  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:44.052  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:44.053  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:44.055  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:44.056  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1 times
  0:03:44.057  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:44.059  2246M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:44.060  2246M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:44.295  2246M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:44.528  2246M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:48.906  2246M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:49.199  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:03:49.202  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:49.219  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:03:49.221  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:49.256  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:03:49.257  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:49.853  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:03:49.855  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:49.856  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:49.858  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:49.859  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:49.860  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:49.861  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:49.862  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:49.864  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:03:49.865  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:03:49.866  2246M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:03:49.867  2246M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:03:50.102  2246M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:03:50.333  2246M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:03:54.701  2246M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:03:54.993  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 0 times
  0:03:54.996  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:03:55.013  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:03:55.015  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:03:55.049  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:03:55.051  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:03:55.633  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:03:55.635  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:03:55.637  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:03:55.638  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:03:55.639  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:03:55.640  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:03:55.641  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:03:55.642  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:03:55.644  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:03:55.645  2246M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:03:55.723  2246M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:03:55.833  2246M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:03:55.967  2242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 124285 times
  0:03:55.981  2240M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:03:55.982  2240M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 47.0009
  0:03:55.994  2240M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 43675197
  0:03:56.009  2240M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 2706
  0:03:56.010  2240M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 158583
  0:03:56.012  2240M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 120486
  0:03:56.013  2240M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Mismatch Correction (id: mismatch_correction)
  0:03:56.014  2240M / 11G   INFO    General                 (graph_pack.cpp            :  67)   Index refill
  0:03:56.015  2240M / 11G   INFO    General                 (edge_index.hpp            : 156)   Using small index (max_id = 10554484)
  0:03:56.098  2240M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:03:56.986  2271M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 43667023 kmers, 31539840 bytes occupied (5.77824 bits per kmer).
  0:03:57.019  2607M / 11G   INFO    General                 (edge_index_builders.hpp   : 252)   Collecting edge information from graph, this takes a while.
  0:03:57.591  2607M / 11G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 27860556 kmers to process
  0:04:01.636  2607M / 11G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:04:01.639  2607M / 11G   INFO    General                 (mismatch_correction.cpp   : 400)   Collect potential mismatches
  0:04:04.417  2626M / 11G   INFO    General                 (mismatch_correction.cpp   : 192)   Total 83550 edges (out of 158583) with 2752950 potential mismatch positions (32.9497 positions per edge)
  0:04:04.425  2626M / 11G   INFO    General                 (mismatch_correction.cpp   : 402)   Potential mismatches collected
  0:04:05.356  2638M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:04:05.657  2641M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:04:05.715  2645M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:04:05.878  2648M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:04:05.926  2649M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:04:05.963  2652M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:04:06.027  2654M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:04:07.437  2659M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:04:09.209  2668M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8400000 reads
  0:04:13.436  2683M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 16800000 reads
  0:04:21.360  2697M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 33600000 reads
  0:04:47.383  3334M / 11G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 47940702 reads processed
  0:04:50.343  2829M / 11G   INFO    General                 (mismatch_correction.cpp   : 395)   All edges processed
  0:04:50.384  2611M / 11G   INFO    General                 (mismatch_correction.cpp   : 450)   Corrected 1068 nucleotides
  0:04:50.397  2611M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:04:50.402  2611M / 11G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/before_rr.fasta
  0:04:50.899  2611M / 11G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/assembly_graph_after_simplification.gfa
  0:04:51.220  2611M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Paired Information Counting (id: late_pair_info_count)
  0:04:51.386  2641M / 11G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 27905502 kmers to process
  0:04:55.680  2641M / 11G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:04:55.709  2641M / 11G   INFO    General                 (pair_info_count.cpp       : 339)   Min edge length for estimation: 2706
  0:04:55.714  2641M / 11G   INFO    General                 (pair_info_count.cpp       : 411)   Mapping single reads of library #0
  0:04:55.725  2641M / 11G   INFO    General                 (pair_info_count.cpp       :  48)   Selecting usual mapper
  0:04:56.923  2642M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:04:57.182  2644M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:04:57.294  2645M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:04:57.304  2645M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:04:57.347  2646M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:04:57.350  2649M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:04:57.523  2650M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:04:58.872  2650M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:05:00.784  2651M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8400000 reads
  0:05:05.177  2653M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 16800000 reads
  0:05:21.130  2717M / 11G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 23970351 reads processed
  0:05:21.135  2716M / 11G   INFO    General                 (pair_info_count.cpp       : 413)   Total paths obtained from single reads: 485401
  0:05:21.136  2716M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Distance Estimation (id: distance_estimation)
  0:05:21.137  2716M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Repeat Resolving (id: repeat_resolving)
  0:05:21.138  2716M / 11G   INFO    General                 (repeat_resolving.cpp      :  87)   Using Path-Extend repeat resolving
  0:05:21.142  2716M / 11G   INFO    General                 (launcher.cpp              : 600)   ExSPAnder repeat resolving tool started
  0:05:21.302  3035M / 11G   INFO    General                 (launcher.cpp              : 418)   Creating main extenders, unique edge length = 2000
  0:05:21.304  3045M / 11G   INFO    General                 (launcher.cpp              : 342)   filling path container
  0:05:22.231  4936M / 11G   INFO    General                 (extenders_logic.cpp       :  47)   resolvable_repeat_length_bound set to 10000
  0:05:24.461  4950M / 11G   INFO    General                 (extenders_logic.cpp       : 543)   Using 0 paired-end libraries
  0:05:24.464  4950M / 11G   INFO    General                 (extenders_logic.cpp       : 544)   Using 0 paired-end scaffolding libraries
  0:05:24.467  4950M / 11G   INFO    General                 (extenders_logic.cpp       : 545)   Using 1 single read library
  0:05:24.470  4950M / 11G   INFO    General                 (launcher.cpp              : 447)   Total number of extenders is 1
  0:05:24.473  4950M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 0 paths from 78464 (0%)
  0:05:25.610  4950M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 128 paths from 78464 (0%)
  0:05:26.884  4951M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 256 paths from 78464 (0%)
  0:05:30.931  4953M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 512 paths from 78464 (0%)
  0:05:33.277  4955M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 1024 paths from 78464 (1%)
  0:05:35.496  4959M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 2048 paths from 78464 (2%)
  0:05:39.101  4968M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 4096 paths from 78464 (5%)
  0:05:43.004  4984M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 7847 paths from 78464 (10%)
  0:05:43.799  4985M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 8192 paths from 78464 (10%)
  0:05:50.363  5016M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 15694 paths from 78464 (20%)
  0:05:51.101  5019M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 16384 paths from 78464 (20%)
  0:05:54.859  5053M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 23541 paths from 78464 (30%)
  0:05:58.648  5090M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 31388 paths from 78464 (40%)
  0:05:58.902  5097M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 32768 paths from 78464 (41%)
  0:06:01.466  5130M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 39235 paths from 78464 (50%)
  0:06:10.703  5169M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 47082 paths from 78464 (60%)
  0:06:24.085  5203M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 54929 paths from 78464 (70%)
  0:06:34.807  5223M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 62776 paths from 78464 (80%)
  0:06:35.619  5225M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 65536 paths from 78464 (83%)
  0:06:37.197  5228M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 70623 paths from 78464 (90%)
  0:06:38.586  5233M / 11G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:06:38.590  5233M / 11G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:06:38.712  5184M / 11G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:06:38.716  5184M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:06:38.719  5184M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:06:38.741  5184M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:06:38.744  5184M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:06:38.926  5189M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:06:39.123  5191M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:06:39.269  5410M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:06:39.490  5172M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:06:39.559  5164M / 11G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:06:39.566  5164M / 11G   INFO    General                 (launcher.cpp              : 454)   Closing gaps in paths
  0:06:39.680  5358M / 11G   INFO    General                 (launcher.cpp              : 484)   Gap closing completed
  0:06:39.710  5372M / 11G   INFO    General                 (launcher.cpp              : 302)   Traversing tandem repeats
  0:06:39.906  5373M / 11G   INFO    General                 (launcher.cpp              : 312)   Traversed 1624 loops
  0:06:39.910  5373M / 11G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:06:39.914  5373M / 11G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:06:39.944  5365M / 11G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:06:39.950  5365M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:06:39.954  5365M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:06:39.981  5365M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:06:39.985  5365M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:06:40.043  5365M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:06:40.101  5365M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:06:40.123  5388M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:06:40.161  5367M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:06:40.216  5369M / 11G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:06:40.220  5369M / 11G   INFO    General                 (launcher.cpp              : 664)   ExSPAnder repeat resolving tool finished
  0:06:40.539  2908M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:06:40.577  2908M / 11G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/before_rr.fasta
  0:06:41.071  2908M / 11G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/assembly_graph_with_scaffolds.gfa
  0:06:41.299  2908M / 11G   INFO    General                 (contig_output_stage.cpp   : 165)   Outputting FastG graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/assembly_graph.fastg
  0:06:42.196  2908M / 11G   INFO    General                 (contig_output_stage.cpp   : 196)   Breaking scaffolds
  0:06:42.501  3187M / 11G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/final_contigs.fasta
  0:06:42.710  3187M / 11G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/final_contigs.paths
  0:06:42.994  2964M / 11G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/scaffolds.fasta
  0:06:43.229  2964M / 11G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/scaffolds.paths
  0:06:43.309  2964M / 11G   INFO    General                 (contig_output_stage.cpp   : 111)   Populating GFA with scaffold paths
  0:06:43.470  2908M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:06:45.658     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 6 minutes 45 seconds

===== K55 finished. 


===== Copy files started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/copy_files.py /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/final_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/K55/final_contigs.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/contigs.paths


===== Copy files finished. 


===== Assembling finished. 


===== Breaking scaffolds started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/breaking_scaffolds_script.py --result_scaffolds_filename /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/scaffolds.fasta --misc_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/misc --threshold_for_breaking_scaffolds 3


===== Breaking scaffolds finished. 


===== Terminate started. 


===== Terminate finished. 

 * Corrected reads are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/corrected/
 * Assembled contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/contigs.fasta
 * Assembled scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/scaffolds.fasta
 * Paths in the assembly graph corresponding to the contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/contigs.paths
 * Paths in the assembly graph corresponding to the scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/scaffolds.paths
 * Assembly graph is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/assembly_graph.fastg
 * Assembly graph in GFA format is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/assembly_graph_with_scaffolds.gfa

======= SPAdes pipeline finished.

SPAdes log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507995/spades.log

Thank you for using SPAdes!
