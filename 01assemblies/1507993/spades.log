Command line: /usr/local/bin/spades.py	-s	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507993.anqdpht.fastq.gz	--sc	-o	/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993	-t	16	-m	128	

System information:
  SPAdes version: 3.15.5
  Python version: 3.10.6
  OS: Linux-4.18.0-553.5.1.el8_10.x86_64-x86_64-with-glibc2.28

Output dir: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993
Mode: read error correction and assembling
Debug mode is turned OFF

Dataset parameters:
  Single-cell mode
  Reads:
    Library number: 1, library type: single
      left reads: not specified
      right reads: not specified
      interlaced reads: not specified
      single reads: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507993.anqdpht.fastq.gz']
      merged reads: not specified
Read error correction parameters:
  Iterations: 1
  PHRED offset will be auto-detected
  Corrected reads will be compressed
Assembly parameters:
  k: [21, 33, 55]
  Repeat resolution is enabled
  Mismatch careful mode is turned OFF
  MismatchCorrector will be SKIPPED
  Coverage cutoff is turned OFF
Other parameters:
  Dir for temp files: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/tmp
  Threads: 16
  Memory limit (in Gb): 128


======= SPAdes pipeline started. Log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/spades.log

/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507993.anqdpht.fastq.gz: max reads length: 150

Reads length: 150


===== Before start started. 


===== Read error correction started. 


===== Read error correction started. 


== Running: /usr/local/bin/spades-hammer /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/corrected/configs/config.info

  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  75)   Starting BayesHammer, built from N/A, git revision N/A
  0:00:00.000     1M / 33M   INFO    General                 (main.cpp                  :  76)   Loading config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/corrected/configs/config.info
  0:00:00.005     1M / 33M   INFO    General                 (main.cpp                  :  78)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.005     1M / 33M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.005     1M / 33M   INFO    General                 (main.cpp                  :  86)   Trying to determine PHRED offset
  0:00:00.007     1M / 33M   INFO    General                 (main.cpp                  :  92)   Determined value is 33
  0:00:00.007     1M / 33M   INFO    General                 (hammer_tools.cpp          :  38)   Hamming graph threshold tau=1, k=21, subkmer positions = [ 0 10 ]
  0:00:00.007     1M / 33M   INFO    General                 (main.cpp                  : 113)   Size of aux. kmer data 24 bytes
     === ITERATION 0 begins ===
  0:00:00.020     1M / 33M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 16 files using 16 threads. This might take a while.
  0:00:00.041     1M / 33M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.041     1M / 33M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66666 Gb
  0:00:00.041     1M / 33M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 4194304
  0:00:00.079  9217M / 9217M INFO   K-mer Splitting          (kmer_data.cpp             :  97)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507993.anqdpht.fastq.gz
  0:00:17.659  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 3659461 reads
  0:00:34.827  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 7319914 reads
  0:00:40.716  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 107)   Processed 8076352 reads
  0:00:40.716  9217M / 15G   INFO   K-mer Splitting          (kmer_data.cpp             : 112)   Total 8076352 reads processed
  0:00:40.716     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:51.140     1M / 15G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 157175320 kmers in total.
  0:00:51.199     1M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:59.838   115M / 15G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 157175320 kmers, 113533688 bytes occupied (5.7787 bits per kmer).
  0:00:59.844   115M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 354)   Arranging kmers in hash map order
  0:01:05.969  2515M / 15G   INFO    General                 (main.cpp                  : 148)   Clustering Hamming graph.
  0:02:07.437  2515M / 15G   INFO    General                 (main.cpp                  : 155)   Extracting clusters:
  0:02:07.437  2515M / 15G   INFO    General                 (concurrent_dsu.cpp        :  18)   Connecting to root
  0:02:07.645  2515M / 15G   INFO    General                 (concurrent_dsu.cpp        :  34)   Calculating counts
  0:02:20.650  4692M / 15G   INFO    General                 (concurrent_dsu.cpp        :  63)   Writing down entries
  0:02:39.890  2515M / 15G   INFO    General                 (main.cpp                  : 167)   Clustering done. Total clusters: 86489841
  0:02:39.894  1315M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 371)   Collecting K-mer information, this takes a while.
  0:02:40.247  4915M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 377)   Processing /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507993.anqdpht.fastq.gz
  0:03:19.690  4915M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 384)   Collection done, postprocessing.
  0:03:19.925  4915M / 15G   INFO   K-mer Counting           (kmer_data.cpp             : 397)   There are 157175320 kmers in total. Among them 84862466 (53.9922%) are singletons.
  0:03:19.925  4915M / 15G   INFO    General                 (main.cpp                  : 173)   Subclustering Hamming graph
  0:04:35.256  4916M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 650)   Subclustering done. Total 2876 non-read kmers were generated.
  0:04:35.262  4916M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 651)   Subclustering statistics:
  0:04:35.273  4916M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 652)     Total singleton hamming clusters: 60301504. Among them 37607764 (62.3662%) are good
  0:04:35.289  4916M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 653)     Total singleton subclusters: 1546369. Among them 1544926 (99.9067%) are good
  0:04:35.300  4916M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 654)     Total non-singleton subcluster centers: 27157420. Among them 20005024 (73.6632%) are good
  0:04:35.310  4916M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 655)     Average size of non-trivial subcluster: 3.56748 kmers
  0:04:35.321  4916M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 656)     Average number of sub-clusters per non-singleton cluster: 1.09605
  0:04:35.332  4916M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 657)     Total solid k-mers: 59157714
  0:04:35.343  4916M / 15G   INFO   Hamming Subclustering    (kmer_cluster.cpp          : 658)     Substitution probabilities: [4,4]((0.939283,0.0284424,0.0289643,0.0033099),(0.0233695,0.942503,0.00645587,0.0276716),(0.0270496,0.00627579,0.942766,0.0239084),(0.00317446,0.0284869,0.0280519,0.940287))
  0:04:35.453  4916M / 15G   INFO    General                 (main.cpp                  : 178)   Finished clustering.
  0:04:35.464  4916M / 15G   INFO    General                 (main.cpp                  : 197)   Starting solid k-mers expansion in 16 threads.
  0:04:53.689  4916M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 0 produced 3756214 new k-mers.
  0:05:11.772  4916M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 1 produced 195226 new k-mers.
  0:05:29.875  4916M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 2 produced 7495 new k-mers.
  0:05:47.974  4916M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 3 produced 450 new k-mers.
  0:06:06.149  4916M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 4 produced 143 new k-mers.
  0:06:24.321  4916M / 15G   INFO    General                 (main.cpp                  : 218)   Solid k-mers iteration 5 produced 0 new k-mers.
  0:06:24.321  4916M / 15G   INFO    General                 (main.cpp                  : 222)   Solid k-mers finalized
  0:06:24.321  4916M / 15G   INFO    General                 (hammer_tools.cpp          : 222)   Starting read correction in 16 threads.
  0:06:24.321  4916M / 15G   INFO    General                 (hammer_tools.cpp          : 266)   Correcting single reads: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/00data/readsf/1507993.anqdpht.fastq.gz
  0:06:26.907  5697M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 0 of 1600000 reads.
  0:06:30.596  5819M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 0
  0:06:31.817  5819M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 0
  0:06:34.125  5844M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 1 of 1600000 reads.
  0:06:37.127  5844M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 1
  0:06:38.321  5844M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 1
  0:06:40.614  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 2 of 1600000 reads.
  0:06:43.622  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 2
  0:06:44.812  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 2
  0:06:47.121  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 3 of 1600000 reads.
  0:06:50.025  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 3
  0:06:51.225  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 3
  0:06:53.657  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 4 of 1600000 reads.
  0:07:08.025  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 4
  0:07:09.224  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 4
  0:07:09.362  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 129)   Prepared batch 5 of 76352 reads.
  0:07:10.134  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 134)   Processed batch 5
  0:07:10.203  5845M / 15G   INFO    General                 (hammer_tools.cpp          : 138)   Written batch 5
  0:07:12.854  4916M / 15G   INFO    General                 (hammer_tools.cpp          : 276)   Correction done. Changed 2919332 bases in 1410782 reads.
  0:07:12.861  4916M / 15G   INFO    General                 (hammer_tools.cpp          : 277)   Failed to correct 0 bases out of 1204867667.
  0:07:12.868     1M / 15G   INFO    General                 (main.cpp                  : 255)   Saving corrected dataset description to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/corrected/corrected.yaml
  0:07:12.937     1M / 15G   INFO    General                 (main.cpp                  : 262)   All done. Exiting.

===== Read error correction finished. 


===== corrected reads compression started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/compress_all.py --input_file /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/corrected/corrected.yaml --ext_python_modules_home /usr/local/share/spades --max_threads 16 --output_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/corrected --gzip_output

== Compressing corrected reads (with gzip)
== Files to compress: ['/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/corrected/1507993.anqdpht.fastq.00.0_0.cor.fastq']
== Files compression is finished
== Dataset yaml file is updated

===== corrected reads compression finished. 


===== Read error correction finished. 


===== Assembling started. 


===== K21 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K21/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K21/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K21/configs/config.info
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K21/configs/mda_mode.info
  0:00:00.002     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/dataset.info) with K=21
  0:00:00.002     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.002     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.002     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.002     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.004     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.004     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.006     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.007     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.015     1M / 35M   INFO    General                 (read_converter.cpp        :  72)   Converting reads to binary format for library #0 (takes a while)
  0:00:00.020     1M / 35M   INFO    General                 (read_converter.cpp        :  73)   Converting paired reads
  0:00:00.041    23M / 35M   INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:00.050     1M / 35M   INFO    General                 (read_converter.cpp        :  86)   Converting single reads
  0:00:00.271    33M / 43M   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:00:00.280    36M / 51M   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:00:00.294    41M / 57M   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:00:00.452    35M / 91M   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:00:00.639    40M / 110M  INFO    General                 (binary_converter.cpp      :  96)   262144 reads processed
  0:00:01.065    34M / 143M  INFO    General                 (binary_converter.cpp      :  96)   524288 reads processed
  0:00:01.856    38M / 169M  INFO    General                 (binary_converter.cpp      :  96)   1048576 reads processed
  0:00:03.387    46M / 224M  INFO    General                 (binary_converter.cpp      :  96)   2097152 reads processed
  0:00:06.776    45M / 277M  INFO    General                 (binary_converter.cpp      :  96)   4194304 reads processed
  0:00:13.044    31M / 329M  INFO    General                 (binary_converter.cpp      : 111)   8069711 reads written
  0:00:13.050    21M / 329M  INFO    General                 (read_converter.cpp        :  92)   Converting merged reads
  0:00:13.060    31M / 329M  INFO    General                 (binary_converter.cpp      : 111)   0 reads written
  0:00:13.397     1M / 331M  INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:13.544     1M / 331M  INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:13.545     1M / 331M  INFO    General                 (construction.cpp          : 159)   Average read length 149.215
  0:00:13.545     1M / 331M  INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:13.545     1M / 331M  INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:13.550     1M / 331M  INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:13.550     1M / 331M  INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:13.550     1M / 331M  INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:00:19.783  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 11887113 reads
  0:00:22.688  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 15803113 reads
  0:00:24.288  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 16139422 reads
  0:00:24.289     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 16139422 reads
  0:00:24.303     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:26.640     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 44559852 kmers in total.
  0:00:26.650     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:26.970     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:26.970     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:26.973     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:26.973     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:26.973     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 419430
  0:00:32.600  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 44559852 kmers
  0:00:32.611  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 44559852 kmers.
  0:00:32.615     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:35.270     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 44028435 kmers in total.
  0:00:35.281     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:36.067    37M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 44028435 kmers, 31921376 bytes occupied (5.80014 bits per kmer).
  0:00:36.067    37M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:00:38.296    81M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:00:39.647    81M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:00:39.677    81M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:00:39.677    81M / 11G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:00:39.677    81M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:00:41.004   101M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 279027
  0:00:41.014   101M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 281987
  0:00:41.029    81M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   5289332 22-mers were removed by early tip clipper
  0:00:41.029    81M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:00:41.180    81M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:00:42.644   141M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 1463102 sequences extracted
  0:00:43.321   141M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:00:43.727   141M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 22 loops collected
  0:00:43.772   141M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 2926248 edges to create
  0:00:43.772   257M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:00:44.785   305M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:00:44.802   305M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:00:44.810   313M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 931707 vertices to create
  0:00:44.811   402M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:00:45.620   324M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:00:45.620   324M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:46.052   359M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 44559852 kmers, 32304232 bytes occupied (5.7997 bits per kmer).
  0:00:46.068   532M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:00:54.897   532M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:00:55.931   532M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 2925517 edges
  0:00:56.694   266M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:00:56.987   266M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 12
  0:00:56.987   266M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 26.1697
  0:00:56.987   266M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 325
  0:00:56.991   266M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 26.1697
  0:00:56.991   266M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:00:56.991   266M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:00:56.992   266M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:00:56.993   266M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:00:56.993   266M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:00:57.010   266M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 0 times
  0:00:57.010   266M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:00:57.010   266M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:00:57.010   266M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:00:57.010   266M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:00:57.011   266M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:00:57.069   266M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 9888 times
  0:00:57.069   266M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:02.472   271M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 100873 times
  0:01:02.474   271M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:04.139   296M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 275831 times
  0:01:04.142   296M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:01:04.143   296M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:04.260   287M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5084 times
  0:01:04.261   287M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:05.386   274M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 9164 times
  0:01:05.388   274M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:05.717   271M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 52064 times
  0:01:05.720   271M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:01:05.720   271M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:05.748   269M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 151 times
  0:01:05.748   269M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:06.689   265M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2105 times
  0:01:06.690   265M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:06.783   266M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 15362 times
  0:01:06.784   266M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:01:06.785   266M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:06.793   265M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 14 times
  0:01:06.793   265M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:07.463   264M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 586 times
  0:01:07.463   264M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:07.527   265M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 10701 times
  0:01:07.528   265M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:01:07.528   265M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:07.532   264M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 6 times
  0:01:07.532   264M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:07.951   264M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 332 times
  0:01:07.951   264M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:07.992   264M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 7081 times
  0:01:07.992   264M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:01:07.992   264M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:07.995   264M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:07.995   264M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:08.258   263M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 204 times
  0:01:08.258   263M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:08.286   260M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 4411 times
  0:01:08.287   260M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:01:08.287   260M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:08.289   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 5 times
  0:01:08.289   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:08.454   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 128 times
  0:01:08.454   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:08.475   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3989 times
  0:01:08.475   259M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:01:08.475   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:08.477   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:08.477   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:08.632   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 115 times
  0:01:08.638   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:08.665   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2778 times
  0:01:08.672   259M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:01:08.682   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:08.694   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3 times
  0:01:08.704   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:08.824   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 94 times
  0:01:08.830   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:08.855   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2673 times
  0:01:08.861   259M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:01:08.872   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:08.883   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:08.893   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:08.988   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 53 times
  0:01:08.993   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:09.016   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2143 times
  0:01:09.021   259M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:01:09.032   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:09.058   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 50 times
  0:01:09.068   259M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:09.871   260M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 172 times
  0:01:09.871   260M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:09.877   243M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1 times
  0:01:09.877   243M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:01:09.877   243M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:09.878   243M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:09.878   243M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:09.879   243M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:09.879   243M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:09.879   243M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:09.889   240M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:01:09.889   240M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:01:09.889   240M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:01:09.889   240M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:01:09.889   240M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:09.889   240M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:09.889   240M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:09.996   239M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 3167 times
  0:01:09.996   239M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:10.249   239M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 358 times
  0:01:10.249   239M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:10.254   239M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:10.254   239M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:10.260   239M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 30 times
  0:01:10.260   239M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:10.822   240M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 32 times
  0:01:10.822   240M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:11.593   241M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 590 times
  0:01:11.593   241M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:11.651   241M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 34 times
  0:01:11.651   241M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:11.871   240M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 9 times
  0:01:11.872   240M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:11.877   240M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:11.877   240M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:11.882   239M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:11.882   239M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:12.444   241M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2 times
  0:01:12.445   241M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:13.021   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 2 times
  0:01:13.021   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:13.104   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:13.104   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:13.337   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 1 times
  0:01:13.338   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:13.338   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:13.338   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:13.338   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:13.338   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:13.339   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:13.339   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:13.339   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:13.339   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:13.421   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:13.421   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:13.652   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:13.652   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:13.652   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:13.652   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:13.652   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:13.653   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:13.653   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:13.653   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:13.653   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:13.653   242M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:01:13.676   242M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:01:13.732   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:01:13.745   242M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 9372 times
  0:01:13.753   239M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:01:13.753   239M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 34.7649
  0:01:13.759   239M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 29036272
  0:01:13.773   239M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 597
  0:01:13.774   239M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 310761
  0:01:13.774   239M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 215158
  0:01:13.774   239M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:01:13.778   239M / 11G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K21/simplified_contigs
  0:01:13.793   241M / 11G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:01:13.795   241M / 11G   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:01:13.807   241M / 11G   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:01:13.818   241M / 11G   INFO    General                 (binary_converter.cpp      :  96)   131072 reads processed
  0:01:13.837   241M / 11G   INFO    General                 (binary_converter.cpp      : 111)   155454 reads written
  0:01:13.896   239M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:01:13.966     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 1 minutes 13 seconds

===== K21 finished. 


===== K33 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K33/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K33/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K33/configs/config.info
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K33/configs/mda_mode.info
  0:00:00.001     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/dataset.info) with K=33
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.003     1M / 35M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.003     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.003     1M / 35M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K21/simplified_contigs
  0:00:00.090     1M / 35M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.090     1M / 35M   INFO    General                 (construction.cpp          : 159)   Average read length 149.215
  0:00:00.090     1M / 35M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.090     1M / 35M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.096     1M / 35M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.096     1M / 35M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.096     1M / 35M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:07.674  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 6600728 reads
  0:00:13.741  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 13381510 reads
  0:00:18.962  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 15867384 reads
  0:00:22.402  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 16329139 reads
  0:00:25.127  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 16450330 reads
  0:00:25.159     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 16450330 reads
  0:00:25.172     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:31.763     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 49060965 kmers in total.
  0:00:31.774     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:32.787     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:32.798     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:32.821     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:32.838     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:32.846     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:42.732  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 49060965 kmers
  0:00:42.749  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 49060965 kmers.
  0:00:42.750     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:47.465     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 48783104 kmers in total.
  0:00:47.466     2M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:00:49.361    39M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 48783104 kmers, 35353328 bytes occupied (5.79763 bits per kmer).
  0:00:49.369    39M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:00:53.120    87M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:00:54.653    87M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:00:54.714    87M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Early tip clipping (id: construction:early_tip_clipper)
  0:00:54.714    87M / 11G   INFO    General                 (construction.cpp          : 301)   Early tip clipper length bound set as (RL - K)
  0:00:54.714    87M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  47)   Early tip clipping
  0:00:55.977   126M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  82)   #tipped junctions: 344495
  0:00:56.006   126M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  93)   Clipped tips: 348665
  0:00:56.049    87M / 11G   INFO   Early tip clipping       (early_simplification.hpp  :  49)   6963487 34-mers were removed by early tip clipper
  0:00:56.057    87M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:00:56.286    87M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:00:57.879   136M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 947266 sequences extracted
  0:00:58.620   136M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:00:59.319   136M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 26 loops collected
  0:00:59.373   136M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 1894584 edges to create
  0:00:59.374   213M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:00:59.995   242M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:00.006   242M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:00.011   250M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 669431 vertices to create
  0:01:00.012   314M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:00.564   262M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:00.565   262M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:00.983   300M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 49060965 kmers, 35554728 bytes occupied (5.79764 bits per kmer).
  0:01:01.000   488M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:01:09.485   488M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:01:10.779   488M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 1894259 edges
  0:01:12.335   196M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:01:12.493   196M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 11
  0:01:12.493   196M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 22.0744
  0:01:12.493   196M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 182
  0:01:12.496   196M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 22.0744
  0:01:12.496   196M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:01:12.496   196M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:01:12.496   196M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:01:12.496   196M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:01:12.497   196M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:01:12.509   196M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 28 times
  0:01:12.509   196M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:01:12.509   196M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:01:12.509   196M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:12.509   196M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:01:12.509   196M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:12.579   196M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 16828 times
  0:01:12.579   196M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:14.910   205M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 88449 times
  0:01:14.912   205M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:15.936   204M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 149944 times
  0:01:15.939   204M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:01:15.939   204M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:16.013   199M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3594 times
  0:01:16.013   199M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:16.476   192M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 8048 times
  0:01:16.478   192M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:16.689   191M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 25741 times
  0:01:16.691   191M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:01:16.691   191M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:16.702   189M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 104 times
  0:01:16.702   189M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:17.086   185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1785 times
  0:01:17.087   185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:17.154   185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 7547 times
  0:01:17.155   185M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:01:17.155   185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:17.159   185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 12 times
  0:01:17.159   185M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:17.283   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 553 times
  0:01:17.283   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:17.313   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 3473 times
  0:01:17.314   184M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:01:17.314   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:17.315   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:17.315   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:17.379   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 231 times
  0:01:17.379   184M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:17.399   182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2292 times
  0:01:17.400   182M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:01:17.400   182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:17.401   182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:17.401   182M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:17.432   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 116 times
  0:01:17.432   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:17.444   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1382 times
  0:01:17.444   183M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:01:17.444   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:17.445   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 3 times
  0:01:17.445   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:17.464   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 71 times
  0:01:17.464   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:17.473   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1044 times
  0:01:17.473   183M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:01:17.473   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:17.473   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:17.473   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:17.486   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 51 times
  0:01:17.486   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:17.493   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 812 times
  0:01:17.493   183M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:01:17.493   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:17.493   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:17.493   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:17.501   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 40 times
  0:01:17.501   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:17.507   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 640 times
  0:01:17.507   183M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:01:17.507   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:17.507   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:17.507   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:17.514   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 30 times
  0:01:17.514   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:17.519   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 566 times
  0:01:17.519   183M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:01:17.519   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:17.524   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 47 times
  0:01:17.524   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:17.710   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 93 times
  0:01:17.711   183M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:17.714   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:17.714   174M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:01:17.714   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:17.715   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 1 times
  0:01:17.715   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:17.715   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:17.715   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:17.715   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:17.715   174M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 13
  0:01:17.715   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:17.715   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:17.715   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:17.715   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:17.715   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:17.715   174M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:17.722   172M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:01:17.722   172M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:01:17.722   172M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:01:17.722   172M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:01:17.722   172M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:17.722   172M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:17.722   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:17.760   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 1351 times
  0:01:17.760   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:17.967   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 417 times
  0:01:17.967   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:17.971   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:17.971   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:17.975   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 21 times
  0:01:17.975   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:18.156   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 17 times
  0:01:18.157   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:18.352   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 362 times
  0:01:18.352   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:18.374   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 23 times
  0:01:18.374   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:18.570   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 6 times
  0:01:18.570   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:18.574   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:18.574   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:18.578   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:18.578   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:18.772   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 2 times
  0:01:18.773   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:18.968   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 2 times
  0:01:18.968   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:18.998   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:18.998   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:19.193   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:19.194   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:19.194   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:19.194   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:19.194   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:19.194   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:19.194   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:19.194   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:19.194   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:19.194   173M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:01:19.241   173M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:01:19.269   173M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:01:19.288   172M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 19698 times
  0:01:19.292   171M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:01:19.292   171M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 29.9456
  0:01:19.295   171M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 30508961
  0:01:19.302   171M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 1524
  0:01:19.302   171M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 134276
  0:01:19.302   171M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 129886
  0:01:19.302   171M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:01:19.310   171M / 11G   INFO    General                 (read_converter.cpp        : 107)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K33/simplified_contigs
  0:01:19.328   174M / 11G   INFO    General                 (binary_converter.cpp      :  96)   16384 reads processed
  0:01:19.331   174M / 11G   INFO    General                 (binary_converter.cpp      :  96)   32768 reads processed
  0:01:19.345   174M / 11G   INFO    General                 (binary_converter.cpp      :  96)   65536 reads processed
  0:01:19.356   174M / 11G   INFO    General                 (binary_converter.cpp      : 111)   67153 reads written
  0:01:19.469   171M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:01:19.496     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 1 minutes 19 seconds

===== K33 finished. 


===== K55 started. 


== Running: /usr/local/bin/spades-core /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/configs/config.info /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/configs/mda_mode.info

  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/configs/config.info
  0:00:00.000     1M / 35M   INFO    General                 (main.cpp                  :  99)   Loaded config from /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/configs/mda_mode.info
  0:00:00.000     1M / 35M   INFO    General                 (memory_limit.cpp          :  54)   Memory limit set to 128 Gb
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 107)   Starting SPAdes, built from N/A, git revision N/A
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 108)   Maximum k-mer length: 128
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 109)   Assembling dataset (/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/dataset.info) with K=55
  0:00:00.001     1M / 35M   INFO    General                 (main.cpp                  : 110)   Maximum # of threads to use (adjusted due to OMP capabilities): 16
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 212)   SPAdes started
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 225)   Starting from stage: read_conversion
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 231)   Two-step repeat resolution disabled
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 672)   Graph created, vertex min_id: 3, edge min_id: 3
  0:00:00.001     1M / 35M   INFO   GraphCore                (graph_core.hpp            : 673)   Vertex size: 48, edge size: 40
  0:00:00.001     1M / 35M   INFO    General                 (edge_index.hpp            : 113)   Size of edge index entries: 12/8
  0:00:00.001     1M / 35M   INFO    General                 (pipeline.cpp              : 242)   Will need read mapping, kmer mapper will be attached
  0:00:00.001     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Binary Read Conversion (id: read_conversion)
  0:00:00.005     1M / 35M   INFO    General                 (read_converter.cpp        :  53)   Binary reads detected
  0:00:00.008     1M / 35M   INFO   StageManager             (stage.cpp                 : 185)   STAGE == de Bruijn graph construction (id: construction)
  0:00:00.008     1M / 35M   INFO    General                 (construction.cpp          : 118)   Contigs from previous K will be used: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K33/simplified_contigs
  0:00:00.193     1M / 35M   INFO    General                 (construction.cpp          : 153)   Max read length 150
  0:00:00.193     1M / 35M   INFO    General                 (construction.cpp          : 159)   Average read length 149.215
  0:00:00.193     1M / 35M   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == k+1-mer counting (id: construction:kpomer_counting)
  0:00:00.193     1M / 35M   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:00.201     1M / 35M   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:00.202     1M / 35M   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:00.202     1M / 35M   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:09.591  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 8349041 reads
  0:00:17.325  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 15354913 reads
  0:00:22.063  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 16059818 reads
  0:00:28.797  9601M / 11G   INFO    General                 (kmer_splitters.hpp        : 125)   Processed 16273728 reads
  0:00:28.928     1M / 11G   INFO    General                 (kmer_splitters.hpp        : 131)   Used 16273728 reads
  0:00:28.943     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:00:39.343     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 52542734 kmers in total.
  0:00:39.350     1M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Extension index construction (id: construction:extension_index_construction)
  0:00:40.865     1M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 438)   Building kmer index
  0:00:40.865     1M / 11G   INFO    General                 (kmer_index_builder.hpp    : 243)   Splitting kmer instances into 160 files using 16 threads. This might take a while.
  0:00:40.878     1M / 11G   INFO    General                 (file_limit.hpp            :  42)   Open file limit set to 131072
  0:00:40.878     1M / 11G   INFO    General                 (kmer_splitter.hpp         :  93)   Memory available for splitting buffers: 2.66665 Gb
  0:00:40.878     1M / 11G   INFO    General                 (kmer_splitter.hpp         : 101)   Using cell size of 209715
  0:00:57.600  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 194)   Processed 52542734 kmers
  0:00:57.611  9602M / 11G   INFO    General                 (kmer_splitters.hpp        : 199)   Used 52542734 kmers.
  0:00:57.623     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 249)   Starting k-mer counting.
  0:01:06.643     2M / 11G   INFO    General                 (kmer_index_builder.hpp    : 260)   K-mer counting done. There are 52462757 kmers in total.
  0:01:06.655     2M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:09.076    43M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 52462757 kmers, 38011024 bytes occupied (5.79627 bits per kmer).
  0:01:09.076    43M / 11G   INFO    General                 (kmer_index_builder.hpp    : 169)   Merging final buckets.
  0:01:15.171    95M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 100)   Building k-mer extensions from k+1-mers
  0:01:16.997    95M / 11G   INFO   DeBruijnExtensionIndexBu (kmer_extension_index_build: 105)   Building k-mer extensions from k+1-mers finished.
  0:01:17.075    94M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Condensing graph (id: construction:graph_condensing)
  0:01:17.497    94M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 354)   Extracting unbranching paths
  0:01:19.495   166M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 373)   Extracting unbranching paths finished. 1343626 sequences extracted
  0:01:20.417   166M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 309)   Collecting perfect loops
  0:01:21.764   166M / 11G   INFO   UnbranchingPathExtractor (debruijn_graph_constructor: 342)   Collecting perfect loops finished. 1 loops collected
  0:01:21.859   166M / 11G   INFO    General                 (debruijn_graph_constructor: 487)   Total 2687254 edges to create
  0:01:21.859   271M / 11G   INFO    General                 (debruijn_graph_constructor: 489)   Collecting link records
  0:01:22.780   315M / 11G   INFO    General                 (debruijn_graph_constructor: 491)   Ordering link records
  0:01:22.797   315M / 11G   INFO    General                 (debruijn_graph_constructor: 493)   Sorting done
  0:01:22.806   331M / 11G   INFO    General                 (debruijn_graph_constructor: 503)   Total 1263650 vertices to create
  0:01:22.807   451M / 11G   INFO    General                 (debruijn_graph_constructor: 506)   Connecting the graph
  0:01:23.908   370M / 11G   INFO    General                 (stage.cpp                 : 117)   PROCEDURE == Filling coverage indices (PHM) (id: construction:coverage_filling_phm)
  0:01:23.908   370M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:24.370   413M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 52542734 kmers, 38068568 bytes occupied (5.79621 bits per kmer).
  0:01:24.388   617M / 11G   INFO    General                 (coverage_hash_map_builder.:  47)   Collecting k-mer coverage information from reads, this takes a while.
  0:01:31.338   617M / 11G   INFO    General                 (construction.cpp          : 430)   Filling coverage and flanking coverage from PHM
  0:01:32.882   617M / 11G   INFO    General                 (coverage_filling.hpp      :  82)   Processed 2687172 edges
  0:01:34.951   290M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == EC Threshold Finding (id: ec_threshold_finder)
  0:01:35.105   291M / 11G   INFO   ThresholdFinder          (ec_threshold_finder.hpp   : 119)   Bucket size: 9
  0:01:35.105   291M / 11G   INFO    General                 (genomic_info_filler.cpp   :  39)   Average edge coverage: 14.4702
  0:01:35.105   291M / 11G   INFO    General                 (genomic_info_filler.cpp   :  40)   Graph threshold: 173
  0:01:35.106   290M / 11G   INFO    General                 (genomic_info_filler.cpp   :  70)   EC coverage threshold value was calculated as 14.4702
  0:01:35.106   290M / 11G   INFO    General                 (genomic_info_filler.cpp   :  71)   Trusted kmer low bound: 0
  0:01:35.107   290M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: early_gapcloser)
  0:01:35.107   290M / 11G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:01:35.107   290M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Raw Simplification (id: raw_simplification)
  0:01:35.107   290M / 11G   INFO    General                 (simplification.cpp        : 127)   PROCEDURE == Initial cleaning
  0:01:35.107   290M / 11G   INFO    General                 (graph_simplification.hpp  : 662)   Flanking coverage based disconnection disabled
  0:01:35.107   290M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Self conjugate edge remover
  0:01:35.128   290M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Self conjugate edge remover triggered 12 times
  0:01:35.128   290M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification (id: simplification)
  0:01:35.128   290M / 11G   INFO    General                 (simplification.cpp        : 368)   Graph simplification started
  0:01:35.128   290M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:35.128   290M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 1
  0:01:35.128   290M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:36.872   267M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 443753 times
  0:01:36.876   267M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:42.332   576M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 52571 times
  0:01:42.333   576M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:42.683   578M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 42652 times
  0:01:42.684   578M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 2
  0:01:42.685   578M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:42.725   576M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2942 times
  0:01:42.726   576M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:43.405   597M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 4207 times
  0:01:43.407   597M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:43.567   596M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 16692 times
  0:01:43.568   596M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 3
  0:01:43.568   596M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:43.576   595M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 276 times
  0:01:43.576   595M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:43.867   606M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1847 times
  0:01:43.867   606M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:43.963   605M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 7640 times
  0:01:43.964   605M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 4
  0:01:43.964   605M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:43.967   605M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 46 times
  0:01:43.967   605M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:44.097   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 840 times
  0:01:44.097   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:44.127   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 2120 times
  0:01:44.128   610M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 5
  0:01:44.128   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:44.129   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 11 times
  0:01:44.129   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:44.169   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 261 times
  0:01:44.170   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:44.187   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 1157 times
  0:01:44.187   611M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 6
  0:01:44.188   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:44.188   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 10 times
  0:01:44.188   611M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:44.208   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 126 times
  0:01:44.208   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:44.218   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 677 times
  0:01:44.218   612M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 7
  0:01:44.218   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:44.219   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 4 times
  0:01:44.219   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:44.228   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 63 times
  0:01:44.228   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:44.235   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 449 times
  0:01:44.235   612M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 8
  0:01:44.235   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:44.235   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 4 times
  0:01:44.235   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:44.242   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 35 times
  0:01:44.242   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:44.247   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 352 times
  0:01:44.247   612M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 9
  0:01:44.247   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:44.247   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 2 times
  0:01:44.247   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:44.252   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 29 times
  0:01:44.252   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:44.256   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 277 times
  0:01:44.256   612M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 10
  0:01:44.256   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:44.257   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:44.257   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:44.261   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 27 times
  0:01:44.261   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:44.264   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 215 times
  0:01:44.264   612M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 11
  0:01:44.264   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:44.277   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 634 times
  0:01:44.277   612M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:44.309   613M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 37 times
  0:01:44.309   613M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:44.315   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:44.315   610M / 11G   INFO    General                 (simplification.cpp        : 373)   PROCEDURE == Simplification cycle, iteration 12
  0:01:44.315   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:44.315   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:44.316   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:44.316   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:44.316   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Low coverage edge remover
  0:01:44.316   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Low coverage edge remover triggered 0 times
  0:01:44.328   610M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Gap Closer (id: late_gapcloser)
  0:01:44.328   610M / 11G   INFO    General                 (gap_closer.cpp            : 429)   No paired-end libraries exist, skipping gap closer
  0:01:44.329   610M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Simplification Cleanup (id: simplification_cleanup)
  0:01:44.329   610M / 11G   INFO    General                 (simplification.cpp        : 176)   PROCEDURE == Post simplification
  0:01:44.329   610M / 11G   INFO    General                 (graph_simplification.hpp  : 448)   Disconnection of relatively low covered edges disabled
  0:01:44.329   610M / 11G   INFO    General                 (graph_simplification.hpp  : 485)   Complex tip clipping disabled
  0:01:44.330   610M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:44.330   610M / 11G   INFO    General                 (graph_simplification.hpp  : 634)   Creating parallel br instance
  0:01:44.330   610M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:44.330   610M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:44.330   610M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:44.419   610M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:44.502   609M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:45.772   609M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:45.857   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:01:45.857   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:45.863   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 7 times
  0:01:45.864   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:45.878   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 369 times
  0:01:45.878   609M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:46.405   623M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 735 times
  0:01:46.406   623M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:46.412   623M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 4 times
  0:01:46.412   623M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:46.419   623M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 56 times
  0:01:46.419   623M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:46.441   623M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 5 times
  0:01:46.441   623M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:46.518   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 250 times
  0:01:46.518   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:46.518   626M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:46.519   626M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:46.599   626M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:46.674   626M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:47.658   626M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:47.734   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:01:47.735   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:47.741   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:01:47.741   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:47.751   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 7 times
  0:01:47.751   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:48.013   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 4 times
  0:01:48.014   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:48.019   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:48.020   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:48.025   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:48.025   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:48.044   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 1 times
  0:01:48.044   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:48.062   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 1 times
  0:01:48.062   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:48.063   626M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:48.063   626M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:48.134   626M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:48.205   626M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:49.035   626M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:49.109   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:01:49.110   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:49.115   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:01:49.116   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:49.127   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:49.127   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:49.384   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:49.385   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:49.385   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:49.385   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:49.385   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:49.386   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:49.386   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:49.386   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:49.386   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:49.386   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:49.387   626M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:49.387   626M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:49.453   626M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:49.523   626M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:50.338   626M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:50.414   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 1 times
  0:01:50.415   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:50.420   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:01:50.421   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:50.432   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:50.432   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:50.692   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:50.692   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:50.693   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:50.693   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:50.693   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:50.694   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:50.694   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:50.694   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:50.694   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:50.694   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final removal of erroneous edges
  0:01:50.695   626M / 11G   INFO    General                 (simplification.cpp        :  82)   Topology-based removal procedures
  0:01:50.695   626M / 11G   INFO    General                 (single_cell_simplification:  49)   Removing connections based on topology
  0:01:50.761   626M / 11G   INFO    General                 (single_cell_simplification: 104)   Removing connections based on topology and reliable coverage
  0:01:50.831   626M / 11G   INFO    General                 (single_cell_simplification:  85)   Removing interstrand connections
  0:01:51.642   626M / 11G   INFO    General                 (single_cell_simplification:  66)   Removing connections based on topological multiplicity counting
  0:01:51.715   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final removal of erroneous edges triggered 0 times
  0:01:51.716   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Topology tip clipper
  0:01:51.721   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Topology tip clipper triggered 0 times
  0:01:51.722   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Relative coverage component remover
  0:01:51.733   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Relative coverage component remover triggered 0 times
  0:01:51.733   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Complex bulge remover
  0:01:51.987   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Complex bulge remover triggered 0 times
  0:01:51.987   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Tip clipper
  0:01:51.988   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Tip clipper triggered 0 times
  0:01:51.988   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final tip clipper
  0:01:51.988   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final tip clipper triggered 0 times
  0:01:51.988   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Bulge remover
  0:01:51.988   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Bulge remover triggered 0 times
  0:01:51.989   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Final bulge remover
  0:01:51.989   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Final bulge remover triggered 0 times
  0:01:51.989   626M / 11G   INFO    General                 (single_cell_simplification: 144)   Removing hidden erroneous connections
  0:01:52.017   626M / 11G   INFO    General                 (simplification.cpp        : 327)   Disrupting self-conjugate edges
  0:01:52.050   626M / 11G   INFO   Simplification           (parallel_processing.hpp   : 167)   Running Removing isolated edges
  0:01:52.092   625M / 11G   INFO   Simplification           (parallel_processing.hpp   : 170)   Removing isolated edges triggered 45005 times
  0:01:52.096   624M / 11G   INFO    General                 (simplification.cpp        : 495)   After simplification:
  0:01:52.096   624M / 11G   INFO    General                 (simplification.cpp        : 496)     Average coverage = 23.9478
  0:01:52.099   624M / 11G   INFO    General                 (simplification.cpp        : 497)     Total length = 30547946
  0:01:52.104   624M / 11G   INFO    General                 (simplification.cpp        : 498)     Median edge length: 2736
  0:01:52.104   624M / 11G   INFO    General                 (simplification.cpp        : 499)     Edges: 79142
  0:01:52.104   624M / 11G   INFO    General                 (simplification.cpp        : 500)     Vertices: 99030
  0:01:52.104   624M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Mismatch Correction (id: mismatch_correction)
  0:01:52.104   624M / 11G   INFO    General                 (graph_pack.cpp            :  67)   Index refill
  0:01:52.105   624M / 11G   INFO    General                 (edge_index.hpp            : 156)   Using small index (max_id = 2700693)
  0:01:52.162   624M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 395)   Building perfect hash indices
  0:01:52.770   646M / 11G   INFO   K-mer Index Building     (kmer_index_builder.hpp    : 431)   Index built. Total 30543840 kmers, 22061448 bytes occupied (5.7783 bits per kmer).
  0:01:52.792   882M / 11G   INFO    General                 (edge_index_builders.hpp   : 252)   Collecting edge information from graph, this takes a while.
  0:01:53.179   882M / 11G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 7801604 kmers to process
  0:01:54.239   882M / 11G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:01:54.240   882M / 11G   INFO    General                 (mismatch_correction.cpp   : 400)   Collect potential mismatches
  0:01:54.996   888M / 11G   INFO    General                 (mismatch_correction.cpp   : 192)   Total 27523 edges (out of 79142) with 849634 potential mismatch positions (30.87 positions per edge)
  0:01:54.998   888M / 11G   INFO    General                 (mismatch_correction.cpp   : 402)   Potential mismatches collected
  0:01:56.292   890M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:01:56.376   891M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:01:56.410   892M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:01:56.417   894M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:01:56.421   895M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:01:56.425   897M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:01:56.558   898M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:01:57.572   901M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:01:59.087   906M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 8400000 reads
  0:02:07.309  1096M / 11G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 16139422 reads processed
  0:02:08.054   951M / 11G   INFO    General                 (mismatch_correction.cpp   : 395)   All edges processed
  0:02:08.064   883M / 11G   INFO    General                 (mismatch_correction.cpp   : 450)   Corrected 262 nucleotides
  0:02:08.064   883M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:02:08.064   883M / 11G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/before_rr.fasta
  0:02:08.952   883M / 11G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/assembly_graph_after_simplification.gfa
  0:02:09.204   883M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Paired Information Counting (id: late_pair_info_count)
  0:02:09.283   898M / 11G   INFO    General                 (graph_pack.cpp            :  77)   Normalizing k-mer map. Total 7813758 kmers to process
  0:02:10.394   898M / 11G   INFO    General                 (graph_pack.cpp            :  79)   Normalizing done
  0:02:10.401   898M / 11G   INFO    General                 (pair_info_count.cpp       : 339)   Min edge length for estimation: 2736
  0:02:10.401   898M / 11G   INFO    General                 (pair_info_count.cpp       : 411)   Mapping single reads of library #0
  0:02:10.401   898M / 11G   INFO    General                 (pair_info_count.cpp       :  48)   Selecting usual mapper
  0:02:11.492   898M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 200000 reads
  0:02:11.514   899M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 400000 reads
  0:02:11.534   899M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 600000 reads
  0:02:11.566   900M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 800000 reads
  0:02:11.598   900M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1000000 reads
  0:02:11.599   901M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 1200000 reads
  0:02:11.720   901M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 2200000 reads
  0:02:12.615   902M / 11G   INFO    General                 (sequence_mapper_notifier.h:  77)   Processed 4200000 reads
  0:02:17.217   922M / 11G   INFO    General                 (sequence_mapper_notifier.h:  95)   Total 8069711 reads processed
  0:02:17.220   921M / 11G   INFO    General                 (pair_info_count.cpp       : 413)   Total paths obtained from single reads: 146811
  0:02:17.220   921M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Distance Estimation (id: distance_estimation)
  0:02:17.221   921M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Repeat Resolving (id: repeat_resolving)
  0:02:17.221   921M / 11G   INFO    General                 (repeat_resolving.cpp      :  87)   Using Path-Extend repeat resolving
  0:02:17.222   921M / 11G   INFO    General                 (launcher.cpp              : 600)   ExSPAnder repeat resolving tool started
  0:02:17.300  1081M / 11G   INFO    General                 (launcher.cpp              : 418)   Creating main extenders, unique edge length = 2000
  0:02:17.301  1086M / 11G   INFO    General                 (launcher.cpp              : 342)   filling path container
  0:02:17.496  1520M / 11G   INFO    General                 (extenders_logic.cpp       :  47)   resolvable_repeat_length_bound set to 10000
  0:02:17.935  1528M / 11G   INFO    General                 (extenders_logic.cpp       : 543)   Using 0 paired-end libraries
  0:02:17.941  1528M / 11G   INFO    General                 (extenders_logic.cpp       : 544)   Using 0 paired-end scaffolding libraries
  0:02:17.941  1528M / 11G   INFO    General                 (extenders_logic.cpp       : 545)   Using 1 single read library
  0:02:17.942  1528M / 11G   INFO    General                 (launcher.cpp              : 447)   Total number of extenders is 1
  0:02:17.942  1528M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 0 paths from 39264 (0%)
  0:02:20.129  1528M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 128 paths from 39264 (0%)
  0:02:20.836  1529M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 256 paths from 39264 (0%)
  0:02:22.226  1530M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 512 paths from 39264 (1%)
  0:02:22.922  1533M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 1024 paths from 39264 (2%)
  0:02:24.641  1537M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 2048 paths from 39264 (5%)
  0:02:27.370  1545M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 3927 paths from 39264 (10%)
  0:02:27.664  1546M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 4096 paths from 39264 (10%)
  0:02:31.270  1561M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 7854 paths from 39264 (20%)
  0:02:31.388  1562M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 8192 paths from 39264 (20%)
  0:02:33.954  1577M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 11781 paths from 39264 (30%)
  0:02:37.049  1592M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 15708 paths from 39264 (40%)
  0:02:37.419  1595M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 16384 paths from 39264 (41%)
  0:02:38.329  1609M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 19635 paths from 39264 (50%)
  0:02:39.413  1625M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 23562 paths from 39264 (60%)
  0:02:39.760  1642M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 27489 paths from 39264 (70%)
  0:02:41.332  1660M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 31416 paths from 39264 (80%)
  0:02:41.881  1664M / 11G   INFO    General                 (path_extenders.cpp        :  34)   Processed 32768 paths from 39264 (83%)
  0:02:42.375  1667M / 11G   INFO    General                 (path_extenders.cpp        :  36)   Processed 35343 paths from 39264 (90%)
  0:02:42.626  1668M / 11G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:02:42.628  1668M / 11G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:02:42.672  1656M / 11G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:02:42.673  1656M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:02:42.674  1656M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:02:42.683  1656M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:02:42.684  1656M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:02:42.755  1657M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:02:42.832  1658M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:02:42.872  1726M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:02:42.969  1653M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:02:42.993  1652M / 11G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:02:42.995  1652M / 11G   INFO    General                 (launcher.cpp              : 454)   Closing gaps in paths
  0:02:43.053  1767M / 11G   INFO    General                 (launcher.cpp              : 484)   Gap closing completed
  0:02:43.068  1774M / 11G   INFO    General                 (launcher.cpp              : 302)   Traversing tandem repeats
  0:02:43.163  1774M / 11G   INFO    General                 (launcher.cpp              : 312)   Traversed 377 loops
  0:02:43.165  1774M / 11G   INFO    General                 (launcher.cpp              : 250)   Finalizing paths
  0:02:43.166  1774M / 11G   INFO    General                 (launcher.cpp              : 252)   Deduplicating paths
  0:02:43.178  1771M / 11G   INFO    General                 (launcher.cpp              : 256)   Paths deduplicated
  0:02:43.179  1771M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  60)   Removing overlaps
  0:02:43.180  1771M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  63)   Sorting paths
  0:02:43.187  1771M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  70)   Marking overlaps
  0:02:43.188  1771M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 116)   Marking start/end overlaps
  0:02:43.214  1771M / 11G   INFO   OverlapRemover           (overlap_remover.hpp       : 119)   Marking remaining overlaps
  0:02:43.239  1772M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  73)   Splitting paths
  0:02:43.246  1779M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  78)   Deduplicating paths
  0:02:43.260  1772M / 11G   INFO   PEResolver               (pe_resolver.cpp           :  80)   Overlaps removed
  0:02:43.280  1774M / 11G   INFO    General                 (launcher.cpp              : 273)   Paths finalized
  0:02:43.281  1774M / 11G   INFO    General                 (launcher.cpp              : 664)   ExSPAnder repeat resolving tool finished
  0:02:43.377  1037M / 11G   INFO   StageManager             (stage.cpp                 : 185)   STAGE == Contig Output (id: contig_output)
  0:02:43.385  1037M / 11G   INFO    General                 (contig_output.hpp         :  16)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/before_rr.fasta
  0:02:43.715  1037M / 11G   INFO    General                 (contig_output_stage.cpp   : 151)   Writing GFA graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/assembly_graph_with_scaffolds.gfa
  0:02:43.854  1037M / 11G   INFO    General                 (contig_output_stage.cpp   : 165)   Outputting FastG graph to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/assembly_graph.fastg
  0:02:44.456  1037M / 11G   INFO    General                 (contig_output_stage.cpp   : 196)   Breaking scaffolds
  0:02:44.641  1202M / 11G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/final_contigs.fasta
  0:02:44.855  1202M / 11G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/final_contigs.paths
  0:02:45.025  1074M / 11G   INFO    General                 (contig_output_stage.cpp   :  98)   Outputting contigs to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/scaffolds.fasta
  0:02:45.236  1074M / 11G   INFO    General                 (contig_output_stage.cpp   : 104)   Outputting FastG paths to /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/scaffolds.paths
  0:02:45.333  1074M / 11G   INFO    General                 (contig_output_stage.cpp   : 111)   Populating GFA with scaffold paths
  0:02:45.464  1037M / 11G   INFO    General                 (pipeline.cpp              : 287)   SPAdes finished
  0:02:46.071     1M / 11G   INFO    General                 (main.cpp                  : 136)   Assembling time: 0 hours 2 minutes 46 seconds

===== K55 finished. 


===== Copy files started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/copy_files.py /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/before_rr.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/assembly_graph_after_simplification.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/final_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/first_pe_contigs.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/strain_graph.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/scaffolds.fasta /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/scaffolds.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/assembly_graph_with_scaffolds.gfa /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/assembly_graph.fastg /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/K55/final_contigs.paths /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/contigs.paths


===== Copy files finished. 


===== Assembling finished. 


===== Breaking scaffolds started. 


== Running: /usr/local/bin/python /usr/local/share/spades/spades_pipeline/scripts/breaking_scaffolds_script.py --result_scaffolds_filename /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/scaffolds.fasta --misc_dir /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/misc --threshold_for_breaking_scaffolds 3


===== Breaking scaffolds finished. 


===== Terminate started. 


===== Terminate finished. 

 * Corrected reads are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/corrected/
 * Assembled contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/contigs.fasta
 * Assembled scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/scaffolds.fasta
 * Paths in the assembly graph corresponding to the contigs are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/contigs.paths
 * Paths in the assembly graph corresponding to the scaffolds are in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/scaffolds.paths
 * Assembly graph is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/assembly_graph.fastg
 * Assembly graph in GFA format is in /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/assembly_graph_with_scaffolds.gfa

======= SPAdes pipeline finished.

SPAdes log can be found here: /clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/01assemblies/1507993/spades.log

Thank you for using SPAdes!
