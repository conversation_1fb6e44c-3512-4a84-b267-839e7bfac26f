#!/usr/bin/env python3
"""
<PERSON>ript to create boxplots with stripplot overlay showing N50 values by treatment method.
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

def create_n50_boxplot(csv_file, output_file="n50_boxplot.png"):
    """
    Create a boxplot with stripplot overlay for N50 values by treatment method.
    
    Args:
        csv_file (str): Path to the CSV file with N50 data
        output_file (str): Output filename for the plot
    """
    
    # Read the data
    df = pd.read_csv(csv_file)
    
    # Set up the plot style
    plt.style.use('default')
    sns.set_palette("Set2")
    
    # Create figure and axis
    fig, ax = plt.subplots(figsize=(8, 6))
    
    # Create boxplot
    box_plot = sns.boxplot(
        data=df, 
        x='treatment', 
        y='n50',
        ax=ax,
        width=0.6,
        boxprops=dict(alpha=0.7),
        showfliers=False  # We'll show individual points with stripplot
    )
    
    # Overlay stripplot (individual data points)
    strip_plot = sns.stripplot(
        data=df,
        x='treatment',
        y='n50',
        ax=ax,
        size=8,
        alpha=0.8,
        jitter=0.2,
        edgecolor='black',
        linewidth=0.5
    )
    
    # Customize the plot
    ax.set_xlabel('Treatment Method', fontsize=12, fontweight='bold')
    ax.set_ylabel('N50 (bp)', fontsize=12, fontweight='bold')
    ax.set_title('N50 Distribution by Treatment Method\n(Discosea Contigs)', 
                 fontsize=14, fontweight='bold', pad=20)
    
    # Format y-axis to show values in thousands
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1000:.1f}k'))
    
    # Add grid for better readability
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_axisbelow(True)
    
    # Add sample size annotations
    for i, treatment in enumerate(df['treatment'].unique()):
        treatment_data = df[df['treatment'] == treatment]
        n_samples = len(treatment_data)
        mean_n50 = treatment_data['n50'].mean()
        
        # Add text annotation with sample size and mean
        ax.text(i, ax.get_ylim()[1] * 0.95, 
                f'n = {n_samples}\nMean = {mean_n50:.0f} bp',
                ha='center', va='top', 
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8),
                fontsize=10)
    
    # Add individual sample labels
    for idx, row in df.iterrows():
        treatment_idx = list(df['treatment'].unique()).index(row['treatment'])
        # Add small offset to avoid overlapping with points
        ax.annotate(row['sample_id'], 
                   (treatment_idx, row['n50']),
                   xytext=(5, 5), textcoords='offset points',
                   fontsize=8, alpha=0.7)
    
    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Plot saved as {output_file}")
    
    # Display summary statistics
    print("\nSummary Statistics:")
    print("=" * 50)
    summary = df.groupby('treatment')['n50'].agg(['count', 'mean', 'std', 'min', 'max'])
    summary.columns = ['Count', 'Mean', 'Std Dev', 'Min', 'Max']
    summary = summary.round(1)
    print(summary)
    
    # Perform basic statistical test
    from scipy import stats
    mda_values = df[df['treatment'] == 'MDA']['n50'].values
    pta_values = df[df['treatment'] == 'PTA']['n50'].values
    
    # Mann-Whitney U test (non-parametric)
    statistic, p_value = stats.mannwhitneyu(mda_values, pta_values, alternative='two-sided')
    
    print(f"\nStatistical Test (Mann-Whitney U):")
    print(f"Statistic: {statistic}")
    print(f"P-value: {p_value:.4f}")
    if p_value < 0.05:
        print("Result: Significant difference between treatments (p < 0.05)")
    else:
        print("Result: No significant difference between treatments (p >= 0.05)")
    
    return fig, ax

def main():
    csv_file = "discosea_n50_by_treatment.csv"
    output_file = "discosea_n50_boxplot.png"
    
    try:
        # Check if required packages are available
        try:
            import scipy.stats
        except ImportError:
            print("Warning: scipy not available, statistical test will be skipped")
        
        # Create the plot
        fig, ax = create_n50_boxplot(csv_file, output_file)
        
        # Show the plot (if running interactively)
        plt.show()
        
    except FileNotFoundError:
        print(f"Error: File '{csv_file}' not found.")
        print("Please make sure you have run the N50 calculation script first.")
    except ImportError as e:
        print(f"Error: Required package not found: {e}")
        print("Please install required packages: pip install pandas matplotlib seaborn")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
