INFO Splitting files based on reference: /clusterfs/jgi/scratch/science/mgs/nelli/databases/eukCC/eukccdb/eukcc2_db_ver_1.2/db_base/backbone/base_tree.aln
WARN The query alignment file '/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/04quality/eukcc_conda/1507990/1507990.bin.1/workdir/epa-ng/base/alignment.fasta' appears to have an alignment width that differs from the reference (12910 vs. 12907).
     This is likely due to the alignment tool stripping gap-only columns, or adding columns to the reference. Please consider using the produced 'reference.fasta' during placement!
