29-04-2025 10:11:25:  EukCC version 2.1.0
29-04-2025 10:11:25:  #####################################
If you publish using EukCC please make sure to also cite:
29-04-2025 10:11:25:  
eukcc:
<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. "Estimating the quality of eukaryotic genomes recovered from metagenomic analysis with EukCC." Genome biology 21.1 (2020): 1-21.

29-04-2025 10:11:25:  
hmmer:
<PERSON>, <PERSON> "Accelerated profile HMM searches." PLoS Comput Biol 7.10 (2011): e1002195.

29-04-2025 10:11:25:  
pplacer:
<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. "pplacer: linear time maximum-likelihood and Bayesian phylogenetic placement of sequences onto a fixed reference tree." BMC bioinformatics 11.1 (2010): 1-16.

29-04-2025 10:11:25:  
epa-ng:
<PERSON>, <PERSON>, et al. "EPA-ng: massively parallel evolutionary placement of genetic sequences." Systematic biology 68.2 (2019): 365-369.

29-04-2025 10:11:25:  
metaEuk:
<PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. "Meta<PERSON>uk—Sensitive, high-throughput gene discovery, and annotation for large-scale eukaryotic metagenomics." Microbiome 8 (2020): 1-15.

29-04-2025 10:11:25:  ####################################
29-04-2025 10:11:25:  Set sequence type to DNA
29-04-2025 10:11:37:  Doing a first pass placement in the base database
29-04-2025 10:11:37:  Searching for marker genes in base database
29-04-2025 10:11:37:  No placement marker genes found.
