diamond v2.1.11.165 (C) Max Planck Society for the Advancement of Science, <PERSON>, University of Tuebingen
Documentation, support and updates available at http://www.diamondsearch.org
Please cite: http://dx.doi.org/10.1038/s41592-021-01101-x Nature Methods (2021)

#CPU threads: 64
Scoring parameters: (Matrix=BLOSUM62 Lambda=0.267 K=0.041 Penalties=11/1)
Temporary directory: 06taxonomy/eukulele_batches/batch8/results/mags_full/diamond
#Target sequences to report alignments for: 100
Opening the database...  [0.034s]
Database: ./marmmetsp/diamond/reference.pep.fa.gz.dmnd (type: Diamond database, sequences: 20051330, letters: 6566064827)
Block size = 3000000000
Opening the input file...  [0.118s]
Opening the output file...  [0.008s]
Loading query sequences...  [0.056s]
Masking queries...  [0.059s]
Building query seed set...  [0.22s]
Algorithm: Query-indexed
Building query histograms...  [0.022s]
Seeking in database...  [0s]
Loading reference sequences...  [4.861s]
Initializing dictionary...  [0.072s]
Initializing temporary storage...  [0.116s]
Building reference histograms...  [4.274s]
Allocating buffers...  [0s]
Processing query block 1, reference block 1/3, shape 1/2.
Building reference seed array...  [2.424s]
Building query seed array...  [0.048s]
Computing hash join...  [0.377s]
Searching alignments...  [0.374s]
Deallocating memory...  [0s]
Processing query block 1, reference block 1/3, shape 2/2.
Building reference seed array...  [2.256s]
Building query seed array...  [0.016s]
Computing hash join...  [0.288s]
Searching alignments...  [0.33s]
Deallocating memory...  [0s]
Deallocating buffers...  [0.125s]
Clearing query masking...  [0s]
Opening temporary output file...  [0.005s]
Computing alignments...  [9.239s]
Deallocating reference...  [0.274s]
Loading reference sequences...  [4.881s]
Initializing dictionary...  [0.024s]
Initializing temporary storage...  [0.239s]
Building reference histograms...  [4.28s]
Allocating buffers...  [0s]
Processing query block 1, reference block 2/3, shape 1/2.
Building reference seed array...  [2.393s]
Building query seed array...  [0.046s]
Computing hash join...  [0.299s]
Searching alignments...  [0.372s]
Deallocating memory...  [0s]
Processing query block 1, reference block 2/3, shape 2/2.
Building reference seed array...  [2.253s]
Building query seed array...  [0.015s]
Computing hash join...  [0.271s]
Searching alignments...  [0.333s]
Deallocating memory...  [0s]
Deallocating buffers...  [0.128s]
Clearing query masking...  [0s]
Opening temporary output file...  [0.012s]
Computing alignments...  [8.77s]
Deallocating reference...  [0.273s]
Loading reference sequences...  [0.741s]
Initializing dictionary...  [0.001s]
Initializing temporary storage...  [0.208s]
Building reference histograms...  [0.822s]
Allocating buffers...  [0s]
Processing query block 1, reference block 3/3, shape 1/2.
Building reference seed array...  [0.496s]
Building query seed array...  [0.022s]
Computing hash join...  [0.058s]
Searching alignments...  [0.075s]
Deallocating memory...  [0s]
Processing query block 1, reference block 3/3, shape 2/2.
Building reference seed array...  [0.43s]
Building query seed array...  [0.016s]
Computing hash join...  [0.053s]
Searching alignments...  [0.069s]
Deallocating memory...  [0s]
Deallocating buffers...  [0s]
Clearing query masking...  [0s]
Opening temporary output file...  [0.015s]
Computing alignments...  [1.615s]
Deallocating reference...  [0.002s]
Loading reference sequences...  [0s]
Deallocating buffers...  [0s]
Joining output blocks... Joining output blocks...  [0.351s]
 [0.461s]
Deallocating queries...  [0.002s]
Loading query sequences...  [0s]
Closing the input file...  [0s]
Closing the output file...  [0.162s]
Closing the database...  [0s]
Cleaning up...  [0s]
Total time = 55.492s
Reported 528494 pairwise alignments, 528494 HSPs.
10652 queries aligned.
