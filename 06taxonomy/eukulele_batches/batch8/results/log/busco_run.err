/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/bin/run_busco.sh: line 29: busco_configurator.py: command not found
sed: can't read 06taxonomy/eukulele_batches/batch8/results/busco/config_1507996.bin.1.proteins.ini: No such file or directory
sed: can't read 06taxonomy/eukulele_batches/batch8/results/busco/config_1507996.bin.1.proteins.ini: No such file or directory
sed: can't read 06taxonomy/eukulele_batches/batch8/results/busco/config_1507996.bin.1.proteins.ini: No such file or directory
2025-05-21 13:44:13 ERROR:	Config file 06taxonomy/eukulele_batches/batch8/results/busco/config_1507996.bin.1.proteins.ini cannot be found
2025-05-21 13:44:13 ERROR:	BUSCO analysis failed!
2025-05-21 13:44:13 ERROR:	Check the logs, read the user guide (https://busco.ezlab.org/busco_userguide.html), and check the BUSCO issue board on https://gitlab.com/ezlab/busco/issues

