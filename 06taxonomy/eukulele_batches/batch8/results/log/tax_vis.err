/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/EUKulele/visualize_results.py:340: RuntimeWarning: More than 20 figures have been opened. Figures created through the pyplot interface (`matplotlib.pyplot.figure`) are retained until explicitly closed and may consume too much memory. (To control this warning, see the rcParam `figure.max_open_warning`). Consider using `matplotlib.pyplot.close()`.
  fig = plt.figure(figsize=(15,7.5))
