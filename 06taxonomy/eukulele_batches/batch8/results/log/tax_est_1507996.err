/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/EUKulele/tax_placement.py:95: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`
  if str(tax_table["Classification"][0]) != "":
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/EUKulele/tax_placement.py:95: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`
  if str(tax_table["Classification"][0]) != "":
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/EUKulele/tax_placement.py:95: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`
  if str(tax_table["Classification"][0]) != "":
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/EUKulele/tax_placement.py:95: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`
  if str(tax_table["Classification"][0]) != "":
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/EUKulele/tax_placement.py:95: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`
  if str(tax_table["Classification"][0]) != "":
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/EUKulele/tax_placement.py:95: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`
  if str(tax_table["Classification"][0]) != "":
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/EUKulele/tax_placement.py:95: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`
  if str(tax_table["Classification"][0]) != "":
/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/EUKulele/tax_placement.py:95: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`
  if str(tax_table["Classification"][0]) != "":
