#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH	-q jgi_normal
#SBATCH -J readqc
#SBATCH -N 1   # -c 8
#SBATCH --mem=256GB
#SBATCH -t 24:00:00
#SBATCH --output=cmsearch_analysis_%A_%a.out
#SBATCH --error=cmsearch_analysis_%A_%a.err

eval "$(conda shell.bash hook)"
conda activate /clusterfs/jgi/groups/science/homes/fschulz/miniconda3/envs/snk

# Path to input file
fnadir=$1

#example for sbatch --array=1-n
#readsbase=$(awk 'NR==n' n=$SLURM_ARRAY_TASK_ID $inputs)
#squeue -u komogase

snakemake -j 64 --use-conda --config modeldir="snakes/models" querydir="$fnadir"
