#!/bin/bash

# <PERSON>ript to map MetaBAT2 bins to Acanthamoeba castellani genome using skani
# Activate skani environment
source ~/miniconda3/bin/activate
conda activate skani-env

# Set paths
BINS_DIR="03bins/metabat2_fixed"
REFERENCE="acanthamoeba_castellani_genome.fna"
OUTPUT_DIR="skani_acanthamoeba_mapping"

# Create output directory if it doesn't exist
mkdir -p $OUTPUT_DIR

# Initialize summary file
echo -e "Sample\tBin\tQuery_file\tReference_file\tANI\tAligned_fraction_query\tAligned_fraction_reference\tBidirectional_fragment_mapping\tTotal_query_sequence_length\tTotal_reference_sequence_length" > $OUTPUT_DIR/skani_summary.tsv

echo "Starting skani mapping of bins to Acanthamoeba castellani genome..."
echo "Reference genome: $REFERENCE"
echo "Bins directory: $BINS_DIR"
echo "Output directory: $OUTPUT_DIR"
echo ""

# Process each sample directory
for sample_dir in $BINS_DIR/*/; do
    if [ -d "$sample_dir" ]; then
        sample_name=$(basename "$sample_dir")
        echo "Processing sample: $sample_name"
        
        # Process each bin file in the sample directory
        for bin_file in "$sample_dir"/*.fa; do
            if [ -f "$bin_file" ]; then
                bin_name=$(basename "$bin_file" .fa)
                output_file="$OUTPUT_DIR/${sample_name}_${bin_name}_vs_acanthamoeba.txt"
                
                echo "  Comparing $bin_name to Acanthamoeba castellani..."
                
                # Run skani dist with detailed output
                skani dist "$bin_file" "$REFERENCE" --detailed -t 8 -o "$output_file"
                
                # Extract key metrics and add to summary (skip header line)
                if [ -f "$output_file" ]; then
                    tail -n +2 "$output_file" | while read line; do
                        echo -e "$sample_name\t$bin_name\t$line" >> $OUTPUT_DIR/skani_summary.tsv
                    done
                fi
            fi
        done
        echo ""
    fi
done

echo "Skani mapping completed!"
echo "Results saved in: $OUTPUT_DIR/"
echo "Summary file: $OUTPUT_DIR/skani_summary.tsv"

# Create a filtered summary with high ANI matches (>80%)
echo "Creating filtered summary for high ANI matches (>80%)..."
head -n 1 $OUTPUT_DIR/skani_summary.tsv > $OUTPUT_DIR/skani_high_ani_summary.tsv
awk -F'\t' 'NR>1 && $5 > 80 {print}' $OUTPUT_DIR/skani_summary.tsv >> $OUTPUT_DIR/skani_high_ani_summary.tsv

echo "High ANI matches saved in: $OUTPUT_DIR/skani_high_ani_summary.tsv"
