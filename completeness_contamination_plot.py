#!/usr/bin/env python3
"""
Script to create boxplots showing completeness vs contamination by treatment method.
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from scipy import stats

def create_completeness_contamination_plots(csv_file):
    """
    Create boxplots showing completeness and contamination by treatment method.
    
    Args:
        csv_file (str): Path to the CSV file with quality metrics
    """
    
    # Read the data
    df = pd.read_csv(csv_file)
    
    # Set up the plot style
    plt.style.use('default')
    sns.set_palette("Set2")
    
    # Create figure with subplots
    fig, axes = plt.subplots(1, 2, figsize=(14, 6), dpi=150)
    colors = {'MDA': 'lightblue', 'PTA': 'lightcoral'}
    
    # Main title
    fig.suptitle('Genome Quality Metrics by Treatment Method\n(Discosea Bins)', 
                 fontsize=16, fontweight='bold', y=0.98)
    
    # Plot 1: Completeness
    ax1 = axes[0]
    sns.boxplot(data=df, x='Treatment', y='Avg_Completeness(%)', 
                ax=ax1, palette=colors, width=0.6)
    
    # Add individual points
    sns.stripplot(data=df, x='Treatment', y='Avg_Completeness(%)', 
                  ax=ax1, size=10, alpha=0.8, jitter=0.2, 
                  edgecolor='black', linewidth=0.5, color='darkblue')
    
    ax1.set_xlabel('Treatment Method', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Completeness (%)', fontsize=12, fontweight='bold')
    ax1.set_title('Genome Completeness', fontsize=14, fontweight='bold', pad=15)
    ax1.grid(True, alpha=0.3, axis='y')
    ax1.set_axisbelow(True)
    
    # Add sample labels
    for idx, row in df.iterrows():
        treatment_idx = 0 if row['Treatment'] == 'MDA' else 1
        ax1.annotate(row['Sample'], 
                    (treatment_idx, row['Avg_Completeness(%)']),
                    xytext=(8, 8), textcoords='offset points',
                    fontsize=9, alpha=0.7)
    
    # Plot 2: Contamination
    ax2 = axes[1]
    sns.boxplot(data=df, x='Treatment', y='Avg_Contamination(%)', 
                ax=ax2, palette=colors, width=0.6)
    
    # Add individual points
    sns.stripplot(data=df, x='Treatment', y='Avg_Contamination(%)', 
                  ax=ax2, size=10, alpha=0.8, jitter=0.2, 
                  edgecolor='black', linewidth=0.5, color='darkred')
    
    ax2.set_xlabel('Treatment Method', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Contamination (%)', fontsize=12, fontweight='bold')
    ax2.set_title('Genome Contamination', fontsize=14, fontweight='bold', pad=15)
    ax2.grid(True, alpha=0.3, axis='y')
    ax2.set_axisbelow(True)
    
    # Add sample labels
    for idx, row in df.iterrows():
        treatment_idx = 0 if row['Treatment'] == 'MDA' else 1
        ax2.annotate(row['Sample'], 
                    (treatment_idx, row['Avg_Contamination(%)']),
                    xytext=(8, 8), textcoords='offset points',
                    fontsize=9, alpha=0.7)
    
    # Add summary statistics annotations
    for i, (ax, metric) in enumerate([(ax1, 'Avg_Completeness(%)'), (ax2, 'Avg_Contamination(%)')]):
        for j, treatment in enumerate(df['Treatment'].unique()):
            treatment_data = df[df['Treatment'] == treatment][metric]
            n_samples = len(treatment_data)
            mean_val = treatment_data.mean()
            std_val = treatment_data.std()
            
            # Position text at top of plot
            y_pos = ax.get_ylim()[1] * 0.95
            ax.text(j, y_pos, f'n = {n_samples}\nμ = {mean_val:.1f}%\nσ = {std_val:.1f}%',
                    ha='center', va='top', fontsize=10,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.85)  # Make room for main title
    
    # Save the plot
    output_file = "completeness_contamination_boxplots.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Plot saved as {output_file}")
    
    plt.show()
    
    return fig, axes

def create_scatter_plot(csv_file):
    """
    Create a scatter plot showing completeness vs contamination relationship.
    
    Args:
        csv_file (str): Path to the CSV file with quality metrics
    """
    
    # Read the data
    df = pd.read_csv(csv_file)
    
    # Create scatter plot
    fig, ax = plt.subplots(figsize=(10, 8), dpi=150)
    colors = {'MDA': 'blue', 'PTA': 'red'}
    
    for treatment in df['Treatment'].unique():
        subset = df[df['Treatment'] == treatment]
        ax.scatter(subset['Avg_Completeness(%)'], subset['Avg_Contamination(%)'], 
                  c=colors[treatment], label=treatment, s=120, alpha=0.7, 
                  edgecolors='black', linewidth=0.5)
        
        # Add sample labels
        for idx, row in subset.iterrows():
            ax.annotate(row['Sample'], 
                       (row['Avg_Completeness(%)'], row['Avg_Contamination(%)']),
                       xytext=(5, 5), textcoords='offset points',
                       fontsize=10, alpha=0.8)
    
    ax.set_xlabel('Completeness (%)', fontsize=14, fontweight='bold')
    ax.set_ylabel('Contamination (%)', fontsize=14, fontweight='bold')
    ax.set_title('Genome Quality: Completeness vs Contamination\n(Discosea Bins)', 
                 fontsize=16, fontweight='bold', pad=20)
    ax.legend(title='Treatment Method', fontsize=12, title_fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.set_axisbelow(True)
    
    # Add quality zones
    ax.axhline(y=5, color='green', linestyle='--', alpha=0.5, label='Low contamination (<5%)')
    ax.axvline(x=50, color='orange', linestyle='--', alpha=0.5, label='Medium completeness (>50%)')
    
    plt.tight_layout()
    
    # Save the plot
    output_file = "completeness_vs_contamination_scatter.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Scatter plot saved as {output_file}")
    
    plt.show()
    
    return fig, ax

def print_statistics(csv_file):
    """
    Print detailed statistics for completeness and contamination.
    
    Args:
        csv_file (str): Path to the CSV file with quality metrics
    """
    
    df = pd.read_csv(csv_file)
    
    print("\n" + "="*60)
    print("GENOME QUALITY STATISTICS BY TREATMENT METHOD")
    print("="*60)
    
    # Summary statistics
    summary = df.groupby('Treatment')[['Avg_Completeness(%)', 'Avg_Contamination(%)']].agg([
        'count', 'mean', 'std', 'min', 'max'
    ]).round(2)
    
    print("\nSummary Statistics:")
    print(summary)
    
    # Statistical tests
    mda_completeness = df[df['Treatment'] == 'MDA']['Avg_Completeness(%)'].values
    pta_completeness = df[df['Treatment'] == 'PTA']['Avg_Completeness(%)'].values
    mda_contamination = df[df['Treatment'] == 'MDA']['Avg_Contamination(%)'].values
    pta_contamination = df[df['Treatment'] == 'PTA']['Avg_Contamination(%)'].values
    
    # Mann-Whitney U tests
    comp_stat, comp_p = stats.mannwhitneyu(mda_completeness, pta_completeness, alternative='two-sided')
    cont_stat, cont_p = stats.mannwhitneyu(mda_contamination, pta_contamination, alternative='two-sided')
    
    print(f"\nStatistical Tests (Mann-Whitney U):")
    print(f"\nCompleteness:")
    print(f"  MDA: {mda_completeness.mean():.1f}% ± {mda_completeness.std():.1f}%")
    print(f"  PTA: {pta_completeness.mean():.1f}% ± {pta_completeness.std():.1f}%")
    print(f"  P-value: {comp_p:.4f}")
    print(f"  Result: {'Significant' if comp_p < 0.05 else 'Not significant'} difference")
    
    print(f"\nContamination:")
    print(f"  MDA: {mda_contamination.mean():.1f}% ± {mda_contamination.std():.1f}%")
    print(f"  PTA: {pta_contamination.mean():.1f}% ± {pta_contamination.std():.1f}%")
    print(f"  P-value: {cont_p:.4f}")
    print(f"  Result: {'Significant' if cont_p < 0.05 else 'Not significant'} difference")
    
    # Quality assessment
    print(f"\nQuality Assessment:")
    high_quality = df[(df['Avg_Completeness(%)'] > 50) & (df['Avg_Contamination(%)'] < 10)]
    medium_quality = df[(df['Avg_Completeness(%)'] > 30) & (df['Avg_Contamination(%)'] < 15)]
    
    print(f"High quality bins (>50% complete, <10% contamination): {len(high_quality)}")
    if len(high_quality) > 0:
        print(f"  Samples: {', '.join(high_quality['Sample'].astype(str))}")
    
    print(f"Medium quality bins (>30% complete, <15% contamination): {len(medium_quality)}")
    if len(medium_quality) > 0:
        print(f"  Samples: {', '.join(medium_quality['Sample'].astype(str))}")

def main():
    csv_file = "/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/ssiddikenv/discosea_quality_metrics.csv"
    
    try:
        # Create boxplots
        print("Creating completeness and contamination boxplots...")
        fig1, axes1 = create_completeness_contamination_plots(csv_file)
        
        # Create scatter plot
        print("\nCreating completeness vs contamination scatter plot...")
        fig2, ax2 = create_scatter_plot(csv_file)
        
        # Print statistics
        print_statistics(csv_file)
        
    except FileNotFoundError:
        print(f"Error: File '{csv_file}' not found.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
