Delete intermediate/incorrect versions only after confirming the final version is correct
When you generate corrected results, immediately delete the incorrect files to avoid confusion
Keep only the final, verified results
Save all working scripts in a dedicated scripts/ directory
Check output files for reasonable values (e.g., GC% should be 0-100%, not >100%)
When developing new analysis scripts, test on 1-2 samples before running on all data
Include environment activation in scripts when necessary
Avoid spaces and special characters in filenames
Include random seeds where applicable