2025-05-21 13:36:26 DEBUG:busco.run_BUSCO	Command line: /clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/bin/busco -i 06taxonomy/eukulele_batches/batch4/proteins/1507993.bin.3.proteins.faa -l eukaryota_odb10 -m proteins --cpu 64 --config 06taxonomy/eukulele_batches/batch4/results/busco/config_1507993.bin.3.proteins.ini -o 1507993.bin.3.proteins -f --offline
2025-05-21 13:36:26 INFO:busco.run_BUSCO	***** Start a BUSCO v5.8.3 analysis, current time: 05/21/2025 13:36:26 *****
2025-05-21 13:36:26 DEBUG:busco.ConfigManager	Getting config file
2025-05-21 13:36:26 INFO:busco.ConfigManager	Configuring BUSCO with 06taxonomy/eukulele_batches/batch4/results/busco/config_1507993.bin.3.proteins.ini
2025-05-21 13:36:26 ERROR:busco.BuscoRunner	Config file 06taxonomy/eukulele_batches/batch4/results/busco/config_1507993.bin.3.proteins.ini cannot be found
2025-05-21 13:36:26 DEBUG:busco.BuscoRunner	Config file 06taxonomy/eukulele_batches/batch4/results/busco/config_1507993.bin.3.proteins.ini cannot be found
Traceback (most recent call last):
  File "/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/busco/BuscoConfig.py", line 441, in _load_config_file
    with open(conf_file) as cfg_file:
FileNotFoundError: [Errno 2] No such file or directory: '06taxonomy/eukulele_batches/batch4/results/busco/config_1507993.bin.3.proteins.ini'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/busco/run_BUSCO.py", line 68, in run
    self.load_config()
  File "/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/busco/run_BUSCO.py", line 60, in load_config
    self.config_manager.load_busco_config_main()
  File "/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/busco/BuscoLogger.py", line 62, in wrapped_func
    self.retval = func(*args, **kwargs)
  File "/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/busco/ConfigManager.py", line 62, in load_busco_config_main
    self.config_main.configure()
  File "/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/busco/BuscoConfig.py", line 617, in configure
    self._load_config_file(self.conf_file)
  File "/clusterfs/jgi/groups/science/homes/ssiddik/miniconda3/envs/EUKulele/lib/python3.10/site-packages/busco/BuscoConfig.py", line 444, in _load_config_file
    raise BatchFatalError("Config file {} cannot be found".format(conf_file))
busco.Exceptions.BatchFatalError: Config file 06taxonomy/eukulele_batches/batch4/results/busco/config_1507993.bin.3.proteins.ini cannot be found
2025-05-21 13:36:26 ERROR:busco.BuscoRunner	BUSCO analysis failed!
2025-05-21 13:36:26 ERROR:busco.BuscoRunner	Check the logs, read the user guide (https://busco.ezlab.org/busco_userguide.html), and check the BUSCO issue board on https://gitlab.com/ezlab/busco/issues

