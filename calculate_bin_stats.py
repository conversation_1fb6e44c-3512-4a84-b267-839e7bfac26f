#!/usr/bin/env python3

import os
import sys
import glob
import statistics

def calculate_n50(lengths):
    """Calculate N50 from a list of contig lengths."""
    if not lengths:
        return 0
    
    sorted_lengths = sorted(lengths, reverse=True)
    total_length = sum(sorted_lengths)
    
    running_sum = 0
    for length in sorted_lengths:
        running_sum += length
        if running_sum >= total_length / 2:
            return length
    
    return 0

def calculate_gc_content(sequence):
    """Calculate GC content of a sequence."""
    sequence = sequence.upper()
    g_count = sequence.count('G')
    c_count = sequence.count('C')
    gc_count = g_count + c_count
    
    if len(sequence) > 0:
        return (gc_count / len(sequence)) * 100
    else:
        return 0.0

def process_bin_file(bin_file):
    """Process a bin file and return statistics."""
    contigs = []
    current_header = ""
    current_seq = ""
    
    with open(bin_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line.startswith('>'):
                if current_seq:
                    contigs.append((current_header, current_seq))
                current_header = line
                current_seq = ""
            else:
                current_seq += line
        
        # Add the last contig
        if current_seq:
            contigs.append((current_header, current_seq))
    
    # Calculate statistics
    lengths = [len(seq) for _, seq in contigs]
    num_contigs = len(contigs)
    assembly_size = sum(lengths)
    n50 = calculate_n50(lengths)
    
    # Calculate GC content
    gc_percentages = [calculate_gc_content(seq) for _, seq in contigs]
    if gc_percentages:
        # Calculate weighted average of GC content
        weighted_gc = sum(gc * length for gc, length in zip(gc_percentages, lengths)) / assembly_size
    else:
        weighted_gc = 0.0
    
    return {
        'num_contigs': num_contigs,
        'assembly_size': assembly_size,
        'n50': n50,
        'gc_percent': weighted_gc
    }

def main():
    """Main function to process all bin files."""
    # Create output directory
    os.makedirs('bin_stats_py', exist_ok=True)
    
    # Create output file
    with open('bin_stats_py/bin_stats.tsv', 'w') as out_f:
        out_f.write("Sample\tBin\tNum_contigs\tAssembly_size\tN50\tGC%\n")
        
        # Process each sample
        for sample in ['1507990', '1507992', '1507993', '1507994', '1507995', '1507996', '1507999']:
            bin_dir = f'03bins/metabat2_fixed/{sample}'
            if not os.path.exists(bin_dir):
                continue
            
            # Process each bin file
            for bin_file in sorted(glob.glob(f'{bin_dir}/{sample}.bin.*.fa')):
                bin_name = os.path.basename(bin_file).replace('.fa', '')
                print(f"Processing {sample} - {bin_name}...")
                
                # Calculate statistics
                stats = process_bin_file(bin_file)
                
                # Write to output file
                out_f.write(f"{sample}\t{bin_name}\t{stats['num_contigs']}\t{stats['assembly_size']}\t{stats['n50']}\t{stats['gc_percent']:.2f}\n")
    
    print("Bin statistics calculation completed.")

if __name__ == "__main__":
    main()
