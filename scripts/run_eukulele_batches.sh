#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J eukulele_batch
#SBATCH -N 1
#SBATCH -c 8
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=log/slurm-eukulele_batch_%A.out
#SBATCH --error=log/slurm-eukulele_batch_%A.err

# Define paths
PRODIGAL_DIR="05genes/prodigal"
OUTPUT_DIR="06taxonomy/eukulele_batches"
FINAL_SUMMARY="${OUTPUT_DIR}/eukulele_taxonomy_combined.tsv"

# Create output directories
mkdir -p ${OUTPUT_DIR}

# Initialize the final summary file
echo -e "Bin\tDomain\tKingdom\tSupergroup\tDivision\tSubdivision\tClass\tOrder\tFamily\tGenus\tSpecies" > ${FINAL_SUMMARY}

# Define batches of bins to process
# Batch 1: Small bins from 1507990
BATCH1="1507990.bin.1 1507990.bin.2 1507990.bin.3 1507990.bin.4 1507990.bin.5"

# Batch 2: Small bins from 1507992
BATCH2="1507992.bin.1 1507992.bin.2 1507992.bin.5"

# Batch 3: Medium bins from 1507992
BATCH3="1507992.bin.3 1507992.bin.4"

# Batch 4: Small and medium bins from 1507993
BATCH4="1507993.bin.2 1507993.bin.3 1507993.bin.4"

# Batch 5: Large bins (one at a time)
BATCH5="1507993.bin.1"
BATCH6="1507994.bin.1"
BATCH7="1507995.bin.1"
BATCH8="1507996.bin.1"
BATCH9="1507999.bin.1"

# Function to process a batch of bins
process_batch() {
    local batch_name=$1
    local batch_bins=$2
    
    echo "Processing batch: ${batch_name}"
    
    # Create batch directory
    local batch_dir="${OUTPUT_DIR}/${batch_name}"
    mkdir -p ${batch_dir}/proteins
    
    # Copy protein files for this batch
    for bin in ${batch_bins}; do
        local sample=$(echo ${bin} | cut -d'.' -f1)
        local protein_file="${PRODIGAL_DIR}/${sample}/${bin}/${bin}.proteins.faa"
        
        if [ -f "${protein_file}" ]; then
            echo "  Copying ${protein_file} to ${batch_dir}/proteins/${bin}.proteins.faa"
            cp ${protein_file} ${batch_dir}/proteins/${bin}.proteins.faa
        else
            echo "  Warning: Protein file not found for ${bin}"
        fi
    done
    
    # Run EUKulele on this batch
    echo "  Running EUKulele on batch ${batch_name}..."
    EUKulele --sample_dir ${batch_dir}/proteins \
             --output_dir ${batch_dir}/results \
             -m mags
    
    # Check if EUKulele completed successfully
    if [ -f "${batch_dir}/results/final_taxonomy_mags.csv" ]; then
        echo "  EUKulele completed successfully for batch ${batch_name}"
        
        # Parse the results and append to the final summary
        tail -n +2 ${batch_dir}/results/final_taxonomy_mags.csv | while IFS=',' read -r bin domain kingdom supergroup division subdivision class order family genus species; do
            # Remove quotes and clean up the bin name
            bin=$(echo ${bin} | tr -d '"' | sed 's/.proteins.faa//')
            domain=$(echo ${domain} | tr -d '"')
            kingdom=$(echo ${kingdom} | tr -d '"')
            supergroup=$(echo ${supergroup} | tr -d '"')
            division=$(echo ${division} | tr -d '"')
            subdivision=$(echo ${subdivision} | tr -d '"')
            class=$(echo ${class} | tr -d '"')
            order=$(echo ${order} | tr -d '"')
            family=$(echo ${family} | tr -d '"')
            genus=$(echo ${genus} | tr -d '"')
            species=$(echo ${species} | tr -d '"')
            
            echo -e "${bin}\t${domain}\t${kingdom}\t${supergroup}\t${division}\t${subdivision}\t${class}\t${order}\t${family}\t${genus}\t${species}" >> ${FINAL_SUMMARY}
        done
    else
        echo "  Warning: EUKulele did not complete successfully for batch ${batch_name}"
    fi
    
    echo "Completed batch: ${batch_name}"
    echo ""
}

# Process each batch
process_batch "batch1" "${BATCH1}"
process_batch "batch2" "${BATCH2}"
process_batch "batch3" "${BATCH3}"
process_batch "batch4" "${BATCH4}"
process_batch "batch5" "${BATCH5}"
process_batch "batch6" "${BATCH6}"
process_batch "batch7" "${BATCH7}"
process_batch "batch8" "${BATCH8}"
process_batch "batch9" "${BATCH9}"

# Create a formatted markdown summary
echo "# EUKulele Taxonomic Classification Results" > ${OUTPUT_DIR}/eukulele_taxonomy_combined.md
echo "" >> ${OUTPUT_DIR}/eukulele_taxonomy_combined.md
echo "| Bin | Domain | Kingdom | Supergroup | Division | Class | Order | Family | Genus | Species |" >> ${OUTPUT_DIR}/eukulele_taxonomy_combined.md
echo "|-----|--------|---------|------------|----------|-------|-------|--------|-------|---------|" >> ${OUTPUT_DIR}/eukulele_taxonomy_combined.md

# Parse the combined results for markdown
tail -n +2 ${FINAL_SUMMARY} | while IFS=$'\t' read -r bin domain kingdom supergroup division subdivision class order family genus species; do
    echo "| ${bin} | ${domain} | ${kingdom} | ${supergroup} | ${division} | ${class} | ${order} | ${family} | ${genus} | ${species} |" >> ${OUTPUT_DIR}/eukulele_taxonomy_combined.md
done

echo "" >> ${OUTPUT_DIR}/eukulele_taxonomy_combined.md
echo "Generated on $(date)" >> ${OUTPUT_DIR}/eukulele_taxonomy_combined.md

echo "All batches processed. Combined results available at:"
echo "  ${FINAL_SUMMARY}"
echo "  ${OUTPUT_DIR}/eukulele_taxonomy_combined.md"
