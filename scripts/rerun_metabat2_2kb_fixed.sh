#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J metabat2_fixed
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-metabat2_fixed_%A_%a.out
#SBATCH --error=slurm-metabat2_fixed_%A_%a.err
#SBATCH --array=1-7

# Get the sample ID from the sample list file
SAMPLE_ID=$(sed -n "${SLURM_ARRAY_TASK_ID}p" config/sample_list.txt)

# Define directories
ASSEMBLY_DIR="01assemblies"
BAM_DIR="02mapping"
OUTPUT_DIR="03bins/metabat2_fixed"

# Define resources
THREADS=16
MIN_CONTIG_LENGTH=2000  # Minimum contig length for binning (2kb)
MIN_CLUSTER_SIZE=200000 # Minimum size of a bin (default)
SEED=123                # Seed for reproducibility

# Create output directory
mkdir -p ${OUTPUT_DIR}/${SAMPLE_ID}

# Path to scaffold file
SCAFFOLD_FILE=${ASSEMBLY_DIR}/${SAMPLE_ID}/scaffolds.fasta.gz
BAM_FILE=${BAM_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam

# Create a temporary directory for intermediate files
TEMP_DIR=${OUTPUT_DIR}/${SAMPLE_ID}/temp
mkdir -p ${TEMP_DIR}

# Decompress scaffold file
echo "Decompressing scaffold file for ${SAMPLE_ID}..."
gunzip -c ${SCAFFOLD_FILE} > ${TEMP_DIR}/scaffolds.fasta

# Generate depth file using jgi_summarize_bam_contig_depths
echo "Generating depth file for ${SAMPLE_ID}..."
singularity exec metabat2_latest.sif jgi_summarize_bam_contig_depths --outputDepth ${TEMP_DIR}/${SAMPLE_ID}.depth.txt ${BAM_FILE}

# Run MetaBAT2 with 2kb minimum contig length and seed
echo "Running MetaBAT2 for ${SAMPLE_ID} with 2kb minimum contig length and seed=${SEED}..."
singularity exec metabat2_latest.sif metabat2 -i ${TEMP_DIR}/scaffolds.fasta \
    -a ${TEMP_DIR}/${SAMPLE_ID}.depth.txt \
    -o ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.bin \
    -t ${THREADS} \
    -m ${MIN_CONTIG_LENGTH} \
    -s ${MIN_CLUSTER_SIZE} \
    --seed ${SEED} \
    -v

# Count the number of bins
BIN_COUNT=$(ls ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.bin.*.fa 2>/dev/null | wc -l)
echo "MetaBAT2 generated ${BIN_COUNT} bins for ${SAMPLE_ID}"

# Generate a summary file
echo "Sample ID: ${SAMPLE_ID}" > ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt
echo "Number of bins: ${BIN_COUNT}" >> ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt
echo "Bins:" >> ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt

# List each bin with its size
if [ ${BIN_COUNT} -gt 0 ]; then
    for BIN_FILE in ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.bin.*.fa; do
        BIN_NAME=$(basename ${BIN_FILE})
        CONTIG_COUNT=$(grep -c ">" ${BIN_FILE})
        BIN_SIZE=$(grep -v ">" ${BIN_FILE} | tr -d '\n' | wc -c)
        echo "${BIN_NAME}: ${CONTIG_COUNT} contigs, ${BIN_SIZE} bp" >> ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt
    done
fi

# Clean up temporary files
rm -rf ${TEMP_DIR}

echo "MetaBAT2 binning completed for ${SAMPLE_ID}"
