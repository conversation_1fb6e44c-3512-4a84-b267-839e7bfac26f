#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J eukulele
#SBATCH -N 1
#SBATCH -c 8
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=log/slurm-eukulele_%A.out
#SBATCH --error=log/slurm-eukulele_%A.err

# Define paths
PRODIGAL_DIR="05genes/prodigal"
OUTPUT_DIR="06taxonomy/eukulele"
PROTEINS_DIR="${OUTPUT_DIR}/all_proteins"

# Create output directories
mkdir -p ${OUTPUT_DIR}
mkdir -p ${PROTEINS_DIR}

# Copy all protein files to a single directory for EUKulele
echo "Copying protein files to ${PROTEINS_DIR}..."
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    for BIN_DIR in $(find ${PRODIGAL_DIR}/${SAMPLE} -type d -name "${SAMPLE}.bin.*"); do
        BIN_NAME=$(basename ${BIN_DIR})
        PROTEIN_FILE="${BIN_DIR}/${BIN_NAME}.proteins.faa"
        
        if [ -f "${PROTEIN_FILE}" ]; then
            echo "  Copying ${PROTEIN_FILE} to ${PROTEINS_DIR}/${BIN_NAME}.proteins.faa"
            cp ${PROTEIN_FILE} ${PROTEINS_DIR}/${BIN_NAME}.proteins.faa
        else
            echo "  Warning: Protein file not found for ${BIN_NAME}"
        fi
    done
done

# Run EUKulele on all protein files
echo "Running EUKulele on all protein files..."
EUKulele --sample_dir ${PROTEINS_DIR} \
         --output_dir ${OUTPUT_DIR}/results \
         -m mags

echo "EUKulele analysis completed."

# Create a summary of the taxonomic classifications
echo "Creating summary of taxonomic classifications..."
echo -e "Bin\tDomain\tKingdom\tSupergroup\tDivision\tSubdivision\tClass\tOrder\tFamily\tGenus\tSpecies" > ${OUTPUT_DIR}/eukulele_taxonomy.tsv

# Parse the EUKulele results
if [ -f "${OUTPUT_DIR}/results/final_taxonomy_mags.csv" ]; then
    # Skip the header line
    tail -n +2 ${OUTPUT_DIR}/results/final_taxonomy_mags.csv | while IFS=',' read -r bin domain kingdom supergroup division subdivision class order family genus species; do
        # Remove quotes and clean up the bin name
        bin=$(echo ${bin} | tr -d '"' | sed 's/.proteins.faa//')
        domain=$(echo ${domain} | tr -d '"')
        kingdom=$(echo ${kingdom} | tr -d '"')
        supergroup=$(echo ${supergroup} | tr -d '"')
        division=$(echo ${division} | tr -d '"')
        subdivision=$(echo ${subdivision} | tr -d '"')
        class=$(echo ${class} | tr -d '"')
        order=$(echo ${order} | tr -d '"')
        family=$(echo ${family} | tr -d '"')
        genus=$(echo ${genus} | tr -d '"')
        species=$(echo ${species} | tr -d '"')
        
        echo -e "${bin}\t${domain}\t${kingdom}\t${supergroup}\t${division}\t${subdivision}\t${class}\t${order}\t${family}\t${genus}\t${species}" >> ${OUTPUT_DIR}/eukulele_taxonomy.tsv
    done
    
    echo "Taxonomy summary created at ${OUTPUT_DIR}/eukulele_taxonomy.tsv"
else
    echo "Warning: EUKulele results file not found"
fi

# Create a formatted markdown summary
echo "# EUKulele Taxonomic Classification Results" > ${OUTPUT_DIR}/eukulele_taxonomy.md
echo "" >> ${OUTPUT_DIR}/eukulele_taxonomy.md
echo "| Bin | Domain | Kingdom | Supergroup | Division | Class | Order | Family | Genus | Species |" >> ${OUTPUT_DIR}/eukulele_taxonomy.md
echo "|-----|--------|---------|------------|----------|-------|-------|--------|-------|---------|" >> ${OUTPUT_DIR}/eukulele_taxonomy.md

# Parse the EUKulele results for markdown
if [ -f "${OUTPUT_DIR}/results/final_taxonomy_mags.csv" ]; then
    # Skip the header line
    tail -n +2 ${OUTPUT_DIR}/results/final_taxonomy_mags.csv | while IFS=',' read -r bin domain kingdom supergroup division subdivision class order family genus species; do
        # Remove quotes and clean up the bin name
        bin=$(echo ${bin} | tr -d '"' | sed 's/.proteins.faa//')
        domain=$(echo ${domain} | tr -d '"')
        kingdom=$(echo ${kingdom} | tr -d '"')
        supergroup=$(echo ${supergroup} | tr -d '"')
        division=$(echo ${division} | tr -d '"')
        class=$(echo ${class} | tr -d '"')
        order=$(echo ${order} | tr -d '"')
        family=$(echo ${family} | tr -d '"')
        genus=$(echo ${genus} | tr -d '"')
        species=$(echo ${species} | tr -d '"')
        
        echo "| ${bin} | ${domain} | ${kingdom} | ${supergroup} | ${division} | ${class} | ${order} | ${family} | ${genus} | ${species} |" >> ${OUTPUT_DIR}/eukulele_taxonomy.md
    done
    
    echo "" >> ${OUTPUT_DIR}/eukulele_taxonomy.md
    echo "Generated on $(date)" >> ${OUTPUT_DIR}/eukulele_taxonomy.md
    
    echo "Formatted taxonomy summary created at ${OUTPUT_DIR}/eukulele_taxonomy.md"
else
    echo "Warning: EUKulele results file not found"
fi
