#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J metabat2
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-metabat2_%A_%a.out
#SBATCH --error=slurm-metabat2_%A_%a.err
#SBATCH --array=1-7

# Get the sample ID from the sample list file
SAMPLE_ID=$(sed -n "${SLURM_ARRAY_TASK_ID}p" config/sample_list.txt)

# Define directories
ASSEMBLY_DIR="01assemblies"
BAM_DIR="02mapping"
OUTPUT_DIR="03bins/metabat2"

# Define resources
THREADS=16
MIN_CONTIG_LENGTH=1500  # Minimum contig length for binning

# Pull the MetaBAT2 Docker image if not already available
if [ ! -f "metabat2_latest.sif" ]; then
    echo "Pulling MetaBAT2 Docker image..."
    singularity pull docker://metabat/metabat:latest
    mv metabat_latest.sif metabat2_latest.sif
fi

# Run MetaBAT2 using Apptainer/Singularity
echo "Processing binning for sample: ${SAMPLE_ID}"
singularity exec metabat2_latest.sif bash scripts/run_metabat2.sh \
    ${SAMPLE_ID} \
    ${ASSEMBLY_DIR} \
    ${BAM_DIR} \
    ${OUTPUT_DIR} \
    ${THREADS} \
    ${MIN_CONTIG_LENGTH}

echo "Binning completed for sample: ${SAMPLE_ID}"
