#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J spades_sc
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=128GB
#SBATCH -t 48:00:00
#SBATCH --output=slurm-%A_%a.out
#SBATCH --error=slurm-%A_%a.err
#SBATCH --array=1-7

# Create logs directory
mkdir -p logs

# Get the sample ID from the sample list file
SAMPLE_ID=$(sed -n "${SLURM_ARRAY_TASK_ID}p" config/sample_list.txt)

# Define directories
INPUT_DIR="00data/readsf"
OUTPUT_DIR="01assemblies"

# Create output directory if it doesn't exist
mkdir -p ${OUTPUT_DIR}

# Define resources
THREADS=16
MEMORY=128

# Pull the SPAdes Docker image if not already available
if [ ! -f "spades_latest.sif" ]; then
    echo "Pulling SPAdes Docker image..."
    singularity pull docker://quay.io/biocontainers/spades:3.15.5--h95f258a_1
    mv spades_3.15.5--h95f258a_1.sif spades_latest.sif
fi

# Run SPAdes using Apptainer/Singularity
echo "Processing sample: ${SAMPLE_ID}"
singularity exec spades_latest.sif bash scripts/run_spades_sc.sh \
    ${SAMPLE_ID} \
    ${INPUT_DIR} \
    ${OUTPUT_DIR} \
    ${THREADS} \
    ${MEMORY}

echo "Assembly completed for sample: ${SAMPLE_ID}"
