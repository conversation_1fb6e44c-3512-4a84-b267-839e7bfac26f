#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J gtdbtk_simple
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=128GB
#SBATCH -t 48:00:00
#SBATCH --output=slurm-gtdbtk_simple_%A.out
#SBATCH --error=slurm-gtdbtk_simple_%A.err

# Define paths
BINS_DIR="03bins/metabat2_fixed"
OUTPUT_DIR="04quality/gtdbtk_simple"

# Create output directory
mkdir -p ${OUTPUT_DIR}/bins

# Copy all bins to the GTDB-Tk bins directory
echo "Copying bins to GTDB-Tk directory..."
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    for BIN_FILE in $(find ${BINS_DIR}/${SAMPLE} -name "${SAMPLE}.bin.*.fa"); do
        BIN_NAME=$(basename ${BIN_FILE})
        echo "Copying ${BIN_FILE} to ${OUTPUT_DIR}/bins/${BIN_NAME}"
        cp ${BIN_FILE} ${OUTPUT_DIR}/bins/${BIN_NAME}
    done
done

# Check if bins were copied
echo "Checking bins directory..."
ls -la ${OUTPUT_DIR}/bins/

# Run GTDB-Tk with the parameters that worked for you
echo "Running GTDB-Tk on all bins..."
gtdbtk classify_wf --genome_dir ${OUTPUT_DIR}/bins \
                   --out_dir ${OUTPUT_DIR} \
                   --extension fa \
                   --cpus 16

echo "GTDB-Tk analysis completed."

# Create a simplified summary file for bacterial results
if [ -f "${OUTPUT_DIR}/classify/gtdbtk.bac120.summary.tsv" ]; then
    echo -e "Bin\tDomain\tPhylum\tClass\tOrder\tFamily\tGenus\tSpecies\tFastANI_Reference\tFastANI_ANI\tClassification_Method" > ${OUTPUT_DIR}/gtdbtk_bac_summary.tsv
    
    # Parse the bacterial results
    tail -n +2 ${OUTPUT_DIR}/classify/gtdbtk.bac120.summary.tsv | while IFS=$'\t' read -r user_genome classification fastani_reference fastani_ani fastani_af closest_placement_reference closest_placement_radius closest_placement_ani closest_placement_af pplacer_taxonomy classification_method note other_related_references msa_percent aa_percent translation_table red_value warnings; do
        domain=$(echo ${pplacer_taxonomy} | cut -d';' -f1)
        phylum=$(echo ${pplacer_taxonomy} | cut -d';' -f2)
        class=$(echo ${pplacer_taxonomy} | cut -d';' -f3)
        order=$(echo ${pplacer_taxonomy} | cut -d';' -f4)
        family=$(echo ${pplacer_taxonomy} | cut -d';' -f5)
        genus=$(echo ${pplacer_taxonomy} | cut -d';' -f6)
        species=$(echo ${pplacer_taxonomy} | cut -d';' -f7)
        
        echo -e "${user_genome}\t${domain}\t${phylum}\t${class}\t${order}\t${family}\t${genus}\t${species}\t${fastani_reference}\t${fastani_ani}\t${classification_method}" >> ${OUTPUT_DIR}/gtdbtk_bac_summary.tsv
    done
    
    echo "GTDB-Tk bacterial summary created at ${OUTPUT_DIR}/gtdbtk_bac_summary.tsv"
fi

# Create a simplified summary file for archaeal results
if [ -f "${OUTPUT_DIR}/classify/gtdbtk.ar53.summary.tsv" ]; then
    echo -e "Bin\tDomain\tPhylum\tClass\tOrder\tFamily\tGenus\tSpecies\tFastANI_Reference\tFastANI_ANI\tClassification_Method" > ${OUTPUT_DIR}/gtdbtk_ar_summary.tsv
    
    # Parse the archaeal results
    tail -n +2 ${OUTPUT_DIR}/classify/gtdbtk.ar53.summary.tsv | while IFS=$'\t' read -r user_genome classification fastani_reference fastani_ani fastani_af closest_placement_reference closest_placement_radius closest_placement_ani closest_placement_af pplacer_taxonomy classification_method note other_related_references msa_percent aa_percent translation_table red_value warnings; do
        domain=$(echo ${pplacer_taxonomy} | cut -d';' -f1)
        phylum=$(echo ${pplacer_taxonomy} | cut -d';' -f2)
        class=$(echo ${pplacer_taxonomy} | cut -d';' -f3)
        order=$(echo ${pplacer_taxonomy} | cut -d';' -f4)
        family=$(echo ${pplacer_taxonomy} | cut -d';' -f5)
        genus=$(echo ${pplacer_taxonomy} | cut -d';' -f6)
        species=$(echo ${pplacer_taxonomy} | cut -d';' -f7)
        
        echo -e "${user_genome}\t${domain}\t${phylum}\t${class}\t${order}\t${family}\t${genus}\t${species}\t${fastani_reference}\t${fastani_ani}\t${classification_method}" >> ${OUTPUT_DIR}/gtdbtk_ar_summary.tsv
    done
    
    echo "GTDB-Tk archaeal summary created at ${OUTPUT_DIR}/gtdbtk_ar_summary.tsv"
fi
