#!/bin/bash

# Output file
OUTPUT_FILE="discosea_summary.tsv"
OUTPUT_MD="discosea_summary.md"

# Create header for the output file
echo -e "Bin\tCompleteness(%)\tContamination(%)\tTotal_Length(bp)\tNum_Contigs\tAvg_Depth\tTaxonomy" > ${OUTPUT_FILE}

# Get bin statistics
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    # Find bin stats file
    BIN_INFO_FILE="03bins/metabat2_fixed/${SAMPLE}/${SAMPLE}.bin.BinInfo.txt"
    
    if [ -f "${BIN_INFO_FILE}" ]; then
        # Skip header line
        tail -n +2 "${BIN_INFO_FILE}" | while IFS=$'\t' read -r bin_num num_contigs total_length avg_coverage filename; do
            # Extract bin name from filename
            bin_name=$(basename ${filename} .fa)
            
            # Get CheckM2 results
            CHECKM2_FILE="04quality/checkm2_working/${SAMPLE}/quality_report.tsv"
            COMPLETENESS="NA"
            CONTAMINATION="NA"
            
            if [ -f "${CHECKM2_FILE}" ]; then
                # Extract completeness and contamination
                COMP_CONT=$(grep "${bin_name}" "${CHECKM2_FILE}" | cut -f2,3)
                if [ ! -z "${COMP_CONT}" ]; then
                    COMPLETENESS=$(echo "${COMP_CONT}" | cut -f1)
                    CONTAMINATION=$(echo "${COMP_CONT}" | cut -f2)
                fi
            fi
            
            # Get taxonomy from EUKulele results
            TAXONOMY="NA"
            EUKULELE_FILE="06taxonomy/eukulele_batches/eukulele_taxonomy_extracted.tsv"
            
            if [ -f "${EUKULELE_FILE}" ]; then
                # Check if this bin has Discosea classification
                TAXONOMY_LINE=$(grep "${bin_name}" "${EUKULELE_FILE}" | grep -i "Discosea")
                if [ ! -z "${TAXONOMY_LINE}" ]; then
                    TAXONOMY=$(echo "${TAXONOMY_LINE}" | cut -f2-10 | tr '\t' ';')
                    
                    # Add to the output file
                    echo -e "${bin_name}\t${COMPLETENESS}\t${CONTAMINATION}\t${total_length}\t${num_contigs}\t${avg_coverage}\t${TAXONOMY}" >> ${OUTPUT_FILE}
                fi
            fi
        done
    else
        echo "Warning: Bin info file not found for ${SAMPLE}"
    fi
done

# Create markdown summary
echo "# Discosea Bins Summary" > ${OUTPUT_MD}
echo "" >> ${OUTPUT_MD}
echo "| Bin | Completeness(%) | Contamination(%) | Total Length(bp) | Num Contigs | Avg Depth | Taxonomy |" >> ${OUTPUT_MD}
echo "|-----|----------------|-----------------|-----------------|------------|-----------|----------|" >> ${OUTPUT_MD}

# Add data to markdown
tail -n +2 ${OUTPUT_FILE} | while IFS=$'\t' read -r bin completeness contamination length contigs depth taxonomy; do
    echo "| ${bin} | ${completeness} | ${contamination} | ${length} | ${contigs} | ${depth} | ${taxonomy} |" >> ${OUTPUT_MD}
done

echo "" >> ${OUTPUT_MD}
echo "Generated on $(date)" >> ${OUTPUT_MD}

echo "Summary created at:"
echo "  ${OUTPUT_FILE}"
echo "  ${OUTPUT_MD}"

# Display the markdown summary
cat ${OUTPUT_MD}
