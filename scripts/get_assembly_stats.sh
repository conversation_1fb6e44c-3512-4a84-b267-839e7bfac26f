#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J assembly_stats
#SBATCH -N 1
#SBATCH -c 4
#SBATCH --mem=16GB
#SBATCH -t 1:00:00
#SBATCH --output=slurm-assembly_stats_%A.out
#SBATCH --error=slurm-assembly_stats_%A.err

# Create output directory for stats
mkdir -p assembly_stats

# Pull seqkit Docker image
if [ ! -f "seqkit_latest.sif" ]; then
    echo "Pulling seqkit Docker image..."
    singularity pull docker://quay.io/biocontainers/seqkit:2.5.1--h9ee0642_0
    mv seqkit_2.5.1--h9ee0642_0.sif seqkit_latest.sif
fi

# Create header for the stats file
echo -e "Sample\tFormat\tType\tNum_seqs\tSum_len\tMin_len\tAvg_len\tMax_len\tN50\tGC%" > assembly_stats/assembly_stats.tsv

# Process each scaffold file
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    SCAFFOLD_FILE="01assemblies/${SAMPLE}/scaffolds.fasta.gz"
    
    if [ -f "$SCAFFOLD_FILE" ]; then
        echo "Processing $SAMPLE..."
        
        # Run seqkit stats
        singularity exec seqkit_latest.sif seqkit stats -T "$SCAFFOLD_FILE" > assembly_stats/${SAMPLE}_stats.tsv
        
        # Extract the stats (excluding the header)
        tail -n 1 assembly_stats/${SAMPLE}_stats.tsv | awk -v sample="$SAMPLE" '{print sample"\t"$0}' >> assembly_stats/assembly_stats.tsv
    else
        echo "Scaffold file for $SAMPLE not found."
    fi
done

echo "Assembly statistics calculation completed."
