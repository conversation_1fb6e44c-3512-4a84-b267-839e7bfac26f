#!/bin/bash

# Define paths
OUTPUT_DIR="06taxonomy/eukulele_batches"
FINAL_SUMMARY="${OUTPUT_DIR}/eukulele_taxonomy_extracted.tsv"
FINAL_SUMMARY_MD="${OUTPUT_DIR}/eukulele_taxonomy_extracted.md"

# Initialize the final summary file
echo -e "Bin\tDomain\tKingdom\tSupergroup\tDivision\tClass\tOrder\tFamily\tGenus\tSpecies\tConfidence" > ${FINAL_SUMMARY}

# Process each batch directory
for BATCH_DIR in $(find ${OUTPUT_DIR} -type d -name "batch*" | sort); do
    BATCH=$(basename ${BATCH_DIR})
    echo "Processing ${BATCH}..."
    
    # Find all estimated taxonomy files
    for TAXONOMY_FILE in $(find ${BATCH_DIR}/results/taxonomy_estimation -name "*-estimated-taxonomy.out" | sort); do
        BIN=$(basename ${TAXONOMY_FILE} | sed 's/-estimated-taxonomy.out//' | sed 's/.proteins//')
        echo "  Processing ${BIN}..."
        
        # Count the number of classifications at each taxonomic level
        DOMAIN_COUNTS=$(grep -v "transcript_name" ${TAXONOMY_FILE} | grep "domain" | cut -f4 | sort | uniq -c | sort -nr)
        KINGDOM_COUNTS=$(grep -v "transcript_name" ${TAXONOMY_FILE} | grep "kingdom" | cut -f4 | sort | uniq -c | sort -nr)
        SUPERGROUP_COUNTS=$(grep -v "transcript_name" ${TAXONOMY_FILE} | grep "supergroup" | cut -f4 | sort | uniq -c | sort -nr)
        DIVISION_COUNTS=$(grep -v "transcript_name" ${TAXONOMY_FILE} | grep "division" | cut -f4 | sort | uniq -c | sort -nr)
        CLASS_COUNTS=$(grep -v "transcript_name" ${TAXONOMY_FILE} | grep "class" | cut -f4 | sort | uniq -c | sort -nr)
        ORDER_COUNTS=$(grep -v "transcript_name" ${TAXONOMY_FILE} | grep "order" | cut -f4 | sort | uniq -c | sort -nr)
        FAMILY_COUNTS=$(grep -v "transcript_name" ${TAXONOMY_FILE} | grep "family" | cut -f4 | sort | uniq -c | sort -nr)
        GENUS_COUNTS=$(grep -v "transcript_name" ${TAXONOMY_FILE} | grep "genus" | cut -f4 | sort | uniq -c | sort -nr)
        SPECIES_COUNTS=$(grep -v "transcript_name" ${TAXONOMY_FILE} | grep "species" | cut -f4 | sort | uniq -c | sort -nr)
        
        # Get the top classification at each level
        DOMAIN=$(echo "${DOMAIN_COUNTS}" | head -1 | awk '{print $2}')
        KINGDOM=$(echo "${KINGDOM_COUNTS}" | head -1 | awk '{print $2}')
        SUPERGROUP=$(echo "${SUPERGROUP_COUNTS}" | head -1 | awk '{print $2}')
        DIVISION=$(echo "${DIVISION_COUNTS}" | head -1 | awk '{print $2}')
        CLASS=$(echo "${CLASS_COUNTS}" | head -1 | awk '{print $2}')
        ORDER=$(echo "${ORDER_COUNTS}" | head -1 | awk '{print $2}')
        FAMILY=$(echo "${FAMILY_COUNTS}" | head -1 | awk '{print $2}')
        GENUS=$(echo "${GENUS_COUNTS}" | head -1 | awk '{print $2}')
        SPECIES=$(echo "${SPECIES_COUNTS}" | head -1 | awk '{print $2}')
        
        # Calculate confidence based on the number of proteins with domain-level classification
        TOTAL_PROTEINS=$(grep -v "transcript_name" ${TAXONOMY_FILE} | wc -l)
        DOMAIN_CLASSIFIED=$(grep -v "transcript_name" ${TAXONOMY_FILE} | grep "domain" | wc -l)
        if [ ${TOTAL_PROTEINS} -gt 0 ]; then
            CONFIDENCE=$(echo "scale=2; ${DOMAIN_CLASSIFIED} * 100 / ${TOTAL_PROTEINS}" | bc)
        else
            CONFIDENCE="0"
        fi
        
        # Set default values for empty fields
        [ -z "${DOMAIN}" ] && DOMAIN="Unknown"
        [ -z "${KINGDOM}" ] && KINGDOM="Unknown"
        [ -z "${SUPERGROUP}" ] && SUPERGROUP="Unknown"
        [ -z "${DIVISION}" ] && DIVISION="Unknown"
        [ -z "${CLASS}" ] && CLASS="Unknown"
        [ -z "${ORDER}" ] && ORDER="Unknown"
        [ -z "${FAMILY}" ] && FAMILY="Unknown"
        [ -z "${GENUS}" ] && GENUS="Unknown"
        [ -z "${SPECIES}" ] && SPECIES="Unknown"
        
        # Add to the summary file
        echo -e "${BIN}\t${DOMAIN}\t${KINGDOM}\t${SUPERGROUP}\t${DIVISION}\t${CLASS}\t${ORDER}\t${FAMILY}\t${GENUS}\t${SPECIES}\t${CONFIDENCE}" >> ${FINAL_SUMMARY}
    done
done

# Create a formatted markdown summary
echo "# EUKulele Taxonomic Classification Results (Extracted)" > ${FINAL_SUMMARY_MD}
echo "" >> ${FINAL_SUMMARY_MD}
echo "| Bin | Domain | Kingdom | Supergroup | Division | Class | Order | Family | Genus | Species | Confidence (%) |" >> ${FINAL_SUMMARY_MD}
echo "|-----|--------|---------|------------|----------|-------|-------|--------|-------|---------|---------------|" >> ${FINAL_SUMMARY_MD}

# Parse the combined results for markdown
tail -n +2 ${FINAL_SUMMARY} | while IFS=$'\t' read -r bin domain kingdom supergroup division class order family genus species confidence; do
    echo "| ${bin} | ${domain} | ${kingdom} | ${supergroup} | ${division} | ${class} | ${order} | ${family} | ${genus} | ${species} | ${confidence} |" >> ${FINAL_SUMMARY_MD}
done

echo "" >> ${FINAL_SUMMARY_MD}
echo "Generated on $(date)" >> ${FINAL_SUMMARY_MD}

echo "Extracted taxonomy summary created at:"
echo "  ${FINAL_SUMMARY}"
echo "  ${FINAL_SUMMARY_MD}"
