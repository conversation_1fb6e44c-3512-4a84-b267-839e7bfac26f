#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J bwa_align
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-bwa_%A_%a.out
#SBATCH --error=slurm-bwa_%A_%a.err
#SBATCH --array=1-7

# Get the sample ID from the sample list file
SAMPLE_ID=$(sed -n "${SLURM_ARRAY_TASK_ID}p" config/sample_list.txt)

# Define directories
READS_DIR="00data/readsf"
ASSEMBLY_DIR="01assemblies"
OUTPUT_DIR="02mapping"

# Define resources
THREADS=16

# Pull the BWA and Samtools Docker image if not already available
if [ ! -f "genomics_latest.sif" ]; then
    echo "Pulling genomics Docker image..."
    singularity pull docker://quay.io/biocontainers/mulled-v2-fe8faa35dbf6dc65a0f7f5d4ea12e31a79f73e40:219b6c272b25e7e642ae3ff0bf0c5c81a5135ab4-0
    mv mulled-v2-fe8faa35dbf6dc65a0f7f5d4ea12e31a79f73e40_219b6c272b25e7e642ae3ff0bf0c5c81a5135ab4-0.sif genomics_latest.sif
fi

# Run BWA alignment using Apptainer/Singularity
echo "Processing alignment for sample: ${SAMPLE_ID}"

# Create output directory
mkdir -p ${OUTPUT_DIR}/${SAMPLE_ID}

# Create a temporary directory for intermediate files
TEMP_DIR=${OUTPUT_DIR}/${SAMPLE_ID}/temp
mkdir -p ${TEMP_DIR}

# Path to scaffold file
SCAFFOLD_FILE=${ASSEMBLY_DIR}/${SAMPLE_ID}/scaffolds.fasta.gz

# Decompress scaffold file for indexing
gunzip -c ${SCAFFOLD_FILE} > ${TEMP_DIR}/scaffolds.fasta

# Index the scaffold file with BWA
echo "Indexing scaffold file for ${SAMPLE_ID}..."
singularity exec genomics_latest.sif bwa index ${TEMP_DIR}/scaffolds.fasta

# Align reads to the scaffold using BWA-MEM and pipe to samtools for BAM conversion and sorting
echo "Aligning reads to scaffolds and creating sorted BAM for ${SAMPLE_ID}..."
singularity exec genomics_latest.sif bash -c "bwa mem -t ${THREADS} ${TEMP_DIR}/scaffolds.fasta ${READS_DIR}/${SAMPLE_ID}.anqdpht.fastq.gz | samtools view -bS - | samtools sort -@ ${THREADS} -o ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam -"

# Index the BAM file
echo "Indexing BAM file for ${SAMPLE_ID}..."
singularity exec genomics_latest.sif samtools index ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam

# Generate mapping statistics
echo "Generating mapping statistics for ${SAMPLE_ID}..."
singularity exec genomics_latest.sif samtools flagstat ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam > ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.flagstat.txt

# Clean up temporary files
rm -rf ${TEMP_DIR}

echo "Alignment completed for sample: ${SAMPLE_ID}"
