#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J eukcc_conda
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-eukcc_conda_%A.out
#SBATCH --error=slurm-eukcc_conda_%A.err

# Define paths
BINS_DIR="03bins/metabat2_fixed"
OUTPUT_DIR="04quality/eukcc_conda"
EUKCC_DB="/clusterfs/jgi/scratch/science/mgs/nelli/databases/eukCC/eukccdb/eukcc2_db_ver_1.2"

# Create output directory
mkdir -p ${OUTPUT_DIR}

# Activate conda environment
echo "Activating conda environment..."
source ~/miniconda3/bin/activate

# Create a list of all bin files
find ${BINS_DIR} -name "*.bin.*.fa" > ${OUTPUT_DIR}/bin_list.txt
echo "Found $(wc -l < ${OUTPUT_DIR}/bin_list.txt) bin files."

# Run EukCC on each bin
echo "Running EukCC on all bins..."
while read -r BIN_FILE; do
    BIN_NAME=$(basename ${BIN_FILE} .fa)
    SAMPLE_ID=$(echo ${BIN_NAME} | cut -d'.' -f1)
    
    echo "Processing ${BIN_NAME}..."
    
    # Create output directory for this bin
    mkdir -p ${OUTPUT_DIR}/${SAMPLE_ID}
    
    # Run EukCC
    eukcc single \
        --db ${EUKCC_DB} \
        --threads 16 \
        --out ${OUTPUT_DIR}/${SAMPLE_ID}/${BIN_NAME} \
        ${BIN_FILE}
    
    echo "Completed ${BIN_NAME}"
done < ${OUTPUT_DIR}/bin_list.txt

echo "EukCC analysis completed."

# Create a summary file
echo -e "Bin\tCompleteness\tContamination\tTaxonomy" > ${OUTPUT_DIR}/eukcc_summary.tsv

# Parse the results
find ${OUTPUT_DIR} -name "*.csv" | while read -r RESULT_FILE; do
    BIN_DIR=$(dirname ${RESULT_FILE})
    BIN_NAME=$(basename ${BIN_DIR})
    
    # Skip the first line (header) and get the second line (results)
    RESULT_LINE=$(tail -n +2 ${RESULT_FILE} | head -n 1)
    
    if [ ! -z "${RESULT_LINE}" ]; then
        # Extract completeness, contamination, and taxonomy
        COMPLETENESS=$(echo ${RESULT_LINE} | cut -d',' -f2)
        CONTAMINATION=$(echo ${RESULT_LINE} | cut -d',' -f3)
        TAXONOMY=$(echo ${RESULT_LINE} | cut -d',' -f4)
        
        echo -e "${BIN_NAME}\t${COMPLETENESS}\t${CONTAMINATION}\t${TAXONOMY}" >> ${OUTPUT_DIR}/eukcc_summary.tsv
    else
        echo -e "${BIN_NAME}\tNA\tNA\tNA" >> ${OUTPUT_DIR}/eukcc_summary.tsv
    fi
done

echo "EukCC summary created at ${OUTPUT_DIR}/eukcc_summary.tsv"
