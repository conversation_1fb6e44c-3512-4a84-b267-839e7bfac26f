#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J prodigal_bins
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=log/slurm-prodigal_bins_%A.out
#SBATCH --error=log/slurm-prodigal_bins_%A.err

# Define paths
BINS_DIR="03bins/metabat2_fixed"
OUTPUT_DIR="05genes/prodigal"
GENOMICS_SIF="./genomics_latest.sif"

# Create output directory
mkdir -p ${OUTPUT_DIR}

# Check if the genomics container has Prodigal
echo "Checking if Prodigal is available in the genomics container..."
singularity exec ${GENOMICS_SIF} which prodigal || { echo "Prodigal not found in genomics container"; exit 1; }

# Process each sample
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    SAMPLE_DIR="${OUTPUT_DIR}/${SAMPLE}"
    mkdir -p ${SAMPLE_DIR}
    
    echo "Processing sample ${SAMPLE}..."
    
    # Process each bin in the sample
    for BIN_FILE in $(find ${BINS_DIR}/${SAMPLE} -name "${SAMPLE}.bin.*.fa" | sort); do
        BIN_NAME=$(basename ${BIN_FILE} .fa)
        BIN_OUTPUT_DIR="${SAMPLE_DIR}/${BIN_NAME}"
        mkdir -p ${BIN_OUTPUT_DIR}
        
        echo "  Processing bin ${BIN_NAME}..."
        
        # Define output files
        GFF_OUTPUT="${BIN_OUTPUT_DIR}/${BIN_NAME}.gff"
        PROTEIN_OUTPUT="${BIN_OUTPUT_DIR}/${BIN_NAME}.proteins.faa"
        NUCLEOTIDE_OUTPUT="${BIN_OUTPUT_DIR}/${BIN_NAME}.genes.fna"
        
        echo "    Running Prodigal..."
        
        # Run Prodigal using the genomics container
        singularity exec ${GENOMICS_SIF} \
            prodigal -i ${BIN_FILE} \
                     -o ${GFF_OUTPUT} \
                     -a ${PROTEIN_OUTPUT} \
                     -d ${NUCLEOTIDE_OUTPUT} \
                     -p meta
        
        # Count the number of genes and proteins
        GENE_COUNT=$(grep -c "^>" ${NUCLEOTIDE_OUTPUT} || echo "0")
        PROTEIN_COUNT=$(grep -c "^>" ${PROTEIN_OUTPUT} || echo "0")
        
        echo "  Completed bin ${BIN_NAME}: ${GENE_COUNT} genes, ${PROTEIN_COUNT} proteins"
    done
    
    echo "Completed sample ${SAMPLE}"
done

echo "All samples processed."

# Create a summary of the gene prediction results
echo -e "Sample\tBin\tGenes\tProteins" > ${OUTPUT_DIR}/prodigal_summary.tsv

for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    for BIN_FILE in $(find ${BINS_DIR}/${SAMPLE} -name "${SAMPLE}.bin.*.fa" | sort); do
        BIN_NAME=$(basename ${BIN_FILE} .fa)
        NUCLEOTIDE_FILE="${OUTPUT_DIR}/${SAMPLE}/${BIN_NAME}/${BIN_NAME}.genes.fna"
        PROTEIN_FILE="${OUTPUT_DIR}/${SAMPLE}/${BIN_NAME}/${BIN_NAME}.proteins.faa"
        
        # Count genes and proteins
        if [ -f "${NUCLEOTIDE_FILE}" ]; then
            GENE_COUNT=$(grep -c "^>" ${NUCLEOTIDE_FILE} || echo "0")
        else
            GENE_COUNT="0"
        fi
        
        if [ -f "${PROTEIN_FILE}" ]; then
            PROTEIN_COUNT=$(grep -c "^>" ${PROTEIN_FILE} || echo "0")
        else
            PROTEIN_COUNT="0"
        fi
        
        echo -e "${SAMPLE}\t${BIN_NAME}\t${GENE_COUNT}\t${PROTEIN_COUNT}" >> ${OUTPUT_DIR}/prodigal_summary.tsv
    done
done

echo "Summary created at ${OUTPUT_DIR}/prodigal_summary.tsv"

# Create a formatted markdown summary
echo "# Prodigal Gene Prediction Results" > ${OUTPUT_DIR}/prodigal_summary.md
echo "" >> ${OUTPUT_DIR}/prodigal_summary.md
echo "| Sample | Bin | Number of Genes | Number of Proteins |" >> ${OUTPUT_DIR}/prodigal_summary.md
echo "|--------|-----|-----------------|-------------------|" >> ${OUTPUT_DIR}/prodigal_summary.md

while IFS=$'\t' read -r sample bin genes proteins; do
    # Skip the header line
    if [ "$sample" != "Sample" ]; then
        echo "| $sample | $bin | $genes | $proteins |" >> ${OUTPUT_DIR}/prodigal_summary.md
    fi
done < ${OUTPUT_DIR}/prodigal_summary.tsv

echo "" >> ${OUTPUT_DIR}/prodigal_summary.md
echo "Generated on $(date)" >> ${OUTPUT_DIR}/prodigal_summary.md

echo "Formatted summary created at ${OUTPUT_DIR}/prodigal_summary.md"
