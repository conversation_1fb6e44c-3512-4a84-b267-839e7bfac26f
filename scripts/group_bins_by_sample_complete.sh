#!/bin/bash

# Input file
INPUT_FILE="discosea_summary.tsv"
OUTPUT_FILE="discosea_by_sample_complete.tsv"
OUTPUT_MD="discosea_by_sample_complete.md"

# Create header for the output file
echo -e "Sample\tNum_Bins\tAvg_Completeness(%)\tAvg_Contamination(%)\tTotal_Length(bp)\tAvg_N50\tAvg_GC(%)\tAvg_Depth\tAvg_Depth_Variance\tTotal_Contigs\tTaxonomy" > ${OUTPUT_FILE}

# Process each sample
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    # Extract rows for this sample
    SAMPLE_ROWS=$(grep "^${SAMPLE}" ${INPUT_FILE})
    
    if [ ! -z "${SAMPLE_ROWS}" ]; then
        # Count number of bins
        NUM_BINS=$(echo "${SAMPLE_ROWS}" | wc -l)
        
        # Calculate average completeness
        AVG_COMPLETENESS=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{sum+=$2} END {print sum/NR}')
        
        # Calculate average contamination
        AVG_CONTAMINATION=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{sum+=$3} END {print sum/NR}')
        
        # Calculate total length
        TOTAL_LENGTH=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{sum+=$4} END {print sum}')
        
        # Calculate total contigs
        TOTAL_CONTIGS=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{sum+=$5} END {print sum}')
        
        # Calculate average depth
        AVG_DEPTH=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{sum+=$6} END {print sum/NR}')
        
        # Get the most common taxonomy
        TAXONOMY=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{print $7}' | sort | uniq -c | sort -nr | head -1 | sed 's/^ *[0-9]* *//')
        
        # Get additional metrics from BinInfo.txt
        BIN_INFO_FILE="03bins/metabat2_fixed/${SAMPLE}/${SAMPLE}.bin.BinInfo.txt"
        if [ -f "${BIN_INFO_FILE}" ]; then
            # Calculate average N50
            AVG_N50=$(tail -n +2 ${BIN_INFO_FILE} | awk '{sum+=$0; count++} END {print sum/count}')
            
            # Calculate average GC%
            AVG_GC=$(tail -n +2 ${BIN_INFO_FILE} | awk '{sum+=$0; count++} END {print sum/count}')
            
            # Calculate average depth variance
            AVG_DEPTH_VAR=$(tail -n +2 ${BIN_INFO_FILE} | awk '{sum+=$0; count++} END {print sum/count}')
        else
            # If BinInfo.txt doesn't have these metrics, try to get them from summary.txt
            SUMMARY_FILE="03bins/metabat2_fixed/${SAMPLE}/${SAMPLE}.summary.txt"
            if [ -f "${SUMMARY_FILE}" ]; then
                # Calculate average N50
                AVG_N50=$(grep -A 100 "^Bin" ${SUMMARY_FILE} | grep -v "^Bin" | awk '{sum+=$4; count++} END {print sum/count}')
                
                # Calculate average GC%
                AVG_GC=$(grep -A 100 "^Bin" ${SUMMARY_FILE} | grep -v "^Bin" | awk '{sum+=$5; count++} END {print sum/count}')
                
                # Calculate average depth variance
                AVG_DEPTH_VAR=$(grep -A 100 "^Bin" ${SUMMARY_FILE} | grep -v "^Bin" | awk '{sum+=$7; count++} END {print sum/count}')
            else
                # If neither file has these metrics, set them to NA
                AVG_N50="NA"
                AVG_GC="NA"
                AVG_DEPTH_VAR="NA"
            fi
        fi
        
        # Add to the output file
        echo -e "${SAMPLE}\t${NUM_BINS}\t${AVG_COMPLETENESS}\t${AVG_CONTAMINATION}\t${TOTAL_LENGTH}\t${AVG_N50}\t${AVG_GC}\t${AVG_DEPTH}\t${AVG_DEPTH_VAR}\t${TOTAL_CONTIGS}\t${TAXONOMY}" >> ${OUTPUT_FILE}
    else
        echo "Warning: No bins found for sample ${SAMPLE}"
    fi
done

# Create markdown summary
echo "# Discosea Bins Grouped by Sample (Complete Metrics)" > ${OUTPUT_MD}
echo "" >> ${OUTPUT_MD}
echo "| Sample | Num Bins | Avg Completeness(%) | Avg Contamination(%) | Total Length(bp) | Avg N50 | Avg GC(%) | Avg Depth | Avg Depth Variance | Total Contigs | Taxonomy |" >> ${OUTPUT_MD}
echo "|--------|----------|---------------------|---------------------|------------------|---------|-----------|-----------|-------------------|---------------|----------|" >> ${OUTPUT_MD}

# Add data to markdown
tail -n +2 ${OUTPUT_FILE} | while IFS=$'\t' read -r sample num_bins completeness contamination length n50 gc depth var contigs taxonomy; do
    # Format numbers
    completeness=$(printf "%.2f" ${completeness})
    contamination=$(printf "%.2f" ${contamination})
    length=$(printf "%'d" ${length})
    depth=$(printf "%.2f" ${depth})
    
    echo "| ${sample} | ${num_bins} | ${completeness} | ${contamination} | ${length} | ${n50} | ${gc} | ${depth} | ${var} | ${contigs} | ${taxonomy} |" >> ${OUTPUT_MD}
done

echo "" >> ${OUTPUT_MD}
echo "Generated on $(date)" >> ${OUTPUT_MD}

echo "Summary created at:"
echo "  ${OUTPUT_FILE}"
echo "  ${OUTPUT_MD}"

# Display the markdown summary
cat ${OUTPUT_MD}
