#!/bin/bash

# This script aligns reads to the assembled scaffolds using BWA-MEM

# Input parameters
SAMPLE_ID=$1
READS_DIR=$2
ASSEMBLY_DIR=$3
OUTPUT_DIR=$4
THREADS=$5

# Create output directory
mkdir -p ${OUTPUT_DIR}/${SAMPLE_ID}

# Create a temporary directory for intermediate files
TEMP_DIR=${OUTPUT_DIR}/${SAMPLE_ID}/temp
mkdir -p ${TEMP_DIR}

# Path to scaffold file
SCAFFOLD_FILE=${ASSEMBLY_DIR}/${SAMPLE_ID}/scaffolds.fasta.gz

# Decompress scaffold file for indexing
gunzip -c ${SCAFFOLD_FILE} > ${TEMP_DIR}/scaffolds.fasta

# Index the scaffold file with BWA
echo "Indexing scaffold file for ${SAMPLE_ID}..."
bwa index ${TEMP_DIR}/scaffolds.fasta

# Align reads to the scaffold using BWA-MEM
echo "Aligning reads to scaffolds for ${SAMPLE_ID}..."
bwa mem -t ${THREADS} ${TEMP_DIR}/scaffolds.fasta ${READS_DIR}/${SAMPLE_ID}.anqdpht.fastq.gz | \
    samtools view -bS - | \
    samtools sort -@ ${THREADS} -o ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam -

# Index the BAM file
echo "Indexing BAM file for ${SAMPLE_ID}..."
samtools index ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam

# Generate mapping statistics
echo "Generating mapping statistics for ${SAMPLE_ID}..."
samtools flagstat ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam > ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.flagstat.txt

# Clean up temporary files
rm -rf ${TEMP_DIR}

echo "Alignment completed for ${SAMPLE_ID}"
