#!/bin/bash

# This script runs SPAdes in single-cell mode for a given sample

# Input parameters
SAMPLE_ID=$1
INPUT_DIR=$2
OUTPUT_DIR=$3
THREADS=$4
MEMORY=$5

# Create output directory
mkdir -p ${OUTPUT_DIR}/${SAMPLE_ID}

# Run SPAdes in single-cell mode
spades.py \
    -s ${INPUT_DIR}/${SAMPLE_ID}.anqdpht.fastq.gz \
    --sc \
    -o ${OUTPUT_DIR}/${SAMPLE_ID} \
    -t ${THREADS} \
    -m ${MEMORY}

# Compress the contigs and scaffolds
gzip -f ${OUTPUT_DIR}/${SAMPLE_ID}/contigs.fasta
gzip -f ${OUTPUT_DIR}/${SAMPLE_ID}/scaffolds.fasta

echo "SPAdes assembly completed for ${SAMPLE_ID}"
