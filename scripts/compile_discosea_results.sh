#!/bin/bash

# Output file
OUTPUT_FILE="discosea_summary.tsv"
OUTPUT_MD="discosea_summary.md"

# Create header for the output file
echo -e "Bin\tCompleteness(%)\tContamination(%)\tTotal_Length(bp)\tN50\tGC(%)\tAvg_Depth\tDepth_Variance\tNum_Contigs\tTaxonomy" > ${OUTPUT_FILE}

# Get bin statistics
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    # Find bin stats file
    BIN_STATS_FILE="03bins/metabat2_fixed/${SAMPLE}/bin_stats.txt"
    
    if [ -f "${BIN_STATS_FILE}" ]; then
        # Skip header line
        tail -n +2 "${BIN_STATS_FILE}" | while IFS=$'\t' read -r bin total_length num_contigs n50 gc avg_depth depth_var; do
            # Get CheckM2 results
            CHECKM2_FILE="04quality/checkm2_working/${SAMPLE}/quality_report.tsv"
            COMPLETENESS="NA"
            CONTAMINATION="NA"
            
            if [ -f "${CHECKM2_FILE}" ]; then
                # Extract completeness and contamination
                COMP_CONT=$(grep "${bin}" "${CHECKM2_FILE}" | cut -f2,3)
                if [ ! -z "${COMP_CONT}" ]; then
                    COMPLETENESS=$(echo "${COMP_CONT}" | cut -f1)
                    CONTAMINATION=$(echo "${COMP_CONT}" | cut -f2)
                fi
            fi
            
            # Get taxonomy from EUKulele results
            TAXONOMY="NA"
            EUKULELE_FILE="06taxonomy/eukulele_batches/eukulele_taxonomy_extracted.tsv"
            
            if [ -f "${EUKULELE_FILE}" ]; then
                # Check if this bin has Discosea classification
                TAXONOMY_LINE=$(grep "${bin}" "${EUKULELE_FILE}" | grep -i "Discosea")
                if [ ! -z "${TAXONOMY_LINE}" ]; then
                    TAXONOMY=$(echo "${TAXONOMY_LINE}" | cut -f2-10 | tr '\t' ';')
                    
                    # Add to the output file
                    echo -e "${bin}\t${COMPLETENESS}\t${CONTAMINATION}\t${total_length}\t${n50}\t${gc}\t${avg_depth}\t${depth_var}\t${num_contigs}\t${TAXONOMY}" >> ${OUTPUT_FILE}
                fi
            fi
        done
    else
        echo "Warning: Bin stats file not found for ${SAMPLE}"
    fi
done

# Create markdown summary
echo "# Discosea Bins Summary" > ${OUTPUT_MD}
echo "" >> ${OUTPUT_MD}
echo "| Bin | Completeness(%) | Contamination(%) | Total Length(bp) | N50 | GC(%) | Avg Depth | Depth Variance | Num Contigs | Taxonomy |" >> ${OUTPUT_MD}
echo "|-----|----------------|-----------------|-----------------|-----|-------|-----------|----------------|------------|----------|" >> ${OUTPUT_MD}

# Add data to markdown
tail -n +2 ${OUTPUT_FILE} | while IFS=$'\t' read -r bin completeness contamination length n50 gc depth var contigs taxonomy; do
    echo "| ${bin} | ${completeness} | ${contamination} | ${length} | ${n50} | ${gc} | ${depth} | ${var} | ${contigs} | ${taxonomy} |" >> ${OUTPUT_MD}
done

echo "" >> ${OUTPUT_MD}
echo "Generated on $(date)" >> ${OUTPUT_MD}

echo "Summary created at:"
echo "  ${OUTPUT_FILE}"
echo "  ${OUTPUT_MD}"

# Display the markdown summary
cat ${OUTPUT_MD}
