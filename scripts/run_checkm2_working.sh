#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J checkm2_working
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-checkm2_working_%A.out
#SBATCH --error=slurm-checkm2_working_%A.err

# Define paths
BINS_DIR="03bins/metabat2_fixed"
OUTPUT_DIR="04quality/checkm2_working"
CHECKM2_DB="/clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database/uniref100.KO.1.dmnd"

# Create output directory
mkdir -p ${OUTPUT_DIR}

# Run CheckM2 on each sample's bins directory
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    SAMPLE_OUTPUT_DIR="${OUTPUT_DIR}/${SAMPLE}"
    mkdir -p ${SAMPLE_OUTPUT_DIR}
    
    echo "Running CheckM2 on ${SAMPLE} bins..."
    checkm2 predict --threads 16 \
                    --input ${BINS_DIR}/${SAMPLE} \
                    --output-directory ${SAMPLE_OUTPUT_DIR} \
                    -x fa \
                    --database_path ${CHECKM2_DB}
    
    echo "CheckM2 analysis completed for ${SAMPLE}."
done

# Create a combined summary file
echo -e "Sample\tBin\tCompleteness\tContamination\tTaxonomy" > ${OUTPUT_DIR}/checkm2_summary.tsv

# Parse the results from each sample directory
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    QUALITY_REPORT="${OUTPUT_DIR}/${SAMPLE}/quality_report.tsv"
    
    if [ -f "${QUALITY_REPORT}" ]; then
        # Skip the header line
        tail -n +2 ${QUALITY_REPORT} | while IFS=$'\t' read -r name completeness contamination taxonomy; do
            echo -e "${SAMPLE}\t${name}\t${completeness}\t${contamination}\t${taxonomy}" >> ${OUTPUT_DIR}/checkm2_summary.tsv
        done
    else
        echo "Warning: Quality report not found for ${SAMPLE}"
    fi
done

echo "CheckM2 summary created at ${OUTPUT_DIR}/checkm2_summary.tsv"
