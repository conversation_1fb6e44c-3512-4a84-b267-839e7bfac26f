#!/bin/bash

# <PERSON><PERSON>t to clean up and finalize the depth variance results
# This script processed the raw output to create the final clean results

# Clean up the output file format and create grouped summary
INPUT_FILE="discosea_metrics_real_depth_final.tsv"
OUTPUT_FILE="discosea_metrics_real_depth_clean.tsv"
OUTPUT_MD="discosea_metrics_real_depth_clean.md"
OUTPUT_SAMPLE_FILE="discosea_by_sample_real_depth_final.tsv"
OUTPUT_SAMPLE_MD="discosea_by_sample_real_depth_final.md"

echo "Cleaning up depth results..."

# Create header for the cleaned output file
echo -e "Bin\tCompleteness(%)\tContamination(%)\tGC(%)\tN50\tTotal_Length(bp)\tNum_Contigs\tReal_Avg_Depth\tReal_Depth_Variance\tTaxonomy" > ${OUTPUT_FILE}

# Clean up the duplicated depth values
tail -n +2 ${INPUT_FILE} | while IFS=$'\t' read -r bin completeness contamination gc n50 length contigs depth_info1 depth_info2 taxonomy; do
    # Extract just the first depth and variance values
    REAL_AVG_DEPTH=$(echo ${depth_info1} | awk '{print $1}')
    REAL_DEPTH_VAR=$(echo ${depth_info1} | awk '{print $2}')
    
    # Add to cleaned output file
    echo -e "${bin}\t${completeness}\t${contamination}\t${gc}\t${n50}\t${length}\t${contigs}\t${REAL_AVG_DEPTH}\t${REAL_DEPTH_VAR}\t${taxonomy}" >> ${OUTPUT_FILE}
done

# Create markdown summary
echo "# Discosea Bins Metrics (Real Depth Calculations)" > ${OUTPUT_MD}
echo "" >> ${OUTPUT_MD}
echo "| Bin | Completeness(%) | Contamination(%) | GC(%) | N50 | Total Length(bp) | Num Contigs | Real Avg Depth | Real Depth Variance | Taxonomy |" >> ${OUTPUT_MD}
echo "|-----|----------------|-----------------|-------|-----|-----------------|------------|----------------|---------------------|----------|" >> ${OUTPUT_MD}

# Add data to markdown
tail -n +2 ${OUTPUT_FILE} | while IFS=$'\t' read -r bin completeness contamination gc n50 length contigs depth var taxonomy; do
    echo "| ${bin} | ${completeness} | ${contamination} | ${gc} | ${n50} | ${length} | ${contigs} | ${depth} | ${var} | ${taxonomy} |" >> ${OUTPUT_MD}
done

echo "" >> ${OUTPUT_MD}
echo "Generated on $(date)" >> ${OUTPUT_MD}

# Group by sample
echo "Creating sample-grouped summary..."
echo -e "Sample\tNum_Bins\tAvg_Completeness(%)\tAvg_Contamination(%)\tAvg_GC(%)\tAvg_N50\tTotal_Length(bp)\tTotal_Contigs\tReal_Avg_Depth\tReal_Avg_Depth_Variance\tTaxonomy" > ${OUTPUT_SAMPLE_FILE}

# Process each sample
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    # Extract rows for this sample
    SAMPLE_ROWS=$(grep "^${SAMPLE}" ${OUTPUT_FILE})
    
    if [ ! -z "${SAMPLE_ROWS}" ]; then
        # Count number of bins
        NUM_BINS=$(echo "${SAMPLE_ROWS}" | wc -l)
        
        # Calculate averages
        AVG_COMPLETENESS=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{if($2!="NA") {sum+=$2; count++}} END {if(count>0) printf "%.2f", sum/count; else print "NA"}')
        AVG_CONTAMINATION=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{if($3!="NA") {sum+=$3; count++}} END {if(count>0) printf "%.2f", sum/count; else print "NA"}')
        AVG_GC=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{if($4!="NA") {sum+=$4; count++}} END {if(count>0) printf "%.2f", sum/count; else print "NA"}')
        AVG_N50=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{if($5!="NA") {sum+=$5; count++}} END {if(count>0) printf "%.0f", sum/count; else print "NA"}')
        TOTAL_LENGTH=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{if($6!="NA") sum+=$6} END {print sum}')
        TOTAL_CONTIGS=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{if($7!="NA") sum+=$7} END {print sum}')
        AVG_DEPTH=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{if($8!="NA") {sum+=$8; count++}} END {if(count>0) printf "%.2f", sum/count; else print "NA"}')
        AVG_DEPTH_VAR=$(echo "${SAMPLE_ROWS}" | awk -F'\t' '{if($9!="NA") {sum+=$9; count++}} END {if(count>0) printf "%.2f", sum/count; else print "NA"}')
        
        # Get the most common taxonomy
        TAXONOMY="Eukaryota;Amoebozoa;Discosea"
        
        # Add to the output file
        echo -e "${SAMPLE}\t${NUM_BINS}\t${AVG_COMPLETENESS}\t${AVG_CONTAMINATION}\t${AVG_GC}\t${AVG_N50}\t${TOTAL_LENGTH}\t${TOTAL_CONTIGS}\t${AVG_DEPTH}\t${AVG_DEPTH_VAR}\t${TAXONOMY}" >> ${OUTPUT_SAMPLE_FILE}
    fi
done

# Create markdown summary for samples
echo "# Discosea Bins Grouped by Sample (Real Depth Calculations)" > ${OUTPUT_SAMPLE_MD}
echo "" >> ${OUTPUT_SAMPLE_MD}
echo "| Sample | Num Bins | Avg Completeness(%) | Avg Contamination(%) | Avg GC(%) | Avg N50 | Total Length(bp) | Total Contigs | Real Avg Depth | Real Avg Depth Variance | Taxonomy |" >> ${OUTPUT_SAMPLE_MD}
echo "|--------|----------|---------------------|---------------------|-----------|---------|-----------------|---------------|----------------|-------------------------|----------|" >> ${OUTPUT_SAMPLE_MD}

# Add data to markdown
tail -n +2 ${OUTPUT_SAMPLE_FILE} | while IFS=$'\t' read -r sample num_bins completeness contamination gc n50 length contigs depth var taxonomy; do
    echo "| ${sample} | ${num_bins} | ${completeness} | ${contamination} | ${gc} | ${n50} | ${length} | ${contigs} | ${depth} | ${var} | ${taxonomy} |" >> ${OUTPUT_SAMPLE_MD}
done

echo "" >> ${OUTPUT_SAMPLE_MD}
echo "Generated on $(date)" >> ${OUTPUT_SAMPLE_MD}

echo "Final results created:"
echo "  Individual bins: ${OUTPUT_FILE} and ${OUTPUT_MD}"
echo "  Sample groups: ${OUTPUT_SAMPLE_FILE} and ${OUTPUT_SAMPLE_MD}"
