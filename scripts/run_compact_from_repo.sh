#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J compact
#SBATCH -N 1
#SBATCH -c 8
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=log/slurm-compact_%A.out
#SBATCH --error=log/slurm-compact_%A.err

# Define paths
PRODIGAL_DIR="05genes/prodigal"
OUTPUT_DIR="06taxonomy/compact"
PROTEINS_DIR="${OUTPUT_DIR}/all_proteins"
COMPACT_REPO="/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/ssiddikenv/compact_classification"

# Create output directories
mkdir -p ${OUTPUT_DIR}
mkdir -p ${PROTEINS_DIR}

# Copy all protein files to a single directory for CompaCT
echo "Copying protein files to ${PROTEINS_DIR}..."
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    for BIN_DIR in $(find ${PRODIGAL_DIR}/${SAMPLE} -type d -name "${SAMPLE}.bin.*"); do
        BIN_NAME=$(basename ${BIN_DIR})
        PROTEIN_FILE="${BIN_DIR}/${BIN_NAME}.proteins.faa"
        
        if [ -f "${PROTEIN_FILE}" ]; then
            echo "  Copying ${PROTEIN_FILE} to ${PROTEINS_DIR}/${BIN_NAME}.proteins.faa"
            cp ${PROTEIN_FILE} ${PROTEINS_DIR}/${BIN_NAME}.proteins.faa
        else
            echo "  Warning: Protein file not found for ${BIN_NAME}"
        fi
    done
done

# Run CompaCT on all protein files
echo "Running CompaCT on all protein files..."

# Run CompaCT on each protein file
for PROTEIN_FILE in $(find ${PROTEINS_DIR} -name "*.proteins.faa" | sort); do
    BIN_NAME=$(basename ${PROTEIN_FILE} .proteins.faa)
    echo "  Processing ${BIN_NAME}..."
    
    # Create output directory for this bin
    BIN_OUTPUT_DIR="${OUTPUT_DIR}/${BIN_NAME}"
    mkdir -p ${BIN_OUTPUT_DIR}
    
    # Run CompaCT using the Python script from the repository
    cd ${COMPACT_REPO}
    python compact.py -i ${PROTEIN_FILE} -o ${BIN_OUTPUT_DIR} -t 8
    cd -
done

# Create a summary of the taxonomic classifications
echo "Creating summary of taxonomic classifications..."
echo -e "Bin\tDomain\tKingdom\tPhylum\tClass\tOrder\tFamily\tGenus\tSpecies\tConfidence" > ${OUTPUT_DIR}/compact_summary.tsv

# Parse the CompaCT results
for BIN_DIR in $(find ${OUTPUT_DIR} -type d -name "1507*" | sort); do
    BIN_NAME=$(basename ${BIN_DIR})
    TAXONOMY_FILE="${BIN_DIR}/taxonomy.txt"
    
    if [ -f "${TAXONOMY_FILE}" ]; then
        echo "  Processing results for ${BIN_NAME}..."
        
        # Extract the taxonomic classification
        TAXONOMY=$(cat ${TAXONOMY_FILE})
        
        # Split the taxonomy string into levels
        DOMAIN=$(echo ${TAXONOMY} | cut -d';' -f1 | sed 's/^ *//' | sed 's/ *$//')
        KINGDOM=$(echo ${TAXONOMY} | cut -d';' -f2 | sed 's/^ *//' | sed 's/ *$//')
        PHYLUM=$(echo ${TAXONOMY} | cut -d';' -f3 | sed 's/^ *//' | sed 's/ *$//')
        CLASS=$(echo ${TAXONOMY} | cut -d';' -f4 | sed 's/^ *//' | sed 's/ *$//')
        ORDER=$(echo ${TAXONOMY} | cut -d';' -f5 | sed 's/^ *//' | sed 's/ *$//')
        FAMILY=$(echo ${TAXONOMY} | cut -d';' -f6 | sed 's/^ *//' | sed 's/ *$//')
        GENUS=$(echo ${TAXONOMY} | cut -d';' -f7 | sed 's/^ *//' | sed 's/ *$//')
        SPECIES=$(echo ${TAXONOMY} | cut -d';' -f8 | sed 's/^ *//' | sed 's/ *$//')
        
        # Extract the confidence score
        CONFIDENCE_FILE="${BIN_DIR}/confidence.txt"
        if [ -f "${CONFIDENCE_FILE}" ]; then
            CONFIDENCE=$(cat ${CONFIDENCE_FILE})
        else
            CONFIDENCE="NA"
        fi
        
        # Add to the summary file
        echo -e "${BIN_NAME}\t${DOMAIN}\t${KINGDOM}\t${PHYLUM}\t${CLASS}\t${ORDER}\t${FAMILY}\t${GENUS}\t${SPECIES}\t${CONFIDENCE}" >> ${OUTPUT_DIR}/compact_summary.tsv
    else
        echo "  Warning: Taxonomy file not found for ${BIN_NAME}"
        echo -e "${BIN_NAME}\tNA\tNA\tNA\tNA\tNA\tNA\tNA\tNA\tNA" >> ${OUTPUT_DIR}/compact_summary.tsv
    fi
done

# Create a formatted markdown summary
echo "# CompaCT Taxonomic Classification Results" > ${OUTPUT_DIR}/compact_summary.md
echo "" >> ${OUTPUT_DIR}/compact_summary.md
echo "| Bin | Domain | Kingdom | Phylum | Class | Order | Family | Genus | Species | Confidence |" >> ${OUTPUT_DIR}/compact_summary.md
echo "|-----|--------|---------|--------|-------|-------|--------|-------|---------|------------|" >> ${OUTPUT_DIR}/compact_summary.md

# Parse the summary file for markdown
tail -n +2 ${OUTPUT_DIR}/compact_summary.tsv | while IFS=$'\t' read -r bin domain kingdom phylum class order family genus species confidence; do
    echo "| ${bin} | ${domain} | ${kingdom} | ${phylum} | ${class} | ${order} | ${family} | ${genus} | ${species} | ${confidence} |" >> ${OUTPUT_DIR}/compact_summary.md
done

echo "" >> ${OUTPUT_DIR}/compact_summary.md
echo "Generated on $(date)" >> ${OUTPUT_DIR}/compact_summary.md

echo "CompaCT analysis completed. Results available at:"
echo "  ${OUTPUT_DIR}/compact_summary.tsv"
echo "  ${OUTPUT_DIR}/compact_summary.md"
