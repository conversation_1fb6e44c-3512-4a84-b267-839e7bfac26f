#!/bin/bash

# Create a formatted summary table
echo "# Bin Statistics Summary (MetaBAT2 with 2 kb minimum contig length)" > bin_stats_summary.md
echo "" >> bin_stats_summary.md
echo "| Sample | Bin | Number of Contigs | Assembly Size (bp) | N50 (bp) | GC% |" >> bin_stats_summary.md
echo "|--------|-----|-------------------|-------------------|----------|-----|" >> bin_stats_summary.md

# Read the stats file and format the data
while IFS=$'\t' read -r sample bin num_contigs size n50 gc; do
    # Skip the header line
    if [ "$sample" != "Sample" ]; then
        # Format the assembly size with commas
        formatted_size=$(echo "$size" | sed ':a;s/\B[0-9]\{3\}\>/,&/;ta')
        
        # Format the N50 with commas
        formatted_n50=$(echo "$n50" | sed ':a;s/\B[0-9]\{3\}\>/,&/;ta')
        
        # Add the formatted line to the summary table
        echo "| $sample | $bin | $num_contigs | $formatted_size | $formatted_n50 | $gc |" >> bin_stats_summary.md
    fi
done < bin_stats_py/bin_stats.tsv

echo "" >> bin_stats_summary.md
echo "Generated on $(date)" >> bin_stats_summary.md

cat bin_stats_summary.md
