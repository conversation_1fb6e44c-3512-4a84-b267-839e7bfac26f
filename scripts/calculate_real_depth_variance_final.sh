#!/bin/bash

# Script to calculate real depth variance using samtools
# This script generated the correct results in discosea_metrics_real_depth_clean.tsv

# Input and output files
INPUT_FILE="discosea_metrics_complete.tsv"
OUTPUT_FILE="discosea_metrics_real_depth_final.tsv"
OUTPUT_MD="discosea_metrics_real_depth_final.md"

echo "Starting real depth variance calculation..."
echo "samtools found at: $(which samtools)"

# Create header for the output file
echo -e "Bin\tCompleteness(%)\tContamination(%)\tGC(%)\tN50\tTotal_Length(bp)\tNum_Contigs\tReal_Avg_Depth\tReal_Depth_Variance\tTaxonomy" > ${OUTPUT_FILE}

# Process each bin in the input file
tail -n +2 ${INPUT_FILE} | while IFS=$'\t' read -r bin completeness contamination gc n50 length contigs old_depth taxonomy; do
    SAMPLE=$(echo ${bin} | cut -d'.' -f1)
    
    echo "Processing ${bin}..."
    
    # Find the BAM file for this sample
    BAM_FILE="02mapping/${SAMPLE}/${SAMPLE}.sorted.bam"
    BIN_FILE="03bins/metabat2_fixed/${SAMPLE}/${bin}.fa"
    
    REAL_AVG_DEPTH="NA"
    REAL_DEPTH_VAR="NA"
    
    if [ -f "${BAM_FILE}" ] && [ -f "${BIN_FILE}" ]; then
        echo "  Found BAM file: ${BAM_FILE}"
        echo "  Found bin file: ${BIN_FILE}"
        
        # Extract contig names from the bin (only the first part before any whitespace or tab)
        CONTIG_NAMES=$(grep ">" ${BIN_FILE} | sed 's/>//' | awk '{print $1}')
        
        # Create temporary file for depths
        TEMP_DEPTH_FILE=$(mktemp)
        
        # Calculate depth for each contig using samtools depth
        echo "  Calculating depths for contigs..."
        echo "${CONTIG_NAMES}" | while read -r contig; do
            if [ ! -z "${contig}" ]; then
                # Get average depth for this contig
                echo "    Processing contig: ${contig}"
                CONTIG_DEPTH=$(samtools depth -r "${contig}" "${BAM_FILE}" | awk '{sum+=$3; count++} END {if(count>0) printf "%.2f", sum/count; else print "0"}')
                echo "${CONTIG_DEPTH}" >> ${TEMP_DEPTH_FILE}
            fi
        done
        
        # Calculate average depth and variance from the contig depths
        if [ -s "${TEMP_DEPTH_FILE}" ]; then
            echo "  Calculating statistics..."
            STATS=$(awk '
                {
                    depths[NR] = $1
                    sum += $1
                    count++
                }
                END {
                    if (count > 0) {
                        mean = sum / count
                        sumsq = 0
                        for (i = 1; i <= count; i++) {
                            sumsq += (depths[i] - mean) * (depths[i] - mean)
                        }
                        if (count > 1) {
                            variance = sumsq / (count - 1)
                        } else {
                            variance = 0
                        }
                        printf "%.2f\t%.2f", mean, variance
                    } else {
                        print "NA\tNA"
                    }
                }' ${TEMP_DEPTH_FILE})
            
            REAL_AVG_DEPTH=$(echo ${STATS} | cut -f1)
            REAL_DEPTH_VAR=$(echo ${STATS} | cut -f2)
        fi
        
        # Clean up temporary files
        rm -f ${TEMP_DEPTH_FILE}
    else
        echo "  BAM or bin file not found for ${bin}"
    fi
    
    # Add to output file
    echo -e "${bin}\t${completeness}\t${contamination}\t${gc}\t${n50}\t${length}\t${contigs}\t${REAL_AVG_DEPTH}\t${REAL_DEPTH_VAR}\t${taxonomy}" >> ${OUTPUT_FILE}
    
    echo "Completed ${bin}"
done

echo "Real depth metrics created at: ${OUTPUT_FILE}"
