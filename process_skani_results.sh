#!/bin/bash

# Script to process skani results and create clean summaries
RESULTS_DIR="skani_acanthamoeba_mapping"

echo "Processing skani results..."

# Create clean summary header
echo -e "Sample\tBin\tANI\tAligned_fraction_query\tAligned_fraction_reference\tRef_name\tQuery_name\tNum_ref_contigs\tNum_query_contigs\tTotal_bases_covered" > $RESULTS_DIR/skani_clean_summary.tsv

# Process each result file
for file in $RESULTS_DIR/*_vs_acanthamoeba.txt; do
    if [ -f "$file" ]; then
        # Extract sample and bin from filename
        filename=$(basename "$file")
        # Remove the suffix
        base_name=${filename%_vs_acanthamoeba.txt}
        
        # Split by underscore to get sample and bin
        sample=$(echo "$base_name" | cut -d'_' -f1)
        bin=$(echo "$base_name" | cut -d'_' -f2-)
        
        # Extract data from the file (skip header, take first data line)
        if [ -s "$file" ]; then
            tail -n +2 "$file" | head -n 1 | while IFS=$'\t' read -r ref_file query_file ani align_frac_ref align_frac_query ref_name query_name num_ref_contigs num_query_contigs ani_5 ani_95 std_dev ref_90 ref_50 ref_10 query_90 query_50 query_10 avg_chain total_bases; do
                # Clean up ref_name and query_name (take first word)
                clean_ref_name=$(echo "$ref_name" | awk '{print $1}')
                clean_query_name=$(echo "$query_name" | awk '{print $1}')
                
                # Output clean line
                echo -e "$sample\t$bin\t$ani\t$align_frac_query\t$align_frac_ref\t$clean_ref_name\t$clean_query_name\t$num_ref_contigs\t$num_query_contigs\t$total_bases" >> $RESULTS_DIR/skani_clean_summary.tsv
            done
        fi
    fi
done

echo "Clean summary created: $RESULTS_DIR/skani_clean_summary.tsv"

# Create high ANI summary (>80%)
echo "Creating high ANI summary (>80%)..."
head -n 1 $RESULTS_DIR/skani_clean_summary.tsv > $RESULTS_DIR/skani_high_ani_clean.tsv
awk -F'\t' 'NR>1 && $3 > 80 {print}' $RESULTS_DIR/skani_clean_summary.tsv >> $RESULTS_DIR/skani_high_ani_clean.tsv

# Create very high ANI summary (>90%)
echo "Creating very high ANI summary (>90%)..."
head -n 1 $RESULTS_DIR/skani_clean_summary.tsv > $RESULTS_DIR/skani_very_high_ani.tsv
awk -F'\t' 'NR>1 && $3 > 90 {print}' $RESULTS_DIR/skani_clean_summary.tsv >> $RESULTS_DIR/skani_very_high_ani.tsv

# Print summary statistics
echo ""
echo "=== SUMMARY STATISTICS ==="
total_comparisons=$(tail -n +2 $RESULTS_DIR/skani_clean_summary.tsv | wc -l)
high_ani=$(tail -n +2 $RESULTS_DIR/skani_high_ani_clean.tsv | wc -l)
very_high_ani=$(tail -n +2 $RESULTS_DIR/skani_very_high_ani.tsv | wc -l)
extremely_high_ani=$(awk -F'\t' 'NR>1 && $3 > 95 {print}' $RESULTS_DIR/skani_clean_summary.tsv | wc -l)

echo "Total comparisons: $total_comparisons"
echo "High ANI matches (>80%): $high_ani"
echo "Very high ANI matches (>90%): $very_high_ani"
echo "Extremely high ANI matches (>95%): $extremely_high_ani"

echo ""
echo "=== TOP 10 ANI MATCHES ==="
echo -e "Sample\tBin\tANI\tQuery_AF\tRef_AF"
tail -n +2 $RESULTS_DIR/skani_clean_summary.tsv | sort -k3,3nr | head -10 | awk -F'\t' '{printf "%-8s\t%-15s\t%-6s\t%-8s\t%-8s\n", $1, $2, $3, $4, $5}'

echo ""
echo "Files created:"
echo "- $RESULTS_DIR/skani_clean_summary.tsv (all results)"
echo "- $RESULTS_DIR/skani_high_ani_clean.tsv (ANI > 80%)"
echo "- $RESULTS_DIR/skani_very_high_ani.tsv (ANI > 90%)"
