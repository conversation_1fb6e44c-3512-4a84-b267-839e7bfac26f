#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J metabat_1507999
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-metabat_1507999_%A.out
#SBATCH --error=slurm-metabat_1507999_%A.err

# Define sample ID
SAMPLE_ID=1507999

# Define directories
READS_DIR="00data/readsf"
ASSEMBLY_DIR="01assemblies"
BAM_DIR="02mapping"
BIN_DIR="03bins/metabat2"

# Define resources
THREADS=16
MIN_CONTIG_LENGTH=1500  # Minimum contig length for binning

# Step 1: Alignment
echo "Step 1: Alignment for ${SAMPLE_ID}"

# Clean up any existing files
rm -rf ${BAM_DIR}/${SAMPLE_ID}

# Create output directory
mkdir -p ${BAM_DIR}/${SAMPLE_ID}

# Create a temporary directory for intermediate files
TEMP_DIR=${BAM_DIR}/${SAMPLE_ID}/temp
mkdir -p ${TEMP_DIR}

# Path to scaffold file
SCAFFOLD_FILE=${ASSEMBLY_DIR}/${SAMPLE_ID}/scaffolds.fasta.gz

# Decompress scaffold file for indexing
gunzip -c ${SCAFFOLD_FILE} > ${TEMP_DIR}/scaffolds.fasta

# Index the scaffold file with BWA
echo "Indexing scaffold file for ${SAMPLE_ID}..."
singularity exec bwa_latest.sif bwa index ${TEMP_DIR}/scaffolds.fasta

# Align reads to the scaffold using BWA-MEM
echo "Aligning reads to scaffolds for ${SAMPLE_ID}..."
singularity exec bwa_latest.sif bwa mem -t ${THREADS} ${TEMP_DIR}/scaffolds.fasta ${READS_DIR}/${SAMPLE_ID}.anqdpht.fastq.gz > ${TEMP_DIR}/${SAMPLE_ID}.sam

# Convert SAM to BAM, sort and index
echo "Converting SAM to BAM and sorting for ${SAMPLE_ID}..."
singularity exec samtools_latest.sif samtools view -bS ${TEMP_DIR}/${SAMPLE_ID}.sam | \
    singularity exec samtools_latest.sif samtools sort -@ ${THREADS} -o ${BAM_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam -

# Index the BAM file
echo "Indexing BAM file for ${SAMPLE_ID}..."
singularity exec samtools_latest.sif samtools index ${BAM_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam

# Generate mapping statistics
echo "Generating mapping statistics for ${SAMPLE_ID}..."
singularity exec samtools_latest.sif samtools flagstat ${BAM_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam > ${BAM_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.flagstat.txt

# Step 2: Binning
echo "Step 2: Binning for ${SAMPLE_ID}"

# Create output directory
mkdir -p ${BIN_DIR}/${SAMPLE_ID}

# Create a temporary directory for intermediate files
BIN_TEMP_DIR=${BIN_DIR}/${SAMPLE_ID}/temp
mkdir -p ${BIN_TEMP_DIR}

# Decompress scaffold file again for binning
gunzip -c ${SCAFFOLD_FILE} > ${BIN_TEMP_DIR}/scaffolds.fasta

# Generate depth file using jgi_summarize_bam_contig_depths
echo "Generating depth file for ${SAMPLE_ID}..."
singularity exec metabat2_latest.sif jgi_summarize_bam_contig_depths --outputDepth ${BIN_TEMP_DIR}/${SAMPLE_ID}.depth.txt ${BAM_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam

# Run MetaBAT2
echo "Running MetaBAT2 for ${SAMPLE_ID}..."
singularity exec metabat2_latest.sif metabat2 -i ${BIN_TEMP_DIR}/scaffolds.fasta \
    -a ${BIN_TEMP_DIR}/${SAMPLE_ID}.depth.txt \
    -o ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.bin \
    -t ${THREADS} \
    -m ${MIN_CONTIG_LENGTH} \
    -v

# Count the number of bins
BIN_COUNT=$(ls ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.bin.*.fa 2>/dev/null | wc -l)
echo "MetaBAT2 generated ${BIN_COUNT} bins for ${SAMPLE_ID}"

# Generate a summary file
echo "Sample ID: ${SAMPLE_ID}" > ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt
echo "Number of bins: ${BIN_COUNT}" >> ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt
echo "Bins:" >> ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt

# List each bin with its size
if [ ${BIN_COUNT} -gt 0 ]; then
    for BIN_FILE in ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.bin.*.fa; do
        BIN_NAME=$(basename ${BIN_FILE})
        BIN_SIZE=$(grep -v ">" ${BIN_FILE} | tr -d '\n' | wc -c)
        CONTIG_COUNT=$(grep -c ">" ${BIN_FILE})
        echo "${BIN_NAME}: ${CONTIG_COUNT} contigs, ${BIN_SIZE} bp" >> ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt
    done
fi

# Clean up temporary files
rm -rf ${TEMP_DIR} ${BIN_TEMP_DIR}

echo "Processing completed for sample: ${SAMPLE_ID}"
