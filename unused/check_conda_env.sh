#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J check_conda
#SBATCH -N 1
#SBATCH -c 1
#SBATCH --mem=4GB
#SBATCH -t 1:00:00
#SBATCH --output=slurm-check_conda_%A.out
#SBATCH --error=slurm-check_conda_%A.err

# Activate conda environment
echo "Activating conda environment..."
source ~/miniconda3/bin/activate

# Check which environments are available
echo "Available conda environments:"
conda env list

# Check which tools are available in the current environment
echo "Available tools in the current environment:"
which eukcc
which checkm2
which gtdbtk

# List all installed packages
echo "All installed packages:"
conda list
