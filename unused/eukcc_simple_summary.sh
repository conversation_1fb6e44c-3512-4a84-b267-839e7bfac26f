#!/bin/bash

# Create a formatted summary table
echo "# EukCC Quality Assessment Results" > eukcc_simple_summary.md
echo "" >> eukcc_simple_summary.md
echo "| Sample | Bin | Completeness (%) | Contamination (%) |" >> eukcc_simple_summary.md
echo "|--------|-----|-----------------|-------------------|" >> eukcc_simple_summary.md

# Process each result file
for RESULT_FILE in $(find 04quality/eukcc_conda -name "eukcc.csv" | sort); do
    # Extract bin name from the path
    BIN_DIR=$(dirname ${RESULT_FILE})
    BIN_NAME=$(basename ${BIN_DIR})
    SAMPLE=$(echo ${BIN_NAME} | cut -d'.' -f1)
    
    # Skip the header line and get the data line
    DATA_LINE=$(tail -n 1 ${RESULT_FILE})
    
    # Extract completeness and contamination
    COMPLETENESS=$(echo ${DATA_LINE} | awk '{print $2}')
    CONTAMINATION=$(echo ${DATA_LINE} | awk '{print $3}')
    
    # Add the formatted line to the summary table
    echo "| ${SAMPLE} | ${BIN_NAME} | ${COMPLETENESS} | ${CONTAMINATION} |" >> eukcc_simple_summary.md
done

echo "" >> eukcc_simple_summary.md
echo "Generated on $(date)" >> eukcc_simple_summary.md

cat eukcc_simple_summary.md
