#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J checkm2_conda
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-checkm2_conda_%A.out
#SBATCH --error=slurm-checkm2_conda_%A.err

# Define paths
BINS_DIR="03bins/metabat2_fixed"
OUTPUT_DIR="04quality/checkm2_conda"
CHECKM2_DB="/clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database"

# Create output directory
mkdir -p ${OUTPUT_DIR}/bins

# Activate conda environment
echo "Activating conda environment..."
source ~/miniconda3/bin/activate

# Set the CheckM2 database path
export CHECKM2DB=${CHECKM2_DB}

# Copy all bins to the CheckM2 bins directory
echo "Copying bins to CheckM2 directory..."
for BIN_FILE in $(find ${BINS_DIR} -name "*.bin.*.fa"); do
    BIN_NAME=$(basename ${BIN_FILE})
    cp ${BIN_FILE} ${OUTPUT_DIR}/bins/${BIN_NAME}
done

# Run CheckM2
echo "Running CheckM2 on all bins..."
checkm2 predict --threads 16 \
                --input ${OUTPUT_DIR}/bins \
                --output-directory ${OUTPUT_DIR} \
                --extension fa \
                --force

echo "CheckM2 analysis completed."

# Create a simplified summary file
echo -e "Bin\tCompleteness\tContamination\tTaxonomy" > ${OUTPUT_DIR}/checkm2_summary.tsv

# Parse the results
if [ -f "${OUTPUT_DIR}/quality_report.tsv" ]; then
    tail -n +2 ${OUTPUT_DIR}/quality_report.tsv | while IFS=$'\t' read -r name completeness contamination taxonomy; do
        echo -e "${name}\t${completeness}\t${contamination}\t${taxonomy}" >> ${OUTPUT_DIR}/checkm2_summary.tsv
    done
    echo "CheckM2 summary created at ${OUTPUT_DIR}/checkm2_summary.tsv"
else
    echo "Error: CheckM2 quality report not found."
fi
