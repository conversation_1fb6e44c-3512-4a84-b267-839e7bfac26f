#!/bin/bash

# Create a formatted summary table
echo "# Combined Bin Statistics and EukCC Quality Assessment" > combined_bin_stats.md
echo "" >> combined_bin_stats.md
echo "| Sample | Bin | Number of Contigs | Assembly Size (bp) | N50 (bp) | GC% | EukCC Completeness (%) | EukCC Contamination (%) |" >> combined_bin_stats.md
echo "|--------|-----|-------------------|-------------------|----------|-----|------------------------|--------------------------|" >> combined_bin_stats.md

# Read the bin statistics
while IFS=$'\t' read -r sample bin num_contigs size n50 gc; do
    # Skip the header line
    if [ "$sample" != "Sample" ]; then
        # Initialize EukCC values
        EUKCC_COMP="NA"
        EUKCC_CONT="NA"
        
        # Look for matching EukCC results
        EUKCC_FILE="04quality/eukcc_conda/${sample}/${bin}/eukcc.csv"
        if [ -f "${EUKCC_FILE}" ]; then
            # Extract completeness and contamination
            DATA_LINE=$(tail -n 1 ${EUKCC_FILE})
            EUKCC_COMP=$(echo ${DATA_LINE} | awk '{print $2}')
            EUKCC_CONT=$(echo ${DATA_LINE} | awk '{print $3}')
        fi
        
        # Format the assembly size with commas
        formatted_size=$(echo "$size" | sed ':a;s/\B[0-9]\{3\}\>/,&/;ta')
        
        # Format the N50 with commas
        formatted_n50=$(echo "$n50" | sed ':a;s/\B[0-9]\{3\}\>/,&/;ta')
        
        # Add the formatted line to the summary table
        echo "| $sample | $bin | $num_contigs | $formatted_size | $formatted_n50 | $gc | $EUKCC_COMP | $EUKCC_CONT |" >> combined_bin_stats.md
    fi
done < bin_stats_py/bin_stats.tsv

echo "" >> combined_bin_stats.md
echo "Generated on $(date)" >> combined_bin_stats.md

cat combined_bin_stats.md
