#!/bin/bash

# Create a formatted comparison table
echo "# Comparison of MetaBAT2 Binning Results" > binning_comparison.md
echo "" >> binning_comparison.md
echo "| Sample | Bins (1.5 kb) | Binned Size (1.5 kb) | Binning Rate (1.5 kb) | Bins (2 kb) | Binned Size (2 kb) | Binning Rate (2 kb) |" >> binning_comparison.md
echo "|--------|--------------|---------------------|---------------------|------------|-------------------|-------------------|" >> binning_comparison.md

# Process each sample
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    # Get the filtered assembly size
    FILTERED_ASSEMBLY_SIZE=$(grep "^${SAMPLE}" assembly_stats_filtered/assembly_stats.tsv | cut -f3)
    
    # Get data for 1.5 kb run
    if [ -d "03bins/metabat2/${SAMPLE}" ]; then
        BIN_COUNT_15=$(find 03bins/metabat2/${SAMPLE} -name "${SAMPLE}.bin.*.fa" | wc -l)
        
        BINNED_SIZE_15=0
        for BIN_FILE in $(find 03bins/metabat2/${SAMPLE} -name "${SAMPLE}.bin.*.fa"); do
            BIN_SIZE=$(grep -v ">" ${BIN_FILE} | tr -d '\n' | wc -c)
            BINNED_SIZE_15=$((BINNED_SIZE_15 + BIN_SIZE))
        done
        
        BINNING_RATE_15=$(echo "scale=2; (${BINNED_SIZE_15} * 100) / ${FILTERED_ASSEMBLY_SIZE}" | bc)
        FORMATTED_BINNED_SIZE_15=$(echo "$BINNED_SIZE_15" | sed ':a;s/\B[0-9]\{3\}\>/,&/;ta')
    else
        BIN_COUNT_15=0
        FORMATTED_BINNED_SIZE_15=0
        BINNING_RATE_15=0.00
    fi
    
    # Get data for 2 kb run
    if [ -d "03bins/metabat2_fixed/${SAMPLE}" ]; then
        BIN_COUNT_20=$(find 03bins/metabat2_fixed/${SAMPLE} -name "${SAMPLE}.bin.*.fa" | wc -l)
        
        BINNED_SIZE_20=0
        for BIN_FILE in $(find 03bins/metabat2_fixed/${SAMPLE} -name "${SAMPLE}.bin.*.fa"); do
            BIN_SIZE=$(grep -v ">" ${BIN_FILE} | tr -d '\n' | wc -c)
            BINNED_SIZE_20=$((BINNED_SIZE_20 + BIN_SIZE))
        done
        
        BINNING_RATE_20=$(echo "scale=2; (${BINNED_SIZE_20} * 100) / ${FILTERED_ASSEMBLY_SIZE}" | bc)
        FORMATTED_BINNED_SIZE_20=$(echo "$BINNED_SIZE_20" | sed ':a;s/\B[0-9]\{3\}\>/,&/;ta')
    else
        BIN_COUNT_20=0
        FORMATTED_BINNED_SIZE_20=0
        BINNING_RATE_20=0.00
    fi
    
    # Add the formatted line to the comparison table
    echo "| $SAMPLE | $BIN_COUNT_15 | $FORMATTED_BINNED_SIZE_15 | $BINNING_RATE_15% | $BIN_COUNT_20 | $FORMATTED_BINNED_SIZE_20 | $BINNING_RATE_20% |" >> binning_comparison.md
done

echo "" >> binning_comparison.md
echo "Generated on $(date)" >> binning_comparison.md

cat binning_comparison.md
