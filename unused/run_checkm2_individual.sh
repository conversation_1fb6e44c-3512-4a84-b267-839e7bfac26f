#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J checkm2_indiv
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-checkm2_indiv_%A.out
#SBATCH --error=slurm-checkm2_indiv_%A.err

# Define paths
BINS_DIR="03bins/metabat2_fixed"
OUTPUT_DIR="04quality/checkm2_indiv"
CHECKM2_DB="/clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database"

# Create output directory
mkdir -p ${OUTPUT_DIR}

# Set the CheckM2 database path
export CHECKM2DB=${CHECKM2_DB}

# Create a directory for each bin
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    for BIN_FILE in $(find ${BINS_DIR}/${SAMPLE} -name "${SAMPLE}.bin.*.fa"); do
        BIN_NAME=$(basename ${BIN_FILE} .fa)
        BIN_DIR="${OUTPUT_DIR}/${BIN_NAME}"
        mkdir -p ${BIN_DIR}
        
        # Copy the bin file to its own directory
        cp ${BIN_FILE} ${BIN_DIR}/
        
        # Run CheckM2 on this bin
        echo "Running CheckM2 on ${BIN_NAME}..."
        checkm2 predict --threads 16 \
                        --input ${BIN_DIR} \
                        --output-directory ${BIN_DIR} \
                        --extension fa \
                        --force
    done
done

echo "CheckM2 analysis completed for all bins."

# Create a combined summary file
echo -e "Bin\tCompleteness\tContamination\tTaxonomy" > ${OUTPUT_DIR}/checkm2_summary.tsv

# Parse the results from each bin directory
for BIN_DIR in $(find ${OUTPUT_DIR} -maxdepth 1 -type d | grep -v "^${OUTPUT_DIR}$"); do
    BIN_NAME=$(basename ${BIN_DIR})
    QUALITY_REPORT="${BIN_DIR}/quality_report.tsv"
    
    if [ -f "${QUALITY_REPORT}" ]; then
        # Extract the data (skip header)
        DATA_LINE=$(tail -n +2 ${QUALITY_REPORT})
        if [ ! -z "${DATA_LINE}" ]; then
            # Extract completeness, contamination, and taxonomy
            COMPLETENESS=$(echo ${DATA_LINE} | cut -f2)
            CONTAMINATION=$(echo ${DATA_LINE} | cut -f3)
            TAXONOMY=$(echo ${DATA_LINE} | cut -f4)
            
            echo -e "${BIN_NAME}\t${COMPLETENESS}\t${CONTAMINATION}\t${TAXONOMY}" >> ${OUTPUT_DIR}/checkm2_summary.tsv
        else
            echo -e "${BIN_NAME}\tNA\tNA\tNA" >> ${OUTPUT_DIR}/checkm2_summary.tsv
        fi
    else
        echo -e "${BIN_NAME}\tNA\tNA\tNA" >> ${OUTPUT_DIR}/checkm2_summary.tsv
    fi
done

echo "CheckM2 summary created at ${OUTPUT_DIR}/checkm2_summary.tsv"
