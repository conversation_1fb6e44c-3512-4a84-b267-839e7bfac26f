#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J combine_results
#SBATCH -N 1
#SBATCH -c 4
#SBATCH --mem=16GB
#SBATCH -t 1:00:00
#SBATCH --output=slurm-combine_results_%A.out
#SBATCH --error=slurm-combine_results_%A.err
#SBATCH --dependency=afterok:$1:$2:$3

# Define paths
EUKCC_SUMMARY="04quality/eukcc/eukcc_summary.tsv"
CHECKM2_SUMMARY="04quality/checkm2/checkm2_summary.tsv"
GTDBTK_SUMMARY="04quality/gtdbtk/gtdbtk_summary.tsv"
OUTPUT_DIR="04quality"

# Create the combined summary file
echo -e "Bin\tEukCC_Completeness\tEukCC_Contamination\tEukCC_Taxonomy\tCheckM2_Completeness\tCheckM2_Contamination\tCheckM2_Taxonomy\tGTDBTk_Domain\tGTDBTk_Phylum\tGTDBTk_Class\tGTDBTk_Order\tGTDBTk_Family\tGTDBTk_Genus\tGTDBTk_Species\tGTDBTk_ANI\tGTDBTk_Method" > ${OUTPUT_DIR}/combined_quality_summary.tsv

# Get the list of all bins
find 03bins/metabat2_fixed -name "*.bin.*.fa" | xargs -n 1 basename | sort > ${OUTPUT_DIR}/all_bins.txt

# Process each bin
while read -r BIN; do
    # Initialize variables
    EUKCC_COMP=""
    EUKCC_CONT=""
    EUKCC_TAX=""
    CHECKM2_COMP=""
    CHECKM2_CONT=""
    CHECKM2_TAX=""
    GTDBTK_DOMAIN=""
    GTDBTK_PHYLUM=""
    GTDBTK_CLASS=""
    GTDBTK_ORDER=""
    GTDBTK_FAMILY=""
    GTDBTK_GENUS=""
    GTDBTK_SPECIES=""
    GTDBTK_ANI=""
    GTDBTK_METHOD=""
    
    # Get EukCC results
    if [ -f "${EUKCC_SUMMARY}" ]; then
        EUKCC_LINE=$(grep -w "${BIN}" ${EUKCC_SUMMARY} || echo "")
        if [ ! -z "${EUKCC_LINE}" ]; then
            EUKCC_COMP=$(echo "${EUKCC_LINE}" | cut -f2)
            EUKCC_CONT=$(echo "${EUKCC_LINE}" | cut -f3)
            EUKCC_TAX=$(echo "${EUKCC_LINE}" | cut -f4)
        fi
    fi
    
    # Get CheckM2 results
    if [ -f "${CHECKM2_SUMMARY}" ]; then
        CHECKM2_LINE=$(grep -w "${BIN}" ${CHECKM2_SUMMARY} || echo "")
        if [ ! -z "${CHECKM2_LINE}" ]; then
            CHECKM2_COMP=$(echo "${CHECKM2_LINE}" | cut -f2)
            CHECKM2_CONT=$(echo "${CHECKM2_LINE}" | cut -f3)
            CHECKM2_TAX=$(echo "${CHECKM2_LINE}" | cut -f4)
        fi
    fi
    
    # Get GTDB-Tk results
    if [ -f "${GTDBTK_SUMMARY}" ]; then
        GTDBTK_LINE=$(grep -w "${BIN}" ${GTDBTK_SUMMARY} || echo "")
        if [ ! -z "${GTDBTK_LINE}" ]; then
            GTDBTK_DOMAIN=$(echo "${GTDBTK_LINE}" | cut -f2)
            GTDBTK_PHYLUM=$(echo "${GTDBTK_LINE}" | cut -f3)
            GTDBTK_CLASS=$(echo "${GTDBTK_LINE}" | cut -f4)
            GTDBTK_ORDER=$(echo "${GTDBTK_LINE}" | cut -f5)
            GTDBTK_FAMILY=$(echo "${GTDBTK_LINE}" | cut -f6)
            GTDBTK_GENUS=$(echo "${GTDBTK_LINE}" | cut -f7)
            GTDBTK_SPECIES=$(echo "${GTDBTK_LINE}" | cut -f8)
            GTDBTK_ANI=$(echo "${GTDBTK_LINE}" | cut -f10)
            GTDBTK_METHOD=$(echo "${GTDBTK_LINE}" | cut -f11)
        fi
    fi
    
    # Write the combined results
    echo -e "${BIN}\t${EUKCC_COMP}\t${EUKCC_CONT}\t${EUKCC_TAX}\t${CHECKM2_COMP}\t${CHECKM2_CONT}\t${CHECKM2_TAX}\t${GTDBTK_DOMAIN}\t${GTDBTK_PHYLUM}\t${GTDBTK_CLASS}\t${GTDBTK_ORDER}\t${GTDBTK_FAMILY}\t${GTDBTK_GENUS}\t${GTDBTK_SPECIES}\t${GTDBTK_ANI}\t${GTDBTK_METHOD}" >> ${OUTPUT_DIR}/combined_quality_summary.tsv
    
done < ${OUTPUT_DIR}/all_bins.txt

echo "Combined quality summary created at ${OUTPUT_DIR}/combined_quality_summary.tsv"

# Create a formatted markdown summary
echo "# Genome Quality Assessment Summary" > ${OUTPUT_DIR}/quality_summary.md
echo "" >> ${OUTPUT_DIR}/quality_summary.md
echo "## Overview" >> ${OUTPUT_DIR}/quality_summary.md
echo "" >> ${OUTPUT_DIR}/quality_summary.md
echo "This report summarizes the quality assessment results for the genome bins generated by MetaBAT2 with a 2 kb minimum contig length." >> ${OUTPUT_DIR}/quality_summary.md
echo "The assessment was performed using three tools:" >> ${OUTPUT_DIR}/quality_summary.md
echo "" >> ${OUTPUT_DIR}/quality_summary.md
echo "1. **EukCC**: Assesses completeness and contamination of eukaryotic genomes" >> ${OUTPUT_DIR}/quality_summary.md
echo "2. **CheckM2**: Assesses completeness and contamination of prokaryotic genomes" >> ${OUTPUT_DIR}/quality_summary.md
echo "3. **GTDB-Tk**: Provides taxonomic classification of prokaryotic genomes" >> ${OUTPUT_DIR}/quality_summary.md
echo "" >> ${OUTPUT_DIR}/quality_summary.md
echo "## Quality Assessment Results" >> ${OUTPUT_DIR}/quality_summary.md
echo "" >> ${OUTPUT_DIR}/quality_summary.md
echo "| Bin | EukCC Completeness | EukCC Contamination | CheckM2 Completeness | CheckM2 Contamination | Taxonomy | Quality Category |" >> ${OUTPUT_DIR}/quality_summary.md
echo "|-----|-------------------|-------------------|---------------------|---------------------|----------|-----------------|" >> ${OUTPUT_DIR}/quality_summary.md

# Process each bin for the markdown summary
while read -r BIN; do
    # Initialize variables
    EUKCC_COMP=""
    EUKCC_CONT=""
    CHECKM2_COMP=""
    CHECKM2_CONT=""
    TAXONOMY=""
    QUALITY=""
    
    # Get EukCC results
    if [ -f "${EUKCC_SUMMARY}" ]; then
        EUKCC_LINE=$(grep -w "${BIN}" ${EUKCC_SUMMARY} || echo "")
        if [ ! -z "${EUKCC_LINE}" ]; then
            EUKCC_COMP=$(echo "${EUKCC_LINE}" | cut -f2)
            EUKCC_CONT=$(echo "${EUKCC_LINE}" | cut -f3)
            EUKCC_TAX=$(echo "${EUKCC_LINE}" | cut -f4)
        fi
    fi
    
    # Get CheckM2 results
    if [ -f "${CHECKM2_SUMMARY}" ]; then
        CHECKM2_LINE=$(grep -w "${BIN}" ${CHECKM2_SUMMARY} || echo "")
        if [ ! -z "${CHECKM2_LINE}" ]; then
            CHECKM2_COMP=$(echo "${CHECKM2_LINE}" | cut -f2)
            CHECKM2_CONT=$(echo "${CHECKM2_LINE}" | cut -f3)
            CHECKM2_TAX=$(echo "${CHECKM2_LINE}" | cut -f4)
        fi
    fi
    
    # Get GTDB-Tk results
    if [ -f "${GTDBTK_SUMMARY}" ]; then
        GTDBTK_LINE=$(grep -w "${BIN}" ${GTDBTK_SUMMARY} || echo "")
        if [ ! -z "${GTDBTK_LINE}" ]; then
            GTDBTK_PHYLUM=$(echo "${GTDBTK_LINE}" | cut -f3)
            GTDBTK_GENUS=$(echo "${GTDBTK_LINE}" | cut -f7)
            GTDBTK_SPECIES=$(echo "${GTDBTK_LINE}" | cut -f8)
        fi
    fi
    
    # Determine the taxonomy
    if [ ! -z "${GTDBTK_SPECIES}" ] && [ "${GTDBTK_SPECIES}" != "s__" ]; then
        TAXONOMY="${GTDBTK_SPECIES}"
    elif [ ! -z "${GTDBTK_GENUS}" ] && [ "${GTDBTK_GENUS}" != "g__" ]; then
        TAXONOMY="${GTDBTK_GENUS}"
    elif [ ! -z "${GTDBTK_PHYLUM}" ] && [ "${GTDBTK_PHYLUM}" != "p__" ]; then
        TAXONOMY="${GTDBTK_PHYLUM}"
    elif [ ! -z "${CHECKM2_TAX}" ]; then
        TAXONOMY="${CHECKM2_TAX}"
    elif [ ! -z "${EUKCC_TAX}" ]; then
        TAXONOMY="${EUKCC_TAX}"
    else
        TAXONOMY="Unknown"
    fi
    
    # Determine the quality category
    # Use CheckM2 for prokaryotes and EukCC for eukaryotes
    if [ ! -z "${CHECKM2_COMP}" ] && [ ! -z "${CHECKM2_CONT}" ]; then
        COMP=${CHECKM2_COMP}
        CONT=${CHECKM2_CONT}
    elif [ ! -z "${EUKCC_COMP}" ] && [ ! -z "${EUKCC_CONT}" ]; then
        COMP=${EUKCC_COMP}
        CONT=${EUKCC_CONT}
    else
        COMP="NA"
        CONT="NA"
    fi
    
    # Assign quality category based on completeness and contamination
    if [ "${COMP}" != "NA" ] && [ "${CONT}" != "NA" ]; then
        if (( $(echo "${COMP} >= 90" | bc -l) )) && (( $(echo "${CONT} <= 5" | bc -l) )); then
            QUALITY="High-quality"
        elif (( $(echo "${COMP} >= 50" | bc -l) )) && (( $(echo "${CONT} <= 10" | bc -l) )); then
            QUALITY="Medium-quality"
        else
            QUALITY="Low-quality"
        fi
    else
        QUALITY="Unknown"
    fi
    
    # Write the markdown line
    echo "| ${BIN} | ${EUKCC_COMP} | ${EUKCC_CONT} | ${CHECKM2_COMP} | ${CHECKM2_CONT} | ${TAXONOMY} | ${QUALITY} |" >> ${OUTPUT_DIR}/quality_summary.md
    
done < ${OUTPUT_DIR}/all_bins.txt

echo "" >> ${OUTPUT_DIR}/quality_summary.md
echo "## Quality Categories" >> ${OUTPUT_DIR}/quality_summary.md
echo "" >> ${OUTPUT_DIR}/quality_summary.md
echo "- **High-quality**: Completeness ≥ 90% and Contamination ≤ 5%" >> ${OUTPUT_DIR}/quality_summary.md
echo "- **Medium-quality**: Completeness ≥ 50% and Contamination ≤ 10%" >> ${OUTPUT_DIR}/quality_summary.md
echo "- **Low-quality**: Completeness < 50% or Contamination > 10%" >> ${OUTPUT_DIR}/quality_summary.md
echo "" >> ${OUTPUT_DIR}/quality_summary.md
echo "Generated on $(date)" >> ${OUTPUT_DIR}/quality_summary.md

echo "Formatted quality summary created at ${OUTPUT_DIR}/quality_summary.md"
