#!/bin/bash

# Create a formatted summary table
echo "# EukCC Quality Assessment Results" > eukcc_summary.md
echo "" >> eukcc_summary.md
echo "| Sample | Bin | Completeness (%) | Contamination (%) | Clade |" >> eukcc_summary.md
echo "|--------|-----|-----------------|-------------------|-------|" >> eukcc_summary.md

# Process each result file
for RESULT_FILE in $(find 04quality/eukcc_conda -name "eukcc.csv" | sort); do
    # Extract bin name from the path
    BIN_DIR=$(dirname ${RESULT_FILE})
    BIN_NAME=$(basename ${BIN_DIR})
    SAMPLE=$(echo ${BIN_NAME} | cut -d'.' -f1)
    
    # Skip the header line and get the data line
    DATA_LINE=$(tail -n 1 ${RESULT_FILE})
    
    # Extract completeness and contamination
    COMPLETENESS=$(echo ${DATA_LINE} | awk '{print $2}')
    CONTAMINATION=$(echo ${DATA_LINE} | awk '{print $3}')
    
    # Extract clade from the error file if available
    CLADE=$(grep -A 1 "Genome belongs to clade:" slurm-eukcc_conda_17185965.err | grep "clade:" | head -n 1 | sed 's/.*clade: \([^(]*\).*/\1/' | tr -d '\r')
    
    # Add the formatted line to the summary table
    echo "| ${SAMPLE} | ${BIN_NAME} | ${COMPLETENESS} | ${CONTAMINATION} | ${CLADE} |" >> eukcc_summary.md
done

echo "" >> eukcc_summary.md
echo "Generated on $(date)" >> eukcc_summary.md

cat eukcc_summary.md
