#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J metabat_1507996
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-metabat_1507996_%A.out
#SBATCH --error=slurm-metabat_1507996_%A.err

# Define sample ID
SAMPLE_ID=1507996

# Define directories
ASSEMBLY_DIR="01assemblies"
BAM_DIR="02mapping"
BIN_DIR="03bins/metabat2"

# Define resources
THREADS=16
MIN_CONTIG_LENGTH=1500  # Minimum contig length for binning

# Create output directory
mkdir -p ${BIN_DIR}/${SAMPLE_ID}

# Path to scaffold file
SCAFFOLD_FILE=${ASSEMBLY_DIR}/${SAMPLE_ID}/scaffolds.fasta.gz
BAM_FILE=${BAM_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam

# Create a temporary directory for intermediate files
TEMP_DIR=${BIN_DIR}/${SAMPLE_ID}/temp
mkdir -p ${TEMP_DIR}

# Decompress scaffold file
echo "Decompressing scaffold file for ${SAMPLE_ID}..."
gunzip -c ${SCAFFOLD_FILE} > ${TEMP_DIR}/scaffolds.fasta

# Use the metabat2 Docker image directly
echo "Pulling MetaBAT2 Docker image..."
docker pull metabat/metabat:latest

# Generate depth file using jgi_summarize_bam_contig_depths
echo "Generating depth file for ${SAMPLE_ID}..."
docker run --rm -v $(pwd):$(pwd) metabat/metabat:latest jgi_summarize_bam_contig_depths --outputDepth ${TEMP_DIR}/${SAMPLE_ID}.depth.txt ${BAM_FILE}

# Run MetaBAT2
echo "Running MetaBAT2 for ${SAMPLE_ID}..."
docker run --rm -v $(pwd):$(pwd) metabat/metabat:latest metabat2 -i ${TEMP_DIR}/scaffolds.fasta \
    -a ${TEMP_DIR}/${SAMPLE_ID}.depth.txt \
    -o ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.bin \
    -t ${THREADS} \
    -m ${MIN_CONTIG_LENGTH} \
    -v

# Count the number of bins
BIN_COUNT=$(ls ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.bin.*.fa 2>/dev/null | wc -l)
echo "MetaBAT2 generated ${BIN_COUNT} bins for ${SAMPLE_ID}"

# Generate a summary file
echo "Sample ID: ${SAMPLE_ID}" > ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt
echo "Number of bins: ${BIN_COUNT}" >> ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt
echo "Bins:" >> ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt

# List each bin with its size
if [ ${BIN_COUNT} -gt 0 ]; then
    for BIN_FILE in ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.bin.*.fa; do
        BIN_NAME=$(basename ${BIN_FILE})
        BIN_SIZE=$(grep -v ">" ${BIN_FILE} | tr -d '\n' | wc -c)
        CONTIG_COUNT=$(grep -c ">" ${BIN_FILE})
        echo "${BIN_NAME}: ${CONTIG_COUNT} contigs, ${BIN_SIZE} bp" >> ${BIN_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.summary.txt
    done
fi

# Clean up temporary files
rm -rf ${TEMP_DIR}

echo "MetaBAT2 binning completed for ${SAMPLE_ID}"
