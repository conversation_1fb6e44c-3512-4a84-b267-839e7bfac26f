#!/bin/bash

# Submit EukCC job
echo "Submitting EukCC job..."
EUKCC_JOB_ID=$(sbatch run_eukcc.sh | awk '{print $4}')
echo "EukCC job submitted with ID: ${EUKCC_JOB_ID}"

# Submit CheckM2 job
echo "Submitting CheckM2 job..."
CHECKM2_JOB_ID=$(sbatch run_checkm2.sh | awk '{print $4}')
echo "CheckM2 job submitted with ID: ${CHECKM2_JOB_ID}"

# Submit GTDB-Tk job
echo "Submitting GTDB-Tk job..."
GTDBTK_JOB_ID=$(sbatch run_gtdbtk.sh | awk '{print $4}')
echo "GTDB-Tk job submitted with ID: ${GTDBTK_JOB_ID}"

# Submit the combine results job with dependencies
echo "Submitting combine results job (dependent on the completion of the other jobs)..."
COMBINE_JOB_ID=$(sbatch --dependency=afterok:${EUKCC_JOB_ID}:${CHECKM2_JOB_ID}:${GTDBTK_JOB_ID} combine_quality_results.sh ${EUKCC_JOB_ID} ${CHECKM2_JOB_ID} ${GTDBTK_JOB_ID} | awk '{print $4}')
echo "Combine results job submitted with ID: ${COMBINE_JOB_ID}"

echo "All jobs submitted. Use 'squeue -u $(whoami)' to check the status."
