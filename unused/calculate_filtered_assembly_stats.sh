#!/bin/bash

# Create output directory for stats
mkdir -p assembly_stats_filtered

# Create header for the stats file
echo -e "Sample\tNum_contigs\tAssembly_size\tN50\tGC%\tNum_contigs_unfiltered\tAssembly_size_unfiltered" > assembly_stats_filtered/assembly_stats.tsv

# Minimum contig length filter (2 kb)
MIN_LENGTH=2000

# Process each scaffold file
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    SCAFFOLD_FILE="01assemblies/${SAMPLE}/scaffolds.fasta.gz"
    
    if [ -f "$SCAFFOLD_FILE" ]; then
        echo "Processing $SAMPLE..."
        
        # Create temporary files
        LENGTHS_FILE=$(mktemp)
        LENGTHS_FILTERED_FILE=$(mktemp)
        GC_FILE=$(mktemp)
        GC_FILTERED_FILE=$(mktemp)
        
        # Extract scaffold lengths and sequences
        zcat "$SCAFFOLD_FILE" | awk -v min_len="$MIN_LENGTH" '
        BEGIN {
            len=0;
            seq="";
            header="";
            in_record=0;
        }
        /^>/ {
            if (len > 0) {
                print header "\t" len "\t" seq;
                len=0;
                seq="";
            }
            header=$0;
            in_record=1;
            next;
        }
        {
            if (in_record) {
                len += length($0);
                seq = seq $0;
            }
        }
        END {
            if (len > 0) {
                print header "\t" len "\t" seq;
            }
        }' > "$LENGTHS_FILE"
        
        # Filter contigs by length and calculate GC content
        cat "$LENGTHS_FILE" | awk -v min_len="$MIN_LENGTH" '
        BEGIN {
            FS="\t";
        }
        {
            len=$2;
            seq=toupper($3);
            
            if (len >= min_len) {
                print len;
                
                # Calculate GC content for this contig
                gc_count = 0;
                for (i=1; i<=length(seq); i++) {
                    base = substr(seq, i, 1);
                    if (base == "G" || base == "C") {
                        gc_count++;
                    }
                }
                
                print gc_count / len * 100 > "'$GC_FILTERED_FILE'";
            }
        }' > "$LENGTHS_FILTERED_FILE"
        
        # Calculate GC content for all contigs
        cat "$LENGTHS_FILE" | awk '
        BEGIN {
            FS="\t";
            total_gc = 0;
            total_len = 0;
        }
        {
            len=$2;
            seq=toupper($3);
            
            # Calculate GC content for this contig
            gc_count = 0;
            for (i=1; i<=length(seq); i++) {
                base = substr(seq, i, 1);
                if (base == "G" || base == "C") {
                    gc_count++;
                }
            }
            
            total_gc += gc_count;
            total_len += len;
        }
        END {
            if (total_len > 0) {
                printf "%.2f\n", (total_gc / total_len) * 100;
            } else {
                print "0.00";
            }
        }' > "$GC_FILE"
        
        # Calculate statistics for filtered contigs
        NUM_CONTIGS_FILTERED=$(wc -l < "$LENGTHS_FILTERED_FILE")
        TOTAL_LENGTH_FILTERED=$(awk '{sum += $1} END {print sum}' "$LENGTHS_FILTERED_FILE")
        
        # Calculate statistics for all contigs
        NUM_CONTIGS=$(wc -l < "$LENGTHS_FILE")
        TOTAL_LENGTH=$(awk '{sum += $2} END {print sum}' "$LENGTHS_FILE")
        
        # Calculate GC percentage for filtered contigs
        if [ -s "$GC_FILTERED_FILE" ]; then
            GC_PERCENT_FILTERED=$(awk '{sum += $1; count++} END {if (count > 0) printf "%.2f", sum/count; else print "0.00"}' "$GC_FILTERED_FILE")
        else
            GC_PERCENT_FILTERED="0.00"
        fi
        
        # Get GC percentage for all contigs
        GC_PERCENT=$(cat "$GC_FILE")
        
        # Sort lengths in descending order for N50 calculation
        sort -nr "$LENGTHS_FILTERED_FILE" > "${LENGTHS_FILTERED_FILE}.sorted"
        
        # Calculate N50 for filtered contigs
        SUM=0
        N50=0
        if [ "$TOTAL_LENGTH_FILTERED" -gt 0 ]; then
            while read -r LENGTH; do
                SUM=$((SUM + LENGTH))
                if [ $SUM -ge $((TOTAL_LENGTH_FILTERED / 2)) ] && [ $N50 -eq 0 ]; then
                    N50=$LENGTH
                    break
                fi
            done < "${LENGTHS_FILTERED_FILE}.sorted"
        fi
        
        # Append to stats file
        echo -e "${SAMPLE}\t${NUM_CONTIGS_FILTERED}\t${TOTAL_LENGTH_FILTERED}\t${N50}\t${GC_PERCENT_FILTERED}\t${NUM_CONTIGS}\t${TOTAL_LENGTH}" >> assembly_stats_filtered/assembly_stats.tsv
        
        # Clean up temporary files
        rm -f "$LENGTHS_FILE" "$LENGTHS_FILTERED_FILE" "${LENGTHS_FILTERED_FILE}.sorted" "$GC_FILE" "$GC_FILTERED_FILE"
    else
        echo "Scaffold file for $SAMPLE not found."
    fi
done

echo "Assembly statistics calculation completed."
