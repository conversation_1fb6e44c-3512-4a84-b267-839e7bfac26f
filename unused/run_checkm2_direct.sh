#!/bin/bash

# Define paths
BINS_DIR="04quality/checkm2_fixed/bins"
OUTPUT_DIR="04quality/checkm2_direct"
CHECKM2_DB="/clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database"

# Create output directory
mkdir -p ${OUTPUT_DIR}

# Set the CheckM2 database path
export CHECKM2DB=${CHECKM2_DB}

# Check if bins directory exists
echo "Checking bins directory..."
if [ -d "${BINS_DIR}" ]; then
    echo "Bins directory exists: ${BINS_DIR}"
    ls -la ${BINS_DIR} | head -n 5
else
    echo "Bins directory does not exist: ${BINS_DIR}"
    exit 1
fi

# Run CheckM2
echo "Running CheckM2 on all bins..."
checkm2 predict --threads 16 \
                --input ${BINS_DIR} \
                --output-directory ${OUTPUT_DIR} \
                --extension fa \
                --force

echo "CheckM2 analysis completed."
