#!/bin/bash

# Create a formatted summary table
echo "# Combined EukCC and CheckM2 Quality Assessment Results" > combined_quality_summary.md
echo "" >> combined_quality_summary.md
echo "| Sample | Bin | EukCC Completeness (%) | EukCC Contamination (%) | CheckM2 Completeness (%) | CheckM2 Contamination (%) | CheckM2 Model | Notes |" >> combined_quality_summary.md
echo "|--------|-----|------------------------|--------------------------|--------------------------|---------------------------|---------------|-------|" >> combined_quality_summary.md

# Process each bin
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    for BIN_FILE in $(find 03bins/metabat2_fixed/${SAMPLE} -name "${SAMPLE}.bin.*.fa" | sort); do
        BIN_NAME=$(basename ${BIN_FILE} .fa)
        
        # Initialize variables
        EUKCC_COMP="NA"
        EUKCC_CONT="NA"
        CHECKM2_COMP="NA"
        CHECKM2_CONT="NA"
        CHECKM2_MODEL="NA"
        NOTES="NA"
        
        # Get EukCC results
        EUKCC_DIR="04quality/eukcc_conda/${SAMPLE}/${BIN_NAME}"
        if [ -d "${EUKCC_DIR}" ]; then
            EUKCC_FILE="${EUKCC_DIR}/eukcc.csv"
            if [ -f "${EUKCC_FILE}" ]; then
                # Skip header and get data line
                DATA_LINE=$(tail -n 1 ${EUKCC_FILE})
                EUKCC_COMP=$(echo ${DATA_LINE} | awk '{print $2}')
                EUKCC_CONT=$(echo ${DATA_LINE} | awk '{print $3}')
            fi
        fi
        
        # Get CheckM2 results
        CHECKM2_FILE="04quality/checkm2_working/${SAMPLE}/quality_report.tsv"
        if [ -f "${CHECKM2_FILE}" ]; then
            # Find the line for this bin
            CHECKM2_LINE=$(grep "${BIN_NAME}" ${CHECKM2_FILE})
            if [ ! -z "${CHECKM2_LINE}" ]; then
                CHECKM2_COMP=$(echo ${CHECKM2_LINE} | cut -f2)
                CHECKM2_CONT=$(echo ${CHECKM2_LINE} | cut -f3)
                CHECKM2_MODEL=$(echo ${CHECKM2_LINE} | cut -f4)
                NOTES=$(echo ${CHECKM2_LINE} | cut -f14)
                
                # Replace "None" with "NA"
                if [ "${NOTES}" == "None" ]; then
                    NOTES="NA"
                fi
            fi
        fi
        
        # Add the formatted line to the summary table
        echo "| ${SAMPLE} | ${BIN_NAME} | ${EUKCC_COMP} | ${EUKCC_CONT} | ${CHECKM2_COMP} | ${CHECKM2_CONT} | ${CHECKM2_MODEL} | ${NOTES} |" >> combined_quality_summary.md
    done
done

echo "" >> combined_quality_summary.md
echo "Generated on $(date)" >> combined_quality_summary.md

cat combined_quality_summary.md
