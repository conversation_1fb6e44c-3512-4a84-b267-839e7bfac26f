#!/bin/bash

# Create a formatted summary table
echo "# Assembly Statistics Summary" > assembly_summary.md
echo "" >> assembly_summary.md
echo "| Sample | Number of Contigs | Assembly Size (bp) | N50 (bp) | GC% |" >> assembly_summary.md
echo "|--------|------------------|-------------------|----------|-----|" >> assembly_summary.md

# Read the stats file and format the data
while IFS=$'\t' read -r sample num_contigs size n50 gc; do
    # Skip the header line
    if [ "$sample" != "Sample" ]; then
        # Format the assembly size with commas
        formatted_size=$(echo "$size" | sed ':a;s/\B[0-9]\{3\}\>/,&/;ta')
        
        # Format the N50 with commas
        formatted_n50=$(echo "$n50" | sed ':a;s/\B[0-9]\{3\}\>/,&/;ta')
        
        # Add the formatted line to the summary table
        echo "| $sample | $num_contigs | $formatted_size | $formatted_n50 | $gc |" >> assembly_summary.md
    fi
done < assembly_stats_fixed/assembly_stats.tsv

echo "" >> assembly_summary.md
echo "Generated on $(date)" >> assembly_summary.md

cat assembly_summary.md
