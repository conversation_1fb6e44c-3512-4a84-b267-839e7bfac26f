#!/bin/bash

# Create a formatted summary table
echo "# Combined EukCC and CheckM2 Quality Assessment Results" > simple_quality_summary.md
echo "" >> simple_quality_summary.md
echo "| Sample | Bin | EukCC Completeness (%) | EukCC Contamination (%) | CheckM2 Completeness (%) | CheckM2 Contamination (%) |" >> simple_quality_summary.md
echo "|--------|-----|------------------------|--------------------------|--------------------------|---------------------------|" >> simple_quality_summary.md

# Process each sample's CheckM2 results
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    CHECKM2_FILE="04quality/checkm2_working/${SAMPLE}/quality_report.tsv"
    
    if [ -f "${CHECKM2_FILE}" ]; then
        # Skip the header line
        tail -n +2 ${CHECKM2_FILE} | while IFS=$'\t' read -r name completeness contamination rest; do
            # Initialize EukCC values
            EUKCC_COMP="NA"
            EUKCC_CONT="NA"
            
            # Get EukCC results if available
            EUKCC_DIR="04quality/eukcc_conda/${SAMPLE}/${name}"
            if [ -d "${EUKCC_DIR}" ]; then
                EUKCC_FILE="${EUKCC_DIR}/eukcc.csv"
                if [ -f "${EUKCC_FILE}" ]; then
                    # Skip header and get data line
                    DATA_LINE=$(tail -n 1 ${EUKCC_FILE})
                    EUKCC_COMP=$(echo ${DATA_LINE} | awk '{print $2}')
                    EUKCC_CONT=$(echo ${DATA_LINE} | awk '{print $3}')
                fi
            fi
            
            # Add the formatted line to the summary table
            echo "| ${SAMPLE} | ${name} | ${EUKCC_COMP} | ${EUKCC_CONT} | ${completeness} | ${contamination} |" >> simple_quality_summary.md
        done
    fi
done

echo "" >> simple_quality_summary.md
echo "Generated on $(date)" >> simple_quality_summary.md

cat simple_quality_summary.md
