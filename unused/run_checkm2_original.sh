#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J checkm2_orig
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-checkm2_orig_%A.out
#SBATCH --error=slurm-checkm2_orig_%A.err

# Define paths
BINS_DIR="03bins/metabat2_fixed"
OUTPUT_DIR="04quality/checkm2_orig"
CHECKM2_DB="/clusterfs/jgi/scratch/science/mgs/nelli/databases/checkm2/CheckM2_database"

# Create output directory
mkdir -p ${OUTPUT_DIR}

# Set the CheckM2 database path
export CHECKM2DB=${CHECKM2_DB}

# Run CheckM2 directly on the original bin directory
echo "Running CheckM2 on all bins..."
checkm2 predict --threads 16 \
                --input ${BINS_DIR} \
                --output-directory ${OUTPUT_DIR} \
                --extension fa \
                --force

echo "CheckM2 analysis completed."

# Create a simplified summary file
echo -e "Bin\tCompleteness\tContamination\tTaxonomy" > ${OUTPUT_DIR}/checkm2_summary.tsv

# Parse the results
if [ -f "${OUTPUT_DIR}/quality_report.tsv" ]; then
    tail -n +2 ${OUTPUT_DIR}/quality_report.tsv | while IFS=$'\t' read -r name completeness contamination taxonomy; do
        echo -e "${name}\t${completeness}\t${contamination}\t${taxonomy}" >> ${OUTPUT_DIR}/checkm2_summary.tsv
    done
    echo "CheckM2 summary created at ${OUTPUT_DIR}/checkm2_summary.tsv"
else
    echo "Error: CheckM2 quality report not found."
fi
