#!/bin/bash

# Create output directory for stats
mkdir -p assembly_stats

# Function to calculate N50
calculate_n50() {
    local file=$1
    local total_size=0
    local lengths=()
    
    # Extract contig lengths
    while read -r line; do
        if [[ ${line:0:1} == ">" ]]; then
            continue
        fi
        length=${#line}
        total_size=$((total_size + length))
        lengths+=(${length})
    done < <(zcat "$file")
    
    # Sort lengths in descending order
    IFS=$'\n' sorted_lengths=($(sort -nr <<<"${lengths[*]}"))
    unset IFS
    
    # Calculate N50
    local running_sum=0
    local n50=0
    for length in "${sorted_lengths[@]}"; do
        running_sum=$((running_sum + length))
        if [ $running_sum -ge $((total_size / 2)) ]; then
            n50=${length}
            break
        fi
    done
    
    echo "$n50"
}

# Function to calculate GC content
calculate_gc() {
    local file=$1
    local sequence=""
    
    # Extract all sequences
    while read -r line; do
        if [[ ${line:0:1} == ">" ]]; then
            continue
        fi
        sequence+=${line}
    done < <(zcat "$file")
    
    # Count GC bases
    local gc_count=$(echo "$sequence" | tr -cd 'GCgc' | wc -c)
    local total_bases=${#sequence}
    
    # Calculate GC percentage
    local gc_percent=$(echo "scale=2; ($gc_count * 100) / $total_bases" | bc)
    
    echo "$gc_percent"
}

# Function to count contigs
count_contigs() {
    local file=$1
    local count=$(zcat "$file" | grep -c "^>")
    echo "$count"
}

# Function to calculate total assembly size
calculate_size() {
    local file=$1
    local total_size=0
    
    # Extract all sequences and count bases
    while read -r line; do
        if [[ ${line:0:1} == ">" ]]; then
            continue
        fi
        total_size=$((total_size + ${#line}))
    done < <(zcat "$file")
    
    echo "$total_size"
}

# Create header for the stats file
echo -e "Sample\tN50\tGC%\tAssembly Size\tNumber of Contigs" > assembly_stats/assembly_stats.tsv

# Process each scaffold file
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    SCAFFOLD_FILE="01assemblies/${SAMPLE}/scaffolds.fasta.gz"
    
    if [ -f "$SCAFFOLD_FILE" ]; then
        echo "Processing $SAMPLE..."
        
        # Calculate statistics
        N50=$(calculate_n50 "$SCAFFOLD_FILE")
        GC=$(calculate_gc "$SCAFFOLD_FILE")
        SIZE=$(calculate_size "$SCAFFOLD_FILE")
        CONTIGS=$(count_contigs "$SCAFFOLD_FILE")
        
        # Append to stats file
        echo -e "${SAMPLE}\t${N50}\t${GC}\t${SIZE}\t${CONTIGS}" >> assembly_stats/assembly_stats.tsv
    else
        echo "Scaffold file for $SAMPLE not found."
    fi
done

echo "Assembly statistics calculation completed."
