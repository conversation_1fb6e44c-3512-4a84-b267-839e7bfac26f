#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J bwa_1507999
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-bwa_1507999.out
#SBATCH --error=slurm-bwa_1507999.err

# Define sample ID
SAMPLE_ID=1507999

# Define directories
READS_DIR="00data/readsf"
ASSEMBLY_DIR="01assemblies"
OUTPUT_DIR="02mapping"

# Define resources
THREADS=16

# Clean up any existing files
rm -rf ${OUTPUT_DIR}/${SAMPLE_ID}

# Create output directory
mkdir -p ${OUTPUT_DIR}/${SAMPLE_ID}

# Create a temporary directory for intermediate files
TEMP_DIR=${OUTPUT_DIR}/${SAMPLE_ID}/temp
mkdir -p ${TEMP_DIR}

# Path to scaffold file
SCAFFOLD_FILE=${ASSEMBLY_DIR}/${SAMPLE_ID}/scaffolds.fasta.gz

# Decompress scaffold file for indexing
gunzip -c ${SCAFFOLD_FILE} > ${TEMP_DIR}/scaffolds.fasta

# Index the scaffold file with BWA
echo "Indexing scaffold file for ${SAMPLE_ID}..."
module load bwa
bwa index ${TEMP_DIR}/scaffolds.fasta

# Align reads to the scaffold using BWA-MEM and pipe to samtools for BAM conversion and sorting
echo "Aligning reads to scaffolds and creating sorted BAM for ${SAMPLE_ID}..."
module load samtools
bwa mem -t ${THREADS} ${TEMP_DIR}/scaffolds.fasta ${READS_DIR}/${SAMPLE_ID}.anqdpht.fastq.gz | \
    samtools view -bS - | \
    samtools sort -@ ${THREADS} -o ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam -

# Index the BAM file
echo "Indexing BAM file for ${SAMPLE_ID}..."
samtools index ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam

# Generate mapping statistics
echo "Generating mapping statistics for ${SAMPLE_ID}..."
samtools flagstat ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.sorted.bam > ${OUTPUT_DIR}/${SAMPLE_ID}/${SAMPLE_ID}.flagstat.txt

# Clean up temporary files
rm -rf ${TEMP_DIR}

echo "Alignment completed for sample: ${SAMPLE_ID}"
