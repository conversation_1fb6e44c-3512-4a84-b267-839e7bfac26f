#!/bin/bash

# Create output directory for bin stats
mkdir -p bin_stats

# Create header for the stats file
echo -e "Sample\tBin\tNum_contigs\tAssembly_size\tN50\tGC%" > bin_stats/bin_stats.tsv

# Process each bin file
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    for BIN_FILE in $(find 03bins/metabat2_fixed/${SAMPLE} -name "${SAMPLE}.bin.*.fa" | sort); do
        BIN_NAME=$(basename ${BIN_FILE} .fa)
        
        echo "Processing ${SAMPLE} - ${BIN_NAME}..."
        
        # Create temporary files
        LENGTHS_FILE=$(mktemp)
        GC_FILE=$(mktemp)
        
        # Extract contig lengths and calculate GC content
        awk '
        BEGIN {
            len=0;
            seq="";
            in_record=0;
            total_gc=0;
            total_len=0;
        }
        /^>/ {
            if (len > 0) {
                print len;
                len=0;
                seq="";
            }
            in_record=1;
            next;
        }
        {
            if (in_record) {
                line=toupper($0);
                len += length(line);
                
                # Count GC bases
                gc_count = gsub(/[GC]/, "", line);
                total_gc += gc_count;
                total_len += length(line);
                
                seq = seq line;
            }
        }
        END {
            if (len > 0) {
                print len;
            }
            
            # Calculate GC percentage
            if (total_len > 0) {
                printf "%.2f", (total_gc / total_len) * 100 > "'$GC_FILE'";
            } else {
                print "0.00" > "'$GC_FILE'";
            }
        }' ${BIN_FILE} > ${LENGTHS_FILE}
        
        # Calculate statistics
        NUM_CONTIGS=$(wc -l < ${LENGTHS_FILE})
        TOTAL_LENGTH=$(awk '{sum += $1} END {print sum}' ${LENGTHS_FILE})
        GC_PERCENT=$(cat ${GC_FILE})
        
        # Sort lengths in descending order for N50 calculation
        sort -nr ${LENGTHS_FILE} > ${LENGTHS_FILE}.sorted
        
        # Calculate N50
        SUM=0
        N50=0
        if [ ${TOTAL_LENGTH} -gt 0 ]; then
            while read -r LENGTH; do
                SUM=$((SUM + LENGTH))
                if [ ${SUM} -ge $((TOTAL_LENGTH / 2)) ] && [ ${N50} -eq 0 ]; then
                    N50=${LENGTH}
                    break
                fi
            done < ${LENGTHS_FILE}.sorted
        fi
        
        # Append to stats file
        echo -e "${SAMPLE}\t${BIN_NAME}\t${NUM_CONTIGS}\t${TOTAL_LENGTH}\t${N50}\t${GC_PERCENT}" >> bin_stats/bin_stats.tsv
        
        # Clean up temporary files
        rm -f ${LENGTHS_FILE} ${LENGTHS_FILE}.sorted ${GC_FILE}
    done
done

echo "Bin statistics calculation completed."
