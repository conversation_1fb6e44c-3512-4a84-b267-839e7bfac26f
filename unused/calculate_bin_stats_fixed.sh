#!/bin/bash

# Create output directory for bin stats
mkdir -p bin_stats_fixed

# Create header for the stats file
echo -e "Sample\tBin\tNum_contigs\tAssembly_size\tN50\tGC%" > bin_stats_fixed/bin_stats.tsv

# Process each bin file
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    for BIN_FILE in $(find 03bins/metabat2_fixed/${SAMPLE} -name "${SAMPLE}.bin.*.fa" | sort); do
        BIN_NAME=$(basename ${BIN_FILE} .fa)
        
        echo "Processing ${SAMPLE} - ${BIN_NAME}..."
        
        # Create temporary files
        LENGTHS_FILE=$(mktemp)
        SEQS_FILE=$(mktemp)
        
        # Extract contig lengths and sequences
        awk '
        BEGIN {
            len=0;
            seq="";
            header="";
            in_record=0;
        }
        /^>/ {
            if (len > 0) {
                print header "\t" len "\t" seq;
                len=0;
                seq="";
            }
            header=$0;
            in_record=1;
            next;
        }
        {
            if (in_record) {
                len += length($0);
                seq = seq $0;
            }
        }
        END {
            if (len > 0) {
                print header "\t" len "\t" seq;
            }
        }' ${BIN_FILE} > ${SEQS_FILE}
        
        # Calculate statistics
        NUM_CONTIGS=$(wc -l < ${SEQS_FILE})
        
        # Calculate total length and extract lengths
        TOTAL_LENGTH=0
        while IFS=$'\t' read -r header len seq; do
            TOTAL_LENGTH=$((TOTAL_LENGTH + len))
            echo ${len} >> ${LENGTHS_FILE}
        done < ${SEQS_FILE}
        
        # Calculate GC content
        GC_COUNT=0
        TOTAL_BASES=0
        while IFS=$'\t' read -r header len seq; do
            # Convert to uppercase
            seq=$(echo ${seq} | tr '[:lower:]' '[:upper:]')
            
            # Count G and C bases
            g_count=$(echo ${seq} | tr -cd 'G' | wc -c)
            c_count=$(echo ${seq} | tr -cd 'C' | wc -c)
            
            GC_COUNT=$((GC_COUNT + g_count + c_count))
            TOTAL_BASES=$((TOTAL_BASES + len))
        done < ${SEQS_FILE}
        
        # Calculate GC percentage
        if [ ${TOTAL_BASES} -gt 0 ]; then
            GC_PERCENT=$(echo "scale=2; (${GC_COUNT} * 100) / ${TOTAL_BASES}" | bc)
        else
            GC_PERCENT="0.00"
        fi
        
        # Sort lengths in descending order for N50 calculation
        sort -nr ${LENGTHS_FILE} > ${LENGTHS_FILE}.sorted
        
        # Calculate N50
        SUM=0
        N50=0
        if [ ${TOTAL_LENGTH} -gt 0 ]; then
            while read -r LENGTH; do
                SUM=$((SUM + LENGTH))
                if [ ${SUM} -ge $((TOTAL_LENGTH / 2)) ] && [ ${N50} -eq 0 ]; then
                    N50=${LENGTH}
                    break
                fi
            done < ${LENGTHS_FILE}.sorted
        fi
        
        # Append to stats file
        echo -e "${SAMPLE}\t${BIN_NAME}\t${NUM_CONTIGS}\t${TOTAL_LENGTH}\t${N50}\t${GC_PERCENT}" >> bin_stats_fixed/bin_stats.tsv
        
        # Clean up temporary files
        rm -f ${LENGTHS_FILE} ${LENGTHS_FILE}.sorted ${SEQS_FILE}
    done
done

echo "Bin statistics calculation completed."
