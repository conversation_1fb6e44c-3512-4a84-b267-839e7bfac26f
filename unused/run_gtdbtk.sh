#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J gtdbtk
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=128GB
#SBATCH -t 48:00:00
#SBATCH --output=slurm-gtdbtk_%A.out
#SBATCH --error=slurm-gtdbtk_%A.err

# Define paths
BINS_DIR="03bins/metabat2_fixed"
OUTPUT_DIR="04quality/gtdbtk"
GTDBTK_DB="/clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220"

# Create output directory
mkdir -p ${OUTPUT_DIR}

# Load modules or use Singularity
module load gtdbtk/2.3.2

# Set the GTDB-Tk database path
export GTDBTK_DATA_PATH=${GTDBTK_DB}

# Create a directory to store the bins for GTDB-Tk
mkdir -p ${OUTPUT_DIR}/bins

# Copy all bins to the GTDB-Tk bins directory
echo "Copying bins to GTDB-Tk directory..."
for BIN_FILE in $(find ${BINS_DIR} -name "*.bin.*.fa"); do
    BIN_NAME=$(basename ${BIN_FILE})
    cp ${BIN_FILE} ${OUTPUT_DIR}/bins/${BIN_NAME}
done

# Run GTDB-Tk
echo "Running GTDB-Tk on all bins..."
gtdbtk classify_wf --genome_dir ${OUTPUT_DIR}/bins \
                   --out_dir ${OUTPUT_DIR} \
                   --cpus 16 \
                   --extension fa \
                   --skip_ani_screen

echo "GTDB-Tk analysis completed."

# Create a simplified summary file
echo -e "Bin\tDomain\tPhylum\tClass\tOrder\tFamily\tGenus\tSpecies\tFastANI_Reference\tFastANI_ANI\tClassification_Method" > ${OUTPUT_DIR}/gtdbtk_summary.tsv

# Parse the bacterial results
if [ -f "${OUTPUT_DIR}/classify/gtdbtk.bac120.summary.tsv" ]; then
    tail -n +2 ${OUTPUT_DIR}/classify/gtdbtk.bac120.summary.tsv | while IFS=$'\t' read -r user_genome classification fastani_reference fastani_ani fastani_af closest_placement_reference closest_placement_radius closest_placement_ani closest_placement_af pplacer_taxonomy classification_method note other_related_references msa_percent aa_percent translation_table red_value warnings; do
        domain=$(echo ${pplacer_taxonomy} | cut -d';' -f1)
        phylum=$(echo ${pplacer_taxonomy} | cut -d';' -f2)
        class=$(echo ${pplacer_taxonomy} | cut -d';' -f3)
        order=$(echo ${pplacer_taxonomy} | cut -d';' -f4)
        family=$(echo ${pplacer_taxonomy} | cut -d';' -f5)
        genus=$(echo ${pplacer_taxonomy} | cut -d';' -f6)
        species=$(echo ${pplacer_taxonomy} | cut -d';' -f7)
        
        echo -e "${user_genome}\t${domain}\t${phylum}\t${class}\t${order}\t${family}\t${genus}\t${species}\t${fastani_reference}\t${fastani_ani}\t${classification_method}" >> ${OUTPUT_DIR}/gtdbtk_summary.tsv
    done
fi

# Parse the archaeal results
if [ -f "${OUTPUT_DIR}/classify/gtdbtk.ar53.summary.tsv" ]; then
    tail -n +2 ${OUTPUT_DIR}/classify/gtdbtk.ar53.summary.tsv | while IFS=$'\t' read -r user_genome classification fastani_reference fastani_ani fastani_af closest_placement_reference closest_placement_radius closest_placement_ani closest_placement_af pplacer_taxonomy classification_method note other_related_references msa_percent aa_percent translation_table red_value warnings; do
        domain=$(echo ${pplacer_taxonomy} | cut -d';' -f1)
        phylum=$(echo ${pplacer_taxonomy} | cut -d';' -f2)
        class=$(echo ${pplacer_taxonomy} | cut -d';' -f3)
        order=$(echo ${pplacer_taxonomy} | cut -d';' -f4)
        family=$(echo ${pplacer_taxonomy} | cut -d';' -f5)
        genus=$(echo ${pplacer_taxonomy} | cut -d';' -f6)
        species=$(echo ${pplacer_taxonomy} | cut -d';' -f7)
        
        echo -e "${user_genome}\t${domain}\t${phylum}\t${class}\t${order}\t${family}\t${genus}\t${species}\t${fastani_reference}\t${fastani_ani}\t${classification_method}" >> ${OUTPUT_DIR}/gtdbtk_summary.tsv
    done
fi

echo "GTDB-Tk summary created at ${OUTPUT_DIR}/gtdbtk_summary.tsv"
