#!/bin/bash

# Create output directory for stats
mkdir -p assembly_stats_fixed

# Create header for the stats file
echo -e "Sample\tNum_contigs\tAssembly_size\tN50\tGC%" > assembly_stats_fixed/assembly_stats.tsv

# Process each scaffold file
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    SCAFFOLD_FILE="01assemblies/${SAMPLE}/scaffolds.fasta.gz"
    
    if [ -f "$SCAFFOLD_FILE" ]; then
        echo "Processing $SAMPLE..."
        
        # Create temporary files
        LENGTHS_FILE=$(mktemp)
        GC_FILE=$(mktemp)
        
        # Extract scaffold lengths
        zcat "$SCAFFOLD_FILE" | awk '
        BEGIN {
            len=0;
        }
        /^>/ {
            if (len > 0) {
                print len;
                len=0;
            }
            next;
        }
        {
            len += length($0);
        }
        END {
            if (len > 0) {
                print len;
            }
        }' > "$LENGTHS_FILE"
        
        # Calculate GC content
        zcat "$SCAFFOLD_FILE" | awk '
        BEGIN {
            gc=0;
            total=0;
        }
        /^>/ {
            next;
        }
        {
            line=toupper($0);
            gc += gsub(/[GC]/, "", line);
            total += length($0);
        }
        END {
            printf "%.2f\n", (gc / total) * 100;
        }' > "$GC_FILE"
        
        # Calculate statistics
        NUM_CONTIGS=$(wc -l < "$LENGTHS_FILE")
        TOTAL_LENGTH=$(awk '{sum += $1} END {print sum}' "$LENGTHS_FILE")
        GC_PERCENT=$(cat "$GC_FILE")
        
        # Sort lengths in descending order
        sort -nr "$LENGTHS_FILE" > "${LENGTHS_FILE}.sorted"
        
        # Calculate N50
        SUM=0
        N50=0
        while read -r LENGTH; do
            SUM=$((SUM + LENGTH))
            if [ $SUM -ge $((TOTAL_LENGTH / 2)) ] && [ $N50 -eq 0 ]; then
                N50=$LENGTH
                break
            fi
        done < "${LENGTHS_FILE}.sorted"
        
        # Append to stats file
        echo -e "${SAMPLE}\t${NUM_CONTIGS}\t${TOTAL_LENGTH}\t${N50}\t${GC_PERCENT}" >> assembly_stats_fixed/assembly_stats.tsv
        
        # Clean up temporary files
        rm -f "$LENGTHS_FILE" "${LENGTHS_FILE}.sorted" "$GC_FILE"
    else
        echo "Scaffold file for $SAMPLE not found."
    fi
done

echo "Assembly statistics calculation completed."
