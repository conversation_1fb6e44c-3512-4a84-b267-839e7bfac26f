#!/bin/bash

# Create a formatted summary table
echo "# EukCC Quality Assessment Results with Taxonomy" > eukcc_taxonomy_summary.md
echo "" >> eukcc_taxonomy_summary.md
echo "| Sample | Bin | Completeness (%) | Contamination (%) | Clade | TaxID |" >> eukcc_taxonomy_summary.md
echo "|--------|-----|-----------------|-------------------|-------|-------|" >> eukcc_taxonomy_summary.md

# Process each result file
for LOG_FILE in $(find 04quality/eukcc_conda -name "eukcc.log" | sort); do
    # Extract bin name from the path
    BIN_DIR=$(dirname ${LOG_FILE})
    BIN_NAME=$(basename ${BIN_DIR})
    SAMPLE=$(echo ${BIN_NAME} | cut -d'.' -f1)
    
    # Get the CSV file
    CSV_FILE="${BIN_DIR}/eukcc.csv"
    
    # Extract completeness and contamination
    if [ -f "${CSV_FILE}" ]; then
        DATA_LINE=$(tail -n 1 ${CSV_FILE})
        COMPLETENESS=$(echo ${DATA_LINE} | awk '{print $2}')
        CONTAMINATION=$(echo ${DATA_LINE} | awk '{print $3}')
    else
        COMPLETENESS="NA"
        CONTAMINATION="NA"
    fi
    
    # Extract clade and TaxID from the log file
    CLADE_LINE=$(grep "Genome belongs to clade:" ${LOG_FILE})
    if [ ! -z "${CLADE_LINE}" ]; then
        CLADE=$(echo ${CLADE_LINE} | sed 's/.*clade: \([^(]*\).*/\1/' | tr -d '\r')
        TAXID=$(echo ${CLADE_LINE} | sed 's/.*TaxID: \([0-9]*\).*/\1/' | tr -d '\r')
    else
        CLADE="Unknown"
        TAXID="Unknown"
    fi
    
    # Add the formatted line to the summary table
    echo "| ${SAMPLE} | ${BIN_NAME} | ${COMPLETENESS} | ${CONTAMINATION} | ${CLADE} | ${TAXID} |" >> eukcc_taxonomy_summary.md
done

echo "" >> eukcc_taxonomy_summary.md
echo "Generated on $(date)" >> eukcc_taxonomy_summary.md

cat eukcc_taxonomy_summary.md
