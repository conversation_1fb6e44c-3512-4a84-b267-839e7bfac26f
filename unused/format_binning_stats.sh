#!/bin/bash

# Create a formatted summary table
echo "# MetaBAT2 Binning Summary" > binning_summary.md
echo "" >> binning_summary.md
echo "| Sample | Number of Bins | Total Binned Size (bp) | Binning Rate (%) |" >> binning_summary.md
echo "|--------|---------------|------------------------|------------------|" >> binning_summary.md

# Process each sample
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    if [ -d "03bins/metabat2/${SAMPLE}" ]; then
        # Count the number of bins
        BIN_COUNT=$(find 03bins/metabat2/${SAMPLE} -name "${SAMPLE}.bin.*.fa" | wc -l)
        
        # Calculate the total binned size
        BINNED_SIZE=0
        for BIN_FILE in $(find 03bins/metabat2/${SAMPLE} -name "${SAMPLE}.bin.*.fa"); do
            BIN_SIZE=$(grep -v ">" ${BIN_FILE} | tr -d '\n' | wc -c)
            BINNED_SIZE=$((BINNED_SIZE + BIN_SIZE))
        done
        
        # Get the assembly size
        ASSEMBLY_SIZE=$(grep "^${SAMPLE}" assembly_stats_fixed/assembly_stats.tsv | cut -f3)
        
        # Calculate the binning rate
        BINNING_RATE=$(echo "scale=2; (${BINNED_SIZE} * 100) / ${ASSEMBLY_SIZE}" | bc)
        
        # Format the binned size with commas
        FORMATTED_BINNED_SIZE=$(echo "$BINNED_SIZE" | sed ':a;s/\B[0-9]\{3\}\>/,&/;ta')
        
        # Add the formatted line to the summary table
        echo "| $SAMPLE | $BIN_COUNT | $FORMATTED_BINNED_SIZE | $BINNING_RATE |" >> binning_summary.md
    else
        echo "| $SAMPLE | 0 | 0 | 0.00 |" >> binning_summary.md
    fi
done

echo "" >> binning_summary.md
echo "Generated on $(date)" >> binning_summary.md

cat binning_summary.md
