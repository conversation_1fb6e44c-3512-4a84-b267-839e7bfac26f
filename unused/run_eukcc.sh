#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J eukcc
#SBATCH -N 1
#SBATCH -c 16
#SBATCH --mem=64GB
#SBATCH -t 24:00:00
#SBATCH --output=slurm-eukcc_%A.out
#SBATCH --error=slurm-eukcc_%A.err

# Define paths
BINS_DIR="03bins/metabat2_fixed"
OUTPUT_DIR="04quality/eukcc"
EUKCC_DB="/clusterfs/jgi/scratch/science/mgs/nelli/databases/eukCC/eukccdb/eukcc2_db_ver_1.2"

# Create output directory
mkdir -p ${OUTPUT_DIR}

# Load modules or use Singularity
module load eukcc/2.1.0

# Run EukCC on all bins
echo "Running EukCC on all bins..."

# Create a list of all bin files
find ${BINS_DIR} -name "*.bin.*.fa" > ${OUTPUT_DIR}/bin_list.txt

# Run EukCC
eukcc --db ${EUKCC_DB} \
      --threads 16 \
      --out ${OUTPUT_DIR} \
      --fasta_list ${OUTPUT_DIR}/bin_list.txt

echo "EukCC analysis completed."

# Create a summary file
echo -e "Bin\tCompleteness\tContamination\tTaxonomy" > ${OUTPUT_DIR}/eukcc_summary.tsv

# Parse the results
for RESULT_FILE in $(find ${OUTPUT_DIR} -name "*.eukcc.tsv"); do
    BIN_NAME=$(basename ${RESULT_FILE} .eukcc.tsv)
    COMPLETENESS=$(grep -v "^#" ${RESULT_FILE} | cut -f2)
    CONTAMINATION=$(grep -v "^#" ${RESULT_FILE} | cut -f3)
    TAXONOMY=$(grep -v "^#" ${RESULT_FILE} | cut -f4)
    
    echo -e "${BIN_NAME}\t${COMPLETENESS}\t${CONTAMINATION}\t${TAXONOMY}" >> ${OUTPUT_DIR}/eukcc_summary.tsv
done

echo "EukCC summary created at ${OUTPUT_DIR}/eukcc_summary.tsv"
