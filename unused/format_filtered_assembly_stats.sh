#!/bin/bash

# Create a formatted summary table
echo "# Assembly Statistics Summary (Contigs ≥ 2 kb)" > filtered_assembly_summary.md
echo "" >> filtered_assembly_summary.md
echo "| Sample | Number of Contigs | Assembly Size (bp) | N50 (bp) | GC% | % of Total Assembly |" >> filtered_assembly_summary.md
echo "|--------|------------------|-------------------|----------|-----|-------------------|" >> filtered_assembly_summary.md

# Read the stats file and format the data
while IFS=$'\t' read -r sample num_contigs size n50 gc num_contigs_unfiltered size_unfiltered; do
    # Skip the header line
    if [ "$sample" != "Sample" ]; then
        # Format the assembly size with commas
        formatted_size=$(echo "$size" | sed ':a;s/\B[0-9]\{3\}\>/,&/;ta')
        
        # Format the N50 with commas
        formatted_n50=$(echo "$n50" | sed ':a;s/\B[0-9]\{3\}\>/,&/;ta')
        
        # Calculate percentage of total assembly
        percent_of_total=$(echo "scale=2; ($size * 100) / $size_unfiltered" | bc)
        
        # Add the formatted line to the summary table
        echo "| $sample | $num_contigs | $formatted_size | $formatted_n50 | $gc | $percent_of_total |" >> filtered_assembly_summary.md
    fi
done < assembly_stats_filtered/assembly_stats.tsv

echo "" >> filtered_assembly_summary.md
echo "Generated on $(date)" >> filtered_assembly_summary.md

cat filtered_assembly_summary.md
