#!/bin/bash

# Create output directory for stats
mkdir -p assembly_stats_manual

# Create header for the stats file
echo -e "Sample\tNum_contigs\tAssembly_size\tN50\tGC%" > assembly_stats_manual/assembly_stats.tsv

# Process each scaffold file
for SAMPLE in 1507990 1507992 1507993 1507994 1507995 1507996 1507999; do
    SCAFFOLD_FILE="01assemblies/${SAMPLE}/scaffolds.fasta.gz"
    
    if [ -f "$SCAFFOLD_FILE" ]; then
        echo "Processing $SAMPLE..."
        
        # Create a temporary file to store the scaffold lengths
        TEMP_FILE=$(mktemp)
        
        # Extract scaffold lengths and calculate GC content
        zcat "$SCAFFOLD_FILE" | awk '
        BEGIN {
            total_length = 0;
            gc_count = 0;
            total_bases = 0;
            current_length = 0;
        }
        /^>/ {
            if (current_length > 0) {
                print current_length;
                current_length = 0;
            }
            next;
        }
        {
            line = $0;
            current_length += length(line);
            total_length += length(line);
            gc_count += gsub(/[GCgc]/, "", line);
            total_bases += length(line);
        }
        END {
            if (current_length > 0) {
                print current_length;
            }
            print "GC\t" gc_count / total_bases * 100;
            print "TOTAL\t" total_length;
        }' > "$TEMP_FILE"
        
        # Calculate N50
        TOTAL_LENGTH=$(grep "TOTAL" "$TEMP_FILE" | cut -f2)
        GC_PERCENT=$(grep "GC" "$TEMP_FILE" | cut -f2)
        NUM_CONTIGS=$(grep -v "GC\|TOTAL" "$TEMP_FILE" | wc -l)
        
        # Sort lengths in descending order
        grep -v "GC\|TOTAL" "$TEMP_FILE" | sort -nr > "${TEMP_FILE}.sorted"
        
        # Calculate N50
        SUM=0
        N50=0
        while read -r LENGTH; do
            SUM=$((SUM + LENGTH))
            if [ $SUM -ge $((TOTAL_LENGTH / 2)) ] && [ $N50 -eq 0 ]; then
                N50=$LENGTH
                break
            fi
        done < "${TEMP_FILE}.sorted"
        
        # Append to stats file
        echo -e "${SAMPLE}\t${NUM_CONTIGS}\t${TOTAL_LENGTH}\t${N50}\t${GC_PERCENT}" >> assembly_stats_manual/assembly_stats.tsv
        
        # Clean up temporary files
        rm -f "$TEMP_FILE" "${TEMP_FILE}.sorted"
    else
        echo "Scaffold file for $SAMPLE not found."
    fi
done

echo "Assembly statistics calculation completed."
