#!/bin/bash

#variables
MAIN_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/03bins/metabat2_fixed/"
REFERENCE="/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/acanthamoeba_castellani_genome.fna"
OUTPUT_DIR="/clusterfs/jgi/scratch/science/mgs/nelli/shana/protistpta/nf_pipeline_2/acantheamoeba_mapping_results"
THREADS=8
MIN_ANI=95

mkdir -p $OUTPUT_DIR

find $MAIN_DIR -type f -name "*.fa" | while read -r contig_file; do
    dir_name=$(basename $(dirname "$contig_file"))
    file_name=$(basename "$contig_file" | sed 's/\.[^.]*$//')
    output_name="${dir_name}_${file_name}_results.txt"

    echo "Processing $contig_file..."
    
    #run skani dist
    skani dist -q "$contig_file" -r $REFERENCE -o "$OUTPUT_DIR/$output_name" -t $THREADS --min-af 0.05

    awk -v ani=$MIN_ANI '$3 > ani' "$OUTPUT_DIR/$output_name" > "$OUTPUT_DIR/filtered_${output_name}"

    echo "Results saved to: $OUTPUT_DIR/$output_name"
    echo "High ANI matches (>$MIN_ANI%) saved to: $OUTPUT_DIR/filtered_${output_name}"
    echo "---"
done

#combine high quality results
echo "Combining all high-quality results..."
cat "$OUTPUT_DIR/filtered_"* > "$OUTPUT_DIR/all_high_ani_matches.tsv"
echo "Combined results saved to: $OUTPUT_DIR/all_high_ani_matches.tsv"
